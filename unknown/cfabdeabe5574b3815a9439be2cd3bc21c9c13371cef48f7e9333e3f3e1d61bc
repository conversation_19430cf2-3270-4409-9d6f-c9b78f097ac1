import { defaultState } from '../store';
import { REQUEST_USER_ORGANIZATION_SUCCESS } from '../actions';
import { OnboardingState, OrganizationPayload } from '../models';
import { LOCALSTORAGE_LANGUAGE_KEY } from '@common/i18n/createIntl';
import { CaseReducer, createReducer, PayloadAction } from '@reduxjs/toolkit';

type Re<P = unknown> = CaseReducer<OnboardingState, PayloadAction<P>>;
const requestUserOrganizationSuccess: Re<OrganizationPayload> = (
  state,
  { payload }
) => {
  const preferredLanguage = payload.me.userSettings.find(
    ({ key }) => key === 'preferredLanguage'
  )?.value;

  if (preferredLanguage) {
    window.localStorage.setItem(LOCALSTORAGE_LANGUAGE_KEY, preferredLanguage);
  }
  return {
    ...state,
    organization: payload.me.organization,
  };
};

/**
 * Organization composed reducers
 */
const reducer = createReducer(defaultState, (builder) => {
  builder.addCase(
    REQUEST_USER_ORGANIZATION_SUCCESS,
    requestUserOrganizationSuccess
  );
});

export default reducer;

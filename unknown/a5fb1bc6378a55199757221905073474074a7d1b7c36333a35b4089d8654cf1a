import getLastRedactedFile from '@helpers/getLastRedactedFile';
import { has, maxBy } from 'lodash';
import { createSelector } from 'reselect';

import { selectCurrentRoutePayload } from '@common-modules/routing';
import { ContentType, DetailTDO, UDRsPolyAsset, ViewSettings } from '../models';
import { namespace } from '../store';
import {
  CasepointIntegration,
  DeletedOverlaysCache,
  DetectionAssetState,
  ExternalIntegration,
  ExterroIntegration,
  FOIAXpressIntegration,
  NuixIntegration,
} from '../store.models';
import { TDOId } from '@common-modules/universal/models/Brands';
import { CollectionWrapper } from '@common/web-worker';

// export interface LocalState {
//   [namespace]: DetectionAssetState &
//     ViewSettings & { fetchingTdo: boolean } & { [param: string]: any };
// }

export interface LocalState {
  [namespace]: DetectionAssetState &
    ViewSettings & { fetchingTdo: boolean } & {
      mediaDetailsPageSelectedTab: string;
      redactedAssetStarted: boolean;
      buttonTranscriptionDisable: boolean;
      gettingRedactionJobStatus: boolean;
      redactionJobStatus: string;
      redactionJobCreatedDateTime: string | null;
      tdoIsChanged: boolean;
      isFileStatusChanged: boolean;
      isSaveRunning: boolean;
      isShowConfirmLeave: boolean;
      redactionJob: { id: string };
      originalFileStatus: string;
      canLeave: boolean;
      isHoldingShift: boolean;
      udrCollection: CollectionWrapper;
      deletedOverlaysCache: DeletedOverlaysCache;
      detectObjects: {
        headObjects: boolean;
        personObjects: boolean;
      };
      showDetectOptionsDialog: boolean;
      udrsState: {
        localUDRAsset: UDRsPolyAsset;
        selectedUDRGroupId: string | undefined;
      };
      buttonDetectFaceDisable: boolean;
      integrations: {
        govQa: {
          sessionId?: string;
          isFormShown: boolean;
          isSendingFile: boolean;
          isStatusDialogShown: boolean;
        };
        foiaXpress: FOIAXpressIntegration;
        casepoint: CasepointIntegration;
        exterro: ExterroIntegration;
        nuix: NuixIntegration;
        externalIntegration: ExternalIntegration;
      };
      downloadUrl: string | null;
      pollingOpticalTrackingAreProcessing: boolean;
      downloadStarted: boolean;
      pollingDownloadDone: boolean;
      numberOfRedactedFile: number | undefined;
      auditLogs: unknown;
    };
}

export const local = (state: LocalState) => state[namespace];

export const selectCurrentTdoId = createSelector(
  selectCurrentRoutePayload,
  (payload) => payload.tdoId as TDOId | undefined
);

export const selectTdo = createSelector(local, (s) => s && s.tdo);

export const selectIsTdoReady = createSelector(
  selectTdo,
  (tdo) => tdo?.status === 'recorded' && tdo.primaryAsset !== null
);

export const selectPrimaryAsset = createSelector(
  selectTdo,
  (tdo) => tdo?.primaryAsset || null
);

export const selectThumbnailAssets = createSelector(
  selectTdo,
  (tdo) => tdo?.thumbnailAssets || null
);

export const selectTranscriptAssets = createSelector(
  selectTdo,
  (tdo) => tdo?.transcriptAssets || null
);

export const selectContentType = createSelector(
  selectPrimaryAsset,
  (primaryAsset) =>
    primaryAsset && primaryAsset.contentType
      ? primaryAsset.contentType.split('/')[0] === 'audio'
        ? ContentType.AUDIO
        : ContentType.AUDIO_VIDEO
      : ContentType.AUDIO_VIDEO
);

export const selectContentTypeHasVideo = createSelector(
  selectContentType,
  (contentType: ContentType) =>
    ContentType.AUDIO_VIDEO === contentType || ContentType.VIDEO === contentType
);

export const selectContentTypeHasAudio = createSelector(
  selectContentType,
  (contentType) =>
    ContentType.AUDIO === contentType || ContentType.AUDIO_VIDEO === contentType
);

export const selectTdoAreLoading = (state: LocalState) =>
  local(state).fetchingTdo;

export const selectCurrentPosition = createSelector(
  local,
  (s) => s.currentPosition
);

export const selectMediaDetailsPage = (state: LocalState) =>
  local(state).mediaDetailsPageSelectedTab;

export const getRedactedMediaCount = (tdo: DetailTDO | null) =>
  tdo?.redactedMediaAssets?.records?.length || 0;

export const getLastAuditLog = (tdo: DetailTDO | null) => {
  if (!tdo || !has(tdo, 'auditLogAssets.records')) {
    return null;
  }

  // check that audit log is for most recent redacted-media asset
  const redactedFile = getLastRedactedFile(tdo);
  const auditAsset = tdo?.auditLogAssets?.records?.[0];
  if (!auditAsset) {
    return null;
  }
  const auditDetails = auditAsset.details;

  // if no auditDetails must be an older asset - if no redactedFile in a weird state
  if (!auditDetails || !auditDetails['redactedMediaAssetId'] || !redactedFile) {
    return auditAsset;
  }

  if (auditDetails['redactedMediaAssetId'] === redactedFile.id) {
    return auditAsset;
  } else {
    return null;
  }
};

export const getLastRedactExport = (tdo: DetailTDO | null) => {
  // check that export is for most recent redacted-media asset
  const exportAsset = tdo?.redactExportAssets?.records?.[0];
  if (!exportAsset) {
    return null;
  }
  const exportDetails = exportAsset.details;

  const redactedFile = getLastRedactedFile(tdo);
  // if no tdoDetails must be an older asset - if no redactedFile in a weird state
  if (
    !exportDetails ||
    !exportDetails.tdos ||
    !exportDetails.tdos[tdo.id] ||
    !redactedFile
  ) {
    return exportAsset;
  }

  if (exportDetails.tdos[tdo.id]['redactedMediaAssetId'] === redactedFile.id) {
    return exportAsset;
  } else {
    return null;
  }
};

export const pickLastCompletedTaskByCreatedDate = <
  T extends {
    status: string;
    createdDateTime: string | null;
  },
>(
  tasks: T[]
) => {
  if (!tasks) {
    return null;
  }

  const tasksComplete = tasks.filter((item) => item.status === 'complete');

  if (tasksComplete.length === 0) {
    return { createdDateTime: 0 };
  }

  return maxBy(tasksComplete, 'createdDateTime');
};

export const pickLastCompletedTaskByCompletedDate = <
  T extends {
    status: string;
    completedDateTime?: string | null;
  },
>(
  tasks: T[]
) => {
  if (!tasks) {
    return null;
  }

  const tasksComplete = tasks.filter((task) => task.status === 'complete');

  if (tasksComplete.length === 0) {
    return { completedDateTime: 0 };
  }

  return maxBy(tasksComplete, 'completedDateTime');
};

export const selectIsLoading = (state: LocalState) => {
  if (local(state).loaded) {
    return false;
  } else if (!local(state).tdo) {
    return true;
  }
};

export const selectIsLoaded = (state: LocalState) => local(state).loaded;

export const selectDetails = (state: LocalState) =>
  ((tdo) => tdo?.details)(local(state).tdo);

// legacy videos / and audio only don't have frameRate
export const selectFrameRate = (state: LocalState) =>
  ((tdo) => tdo?.details?.veritoneFile?.videoFrameRate)(local(state).tdo);

export const selectTags = (state: LocalState) =>
  ((tdo) => tdo?.details?.tags ?? [])(local(state).tdo);

export const selectRedactedAssetStarted = (state: LocalState) =>
  local(state).redactedAssetStarted;

export const gettingRedactionJobStatusAreLoading = (state: LocalState) =>
  local(state).gettingRedactionJobStatus;

export const selectGettingRedactionJobStatus = (state: LocalState) =>
  local(state).gettingRedactionJobStatus;

export const selectRedactionJob = (state: LocalState) =>
  local(state).redactionJob;

export const selectRedactionJobStatus = (state: LocalState) =>
  local(state).redactionJobStatus;

export const selectRedactionJobCreatedDateTime = (state: LocalState) =>
  local(state).redactionJobCreatedDateTime;

export const selectNumberOfRedactedFile = (state: any) =>
  local(state).numberOfRedactedFile;

export const selectTdoIsChanged = (state: LocalState) =>
  local(state).tdoIsChanged ||
  local(state).isFileStatusChanged ||
  local(state).isSaveRunning;

export const selectIsSaveRunning = (state: LocalState) =>
  local(state).isSaveRunning;

export const selectIsShowConfirmLeave = (state: LocalState) =>
  local(state).isShowConfirmLeave;

export const selectCanLeave = (state: LocalState) => local(state).canLeave;

export const selectOriginalFileStatus = (state: LocalState) =>
  local(state).originalFileStatus;

export const selectIsFileStatusChanged = (state: LocalState) =>
  local(state).isFileStatusChanged;

export const selectIsHoldingShift = (state: LocalState) =>
  local(state).isHoldingShift;

export const selectUdrAsset = (state: LocalState) =>
  local(state).udrsState.localUDRAsset;

export const selectUdrCollection = (state: LocalState) =>
  local(state).udrCollection;

export const selectSelectedUDRGroupId = (state: LocalState) =>
  local(state).udrsState.selectedUDRGroupId;

export const selectDisableFacesButton = (state: LocalState) =>
  local(state).buttonDetectFaceDisable;

export const selectDisableTranscriptionButton = (state: LocalState) =>
  local(state).buttonTranscriptionDisable;

export const selectGovQASessionId = (state: LocalState) =>
  local(state).integrations.govQa.sessionId;

export const selectGovQAState = (state: LocalState) =>
  local(state).integrations.govQa;

export const selectDeletedOverlaysCache = (state: LocalState) =>
  local(state).deletedOverlaysCache;

export const selectFOIAXpressState = (state: LocalState) =>
  local(state).integrations.foiaXpress;

export const selectCasepointState = (state: LocalState) =>
  local(state).integrations.casepoint;

export const selectExterroState = (state: LocalState) =>
  local(state).integrations.exterro;

export const selectNuixState = (state: LocalState) =>
  local(state).integrations.nuix;

export const selectExternalIntegrationState = (state: LocalState) =>
  local(state).integrations.externalIntegration;

export const selectDownloadUrl = (state: LocalState) =>
  local(state).downloadUrl;

export const selectSelectedBoundingPolyGroup = (state: LocalState) =>
  local(state).selectedPolyGroups;

export const selectDetectionSeriesLengthWithDeleted = (state: LocalState) =>
  local(state).detectionSeriesLengthWithDeleted;

export const selectDetectObjects = (state: LocalState) =>
  local(state).detectObjects;

export const selectShowDetectOptionsDialog = (state: LocalState) =>
  local(state).showDetectOptionsDialog;

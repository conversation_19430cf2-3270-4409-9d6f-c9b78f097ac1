import messages from './messages';
import { LOCALES } from './locales';
import { IntlProvider } from 'react-intl';
import { Fragment, ReactNode } from 'react';

interface ProviderProps {
  readonly children: ReactNode | undefined;
  readonly language: string;
}

const Provider = ({ children, language }: ProviderProps) => {
  const locale = Object.values(LOCALES).includes(language)
    ? language
    : LOCALES.ENGLISH;

  return (
    <IntlProvider
      locale={
        Object.values(LOCALES).includes(locale)
          ? navigator.language
          : LOCALES.ENGLISH
      }
      defaultLocale={LOCALES.ENGLISH}
      textComponent={Fragment}
      messages={messages[locale]}
    >
      {children}
    </IntlProvider>
  );
};

export default Provider;

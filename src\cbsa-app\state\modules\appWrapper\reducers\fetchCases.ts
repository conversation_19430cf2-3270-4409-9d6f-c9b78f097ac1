import { Re } from '../reducers';
import { Case } from '@cbsa-modules/universal';

export const fetchCases: Re = (state) => ({
  ...state,
  cases: {},
  media: {},
  loaders: {
    ...state.loaders,
    isFetchingCases: true,
  },
});

export const fetchCasesSuccess: Re<{
  readonly searchMedia: { jsondata: { results: Case[] } };
}> = (state, { payload }) => {
  const cases: { [id: string]: Case } = {};
  payload.searchMedia.jsondata.results.forEach((caseData) => {
    /* Reassign BE variable names */
    caseData.treeObjectId = caseData.folderTreeObjectId;
    caseData.name = caseData.caseName ?? '';
    if (caseData.treeObjectId) {
      cases[caseData.treeObjectId] = caseData;
    }
  });
  return {
    ...state,
    cases,
    loaders: {
      ...state.loaders,
      isFetchingCases: false,
    },
  };
};

export const fetchCasesFailure: Re = (state) => ({
  ...state,
  loaders: {
    ...state.loaders,
    isFetchingCases: false,
  },
});

// TODO: Fix all the widget types
/* eslint-disable @typescript-eslint/no-unsafe-call */
import { createRoot } from 'react-dom/client';
import { isFunction } from 'lodash';
import { Provider } from 'react-redux';
import * as appModule from '@redact-modules/veritoneApp';
import configureAppStore from '@redact-state/configureStore';
import { modules, helpers } from '@veritone/glc-redux';
const { auth: authModule, config: configModule, user: userModule } = modules;
const { promiseMiddleware } = helpers;
const { WAIT_FOR_ACTION, ERROR_ACTION, CALLBACK_ERROR_ARGUMENT } =
  promiseMiddleware;

class VeritoneAppComponent {
  _store = configureAppStore();
  _containerEl: HTMLElement | null = null;
  // TODO: Can we type this better?
  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
  root: any | undefined = undefined;

  constructor(config: any) {
    this._store.dispatch(
      // TODO: Fix glc-redux return type
      configModule.setConfig({ ...window.config, ...config }) as any
    );
  }

  _register(widget: appModule.Widget) {
    console.log('Registering Widget');
    this._store.dispatch(appModule.widgetAdded(widget));
    this._renderReactApp();
  }

  _unregister(widget: appModule.Widget) {
    this._store.dispatch(appModule.widgetRemoved(widget));
    this._renderReactApp();
  }

  login({
    sessionToken,
    OAuthToken,
  }: { sessionToken?: string; OAuthToken?: string } = {}) {
    // Allows us to transform dispatch into a promise by adding symbols
    // See promiseMiddleware.js
    // TODO: can we type this stuff better?
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    const addSymbols = (action: any) => ({
      ...action,
      [WAIT_FOR_ACTION]: userModule.FETCH_USER_SUCCESS,
      [ERROR_ACTION]: userModule.FETCH_USER_FAILURE,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      [CALLBACK_ERROR_ARGUMENT]: (action: { payload: any }) => action.payload,
    });

    if (sessionToken) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return this._store.dispatch(
        addSymbols(authModule.setSessionToken(sessionToken))
      );
    } else if (OAuthToken) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return this._store.dispatch(
        addSymbols(authModule.setOAuthToken(OAuthToken))
      );
    } else {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return this._store.dispatch(addSymbols(authModule.checkAuthNoToken()));
    }
  }

  destroy() {
    if (this._containerEl) {
      // ReactDOM.unmountComponentAtNode(this._containerEl);
      this.root?.unmount();
      try {
        document.body.removeChild(this._containerEl);
      } catch (_e) {
        // ignore
      }
    }
  }

  setWidgetRef = (widget: any, ref: any) => {
    if (!ref) {
      // protect against errors when destroying the app
      return;
    }

    if (isFunction(ref.getWrappedInstance) && !ref.wrappedInstance) {
      console.warn(
        `Warning: the following widget looks like it's wrapped with a
         @connect decorator, but the withRef option is not set to true.
         { withRef: true } should be set as the fourth argument to @connect`
      );
      console.warn(widget);

      return;
    }

    // try to get at the base component for @connected widgets.
    // fixme: generic solution (hoisting specified instance methods?)
    // https://github.com/elado/hoist-non-react-methods
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const r = isFunction(ref.getWrappedInstance)
      ? ref.getWrappedInstance()
      : ref;

    widget.setRefProperties(r);
  };

  _renderReactApp() {
    this._containerEl = document.getElementById('veritone-react-app');
    if (!this._containerEl) {
      this._containerEl = document.createElement('div');
      this._containerEl.setAttribute('id', 'veritone-react-app');
      document.body.appendChild(this._containerEl);
    }
    const root = createRoot(this._containerEl);

    root.render(
      <Provider store={this._store}>
        <div>
          {appModule.widgets(this._store.getState()).map((w) => {
            if (!w._elId) {
              console.warn(
                'The widget',
                w,
                'needs to specify an elId that references an existing dom node.'
              );
              return null;
            }

            if (document.getElementById(w._elId)) {
              // @ts-expect-error TODO: Fix types
              // eslint-disable-next-line @typescript-eslint/no-unsafe-return
              return root.createPortal(
                <w.Component
                  {...w.props}
                  // bind is OK because this isn't a component -- only renders
                  // when mount() is called.

                  ref={this.setWidgetRef.bind(this, w)}
                />,
                document.getElementById(w._elId)
              );
            }
          })}
        </div>
      </Provider>
    );
  }
}

const global = window || {};
export default function VeritoneApp(config: any, { _isWidget }: any = {}) {
  // client calls this on init to configure the app:
  // import VeritoneApp from 'veritone-widgets';
  // VeritoneApp({ ...myConfig })
  if (!global.__veritoneAppSingleton) {
    if (_isWidget) {
      console.warn(
        `A widget was registered to an app which hasn't yet been initialized. Import and call VeritoneApp before constructing any widgets.`
      );
      return;
    }

    (global as any).__veritoneAppSingleton = new VeritoneAppComponent(config);
  }

  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  return global.__veritoneAppSingleton;
}

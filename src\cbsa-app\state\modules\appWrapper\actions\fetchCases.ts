import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { FetchCasesQueryResponse } from '../services/queries/fetchCases';
import { createAction } from '@reduxjs/toolkit';

export const FETCH_CASES = createAction<{
  query: string;
  offset: number;
  limit: number;
}>('CBSA/FETCH_CASES');
export const FETCH_CASES_SUCCESS =
  createGraphQLSuccessAction<FetchCasesQueryResponse>(
    'CBSA/FETCH_CASES_SUCCESS'
  );
export const FETCH_CASES_FAILURE = createGraphQLFailureAction(
  'CBSA/FETCH_CASES_FAILURE'
);

export const fetchCases = ({
  query,
  offset,
  limit,
}: {
  query: string;
  offset: number;
  limit: number;
}) => FETCH_CASES({ query, offset, limit });

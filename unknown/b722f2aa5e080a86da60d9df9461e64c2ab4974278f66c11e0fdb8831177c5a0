import { v4 as uuid } from 'uuid';
import { useIntl } from 'react-intl';
import { noop, groupBy, map, clamp, includes } from 'lodash';
import Audio1000Hz from '@resources/audios/1000.mp3';
import { useDispatch, useSelector } from 'react-redux';
import { stagedBoundingBoxStyles } from './getOverlayStyles';
import { useEffect, useRef, useState, useCallback } from 'react';
import { useMediaPlayer } from '@common-state/hooks/useMediaPlayer';
import { componentActions, componentSelectors } from './reduxHelpers';
import {
  GroupedBoundingPoly,
  search,
  BoundingPolyTree,
  CollectionWrapper,
} from '@worker';
import { isRedactedAudio } from '@redact-components/Transcription/utils';
import { DeletedOverlaysCache } from '@common/state/modules/mediaDetails/store.models';
import DefaultControlBar from '@common-components/SDKComponent/MediaPlayer/DefaultControlBar';
import { default as MediaPlayerComponent } from '@common-components/SDKComponent/MediaPlayer';
import {
  descaleBoundingPolyRect,
  scaleBoundingPolyRect,
  frameAlignStartTimeMs,
  frameAlignStopTimeMs,
} from '@utils';
import {
  DetailTDO,
  BoundingPolyRect,
  UDRsPolyAssetGroupSeriesItem,
} from '@common-modules/mediaDetails/models';
import { Player } from 'video-react';
import {
  DETECTION_COLLECTION_KEYS,
  DETECTION_TYPES,
  OBJECT_TYPE,
} from '@helpers/constants';
import { GroupIdAndType } from '@common/state/modules/mediaDetails';

function requestFullscreen(element: HTMLElement) {
  const elm = element as HTMLElement & {
    // Safari <= 16.3 1/22/2023
    webkitRequestFullscreen?: () => void;
  };
  if (elm.requestFullscreen) {
    elm.requestFullscreen();
  } else if (elm.webkitRequestFullscreen) {
    elm.webkitRequestFullscreen();
  }
}

function getOffsetTime(currentTime: number, videoOffset: number) {
  return Math.max(currentTime - videoOffset, 0);
}

function onPlayerPause({ target }: { target: EventTarget | null }) {
  if (target instanceof HTMLVideoElement) {
    console.log(
      'player info ->',
      target.currentTime,
      target.getVideoPlaybackQuality?.()
    );
  }
}

function filterPolySeries(
  tree: BoundingPolyTree,
  currentTime: number,
  selected: {
    readonly [id: string]: boolean | undefined;
  },
  fps: number | undefined,
  isUdr: boolean
) {
  // search for polys that may be visible at currentTime
  const polysAtCurrentTime = search(tree, currentTime);

  // filter to only selected
  const filteredPolysAtCurrentTime = polysAtCurrentTime.filter(
    (p) => !!selected[p.subsegmentId]
  );

  const polysAtCurrentTimeByLabel = groupBy(
    filteredPolysAtCurrentTime,
    'label'
  );

  // confirm if poly should be displayed on current frame / at currentTime
  let polySeries: GroupedBoundingPoly[];

  if (fps) {
    // if fps defined use exact frame boundaries to determine if poly should be displayed at currentTime
    polySeries = map(polysAtCurrentTimeByLabel, (polys) =>
      polys.find(
        (poly) =>
          currentTime >= frameAlignStartTimeMs(poly.startTimeMs, fps) &&
          currentTime < frameAlignStopTimeMs(poly.stopTimeMs, fps)
      )
    ).filter((poly): poly is GroupedBoundingPoly => !!poly);
  } else if (isUdr) {
    // select last match for each label - old convention for UDRs
    // this would only apply to TDOs before fall 2020, before video frame rate was saved
    polySeries = map(polysAtCurrentTimeByLabel, (polys) => polys.at(-1)).filter(
      (poly): poly is GroupedBoundingPoly => !!poly
    );
  } else {
    // this would only apply to TDOs before fall 2020, before videoFrameRate was saved
    polySeries = map(polysAtCurrentTimeByLabel, (polys) =>
      polys.find(
        (poly) =>
          currentTime >= poly.startTimeMs && currentTime <= poly.stopTimeMs
      )
    ).filter((poly): poly is GroupedBoundingPoly => !!poly);
  }
  return polySeries;
}

function getBoundingPolySeries(getBoundingPolySeriesInfo: {
  currentTime: number;
  fps: number | undefined;
  detectionCollections?: { [key: string]: CollectionWrapper | undefined };
  udrCollection?: BoundingPolyTree;
  selected: {
    readonly [id: string]: boolean | undefined;
  };
  udrBeingUpdated?: GroupedBoundingPoly;
  localOverlayBeingUpdated: GroupedBoundingPoly | null;
  updateOverlayCache: GroupedBoundingPoly | null;
  deletedOverlaysCache: DeletedOverlaysCache;
}): GroupedBoundingPoly[] {
  const {
    currentTime,
    fps,
    detectionCollections,
    udrCollection,
    udrBeingUpdated,
    localOverlayBeingUpdated,
    updateOverlayCache,
    selected,
    deletedOverlaysCache,
  } = getBoundingPolySeriesInfo;

  // logging to analyze lagging/rendering rate. Ideally deltas should be ~16 milliseconds
  // console.log(`%c analyze-lagging-currentTime= ${currentTime}`, 'color:orange');

  const detectionCollectionBoundingPolySeries = detectionCollections
    ? DETECTION_COLLECTION_KEYS.reduce<GroupedBoundingPoly[]>((acc, type) => {
        const detectedCollection = detectionCollections[type];

        if (!detectedCollection) {
          return acc;
        }
        // filter which object should be displayed
        const detectedPolySeries = filterPolySeries(
          detectedCollection.collection,
          currentTime,
          selected,
          fps,
          false
        );

        return acc.concat(detectedPolySeries);
      }, [])
    : [];

  let udrBoundingPolySeries: GroupedBoundingPoly[] = [];
  if (udrCollection) {
    udrBoundingPolySeries = filterPolySeries(
      udrCollection,
      currentTime,
      selected,
      fps,
      true
    );
  }

  // During live tracking we want to not render UDRs from the same group
  // and want to render UDR that's being dragged
  if (localOverlayBeingUpdated) {
    const idx = udrBoundingPolySeries.findIndex(
      ({ groupId }) => groupId === localOverlayBeingUpdated.groupId
    );

    if (idx !== -1) {
      udrBoundingPolySeries[idx] = localOverlayBeingUpdated;
    } else {
      udrBoundingPolySeries.push(localOverlayBeingUpdated);
    }
  }

  let boundingPolySeries = detectionCollectionBoundingPolySeries.concat(
    udrBoundingPolySeries
  );

  const shouldAddUDR =
    udrBeingUpdated &&
    boundingPolySeries.every(
      (p: GroupedBoundingPoly) => p.id !== udrBeingUpdated.id
    );

  if (shouldAddUDR && udrBeingUpdated) {
    boundingPolySeries.push(udrBeingUpdated);
  }

  if (
    updateOverlayCache &&
    boundingPolySeries.find(({ id }) => id === updateOverlayCache.id)
  ) {
    boundingPolySeries.forEach((p, idx, arr) => {
      if (updateOverlayCache?.id === p.id) {
        arr[idx] = updateOverlayCache;
      }
    });
  } else if (updateOverlayCache) {
    boundingPolySeries.push(updateOverlayCache);
  }

  boundingPolySeries = boundingPolySeries.filter(
    ({ id, subsegmentId }) =>
      !deletedOverlaysCache.overlayIdsMap[id] &&
      !deletedOverlaysCache.overlaySubsegmentIdsMap[subsegmentId]
  );

  return boundingPolySeries;
}

const MediaPlayerView = ({
  url,
  streams,
  hasAudio,
  hasVideo,
}: MediaPlayerViewPropTypes) => {
  const {
    detectionCollections,
    udrCollection,
    selected,
    redactedWords,
    overlayStyles,
    globalSettings,
    fps,
    udrBeingUpdated,
    highlightedOverlay,
    deletedOverlaysCache,
    mediaDuration,
  } = useSelector(componentSelectors);
  const intl = useIntl();
  const dispatch = useDispatch();
  const playerContainerRef = useRef<HTMLDivElement | null>(null);
  const playerRef = useRef<Player | null>(null);
  const noiseRef = useRef<HTMLAudioElement | null>(null);

  const [updateOverlayCache, setUpdateOverlayCache] =
    useState<GroupedBoundingPoly | null>(null);

  useEffect(() => {
    setUpdateOverlayCache(null);
  }, [detectionCollections, udrCollection]);

  const faceDetectionScaling = globalSettings.objectTypeEffects.head.scaling;

  const videoDuration = playerRef.current?.video.video?.duration || 0;

  const offsetTime = getOffsetTime(
    useMediaPlayer(playerRef.current?.video.video || null),
    globalSettings.videoOffset
  );

  // currentTime cannot be larger than videoDuration so the value is
  // clamped to mediaDuration but only if mediaDuration is defined
  const currentTime = mediaDuration
    ? clamp(offsetTime, mediaDuration * 1000)
    : offsetTime;

  const mutableContainer = useRef<{
    currentTime: number;
    lastCurrentTimeReported?: number;
    series: {
      boundingPoly: BoundingPolyRect;
      time: number;
    }[];
  }>({ currentTime, series: [], lastCurrentTimeReported: undefined });

  mutableContainer.current.currentTime = currentTime;

  const [prevVolume, setPrevVolume] = useState(
    playerRef.current?.video.video?.volume ?? 1
  );
  const [lastActivePoly, setLastActivePoly] =
    useState<GroupedBoundingPoly | null>(null);

  const [localOverlayBeingUpdated, setLocalOverlayBeingUpdated] =
    useState<GroupedBoundingPoly | null>(null);

  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  useEffect(() => {
    function exitHandler() {
      const document = window.document as typeof window.document & {
        // Safari <= 16.3 1/22/2023
        webkitIsFullScreen?: Element | null;
      };

      const localIsFullscreen =
        document.fullscreenElement || document.webkitIsFullScreen;

      setIsFullscreen(!!localIsFullscreen);
    }

    document.addEventListener('fullscreenchange', exitHandler);
    // Safari <= 16.3 1/22/2023
    document.addEventListener('webkitfullscreenchange', exitHandler);

    return () => {
      document.removeEventListener('fullscreenchange', exitHandler);
      document.removeEventListener('webkitfullscreenchange', exitHandler);
    };
  }, []);

  useEffect(() => {
    if (playerRef.current?.video.video) {
      playerRef.current.video.video.onpause = onPlayerPause;
    }
  });

  const handleStateChange = useCallback(
    (event: HTMLMediaElement) => {
      // HACK: Something keeps constantly updating the store state when SEEK_MEDIA action fires
      // that in turn keeps updating this component. As a result this component fires onUpdateCurrentTime too many times.
      // TODO: figure out why this pages state gets updating many times when SEEK_MEDIA action fires

      if (
        mutableContainer.current.lastCurrentTimeReported !== event.currentTime
      ) {
        mutableContainer.current.lastCurrentTimeReported = event.currentTime;
        if (event.currentTime) {
          dispatch(
            componentActions.onUpdateCurrentTime(event.currentTime * 1000)
          );
        }
      }
    },
    [dispatch]
  );

  useEffect(() => {
    if (playerRef.current) {
      playerRef.current.subscribeToStateChange(handleStateChange);
      (window as any).mediaPlayer = playerRef.current;
    }

    mutableContainer.current.lastCurrentTimeReported = undefined;

    return () => {
      (window as any).mediaPlayer = null;
    };
  }, [handleStateChange]);

  const boundingPolySeries = getBoundingPolySeries({
    currentTime,
    fps,
    detectionCollections: detectionCollections,
    udrCollection: udrCollection?.collection,
    selected,
    udrBeingUpdated,
    localOverlayBeingUpdated,
    updateOverlayCache,
    deletedOverlaysCache,
  });

  const onChangeBoundingBox = useCallback(
    (payload: GroupedBoundingPoly) => {
      if (payload.id !== null && payload.boundingPoly) {
        // } && payload.type === 'udr') {
        const prevBoundingPoly = boundingPolySeries.find(
          (item) => item.id === payload.id
        );

        if (!prevBoundingPoly) {
          return;
        }

        const scalePercentChange = includes(DETECTION_TYPES, payload.type)
          ? globalSettings.objectTypeEffects.head.scaling
          : 0;

        // now get scaled versions - this was what was previously on screen
        const prevBoundingPolyScaled = scaleBoundingPolyRect(
          prevBoundingPoly.boundingPoly,
          scalePercentChange
        );

        let shouldNotUpdate = true;

        for (let i = 0; i < prevBoundingPolyScaled.length; i++) {
          const preItem = prevBoundingPolyScaled[i]!; // Safe because of bounding poly rect
          const nextItem = payload.boundingPoly[i]!; // Safe because of bounding poly rect
          if (
            preItem.x.toFixed(4) !== nextItem.x.toFixed(4) ||
            preItem.x.toFixed(4) !== nextItem.x.toFixed(4)
          ) {
            shouldNotUpdate = false;
            break;
          }
        }

        if (shouldNotUpdate) {
          return;
        }

        dispatch(componentActions.onChangeOverlay(payload));
        // Hacky way to make UDRs not flash mid-update
        // TODO Make update function for Tree collection.
        // 2nd Hack descale to compensate for scaling when shown
        setUpdateOverlayCache({
          ...payload,
          boundingPoly: descaleBoundingPolyRect(
            payload.boundingPoly,
            scalePercentChange
          ),
        });
      }
      if (payload.groupId) {
        dispatch(componentActions.onSelectGroupFaces(payload));
      }
    },
    [boundingPolySeries, dispatch, globalSettings]
  );

  const sendToOpticalTracking = useCallback(
    (overlay: string, trackBack: boolean, trackForward: boolean) => {
      const currentTime = mutableContainer.current.currentTime;
      const payload = {
        id: uuid(),
        overlay,
        timeMs: currentTime,
        trackBack,
        trackForward,
      };
      dispatch(
        componentActions.onProcessEngineRequestWithOverlayAction(payload)
      );
    },
    [dispatch]
  );

  const handleStickToVideo = (lastActivePoly: GroupedBoundingPoly) => {
    const payloadEnd: {
      id: string;
      groupId: string;
      seriesItem: UDRsPolyAssetGroupSeriesItem;
      changeType: 'start' | 'end' | 'stick';
    } = {
      groupId: lastActivePoly.groupId,
      id: lastActivePoly.id,
      seriesItem: {
        id: lastActivePoly.id,
        startTimeMs: 0,
        // stopTimeMs: get(udrCollection, 'end', 0), // alternate way to get duration
        stopTimeMs: Math.ceil(1000 * videoDuration),
        object: {
          id: lastActivePoly.id,
          overlayObjectType: 'udr' as const,
          boundingPoly: lastActivePoly.boundingPoly,
        },
      },
      changeType: 'stick' as const,
    };
    dispatch(
      componentActions.actionChangeUDRsPolyAssetGroupSeriesItem(payloadEnd)
    );
    dispatch(
      componentActions.actionChangeUDRsPolyAssetGroupSeriesItemSubmit(
        payloadEnd
      )
    );
  };

  const deleteMenu = (label: string) => [
    {
      label: intl.formatMessage({ id: 'deleteLabel' }, { label }),
      onClick: lastActivePoly
        ? () => {
            dispatch(componentActions.onDeleteBoundingBox(lastActivePoly));
            setLastActivePoly(null);
          }
        : noop,
    },
    {
      label: intl.formatMessage({ id: 'deleteLabelSegment' }, { label }),
      onClick: lastActivePoly
        ? () => {
            dispatch(
              componentActions.onDeleteSegment({
                groupId: lastActivePoly.groupId,
                timeMs: lastActivePoly.startTimeMs,
                type: lastActivePoly.type,
              })
            );
            setLastActivePoly(null);
          }
        : noop,
    },
  ];

  const defaultMenu = (label: string) => [
    {
      label: intl.formatMessage({ id: 'resizeLabelSegment' }, { label }),
      onClick: lastActivePoly
        ? () =>
            dispatch(
              componentActions.onResizeBoundingBoxSegment(lastActivePoly)
            )
        : noop,
    },
    ...deleteMenu(label),
  ];

  const menus: Record<
    OBJECT_TYPE,
    Array<{ label: string; onClick: () => void }>
  > = {
    head: defaultMenu('head'),
    licensePlate: defaultMenu('license plate'),
    vehicle: defaultMenu('vehicle'),
    laptop: defaultMenu('laptop'),
    notepad: defaultMenu('notepad'),
    card: defaultMenu('card'),
    poim: defaultMenu('POI'),
    person: defaultMenu('person'),
    udr: [
      {
        label: intl.formatMessage({ id: 'trackObject' }),
        onClick: (id: string) => sendToOpticalTracking(id, true, true),
      },
      {
        label: intl.formatMessage({ id: 'trackForwardOnly' }),
        onClick: (id: string) => sendToOpticalTracking(id, false, true),
      },
      {
        label: intl.formatMessage({ id: 'trackBackwardOnly' }),
        onClick: (id: string) => sendToOpticalTracking(id, true, false),
      },
      {
        label: intl.formatMessage({ id: 'stickToVideo' }),
        onClick: lastActivePoly
          ? () => {
              handleStickToVideo(lastActivePoly);
            }
          : noop,
      },
      ...deleteMenu('UDR'),
    ],
  };

  useEffect(() => {
    if (highlightedOverlay) {
      const activePoly =
        boundingPolySeries.find(({ id }) => id === highlightedOverlay.id) ||
        null;
      setLastActivePoly(activePoly);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [highlightedOverlay, currentTime, selected]);

  if (playerRef.current?.video.video) {
    const isRedacted = isRedactedAudio(redactedWords, currentTime);
    const volume = playerRef.current.video.video.volume;
    if (isRedacted) {
      if (volume > 0) {
        playerRef.current.video.video.volume = 0;
        setPrevVolume(volume);
      }
      if (globalSettings.audioType > 0) {
        noiseRef.current?.play();
      }
    } else {
      if (volume === 0) {
        playerRef.current.video.video.volume = prevVolume;
      }
      if (noiseRef.current) {
        noiseRef.current.pause();
        noiseRef.current.currentTime = 0;
      }
    }

    if (playerRef.current.video.video.paused && noiseRef.current) {
      noiseRef.current.pause();
      noiseRef.current.currentTime = 0;
    }
  }

  const onAddBoundingBoxLocal = useCallback(
    function (
      data: {
        boundingPoly: BoundingPolyRect;
        id: string;
        groupId: string;
      },
      currPosition: number,
      opts?: { shift: boolean }
    ) {
      // Remove if this doesn't break anything (February 1, 2024)
      // setUpdateOverlayCache({ ...data, type: 'udr' });

      dispatch(
        componentActions.onAddBoundingBox(
          data,
          getOffsetTime(currPosition, globalSettings.videoOffset),
          opts
        )
      );
    },
    [dispatch, globalSettings.videoOffset]
  );

  const onSprayPaintBoxChangeStart = useCallback(
    function (data: GroupedBoundingPoly) {
      if (includes(DETECTION_TYPES, data.type)) {
        dispatch(componentActions.onFaceHighlight(data));
        return;
      }

      setLocalOverlayBeingUpdated(data);
      setLastActivePoly(data);
      dispatch(componentActions.onFaceHighlight(data));

      const boundingPoly = data.boundingPoly;

      const itemToInsert = {
        time: mutableContainer.current.currentTime,
        boundingPoly,
      };

      mutableContainer.current.series.push(itemToInsert);
    },
    [dispatch]
  );

  const onSprayPaintBoxChangeStop = useCallback(
    function (data: GroupedBoundingPoly) {
      if (includes(DETECTION_TYPES, data.type)) {
        onChangeBoundingBox(data);
        return;
      }

      setLocalOverlayBeingUpdated(null);

      const series = mutableContainer.current.series;

      series.push({
        time: mutableContainer.current.currentTime,
        boundingPoly: data.boundingPoly,
      });

      dispatch(
        componentActions.onSprayPaintSave({
          id: data.id,
          groupId: data.groupId,
          series,
        })
      );

      mutableContainer.current.series = [];
      setUpdateOverlayCache(data);
    },
    [dispatch, onChangeBoundingBox]
  );

  const onChangeBoundingBoxStop = useCallback(
    function (data: GroupedBoundingPoly, hasChanged?: boolean) {
      if (includes(DETECTION_TYPES, data.type)) {
        onChangeBoundingBox(data);
        return;
      }

      setLocalOverlayBeingUpdated(null);
      if (hasChanged === false) {
        return;
      }

      const series: { boundingPoly: BoundingPolyRect; time: number }[] = [
        {
          time: mutableContainer.current.currentTime,
          boundingPoly: data.boundingPoly,
        },
      ];

      dispatch(
        componentActions.onSprayPaintSave({
          id: data.id,
          groupId: data.groupId,
          series,
        })
      );

      mutableContainer.current.series = [];
      setUpdateOverlayCache(data);
    },
    [dispatch, onChangeBoundingBox]
  );

  const onSprayPaintBoxChange = useCallback(function (
    data: GroupedBoundingPoly
  ) {
    if (includes(DETECTION_TYPES, data.type)) {
      return;
    }

    const boundingPoly = data.boundingPoly;

    const itemToInsert = {
      time: mutableContainer.current.currentTime,
      boundingPoly,
    };

    mutableContainer.current.series.push(itemToInsert);
  }, []);

  const onBoxShapeChange = useCallback(
    (
      data: GroupedBoundingPoly,
      udrGroupIds: string[],
      detectionGroups: GroupIdAndType[]
    ) => {
      dispatch(
        componentActions.onBoxShapeChange({
          id: data.id,
          shapeType: data.shapeType,
          udrGroupIds,
          detectionGroups,
        })
      );
    },
    [dispatch]
  );

  const onTogglePlayerFullscreen = useCallback(
    function () {
      if (isFullscreen) {
        window.document.exitFullscreen();
      } else if (playerContainerRef?.current) {
        requestFullscreen(playerContainerRef.current);
      }
    },
    [isFullscreen]
  );

  // const onSelectBoundingBox = useCallback(
  //   function (data: GroupedBoundingPoly) {
  //     dispatch(componentActions.onFaceHighlight(data));
  //
  //     if (includes(DETECTION_TYPES, data.type)) {
  //       return;
  //     }
  //
  //     setLocalOverlayBeingUpdated(null);
  //     mutableContainer.current.series = [];
  //   },
  //   []
  // );

  return (
    <div
      ref={playerContainerRef}
      data-veritone-component="media-player"
      style={{ height: '100%' }}
    >
      <MediaPlayerComponent
        stylesByObjectType={overlayStyles}
        stagedBoundingBoxStyles={stagedBoundingBoxStyles}
        actionMenuItems={(lastActivePoly && menus[lastActivePoly.type]) || []}
        onAddBoundingBox={onAddBoundingBoxLocal}
        onDeleteBoundingBox={(input) =>
          dispatch(componentActions.onDeleteBoundingBox(input))
        }
        boundingPolySeries={boundingPolySeries}
        boundingBoxScale={faceDetectionScaling}
        onSprayPaintBoxChangeStart={onSprayPaintBoxChangeStart}
        onSprayPaintBoxChange={onSprayPaintBoxChange}
        onSprayPaintBoxChangeStop={onSprayPaintBoxChangeStop}
        onChangeBoundingBoxStop={onChangeBoundingBoxStop}
        onBoxShapeChange={onBoxShapeChange}
        // onSelectBoundingBox={onSelectBoundingBox}
        globalSettings={globalSettings}
        readOnly={false}
        src={url}
        streams={streams}
        hasAudio={hasAudio}
        hasVideo={hasVideo}
        ref={playerRef}
        // aspectRatio={'16:9'}
        // preload={'auto'}
        autofocus
        fluid
      />
      <DefaultControlBar
        playerRef={playerRef}
        isFullscreen={isFullscreen}
        togglePlayerFullscreen={onTogglePlayerFullscreen}
        hasVideo={hasVideo}
      />
      <audio ref={noiseRef}>
        <source src={Audio1000Hz} type="audio/mp3" />
      </audio>
    </div>
  );
};

export default MediaPlayerView;

export interface MediaPlayerViewPropTypes {
  readonly url: string;
  readonly streams: DetailTDO['streams'];
  readonly hasAudio: boolean;
  readonly hasVideo: boolean;
}

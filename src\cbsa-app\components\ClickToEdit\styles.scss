.cTEwrapper {
  font-size: 16px;
  height: 35px;
  width: 100%;
  .cTEinput {
    font-size: 100%;
    height: 100%;
    margin: 0 0 0 14px;
    outline: 0;
    padding: 0;
    border: none;
    color: #fff;

    &::after {
      border-bottom: 2px solid #fff;
    }

    &::before {
      border-bottom: 1px solid #fff;
    }

    &:hover {
      &::after {
        border-bottom: 1px solid #fff;
      }

      &::before {
        border-bottom: 1px solid #fff !important;
      }
    }
  }

  .cTEtext {
    font-size: 100%;
    width: 100%;
    height: 100%;
    padding: 4px 15px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    .editP {
      font-size: 100%;
      max-width: 350px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .editIcon {
      display: none;
      cursor: pointer;
    }

    &:hover {
      .editIcon {
        display: block;
      }
    }
  }

  .icon {
    cursor: pointer;
    color: #fff;
  }
}

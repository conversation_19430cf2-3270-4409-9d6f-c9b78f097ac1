import Popover, { PopoverPosition } from '@mui/material/Popover';
import { noop } from 'lodash';
import { AudioRedactionSlice } from '@common-modules/mediaDetails/models';
import ManualRedactionForm from './ManualRedactionForm';
import * as styles from './styles.scss';

const TimelineRangeEditorPopup = ({
  minMs,
  maxMs,
  anchorPosition,
  isOpen,
  timeRange,
  hideGroupButton,
  onChange = noop,
  onCancel,
  onSetRangeTime,
  onUnredactSlice,
}: TimelineRangeEditorPopupPropTypes) => (
  <Popover
    anchorReference="anchorPosition"
    anchorPosition={anchorPosition}
    open={isOpen}
    style={{ zIndex: 101 }}
    disableRestoreFocus
    onClose={onCancel}
  >
    <div className={styles.manualRedactPopperContent}>
      <ManualRedactionForm
        {...{
          minMs,
          maxMs,
          timeRange,
          onCancel,
          onSetRangeTime,
          onChange,
          hideGroupButton,
          onUnredactSlice,
        }}
      />
    </div>
  </Popover>
);

export default TimelineRangeEditorPopup;

export interface TimelineRangeEditorPopupPropTypes {
  readonly minMs: number;
  readonly maxMs: number;
  readonly anchorPosition: PopoverPosition;
  readonly isOpen: boolean;
  readonly timeRange: AudioRedactionSlice;
  readonly hideGroupButton: boolean;
  readonly onCancel: () => void;
  readonly onChange?: (tr: AudioRedactionSlice) => void;
  readonly onSetRangeTime: (tr: AudioRedactionSlice) => void;
  readonly onUnredactSlice: (tr: AudioRedactionSlice) => void;
}

import 'jest';
import { expect, it, describe } from '@jest/globals';
import { createIngestJobQuery } from '../../src/api/queries';

// Test for createIngestJobQuery

describe('createIngestJobQuery', () => {
  it('should generate a mutation string with correct detection and transcription flags', () => {
    const runDetection = { head: true, person: false };
    const runTranscription = true;
    const transcriptionEngineId = 'test-engine-id';
    const transcriptionEngineOptions = { foo: 'bar' };
    const query = createIngestJobQuery(runDetection, runTranscription, transcriptionEngineId, transcriptionEngineOptions);
    expect(typeof query).toBe('string');
    expect(query).toContain('mutation IngestMedia');
    expect(query).toContain('engineId: "test-engine-id"');
    expect(query).toContain('detectHead: true');
    expect(query).toContain('detectPerson: false');
    expect(query).toContain('foo:');
    expect(query).toContain('diarise:"false"');
    expect(query).not.toMatch(/"[^"]*":/); // ensure no strings rather than names
  });

  it('should handle missing transcriptionEngineOptions', () => {
    const runDetection = { head: false, person: true };
    const runTranscription = true;
    const transcriptionEngineId = 'engine-2';
    const query = createIngestJobQuery(runDetection, runTranscription, transcriptionEngineId);
    expect(typeof query).toBe('string');
    expect(query).toContain('engineId: "engine-2"');
    expect(query).toContain('detectHead: false');
    expect(query).toContain('detectPerson: true');
  });
});

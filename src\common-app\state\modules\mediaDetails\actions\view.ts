import { markWorkerAction } from '@utils';
import { DeletedOverlaysCache } from '../store.models';
import {
  ClusterSegment,
  FilterParameters,
  GlobalSettings,
  RedactionConfig,
  ShapeType,
  ViewSettings,
} from '../models';
import { IndividualRedactionCode } from '@redact/state/modules/redactionCodes/models';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { SecureTdoLockResponse } from '../services';
import { createAction } from '@reduxjs/toolkit';
import { FILTER_PARAMETER_TYPE, GROUP_TYPE } from '@helpers/constants';

export const SET_GLOBAL_SETTINGS = createAction<SetGlobalSettingsRequest>(
  'vtn-redact-state-mediaDetails-view/SET_GLOBAL_SETTINGS'
);

export const SET_OVERLAY_PREVIEW = createAction<SetOverlayPreviewRequest>(
  'vtn-redact-state-mediaDetails-view/SET_OVERLAY_PREVIEW'
);
export const SET_SELECTED_GROUPS = createAction<SetSelectedGroupsRequest>(
  'vtn-redact-state-mediaDetails-view/SET_SELECTED_GROUPS'
);
export const SET_MERGE_SELECT_CLUSTER_IDS =
  createAction<SetMergeSelectClusterIdsRequest>(
    'vtn-redact-state-mediaDetails-view/SET_MERGE_SELECT_CLUSTER_IDS'
  );
export const SET_MERGE_GROUP_ID = createAction<SetMergeGroupIdRequest>(
  'vtn-redact-state-mediaDetails-view/SET_MERGE_GROUP_ID'
);
export const SET_CLUSTER_MERGE_SEGMENTS =
  createAction<SetClusterMergeSegmentsRequest>(
    'vtn-redact-state-mediaDetails-view/SET_CLUSTER_MERGE_SEGMENTS'
  );
export const CLEAR_CLUSTER_MERGE = createAction(
  'vtn-redact-state-mediaDetails-view/CLEAR_CLUSTER_MERGE'
);
export const INIT_SET_SELECTED_GROUPS =
  createAction<InitSetSelectedGroupsRequest>(
    'vtn-redact-state-mediaDetails-view/INIT_SET_SELECTED_GROUPS'
  );
export const SET_EXPANDED_GROUPS = createAction<SetExpandedGroupsRequest>(
  'vtn-redact-state-mediaDetails-view/SET_EXPANDED_GROUPS'
);
export const SET_SORT_BY = createAction<SetSortByRequest>(
  'vtn-redact-state-mediaDetails-view/SET_SORT_BY'
);

export const SET_DISPLAY_UNSELECTED_OVERLAYS = createAction<boolean>(
  'vtn-redact-state-mediaDetails-view/SET_DISPLAY_UNSELECTED_OVERLAYS'
);
export const SET_SELECTED_GROUPS_BY_GROUP_ID = createAction<string>(
  'vtn-redact-state-mediaDetails-view/SET_SELECTED_GROUPS_BY_GROUP_ID'
);

export const GLOBAL_SETTINGS_MODAL_SHOW_HIDE = createAction<{
  readonly visible: boolean;
}>('vtn-redact-state-mediaDetails-view/GLOBAL_SETTINGS_MODAL_SHOW_HIDE');

export const SET_FILTER_PARAMETERS = createAction<FilterParametersRequest>(
  'vtn-redact-state-mediaDetails-view/SET_FILTER_PARAMETERS'
);

export const SET_TRIM_INTERVAL = createAction<SetTrimIntervalRequest>(
  'vtn-redact-state-mediaDetails-view/SET_TRIM_INTERVAL'
);
export const REMOVE_TRIM_INTERVAL = createAction(
  'vtn-redact-state-mediaDetails-view/REMOVE_TRIM_INTERVAL'
);

export const CHECK_TDO_LOCK = createAction<UpdateTDOLockRequest>(
  'vtn-redact-state-mediaDetails-view/CHECK_TDO_LOCK'
);
export const TDO_LOCK_FOUND_SUCCESS = createAction<{
  tdoLock: {
    id: string;
    name: string;
    tdoId: string;
    userId: string;
    lastAccessed: string;
  };
  newLockInfo: UpdateTDOLockRequest;
}>('vtn-redact-state-mediaDetails-view/TDO_LOCK_FOUND_SUCCESS');
export const TDO_LOCK_FOUND_FAILURE = createAction<{
  newLockInfo: UpdateTDOLockRequest;
  error?: Error;
}>('vtn-redact-state-mediaDetails-view/TDO_LOCK_FOUND_FAILURE');
export const UPDATE_TDO_READONLY = createAction<{
  tdoReadonly: boolean;
  name?: string;
}>('vtn-redact-state-mediaDetails-view/UPDATE_TDO_READONLY');
export const SECURE_TDO_LOCK_SUCCESS =
  createGraphQLSuccessAction<SecureTdoLockResponse>(
    'vtn-redact-state-mediaDetails-view/SECURE_TDO_LOCK_SUCCESS'
  );
export const SECURE_TDO_LOCK_FAILURE = createGraphQLFailureAction(
  'vtn-redact-state-mediaDetails-view/SECURE_TDO_LOCK_FAILURE'
);
export const REMOVE_TDO_LOCK = createAction<{
  tdoId: string;
  userId: string;
  dontDeleteId?: string;
}>('vtn-redact-state-mediaDetails-view/REMOVE_TDO_LOCK');

export const SET_IS_SAVE_FAILED = createAction<boolean>(
  'vtn-redact-state-mediaDetails-view/SET_IS_SAVE_FAILED'
);

export const actionShowGlobalSettingsModal = (payload: {
  readonly visible: boolean;
}) => GLOBAL_SETTINGS_MODAL_SHOW_HIDE(payload);

export type SetGlobalSettingsRequest = GlobalSettings;
export const actionSetGlobalSettings = (payload: SetGlobalSettingsRequest) =>
  markWorkerAction(SET_GLOBAL_SETTINGS(payload));

export type SetOverlayPreviewRequest = Pick<ViewSettings, 'overlayPreview'>;
export const actionOverlayPreview = (
  overlayPreview: ViewSettings['overlayPreview']
) => SET_OVERLAY_PREVIEW({ overlayPreview });

export interface SetSelectedGroupsRequest {
  readonly selected: ViewSettings['selectedPolyGroups'];
}
export const actionSetSelectedGroups = (payload: SetSelectedGroupsRequest) =>
  markWorkerAction(SET_SELECTED_GROUPS(payload));

export interface SetMergeSelectClusterIdsRequest {
  readonly clusterIds: string[];
  readonly selected: boolean;
}
export const actionSetMergeSelectClusterIds = (
  payload: SetMergeSelectClusterIdsRequest
) => markWorkerAction(SET_MERGE_SELECT_CLUSTER_IDS(payload));

export interface SetMergeGroupIdRequest {
  readonly groupId: string;
}
export const actionSetMergeGroupId = (payload: SetMergeGroupIdRequest) =>
  markWorkerAction(SET_MERGE_GROUP_ID(payload));

export interface SetClusterMergeSegmentsRequest {
  readonly id: string;
  readonly groupId: string;
  readonly segment: ClusterSegment | false;
}
export const actionSetClusterMergeSegments = (
  payload: SetClusterMergeSegmentsRequest
) => markWorkerAction(SET_CLUSTER_MERGE_SEGMENTS(payload));

export const actionClearClusterMerge = () => CLEAR_CLUSTER_MERGE();

export interface InitSetSelectedGroupsRequest {
  readonly selected: ViewSettings['selectedPolyGroups'];
}
export const actionInitSetSelectedGroups = (
  payload: InitSetSelectedGroupsRequest
) => INIT_SET_SELECTED_GROUPS(payload);

export interface SetExpandedGroupsRequest {
  readonly expanded: ViewSettings['expandedPolyGroups'];
}
export const actionSetExpandedGroups = (payload: SetExpandedGroupsRequest) =>
  SET_EXPANDED_GROUPS(payload);

export type SetSortByRequest = ViewSettings['sortPolyGroupsBy'];
export const actionSetSortBy = (payload: SetSortByRequest) =>
  SET_SORT_BY(payload);

export interface FilterParametersRequest {
  readonly [id: string]: boolean;
}

export interface InChangeFilterParameters {
  readonly filterParameters: FilterParameters;
  readonly preventUndo?: boolean;
}
export interface ChangeShapeRequest {
  id: string;
  shapeType?: ShapeType;
  udrGroupIds: string[];
  detectionGroups: GroupIdAndType[];
}

export interface ChangeRedactionRequest {
  id: string;
  redactionConfig?: RedactionConfig;
  startTimeMs?: number;
  stopTimeMs?: number;
  udrGroupIds: string[];
  detectionGroups: GroupIdAndType[];
}

export interface GroupIdAndType {
  id: string;
  type: GROUP_TYPE;
}
export interface ChangeCodeRequest {
  objectId: string;
  redactionCode: IndividualRedactionCode | undefined;
  udrGroupIds: string[];
  detectionGroups: GroupIdAndType[];
}

export type BulkRedactionFilterList =
  | 'head'
  | 'person'
  | 'plate'
  | 'laptop'
  | 'udr'
  | 'vehicle'
  | 'notepad'
  | 'card';

export interface BulkRedactRequest {
  detectionType: BulkRedactionFilterList;
  redactionConfig: RedactionConfig | undefined;
}

export interface UpdateTDOLockRequest {
  name: string;
  tdoId: string;
  userId: string;
  lastAccessed: string;
}

export interface UpdateTDOLockResponse {
  id: string;
  name: string;
  tdoId: string;
  userId: string;
  lastAccessed: string;
}

export interface FILTER_TOGGLE_ALL_REQUEST {
  filter: boolean;
  preventUndo?: boolean;
  // isUndo?: boolean;
}

export interface FILTER_TYPE_REQUEST {
  filterType: FILTER_PARAMETER_TYPE;
  value: boolean;
  preventUndo?: boolean;
  isDropDown?: boolean; // for cbsa only can select one filter type
  // isUndo?: boolean;
}

export const actionSetDisplayUnselectedOverlays = (payload: boolean) =>
  SET_DISPLAY_UNSELECTED_OVERLAYS(payload);

export const actionSetSelectedGroupsByGroupId = (payload: string) =>
  markWorkerAction(SET_SELECTED_GROUPS_BY_GROUP_ID(payload));

export const SET_DELETED_OVERLAYS_CACHE = createAction<DeletedOverlaysCache>(
  'vtn-redact-state-mediaDetails-view/SET_DELETED_OVERLAYS_CACHE'
);

export const actionSetDeletedOverlaysCache = (payload: DeletedOverlaysCache) =>
  SET_DELETED_OVERLAYS_CACHE(payload);

export const setFilterParameters = (payload: FilterParametersRequest) =>
  markWorkerAction(SET_FILTER_PARAMETERS(payload));

export interface SetTrimIntervalRequest {
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
}
export const setTrimInterval = (payload: SetTrimIntervalRequest) =>
  SET_TRIM_INTERVAL(payload);

export const removeTrimInterval = () => REMOVE_TRIM_INTERVAL();

export const checkTdoLock = (payload: UpdateTDOLockRequest) =>
  CHECK_TDO_LOCK(payload);

export const updateTdoReadonly = (tdoReadonly: boolean, name?: string) =>
  UPDATE_TDO_READONLY({ tdoReadonly, name });

export const removeTdoLock = (
  tdoId: string,
  userId: string,
  dontDeleteId?: string
) => REMOVE_TDO_LOCK({ tdoId, userId, dontDeleteId });

export const SEEK_PREV_MARKER_TIME = createAction<number>(
  'vtn-redact-state-mediaDetails-view/SEEK_PREV_MARKER_TIME'
);
export const actionSeekPrevMarkerTime = (payload: number) =>
  SEEK_PREV_MARKER_TIME(payload);

export const SET_PREV_MARKER_TIME = createAction<number>(
  'vtn-redact-state-mediaDetails-view/SET_PREV_MARKER_TIME'
);
export const actionSetPrevMarkerTime = (payload: number) =>
  SET_PREV_MARKER_TIME(payload);

export const SET_FILTER_TOGGLE_ALL = createAction<FILTER_TOGGLE_ALL_REQUEST>(
  'vtn-redact-state-mediaDetails-view/SET_FILTER_PARAMETERS_TOGGLE_ALL'
);
export const actionFilterToggleAll = (payload: FILTER_TOGGLE_ALL_REQUEST) =>
  SET_FILTER_TOGGLE_ALL(payload);

export const SET_SHOW_FILTER_TYPE = createAction<FILTER_TYPE_REQUEST>(
  'vtn-redact-state-mediaDetails-view/SET_FILTER_PARAMETERS_SHOW_TYPE'
);
export const actionFilterToggleShowType = (payload: FILTER_TYPE_REQUEST) =>
  SET_SHOW_FILTER_TYPE(payload);

export const SET_REMOVE_FILTER_TYPE = createAction<FILTER_TYPE_REQUEST>(
  'vtn-redact-state-mediaDetails-view/SET_FILTER_PARAMETERS_REMOVE_TYPE'
);
export const actionFilterToggleRemoveType = (payload: FILTER_TYPE_REQUEST) =>
  SET_REMOVE_FILTER_TYPE(payload);

export const SET_DRAGGED_OVERLAY_ID = createAction<string | null>(
  'vtn-redact-state-mediaDetails-view/SET_DRAGGED_OVERLAY_ID'
);
export const actionSetDraggedOverlayId = (payload: string | null) =>
  SET_DRAGGED_OVERLAY_ID(payload);

export const actionSetIsSaveFailed = (payload: boolean) =>
  SET_IS_SAVE_FAILED(payload);

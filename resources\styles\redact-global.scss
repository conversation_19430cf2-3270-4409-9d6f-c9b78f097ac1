@import url('https://static.veritone.com/veritone-ui/veritone-icons-27/style.css');

html {
  color: #222;
  font-size: 16px;
  line-height: 1.4;
  box-sizing: border-box;
}

body {
  background-color: #fff;
  min-width: 375px;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

p {
  font-size: 1.125rem;
  font-weight: 200;
  line-height: 1.8;
}

// workaround https://github.com/callemall/material-ui/issues/283
input:-webkit-autofill {
  box-shadow: 0 0 0 1000px white inset;
}

/*
 * Remove text-shadow in selection highlight:
 * https://twitter.com/miketaylr/status/12228805301
 *
 * These selection rule sets have to be separate.
 * Customize the background color to match your design.
 */

::selection {
  background: #b3d4fc;
  text-shadow: none;
}

/*
 * Remove the gap between audio, canvas, iframes,
 * images, videos and the bottom of their containers:
 * https://github.com/h5bp/html5-boilerplate/issues/440
 */

audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}

/*
 * Remove default fieldset styles.
 */

fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

/*
 * Allow only vertical resizing of textareas.
 */

textarea {
  resize: vertical;
}

/*
 * A better looking default horizontal rule
 */

hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0;
}

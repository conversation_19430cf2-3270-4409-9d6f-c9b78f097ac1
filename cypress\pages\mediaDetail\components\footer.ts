import {
  DataTestSelector,
  DataTestSelectorTimeline,
  DataVeritoneSelector,
} from '../../../support/helperFunction/mediaDetailHelper';

export const footer = {
  previewObject: () =>
    cy.getDataIdCy({ idAlias: DataTestSelectorTimeline.PreviewObject }),
  switchShowUnselected: () =>
    cy.get(
      `[data-veritone-element=${DataVeritoneSelector.SwitchShowUnselected}]`
    ),
  resultTabSaveBtn: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.ResultTabSaveBtn }),
  resultTabRedactBtn: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.ResultTabRedactBtn }),
  expandMoreButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.ExpandMoreButton }),

  notificationWindow: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.NotificationWindow }),
  shortcutsHeaderDialog: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.ShortcutsHeaderDialog }),
  globalSettingsModalTitle: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.GlobalSettingsModalTitle }),
  globalSettingsModalCloseBtn: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.SettingsButtonClose }),
};

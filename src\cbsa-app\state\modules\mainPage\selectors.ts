import { TreeObjectId } from '@cbsa-modules/universal';
import { MainPageStore } from './MainPageStore';
import { createSelector } from 'reselect';
import { namespace } from './store';

const selectStore = (store: { [namespace]: MainPageStore }): MainPageStore =>
  store[namespace];

export const selectCasesSummary = createSelector(
  selectStore,
  (s) => s.casesSummary
);

export const selectSearchText = createSelector(
  selectStore,
  (s) => s.searchText
);

export const selectStatusFilter = createSelector(
  selectStore,
  (s) => s.statusFilter
);

export const selectShowArchived = createSelector(
  selectStore,
  (s) => s.showArchived
);

export const selectCasesList = createSelector(selectStore, (s) => s.casesList);

export const selectCase = (treeObjectId: TreeObjectId) =>
  createSelector(selectStore, (s) => {
    const selectedCase = s.casesList?.find(
      (current) => current.treeObjectId === treeObjectId
    );
    if (!selectedCase) {
      throw new Error('tried to select non-existent case');
    }
    return selectedCase;
  });

export const selectCasesTotal = createSelector(
  selectStore,
  (s) => s.casesTotal
);

export const selectCasesSortColumn = createSelector(
  selectStore,
  (s) => s.casesSortColumn
);

export const selectCasesSortOrder = createSelector(
  selectStore,
  (s) => s.casesSortOrder
);

export const selectPaginationAmount = createSelector(
  selectStore,
  (s) => s.paginationAmount
);

export const selectPaginationStart = createSelector(
  selectStore,
  (s) => s.paginationStart
);

import 'jest';
import { StatusCodes }  from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { isAuthorized } from '../../src/controllers/authorization';

describe('authorization', () => {
  const { res, mockClear } = getMockRes();

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('isAuthorized function exists',  () => {
    expect(isAuthorized).not.toBeNull();
  });

  it('successful authorization w/ API Token Key', async () => {
    const req = getMockReq({
      headers: {
        authorization: 'Bearer abcdef:1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqr',
      },
    });

    await isAuthorized(req, res);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
    expect(res.json).toHaveBeenCalledWith({
      status: 'Authorized!',
      tokenType: 'API Token Key',
    });
  });

  it('successful authorization w/ Session Token', async () => {
    const req = getMockReq({
      headers: {
        authorization: 'Bearer abcdefgh-1234-5678-90ab-cdefghijklmn',
      },
    });

    await isAuthorized(req, res);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
    expect(res.json).toHaveBeenCalledWith({
      status: 'Authorized!',
      tokenType: 'Session Token',
    });
  });
});

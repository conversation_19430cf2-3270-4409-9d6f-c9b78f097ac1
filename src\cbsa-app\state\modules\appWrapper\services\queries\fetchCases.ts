export const FETCH_CASES_QUERY = `
  query fetchCases($schemaId: ID!, $offset: Int, $limit: Int, $query: [JSONData]) {
    searchMedia(search: {
      index: ["mine"],
      type: $schemaId,
      offset: $offset,
      limit: $limit,
      query: {
        operator: "and",
        conditions: $query,
      },
    }) {
      jsondata
    }
  }`;
export interface FetchCasesQueryResponse {
  searchMedia: {
    jsondata: any;
  };
}

import { act, renderRedact, screen } from '@test/testUtils';
import Qimg, { QimgPropTypes } from '@redact-components/Qimg';

const vars: QimgPropTypes = {
  src: '',
};
describe('React testing library test', () => {
  beforeAll(() => {
    global.IntersectionObserver = class {
      readonly root: Element | null = null;
      readonly rootMargin: string = '';
      readonly thresholds: ReadonlyArray<number> = [];
      readonly takeRecords: () => IntersectionObserverEntry[] = jest.fn();
      unobserve() {}
      disconnect() {}
      _callback: IntersectionObserverCallback;

      constructor(callback: IntersectionObserverCallback) {
        this._callback = callback;
      }

      observe = (element: Element) => {
        const entry: IntersectionObserverEntry = {
          target: element,
          isIntersecting: true,
          intersectionRatio: 1,
          time: 0,
          boundingClientRect: {} as DOMRectReadOnly,
          intersectionRect: {} as DOMRectReadOnly,
          rootBounds: null,
        };
        this._callback([entry], this);
      };
    };
  });

  test('renders correctly Qimg', () => {
    jest.useFakeTimers();

    const { container } = renderRedact(<Qimg src={vars.src} />);

    act(() => jest.advanceTimersByTime(500));

    expect(container).toMatchSnapshot();
    expect(screen.getByRole('img')).toBeInTheDocument();

    jest.useRealTimers();
  });
});

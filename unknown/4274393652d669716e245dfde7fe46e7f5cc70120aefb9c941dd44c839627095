import {
  all,
  fork,
  put,
  select,
  take,
  takeLatest,
  delay,
} from 'typed-redux-saga/macro';
import { enqueueSnackbar } from '@common-modules/snackbar';

import { actionSetUserPermissions } from '@user-permissions';
import {
  actionOpenUpgradeAccount,
  REQUEST_USER_ORGANIZATION_FAILURE,
  REQUEST_USER_ORGANIZATION_SUCCESS,
  START_POLLING_USER_ORG,
  StartPollingUserOrgRequest,
  STOP_POLLING_USER_ORG,
} from '../actions';
import { Action } from '../models';
import {
  selectHasEnterprise,
  selectHasFreeTrial,
  selectIsUsageLimitExceeded,
} from '../selectors';
import { serviceFetchUserOrg } from '../services';

export function* organizationSagas() {
  yield* all([fork(onActionStartPollingUserOrg)]);
}

function* onActionStartPollingUserOrg() {
  let isLimitExceeded = false;
  yield* takeLatest<
    | Action<typeof START_POLLING_USER_ORG, StartPollingUserOrgRequest>
    | Action<typeof STOP_POLLING_USER_ORG, void>
  >([START_POLLING_USER_ORG, STOP_POLLING_USER_ORG], function* (action) {
    if (action.type === STOP_POLLING_USER_ORG) {
      return;
    }
    let delayMs = action.payload.initWait;
    while (true) {
      const hasEnterprise = yield* select(selectHasEnterprise);
      if (hasEnterprise) {
        return;
      }
      const hasFreeTrial = yield* select(selectHasFreeTrial);
      if (!isLimitExceeded) {
        isLimitExceeded = yield* select(selectIsUsageLimitExceeded);
        if (isLimitExceeded) {
          yield* put(actionOpenUpgradeAccount());
        }
      }
      yield* put(
        actionSetUserPermissions({
          allowDownload: hasFreeTrial
            ? [
                false,
                10,
                function* () {
                  yield* put(actionOpenUpgradeAccount());
                },
              ]
            : [true, 10],
          allowRunRedaction: isLimitExceeded
            ? [
                false,
                10,
                function* () {
                  yield* put(actionOpenUpgradeAccount());
                },
              ]
            : [true, 10],
          allowNoUploadSizeLimit: hasFreeTrial
            ? [
                false,
                10,
                function* () {
                  yield* put(
                    enqueueSnackbar({
                      message:
                        'During the free trial, file uploads are limited to 100MB. Please choose a smaller file.',
                    })
                  );
                },
              ]
            : [true, 10],
        })
      );
      yield* delay(delayMs);
      delayMs = Math.min(delayMs * 1.05, 60_000);
      yield* put(serviceFetchUserOrg());
      yield* take([
        REQUEST_USER_ORGANIZATION_SUCCESS,
        REQUEST_USER_ORGANIZATION_FAILURE,
      ]);
    }
  });
}

import moment from 'moment';
import {
  UPDATE_CASE_NAME_FOLDER_QUERY,
  UPDATE_CASE_NAME_SDO_QUERY,
  UpdateCaseNameSDOQueryResponse,
} from './queries/updateCaseName';
import callGraph<PERSON><PERSON><PERSON> from '@helpers/callGraphQLApi';
import { NOOP } from '@cbsa-modules/universal/actions';
import { UPDATE_CASE_NAME_SUCCESS, UPDATE_CASE_NAME_FAILURE } from '../actions';
import { Case, Thunk, lookupLatestCaseSchemaId } from '@cbsa-modules/universal';

export const updateCaseName: Thunk<{
  readonly caseDetails: Case;
  readonly name: string;
}> =
  ({ caseDetails, name }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const { archive, createdDateTime, id, status, treeObjectId } = caseDetails;

    await callGraph<PERSON>Api({
      actionTypes: [NOOP, NOOP, NOOP],
      query: UPDATE_CASE_NAME_FOLDER_QUERY,
      variables: { caseId: treeObjectId, name },
      dispatch,
      getState,
    });

    await callGraphQLApi<UpdateCaseNameSDOQueryResponse>({
      actionTypes: [NOOP, UPDATE_CASE_NAME_SUCCESS, UPDATE_CASE_NAME_FAILURE],
      query: UPDATE_CASE_NAME_SDO_QUERY,
      variables: {
        sdoId: id,
        schemaId,
        data: {
          caseName: name,
          status: status,
          archive: archive,
          folderTreeObjectId: treeObjectId,
          createdDateTime: createdDateTime,
          modifiedDateTime: moment.utc(new Date()).toDate().toISOString(),
          priority: 1,
          processingTime: 3600,
        },
      },
      dispatch,
      getState,
    });
  };

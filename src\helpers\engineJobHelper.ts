import { ClusterSimThresholdType } from '@common/state/modules/mediaDetails/models';
import { DAG_REFERENCE_IDS } from './constants';

const DEFAULT_AUDIO_CHUNK_DURATION = 180;
const DEFAULT_VIDEO_CHUNK_DURATION = 60;
const DEFAULT_VIDEO_CHUNK_OVERLAP = 3;
const DEFAULT_AUDIO_CHUNK_OVERLAP = 3;
export interface Task {
  engineId: string;
}

export interface IngestionTask extends Task {
  payload: {
    chunkSize?: number;
    clusterSimThreshold?: ClusterSimThresholdType;
    faceDetectionThreshold?: number;
    fileType?: string;
    tdoId?: string;
    url?: string;
    user?: string; // user's email
    zeropadOffsetAudio?: boolean;
    legacyClustering?: boolean;
    rotate?: number;
    chunkDuration?: number;
    chunkOverlap?: number;
    chunkType?: string;
  };
  executionPreferences?: {
    priority: number;
  };
  ioFolders?: Array<{
    referenceId: string;
    mode: string;
    type: string;
  }>;
}

interface ChunkerTask extends Task {
  payload: {
    comment?: string;
    app?: string;
    url?: string;
    chunkType?: string;
    chunkDurationSecs: number;
    chunkOverlapSecs: number;
  };
  executionPreferences: {
    priority: number;
    parentCompleteBeforeStarting?: boolean;
  };
  ioFolders: Array<{
    referenceId: string;
    mode: string;
    type: string;
  }>;
}

interface DetectionTask extends Task {
  payload: {
    confidenceThreshold: number;
    videoType: string;
    stepSizeDetection: number;
    app: string;
    detectHead: boolean;
    detectLaptop: boolean;
    detectLicense: boolean;
    detectVehicle: boolean;
    detectNotepad: boolean;
    detectCard: boolean;
    detectPerson: boolean;
    mergeVehiclePlate: string;
    mergePersonHead: boolean;
    detectorType: string;
    legacyClustering?: boolean;
    clusterSimThreshold?: ClusterSimThresholdType;
  };
  executionPreferences: {
    maxEngines: number;
    parentCompleteBeforeStarting: boolean;
    priority: number;
  };
  ioFolders: Array<{
    referenceId: string;
    mode: string;
    type: string;
  }>;
}

interface TranscriptionTask extends Task {
  payload: {
    app: string;
    diarise: string;
  };
  executionPreferences: {
    maxEngines: number;
    parentCompleteBeforeStarting: boolean;
    priority: number;
  };
  ioFolders: Array<{
    referenceId: string;
    mode: string;
    type: string;
  }>;
}
interface VoiceMaskTasks extends Task {
  executionPreferences: {
    maxEngines: number;
    parentCompleteBeforeStarting: boolean;
    priority: number;
  };
  ioFolders: Array<{
    referenceId: string;
    mode: string;
    type: string;
  }>;
}

interface OutputWriterTask extends Task {
  executionPreferences: {
    parentCompleteBeforeStarting: boolean;
    priority: number;
  };
  ioFolders: Array<{
    referenceId: string;
    mode: string;
    type: string;
  }>;
}

export enum DetectionType {
  HEAD = 'head',
  TRANSCRIPTION = 'transcription',
}

interface Props {
  url?: string;
  glcIngestionEngineId?: string;
  voiceMaskEngineId?: string;
  outputWriterEngineId: string;
  fastChunkerEngineId?: string;
  transcriptionEngineId?: string;
  transcriptionEngineOptions?: Record<string, unknown>;
  runTranscription?: boolean;
  runDetection?: boolean;
  ingestionPayload?: IngestionTask['payload'];
  voiceChange?: boolean;
  detectionOptions?: {
    engineId: string;
    confidenceThreshold: number;
    videoType: string;
    detectionRate: number;
    detectHead?: boolean;
    detectNotepad?: boolean;
    detectCard?: boolean;
    detectPerson?: boolean;
    legacyClustering?: boolean;
    clusterSimThreshold?: ClusterSimThresholdType;
  };
}

const transcriptionChunkerPayload = (
  chunkLengthSeconds: number,
  url?: string
) => ({
  comment: 'Fast Audio Chunk',
  app: 'redact',
  chunkType: 'audio',
  chunkDurationSecs: chunkLengthSeconds,
  chunkOverlapSecs: DEFAULT_AUDIO_CHUNK_OVERLAP,
  ...(url && { url }),
});

const detectionChunkerPayload = (chunkLengthSeconds: number, url?: string) => ({
  comment: 'Fast Video Chunk',
  app: 'redact',
  chunkDurationSecs: chunkLengthSeconds,
  chunkOverlapSecs: DEFAULT_VIDEO_CHUNK_OVERLAP,
  ...(url && { url }),
});

interface Route {
  parentIoFolderReferenceId: string;
  childIoFolderReferenceId: string;
  options: unknown;
}

/** Build DAG task and routes for jobs involving any combination of ingestion, transcription and object detection */
export function generateTasksAndRoutes({
  url,
  glcIngestionEngineId,
  outputWriterEngineId,
  fastChunkerEngineId,
  transcriptionEngineId,
  transcriptionEngineOptions,
  runTranscription,
  runDetection,
  ingestionPayload,
  detectionOptions,
  voiceMaskEngineId,
  voiceChange,
}: Props) {
  const routes: Route[] = [];
  const tasks: Task[] = [];

  const runIngestion = !!ingestionPayload;

  if (ingestionPayload && glcIngestionEngineId) {
    // check if using ingestion engine to produce chunks instead of fast chunker
    if (runDetection && !runTranscription) {
      ingestionPayload['chunkDuration'] = DEFAULT_VIDEO_CHUNK_DURATION;
      ingestionPayload['chunkOverlap'] = DEFAULT_VIDEO_CHUNK_OVERLAP;
    }

    if (!runDetection && runTranscription) {
      ingestionPayload['chunkDuration'] = DEFAULT_AUDIO_CHUNK_DURATION;
      ingestionPayload['chunkOverlap'] = DEFAULT_AUDIO_CHUNK_OVERLAP;
      ingestionPayload['chunkType'] = 'audio';
    }

    tasks.push({
      engineId: glcIngestionEngineId,
      payload: ingestionPayload,
      executionPreferences: {
        priority: -20,
      },
      ...((runDetection || runTranscription) &&
        ({
          // check if need output
          ioFolders: [
            {
              referenceId: 'ingestOutputFolder',
              mode: 'chunk',
              type: 'output',
            },
          ],
        } as IngestionTask)),
    });
  }

  // ---------------------------------------
  // add object detection
  if (runDetection && detectionOptions) {
    // if also doing transcription will need split into two chunkers (or if ingesting previously ingested)
    const useVideoChunker = runTranscription || !runIngestion; // eventually will able to skip chunker in some scenarios

    const hasParent = !!runIngestion;

    if (useVideoChunker) {
      tasks.push({
        engineId: fastChunkerEngineId,
        payload: detectionChunkerPayload(
          DEFAULT_VIDEO_CHUNK_DURATION,
          hasParent ? undefined : url // if parent engine uses input chunk instead of url
        ),
        executionPreferences: {
          priority: -5,
          ...(runIngestion && { parentCompleteBeforeStarting: true }),
        },
        ioFolders: hasParent
          ? [
              {
                referenceId: 'chunkVideoInputFolder',
                mode: 'chunk',
                type: 'input',
              },
              {
                referenceId: 'chunkVideoOutputFolder',
                mode: 'chunk',
                type: 'output',
              },
            ]
          : [
              {
                referenceId: 'chunkVideoOutputFolder',
                mode: 'chunk',
                type: 'output',
              },
            ],
      } as ChunkerTask);

      // add routes
      if (runIngestion) {
        routes.push({
          // ingest to chunkVideo
          parentIoFolderReferenceId: 'ingestOutputFolder',
          childIoFolderReferenceId: 'chunkVideoInputFolder',
          options: {},
        });
      }

      routes.push({
        // chunkVideo --> Head Detection
        parentIoFolderReferenceId: 'chunkVideoOutputFolder',
        childIoFolderReferenceId: 'hdInputFolder',
        options: {},
      });
    } else {
      // otherwise will go directly from ingestor chunks to head detection
      routes.push({
        // ingestor --> Head Detection
        parentIoFolderReferenceId: 'ingestOutputFolder',
        childIoFolderReferenceId: 'hdInputFolder',
        options: {},
      });
    }

    const {
      engineId: headEngineId,
      confidenceThreshold,
      videoType,
      detectionRate,
      detectHead,
      detectNotepad,
      detectCard,
      detectPerson,
      legacyClustering,
      clusterSimThreshold,
    } = detectionOptions;

    tasks.push({
      engineId: headEngineId,
      payload: {
        confidenceThreshold,
        videoType: videoType,
        stepSizeDetection: detectionRate,
        app: 'redact',
        detectHead: !!detectHead,
        detectLaptop: true,
        detectLicense: true,
        detectVehicle: true,
        detectNotepad: !!detectNotepad,
        detectCard: !!detectCard,
        detectPerson: !!detectPerson,
        mergeVehiclePlate: 'plate',
        detectorType: 'full',
        mergePersonHead: false,
        ...(legacyClustering && { legacyClustering }),
        ...(clusterSimThreshold && { clusterSimThreshold }),
      },
      executionPreferences: {
        maxEngines: 20,
        parentCompleteBeforeStarting: true,
        priority: -5,
      },
      ioFolders: [
        {
          referenceId: 'hdInputFolder',
          mode: 'chunk',
          type: 'input',
        },
        {
          referenceId: 'hdOutputFolder',
          mode: 'chunk',
          type: 'output',
        },
      ],
    } as DetectionTask);

    // add outputwriter
    tasks.push({
      engineId: outputWriterEngineId,
      executionPreferences: {
        parentCompleteBeforeStarting: true,
        priority: -15,
      },
      ioFolders: [
        {
          referenceId: DAG_REFERENCE_IDS['owFromHead'],
          mode: 'chunk',
          type: 'input',
        },
      ],
    } as OutputWriterTask);

    routes.push({
      // HeadDetection --> output writer
      parentIoFolderReferenceId: 'hdOutputFolder',
      childIoFolderReferenceId: DAG_REFERENCE_IDS['owFromHead'],
      options: {},
    });
  }

  // ---------------------------------------
  // transcription
  if (runTranscription) {
    // if also doing head detection will need chunker
    // or if using previously ingested
    // or is using voiceChange need to prevent more than 1 chunk going to voice mask engine
    const needAudioChunker = runDetection || !runIngestion || voiceChange;

    // for now only run voice mask if Run transcription box is checked during ingestion
    if (runIngestion && voiceChange) {
      tasks.push({
        engineId: voiceMaskEngineId,
        executionPreferences: {
          parentCompleteBeforeStarting: true,
          priority: -15,
        },
        ioFolders: [
          {
            referenceId: 'voiceMaskInputFolder',
            mode: 'chunk',
            type: 'input',
          },
        ],
      } as VoiceMaskTasks);

      // add routes
      routes.push({
        // ingest --> voiceMask
        parentIoFolderReferenceId: 'ingestOutputFolder',
        childIoFolderReferenceId: 'voiceMaskInputFolder',
        options: {},
      });
    }

    if (needAudioChunker) {
      tasks.push({
        engineId: fastChunkerEngineId,
        payload: transcriptionChunkerPayload(
          DEFAULT_AUDIO_CHUNK_DURATION,
          runIngestion ? undefined : url // if parent engine uses input chunk instead of url
        ),
        executionPreferences: {
          ...(runIngestion && { parentCompleteBeforeStarting: true }),
          priority: -5,
        },
        ioFolders: !runIngestion
          ? [
              {
                referenceId: 'chunkAudioOutputFolder',
                mode: 'chunk',
                type: 'output',
              },
            ]
          : [
              {
                referenceId: 'chunkAudioInputFolder',
                mode: 'chunk',
                type: 'input',
              },
              {
                referenceId: 'chunkAudioOutputFolder',
                mode: 'chunk',
                type: 'output',
              },
            ],
      } as ChunkerTask);

      // add routes
      if (runIngestion) {
        routes.push({
          // ingest --> chunkAudio
          parentIoFolderReferenceId: 'ingestOutputFolder',
          childIoFolderReferenceId: 'chunkAudioInputFolder',
          options: {},
        });
      }

      routes.push({
        // chunkAudio --> transcription
        parentIoFolderReferenceId: 'chunkAudioOutputFolder',
        childIoFolderReferenceId: 'transcriptionInputFolder',
        options: {},
      });
    } else {
      // otherwise will go directly from ingestor chunks to to transcription engine
      routes.push({
        // ingestor --> transcription
        parentIoFolderReferenceId: 'ingestOutputFolder',
        childIoFolderReferenceId: 'transcriptionInputFolder',
        options: {},
      });
    }

    tasks.push({
      engineId: transcriptionEngineId,
      payload: Object.assign({}, transcriptionEngineOptions, {
        diarise: 'false',
        app: 'redact',
      }),
      executionPreferences: {
        maxEngines: 10,
        parentCompleteBeforeStarting: true,
        priority: -5,
      },
      ioFolders: [
        {
          referenceId: 'transcriptionInputFolder',
          mode: 'chunk',
          type: 'input',
        },
        {
          referenceId: 'transcriptionOutputFolder',
          mode: 'chunk',
          type: 'output',
        },
      ],
    } as TranscriptionTask);

    tasks.push({
      engineId: outputWriterEngineId,
      executionPreferences: {
        parentCompleteBeforeStarting: true,
        priority: -15,
      },
      ioFolders: [
        {
          referenceId: DAG_REFERENCE_IDS['owFromTranscription'],
          mode: 'chunk',
          type: 'input',
        },
      ],
    } as OutputWriterTask);

    routes.push({
      // Transcription --> output writer
      parentIoFolderReferenceId: 'transcriptionOutputFolder',
      childIoFolderReferenceId: DAG_REFERENCE_IDS['owFromTranscription'],
      options: {},
    });
  }

  return [tasks, routes];
}

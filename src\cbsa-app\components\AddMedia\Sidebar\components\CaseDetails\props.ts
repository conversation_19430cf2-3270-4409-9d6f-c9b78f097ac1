import { createSelector } from 'reselect';
import {
  selectLoaders,
  updateCaseName,
  selectCaseDetails,
} from '@cbsa-modules/addMedia';
import { Case } from '@cbsa-modules/universal';
import { connect, ConnectedProps } from 'react-redux';

const mapStateToProps = createSelector(
  selectLoaders,
  selectCaseDetails,
  (loaders, caseDetails) => ({
    loaders,
    caseDetails,
  })
);

const mapDispatchToProps = {
  updateCaseName: ({
    caseDetails,
    name,
  }: {
    caseDetails: Case;
    name: string;
  }) => updateCaseName({ caseDetails, name }),
};

export const connector = connect(mapStateToProps, mapDispatchToProps);
export type Props = ConnectedProps<typeof connector>;

export enum ProfileDataTestSelector {
  SettingsSuggestIcon = 'SettingsSuggestIcon',
  AddIcon = 'AddIcon',
  EditIcon = 'EditIcon',
  DeleteIcon = 'DeleteIcon',
  NonCheckIcon = 'CheckBoxOutlineBlankIcon',
  CheckedIcon = 'CheckBoxIcon',
}
export enum DataVeritoneSelector {
  AppBarMenu = 'app-bar-menu',
  ProfileDropdownId = 'mui-component-select-settings-profile',
  ProfileSettingsDropdownId = 'menu-settings-profile',
  CloseHeaderIcon = 'picker-header-close-btn',
  SelectProfileDropdownId = '#mui-component-select-settings-profile',
}

export enum ProfileDisplayText {
  ModalDeleteDescription = "Are you sure you'd like to delete this? Once deleted, it cannot be recovered.",
  ProfileNameLabelTextInput = 'Profile Name',
  EditButton = 'Edit',
  DeleteButton = 'Delete',
  LeftArrowButtonTitle = 'Previous Page',
  RightArrowButtonTitle = 'Next Page',
}

export enum ProfileGraphQlQuery {
  FetchProfiles = `\n query fetchProfileListQuery ($schemaId: ID!, $limit:Int, $offset:Int){\n    structuredDataObjects(\n      schemaId: $schemaId, \n      limit: $limit, \n      offset: $offset) {\n      count\n      records {\n        id\n        createdDateTime\n        data\n      }\n    }\n  }\n`,
  DeleteProfile = `\n  mutation deleteSdo ($profileId: ID!, $schemaId: ID!) {\n    deleteStructuredData(input: {\n      id: $profileId\n      schemaId: $schemaId\n    }) {\n      id\n    }\n  }\n`,
  CreateProfile = `\n  mutation createSettingsProfile($data: JSONData!, $schemaId: ID!) {\n    createStructuredData(input: {\n      data: $data\n      schemaId: $schemaId,\n    }) {\n      id\n      data\n      createdDateTime\n      modifiedDateTime\n    }\n  }\n`,
  UpdateProfile = `\n  mutation updateSettingProfile($profileId: ID!, $data: JSONData!, $schemaId: ID!) {\n    createStructuredData(input: {\n      id: $profileId,     \n      data: $data\n      schemaId: $schemaId,\n    }) {\n      id\n      data\n      createdDateTime\n      modifiedDateTime\n    }\n  }\n`,
}

export enum VideoType {
  CCTV = 'CCTV',
  BodyCam = 'Bodycam',
}

export const veritoneDefaultProfile = 'Veritone Default Profile';
export const dateTimeFormat =
  /^[A-Z][a-z]{2}, [A-Z][a-z]{2} \d{1,2}, \d{4}, \d{1,2}:\d{2} (AM|PM)$/;

export enum AccountProfile {
  TRIAL = 'sss-redact-trial',
  BUSINESS = 'sss-redact-business',
}

export interface OrganizationJsonData {
  // Undefined indicates no restrictions enterprise user.
  readonly accountProfile?: AccountProfile;
  readonly billing?: {
    readonly mediaProcessingLimitMs?: number;
    readonly mediaProcessedMs_lastUpdated?: string;
    readonly mediaProcessedMs?: number;
  };
  readonly features?: {
    redact?: {
      enableFolderView?: boolean;
      enableAutoColumn?: boolean;
      numberOfDaysBeforeToDelete?: number;
    };
  };
  readonly applicationIds: Array<string>;
}

export interface Organization {
  readonly id: string;
  readonly name: string;
  readonly jsondata: OrganizationJsonData;
  readonly mediaUsageMs: number | null;
}

type UserSettings = Array<{
  readonly key: string;
  readonly value: string;
}>;

export interface OrganizationPayload {
  readonly me: {
    readonly organization: Organization;
    readonly userSettings: UserSettings;
  };
}

# Redact API
**Provides backend api services for external users to ingest media files to redact application and retrieve the related media assets.**
## Run Locally
Clone this repo and navigate to server folder in terminal.

```bash
# Note: Before execute the command "yarn install", make sure ".npmrc" file exists in your root home directory with following line. //npm.pkg.github.com/:_authToken=<YOUR TOKEN HERE>

# Copy server config json.
$ cp apiConfig.json.sample ./apiConfig.json
# Install node modules
$ yarn install
# Install node modules
$ yarn start:dev

Your api server should be up and listening in port 9001.
```
## To launch in browser

```bash
  API Home: http://localhost:9001/api/v1/
  API Documentation:  http://localhost:9001/api-docs
```

## API Token Permissions
List of minimum permissions required for API Token key.

```bash 
"rights":[
  "admin.user.read", "job:create",
  "collections.collections.read", "collections.collections.create",
  "cms.media.read", "cms.media.delete", 
  recording:read", "recording:create","recording:delete"
],

```
Note: "admin.user.read" permission is required to support createFolder & createCase endpoints as userId is required for this workflow.
## Endpoints

| Action | Url | Description
| ----------- | ----------- |----------- |
| GET | /api/v1 | Home |
| GET | /api/v1/ping | Health Check |
| GET | /api/v1/isAuthorized | To check token valid and authorized to access redact api.
| POST | /api/v1/createFolder | To create a new folder.
| POST | /api/v1/createCase | To create a new case.
| POST | /api/v1/ingestMedia | To ingest one or more media files to case.
| GET | /api/v1/fileStatus/:tdoId | To check the status of ingested file.
| GET | /api/v1/caseFileList/:caseId | To retrieve the list of files by caseId.
| GET | /api/v1/redactMedia/:tdoId | To retrieve redacted media and audit log files by tdoId.
| GET | /api/v1/mediaFileDetails/:tdoId | To retrieve all assets of ingested media by tdoId.
| DELETE | /api/v1/deleteFolder/:folderId | To delete a folder using folderId.
| DELETE| /api/v1/deleteCase/:caseId | To delete a case using caseId.
| DELETE | /api/v1/deleteFile/:tdoId | To delete a file by tdoId.

## Request / Response Details
Below are sample request and response details for each endpoint.
<details>
  <summary>GET - /api/v1</summary>

  Response:
  ```json
  {
    "message": "Redact API!"
  }
  ```
</details>

<details>
  <summary>GET -  /api/v1/ping</summary>

  Response:
  ```json
    {
    "status": "Alive",
    "time": "2023-02-27T15:26:41.321Z"
    }
  ```
</details>

<details>
  <summary>GET - /api/v1/isAuthorized</summary>

  Request Header:
  ```json
     "headers": {
       "authorization": "Bearer {Token}"
     }
  ```


  Response:
  ```json
    {
      "status": "Authorized!",
      "tokenType": "Bearer"
    }
  ```
</details>

<details>
  <summary>POST - /api/v1/createFolder</summary>

  Request Header:
  ```json
    "headers": {
       "authorization": "Bearer {Token}"
     }
  ```

  Request Body:
  ```json
    {
        "name": "Test Folder123",
        "description": "test folder",
        "parentFolderId": "3aa52c20-0957-4b39-b0d0-1e4945252444"
    }
  ```

  Response:
  ```json
    {
        "newFolderId": "07dfaa4d-fde9-477c-9b6e-19004289ef80",
        "name": "Test Folder123"
   }
  ```
</details>

<details>
  <summary>POST - /api/v1/createCase</summary>

  Request Header:
  ```json
    "headers": {
       "authorization": "Bearer {Token}"
     }
  ```

  Request Body:
  ```json
    {
        "name": "Test Case123",
        "description": "test case",
        "parentFolderId": "07dfaa4d-fde9-477c-9b6e-19004289ef80"
    }
  ```

  Response:
  ```json
    {
     "caseId": "f4e5d88c-b0ec-4d0f-a51d-42b529760c92"
    }
  ```
</details>

<details>
  <summary>POST - /api/v1/ingestMedia</summary>
  Note: For audio only file set runDetection false otherwise head detection job will fail for that request.

  Request Header:
  ```json
    "headers": {
       "authorization": "Bearer {Token}"
    }
  ```

  Request Body: (sample using existingCaseId)
  ```json
    {
        "urls": [
            "https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4",
            "https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3"
        ],
        "runDetection": true,
        "runTranscription": true,
        "existingCaseId": "f4e5d88c-b0ec-4d0f-a51d-42b529760c92"
    }
  ```
   Request Body: (sample using createNewCase)
  ```json
    {
        "urls": [
                "https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4",
                "https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3"
            ],
            "runDetection": true,
            "runTranscription": true,
        "createNewCase": {
            "name": "SomeNewCase123",
            "description": "test case",
            "parentFolderId": "07dfaa4d-fde9-477c-9b6e-19004289ef80"
        }
    }
  ```

  Response:
  ```json
    {
        "caseId": "f4e5d88c-b0ec-4d0f-a51d-42b529760c92",
        "ingestedJobs": [
            {
            "id": "23020927_v0hm8JzzDI",
            "targetId": "2470000004",
            "status": "pending"
            },
            {
            "id": "23020927_ZhuPulx77W",
            "targetId": "2470000005",
            "status": "pending"
            }
        ]
    }
  ```
</details>

<details>
  <summary>GET - /api/v1/fileStatus/:tdoId</summary>

  Request Header:
  ```json
    "headers": {
       "authorization": "Bearer {Token}"
    }
  ```
  Request Path: /api/v1/fileStatus/2470000006


  Response:
  ```json
    {
    "id": "2470000006",
    "name": "sample.mp4",
    "signedUri": "https://api.stage.us-gov-2.veritone.com/media-streamer/download/tdo/2470000006",
    "status": "complete"
   }
  ```
</details>

<details>
  <summary>GET - /api/v1/caseFileList/:caseId</summary>

  Request Header:
  ```json
      "headers": {
       "authorization": "Bearer {Token}"
      }
  ```
  Request Path: /api/v1/fileStatus/4840882d-1065-4f5c-a744-0621666050b0?limit=10&offset=10

    caseId (in path) - 4840882d-1065-4f5c-a744-0621666050b0
    limit (in query) - 10 [Note: minimum should be 0 and maximum should be 9999. Default value is 100]
    offset (in query)- 0 [Note: minimum should be 0. Default value is 0]

  Response:
  ```json
    {
  "case": {
    "name": "SomeNewCase123",
    "files": [
      {
        "id": "2470000006",
        "name": "sample.mp4",
      },
      {
        "id": "2470000007",
        "name": "bloomberg.mp3",
      }
    ]
  }
}
  ```
</details>


<details>
  <summary>GET - /api/v1/redactedMedia/:tdoId</summary>

  Request Header:
  ```json
    "headers": {
       "authorization": "Bearer {Token}"
     }
  ```
  Request Path: /api/v1/redactedMedia/2470000006

  Response:
  ```json
    {
    "redactedMediaAssets": {
        "records": [
        {
            "type": "redacted-media",
            "signedUri": "https://vtstorcorestage.blob.core.usgovcloudapi.net/recording/assets%2F2470000006%2F73e09685-9416-4fa6-909d-b9c61882a17f.json?sv=2019-02-02&se=2023-02-27T17%3A18%3A30Z&sr=c&sp=r&sig=JJ9ZCZLEx%2F3uiRSSpGmZpKs5L%2FXY3s%2Brx7Qo8%2FJtqZc%3D",
            "createdDateTime": "2023-02-27T16:13:28.000Z"
        }
        ]
    },
    "auditLogAssets": {
        "records": [
        {
            "type": "blur-audit-log",
            "signedUri": "https://vtstorcorestage.blob.core.usgovcloudapi.net/api/1%2Fother%2F2023%2F1%2F1%2F1677514438771132160%2F16-58-797_0c90a594-f17b-47b7-b269-0ea03088319d?sv=2019-02-02&se=2023-02-27T19%3A18%3A30Z&sr=c&sp=r&sig=S%2BhfJBU0iFafmDZGm0kAR9Pbj7Dtj3M82aDYGXrGx0c%3D&rscd=attachment%3B%20filename%3D%22sample%255BREDACTED%255D.v1.mp4.csv%22",
            "createdDateTime": "2023-02-27T16:13:59.000Z"
        }
        ]
    }
    }
  ```
</details>

<details>
  <summary>GET - /api/v1/mediaFileDetails/:tdoId</summary>

  Request Header:
  ```json
    "headers": {
       "authorization": "Bearer {Token}"
     }
  ```
  Request Path: /api/v1/mediaFileDetails/2470000006

  Response:
  ```json
        {
        "isRedacted": true,
        "thumbnailUrl": "https://vtstorcorestage.blob.core.usgovcloudapi.net/recording/assets%2F2470000006%2Fcaf6a3a6-faef-4c1b-b6c7-31a6ca676863.jpeg?sv=2019-02-02&se=2023-02-27T17%3A19%3A04Z&sr=c&sp=r&sig=JpRdA8x%2B2i6eq0XA6tpd46raXM4fka2FFSeKuLw1ySA%3D",
        "originalFile": "https://api.stage.us-gov-2.veritone.com/media-streamer/download/tdo/2470000006",
        "redactedMediaFile": "https://vtstorcorestage.blob.core.usgovcloudapi.net/recording/assets%2F2470000006%2F73e09685-9416-4fa6-909d-b9c61882a17f.json?sv=2019-02-02&se=2023-02-27T17%3A19%3A04Z&sr=c&sp=r&sig=JpRdA8x%2B2i6eq0XA6tpd46raXM4fka2FFSeKuLw1ySA%3D",
        "auditLog": "https://vtstorcorestage.blob.core.usgovcloudapi.net/api/1%2Fother%2F2023%2F1%2F1%2F1677514438771132160%2F16-58-797_0c90a594-f17b-47b7-b269-0ea03088319d?sv=2019-02-02&se=2023-02-27T19%3A19%3A04Z&sr=c&sp=r&sig=UNSYK%2FxsGlU0eXBRNbr8BDUB5GxgP6cMvfjb4SNwbTw%3D&rscd=attachment%3B%20filename%3D%22sample%255BREDACTED%255D.v1.mp4.csv%22"
   }
  ```
</details>

<details>
  <summary>DELETE - /api/v1/deleteFolder/:folderId</summary>

  Request Header:
  ```json
    "headers": {
       "authorization": "Bearer {Token}"
     }
  ```
  Request Path: /api/v1/deleteFolder/03e42e45-585e-4313-9ab2-c6746f4818c5

  Response:
  ```json
    {
        "folderId": "03e42e45-585e-4313-9ab2-c6746f4818c5",
        "message": "Deleted folder successfully."
    }
  ```
</details>

<details>
  <summary>DELETE- /api/v1/deleteCase/:caseId</summary>

  Request Header:
  ```json
    "headers": {
       "authorization": "Bearer {Token}"
     }
  ```
  Request Path: /api/v1/deleteCase/bca11a08-1ecf-4724-8b14-a170c8cf0a97

  Response:
  ```json
    {
        "caseId": "bca11a08-1ecf-4724-8b14-a170c8cf0a97",
        "message": "Deleted case successfully."
    }
  ```
</details>

<details>
  <summary>DELETE - /api/v1/deleteFile/:tdoId</summary>

  Request Header:
  ```json
    "headers": {
       "authorization": "Bearer {Token}"
     }
  ```
  Request Path: /api/v1/deleteCase/2470000009

  Response:
  ```json
    {
        "id": "2470000009",
        "message": "TemporalDataObject 2470000009 and all associated asset content was deleted."
    }
  ```
</details>







/**
 * Converts a value between aStart and aEnd to a proportional value
 *  between bStart and bEnd
 */
export const aToB = (
  aStart: number,
  aEnd: number,
  bStart: number,
  bEnd: number
) => {
  const aRange = aEnd - aStart;
  const bRange = bEnd - bStart;
  return (i: number) => Math.round((i / aRange) * bRange + bStart);
};

export const formatTime = (
  timeMs: number,
  precision = 1,
  truncateZeroMs = false
) => {
  // comment out version return format without hours
  // const time = Math.round(ms) / 1000;
  // const hours = Math.trunc(time/3600)
  // const minutes = Math.trunc((time - hours*3600) / 60);
  // const seconds = Math.floor(time % 60);
  //
  // const seconds = ('0' + Math.abs(time % 60).toFixed(precision)).slice(
  //   -3 - precision
  // );
  // const milliseconds = time - Math.floor(time);
  //
  // if (hours)
  //
  // return truncateZeroMs && milliseconds === 0
  //   ? `${minutes}:${seconds.split('.')[0]}`
  //   : `${minutes}:${seconds}`;

  const duration = timeMs / 1000;

  const hours = Math.trunc(duration / 3600) | 0;
  const minutes = Math.trunc((duration / 60) % 60) | 0;
  const min = minutes < 10 ? '0' + minutes : minutes;
  let sec = ('0' + Math.abs(duration % 60).toFixed(precision)).slice(
    -3 - precision
  );
  const milliseconds = timeMs % 1000 | 0;
  if (truncateZeroMs && milliseconds === 0) {
    sec = `${sec.split('.')[0]}`;
  }
  return hours ? `${hours}:${min}:${sec}` : `${minutes}:${sec}`;
};

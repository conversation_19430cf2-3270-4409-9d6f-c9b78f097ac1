import 'moment/locale/fr';
import { useState } from 'react';
import { useStyles } from './styles';
import Notification from './Notification';
import { I18nTranslate } from '@common/i18n';
import { ThemeProvider } from '@mui/material';
import Typography from '@mui/material/Typography';
import { useSelector, useDispatch } from 'react-redux';
import { defaultTheme } from '@cbsa/styles/materialThemes';
import {
  selectNotifications,
  dismissNotification,
  clearNotification,
  AIWareNotification,
} from '@cbsa-modules/addMedia';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
} from '@cbsa-components/AddMedia/Accordion';

const NotificationsContent = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const notifications = useSelector(selectNotifications);
  const [newNotifications, dismissedNotifications] = notifications.reduce<
    [AIWareNotification[], AIWareNotification[]]
  >(
    ([n, d], notification) => {
      const sortByDate = (arr: AIWareNotification[]) =>
        arr.sort((a, b) =>
          a.data.createdDateTime > b.data.createdDateTime
            ? -1
            : a.data.createdDateTime < b.data.createdDateTime
              ? 1
              : 0
        );
      switch (notification.data.status) {
        case 'new':
          return [sortByDate([...n, notification]), d];
        case 'dismissed':
          return [n, sortByDate([...d, notification])];
        case 'failed':
          return [n, d];
        default:
          return [n, d];
      }
    },
    [[], []]
  );

  const mapNotifications = ({
    notifications,
    action,
  }: {
    notifications: AIWareNotification[];
    action: (n: AIWareNotification) => any;
  }) =>
    notifications.length > 0 ? (
      notifications.map((notification) => (
        <Notification
          action={action}
          key={notification.id}
          notification={notification}
        />
      ))
    ) : (
      <div className={'None'}>
        {I18nTranslate.TranslateMessage('noNotifications')}
      </div>
    );

  const [expanded, setExpanded] = useState(false);

  const dismissAll = () =>
    newNotifications.forEach((notification) =>
      dispatch(dismissNotification(notification))
    );

  const clearAll = () =>
    dismissedNotifications.forEach((notification) =>
      dispatch(clearNotification(notification))
    );

  return (
    <Accordion expanded={expanded} onChange={() => setExpanded(!expanded)}>
      <AccordionSummary aria-controls="notifications">
        <Typography>
          {I18nTranslate.TranslateMessage('notifications')}
        </Typography>
      </AccordionSummary>
      <AccordionDetails className={classes.notifications}>
        <div className={'Header'}>
          <div className={'Title'}>
            {I18nTranslate.TranslateMessage('newNotifications')}
          </div>
          {newNotifications.length > 0 && (
            <div className={'Action'} onClick={dismissAll}>
              {I18nTranslate.TranslateMessage('dismissAll')}
            </div>
          )}
        </div>
        {mapNotifications({
          notifications: newNotifications,
          action: (noti: AIWareNotification) =>
            dispatch(dismissNotification(noti)),
        })}
        <div className={'Header'}>
          <div className={'Title'}>
            {I18nTranslate.TranslateMessage('dismissedNotifications')}
          </div>
          {dismissedNotifications.length > 0 && (
            <div className={'Action'} onClick={clearAll}>
              {I18nTranslate.TranslateMessage('clearAll')}
            </div>
          )}
        </div>
        {mapNotifications({
          notifications: dismissedNotifications,
          action: (noti: AIWareNotification) =>
            dispatch(clearNotification(noti)),
        })}
      </AccordionDetails>
    </Accordion>
  );
};

const Notifications = () => (
  <ThemeProvider theme={defaultTheme}>
    <NotificationsContent />
  </ThemeProvider>
);

export default Notifications;

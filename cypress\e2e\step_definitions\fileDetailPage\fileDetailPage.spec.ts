import { Before, Then, When } from '@badeball/cypress-cucumber-preprocessor';
import { DataTestSelector } from '../../../support/helperFunction/mediaDetailHelper';
import '../common/mediaPlayerCommonStep';

Before(() => {
  cy.LoginLandingPage();
});

When('The user clicks on {string} Tab', (tabName: string) => {
  cy.getDataIdCy({ idAlias: `@${tabName}-tab-button` }).click();
});

Then('The user is on {string} Tab', (tabName: string) => {
  if (tabName === 'video') {
    cy.getDataIdCy({ idAlias: DataTestSelector.FoundObjectPlace }).should(
      'be.visible'
    );
  } else if (tabName === 'audio') {
    cy.getDataIdCy({ idAlias: DataTestSelector.ResultTabSaveBtn }).contains(
      'Save'
    );
    cy.getDataIdCy({ idAlias: DataTestSelector.ResultTabRedactBtn }).contains(
      'Redact'
    );
    cy.getDataIdCy({ idAlias: DataTestSelector.FileDetailPreview }).as(
      'preview'
    );
    cy.get('@preview').scrollIntoView();
    cy.get('@preview').contains('Preview').should('be.visible');
  }
});

import { renderCBSA, screen } from '@test/testUtils';
import CheckBox from './index';

describe('CheckBox', () => {
  const title = 'testTitle';
  it('should render title if present', () => {
    renderCBSA(<CheckBox title={title} />);
    expect(screen.getByTestId('checkbox-title')).toHaveTextContent(title);
  });

  it('should render svg if checked', () => {
    const { queryAllByDataTest } = renderCBSA(
      <CheckBox title={title} checked />
    );
    expect(queryAllByDataTest('svg')).toHaveLength(1);
  });

  it('should not render svg if unchecked', () => {
    const { queryAllByDataTest } = renderCBSA(
      <CheckBox title={title} checked={false} />
    );
    expect(queryAllByDataTest('svg')).toHaveLength(0);
  });
});

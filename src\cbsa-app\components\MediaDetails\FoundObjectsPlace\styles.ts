import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles(() => ({
  tabs: {
    '&>div>div>div': {
      flexDirection: 'column',
    },
  },

  content: {
    background: '#3D3D3D',
    display: 'grid',
    gridTemplateRows: '240px 1fr',
    maxHeight: '65vh',
  },

  indicator: {
    backgroundColor: '#81c784 !important',
    width: '2px !important',
    height: '100% !important',
  },

  tabsRoot: {
    height: '48px',
    backgroundColor: '#141414',
    color: 'rgba(255, 255, 255, 0.87)',
  },

  tabRoot: {
    width: '50%',
  },

  tab: {
    '& span': {
      fontFamily: 'Roboto, Arial, Helvetica, sans-serif',
      fontSize: '14px !important',
      fontWeight: 'normal',
      lineHeight: '16px',
      textAlign: 'center',
      color: 'rgba(255, 255, 255, 0.54)',
    },
  },

  activeTab: {
    '& span': {
      fontFamily: 'Roboto, Arial, Helvetica, sans-serif',
      fontSize: '14px !important',
      fontWeight: 500,
      lineHeight: '16px',
      textAlign: 'center',
      color: '#fff',
    },
  },

  videoResultsTab: {
    height: 'calc(100vh - 547px)',
    display: 'flex',
  },
}));

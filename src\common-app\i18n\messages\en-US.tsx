import { LOCALES } from '../locales';
const getCommonMessages = () => ({
  schemaIdNotFound:
    'SchemaId not found. Please verify the system configuration file.',
  review: 'Review',
  uploadMedia: 'Upload Media',
  sortBy: 'Sort by',
  createdDate: 'Created Date',
  lastModifiedDate: 'Last Modified Date',
  draft: 'Draft',
  pendingReview: 'Pending Review',
  complete: 'Complete',
  results: 'Results',
  redactedFiles: 'Redacted Files',
  save: 'Save',
  redactFile: 'Redact File',
  trimAndRedact: 'Trim and Redact',
  setTrimRange: 'Set Trim Range',
  clearTrimRange: 'Clear Trim Range',
  none: 'None',
  black_fill: 'Blackfill',
  outline: 'Outline',
  redacted: 'Redacted',
  preview: 'Preview',
  videoOnly: 'Video Only',
  detectObjects: 'DETECT OBJECTS',
  detectDescription:
    'Automatically detect and place bounding boxes on objects within this video, making redaction faster and easier for you.',
  detectEmptyDescription: 'No objects were detected in this media.',
  detectDeletedDescription: 'All detected objects have been deleted.',
  objectDetection: 'Object Detection',
  noPersonOfInterestImages: 'NO PERSON OF INTEREST IMAGES',
  POIReferenceImages: 'POI Reference Images',
  images: 'POI Reference Images',
  video: 'Video',
  audio: 'Audio',
  other: 'Other',
  comments: 'Comments',
  somethingWrong: 'Something went wrong',
  home: 'Home',
  transcription: 'Transcription',
  errorTranscriptionRun:
    'There was an error running Transcription engine. Would you like to rerun the engine again?',
  errorObjectDetectionRun:
    'There was an error running Object Detection engine. Would you like to rerun the engine again?',
  transcribeAudio: 'Transcribe Audio',
  transcriptionMessage:
    'Automatically transcribe this media to conveniently redact any portions of the audio.',
  noTranscribableAudio: 'No transcribable audio was found in this media.',
  deleteReactionTitle: 'Delete latest redacted file',
  deleteReactionContent: 'Would you like to delete the latest redacted file?',
  previousVersions: '{numVersions} previous version(s) remain.',
  exitWithoutSaving: 'Exit without saving',
  delete: 'Delete',
  remove: 'Remove',
  latestRedactedFile: 'Latest Redacted File',
  version: 'Version',
  download: 'Download',
  downloadZip: 'Download ZIP',
  exportZip: 'Export ZIP',
  exportZipWarning:
    'Zip file includes both redacted-media and audit-log. Warning: exporting to a zip file is slower than downloading redacted-media and audit-logs directly!',
  downloadAllMessage: 'Will download both redacted-media and the audit-log.',
  downloadMedia: 'Download Media',
  downloadAuditLog: 'DOWNLOAD AUDIT LOG',
  downloadRedactedTranscript: 'Download Redacted Transcript',
  downloadRedactionReport: 'Download Redaction Report',
  sendToGovQa: 'Send to GovQa',
  sendToFOIAXpress: 'Send to FOIAXpress',
  sendToCasepoint: 'Send to Casepoint',
  sendToExterro: 'Send to Exterro Review',
  sendToNuix: 'Send to Nuix Discover',
  sendToExternal: 'Send to External System',
  GovQAAuthentication: 'GovQA Authentication',
  add: 'Add',
  cancel: 'Cancel',
  confirm: 'Confirm',
  ok: 'Ok',
  mute: 'Mute',
  tone: 'Tone',
  blur: 'Blur',
  blackfill: 'Blackfill',

  headShape: 'Head Shape',
  udrShape: 'UDR Shape',
  laptopShape: 'Laptop Shape',
  vehicleShape: 'Vehicle Shape',
  plateShape: 'License Plate Shape',
  cardShape: 'ID Card (PII) Shape',
  notepadShape: 'Notepad (PII) Shape',
  personShape: 'Person Shape',

  headRedaction: 'Head Redaction',
  udrRedaction: 'UDR Redaction',
  laptopRedaction: 'Laptop Redaction',
  vehicleRedaction: 'Vehicle Redaction',
  plateRedaction: 'License Plate Redaction',
  cardRedaction: 'ID Card (PII) Redaction',
  notepadRedaction: 'Notepad (PII) Redaction',
  personRedaction: 'Person Redaction',

  shapes: 'Shapes',
  rectangle: 'Rectangle',
  ellipse: 'Ellipse',

  settings: 'Settings',
  videoType: 'Video Type',
  confidenceThreshold: 'Confidence Threshold',
  legacyClustering: 'Legacy Clustering',
  clusterSimThreshold: 'Cluster Similarity Threshold',
  verylowThreshold: 'Very Low (Less Clusters)',
  lowThreshold: 'Low',
  mediumThreshold: 'Medium',
  highThreshold: 'High',
  veryhighThreshold: 'Very High (More Clusters)',
  accuracySpeed: 'Accuracy ↔ Speed',
  trackBackwardsLimit: 'Track Backwards Limit',
  seconds: 'seconds',
  trackForwardLimit: 'Track Forward Limit',
  autoInterpolation: 'Auto Interpolation',
  milliseconds: 'milliseconds',
  millisecondsNote: 'milliseconds ({value})',
  manualInterpolationWindow: 'Manual Interpolation Window',
  videoOffset: 'Video Offset',
  fillType: 'Fill',
  redactionType: 'Redaction Type',
  detectionType: 'Detection Type',

  featherTypeHead: 'Feather Type Head (Blur)',
  featherTypeUdr: 'Feather Type Udr (Blur)',
  featherTypeLaptop: 'Feather Type Laptop (Blur)',
  featherTypeVehicle: 'Feather Type Vehicle (Blur)',
  featherTypeLicensePlate: 'Feather Type License Plate (Blur)',
  light: 'Light',
  medium: 'Medium',
  heavy: 'Heavy',

  blurLevel: 'Blur Level',
  aTob: '{a} to {b}',
  allWorkWillBeReset: 'All work will be reset',
  blurLevelVehicle: 'Blur Level (Vehicle)',
  patchPreviousVersion: 'Patch Previous Version',
  saveChanges: 'Save changes',
  saveContentModal: 'Would you like to save your changes before closing?',
  saveRunning: 'Save In Progress',
  saveRunningContentModal:
    'Are you sure you want to exit before the save has completed?',
  saveFailed: 'Save failed!',
  saveFailedContentModal:
    'Failed to save changes. Would you like to try again?',
  request: 'Request',
  notifications: 'Notifications',
  newNotifications: 'New Notifications',
  dismissedNotifications: 'Dismissed Notifications',
  dismissAll: 'Dismiss All',
  clearAll: 'Clear All',
  keyboardShortcuts: 'Keyboard Shortcuts',
  general: 'General',
  redactMedia: 'Redact Media',
  stickToVideo: 'Stick to video',
  trackObject: 'Track Object',
  trackForwardOnly: 'Track forward only',
  trackBackwardOnly: 'Track backward only',
  zoomInTimeline: 'Zoom in Timeline',
  zoomOutTimeline: 'Zoom out Timeline',
  toggleUdrTimeline: 'Expand / Collapse UDR Timeline',
  warningAssignMergedGroup: 'Warning! Group is assigned to a Merged Group!',
  addRedactionConfiguration: 'Add Redaction Effect',
  editRedactionConfiguration: 'Edit Redaction Effect',
  restoreRedactionConfiguration: 'Restore Redaction Effect',
  addEditRedactionConfigurationContent:
    'You are about to add/edit a redaction effect. Apply change to',
  restoreRedactionConfigurationContent:
    'You are about to restore a redaction effect. Apply change to',
  removeRedactionConfiguration: 'Remove Redaction Effect',
  customEffects: 'Custom Redaction Effect',
  removeRedactionConfigurationContent:
    'You are about to delete the custom redaction effect. Are you sure?',
  removeRedactionConfigurationContentMergedGroup:
    'You are about to delete the custom redaction effect. Apply change to',
  redactionEffects: 'Redaction Effects',
  objectType: 'Object Type',
  restoreDefault: 'Restore Default',
  addRedactionCode: 'Add Redaction Code',
  editRedactionCode: 'Edit Redaction Code',
  addEditRedactionCodeContent:
    'You are about to add/edit a redaction code. Apply change to',
  removeRedactionCode: 'Remove Redaction Code',
  removeRedactionCodeContent:
    'You are about to delete the redaction code. Are you sure?',
  removeRedactionCodeContentMergedGroup:
    'You are about to delete the redaction code. Apply change to',
  addCode: 'Add Code',
  editCode: 'Edit Code',
  chooseColorLabel: 'Select your color',
  chooseCodeLabel: 'Choose redaction code name',
  changeShape: 'Change Shape',
  changeShapeContent: 'You are about edit the shape type. Apply change to',
  center: 'Center',
  top: 'Top',
  bottom: 'Bottom',
  above: 'Above',
  below: 'Below',
  left: 'Left',
  right: 'Right',
  addRedactionCodeLocationPlaceholder: '- Select Position -',
  restoreDefaultColor: 'Restore Default Color',
  setTimeStamp: 'Set Time Stamp',
  minVal: 'Minimum value: {value}',
  maxVal: 'Maximum value: {value}',
  errorExceededName: 'Maximum value: exceeded',
  errorExceededType: 'exceeded',
  errorStartAfterEndName: 'Start cannot be after end',
  errorStartAfterEndType: 'Invalid',
  manualTrackingLocked: 'Manual Tracking (Locked)',
  increaseRectSize: 'Increase Rect Size',
  decreaseRectSize: 'Decrease Rect Size',
  increaseUDRSizeHorizontally: 'Increase UDR Size Horizontally',
  decreaseUDRSizeHorizontally: 'Decrease UDR Size Horizontally',
  increaseUDRSizeVertically: 'Increase UDR Size Vertically',
  decreaseUDRSizeVertically: 'Decrease UDR Size Vertically',
  playback: 'Playback',
  playPause: 'Play/Pause',
  spaceBar: 'space bar',
  goBackFrame: 'Go Back a Frame',
  or: 'or',
  advanceFrame: 'Advance a Frame',
  start: 'Start',
  end: 'End',
  jumpOneSecond: 'Jump 1 Second',
  jump10Seconds: 'Jump 10 Seconds',
  jumpOneMinute: 'Jump 1 Minute',
  jumpToStart: 'Jump to Start',
  jumpToEnd: 'Jump to End',
  increasePlaybackSpeed: 'Increase Playback Speed',
  decreasePlaybackSpeed: 'Decrease Playback Speed',
  defaultPlaybackSpeed: 'Default Playback Speed',
  returnToPreviousMarker: 'Return to the Previous Marker',
  setMarkerToCurrent: 'Set Marker to Current Position',
  editor: 'Editor',
  deleteUDR: 'Delete UDR',
  deleteOverlay: 'Delete Overlay',
  deleteInFrame: 'Delete in Frame',
  deleteGroup: 'Delete Group',
  deleteGroupContent: 'You are about to delete the group. Apply change to',
  deleteGroupFailure: 'Failed to delete group.',
  deleteLabelInFrame: 'Delete {label} in Frame',
  deleteLabelOverlay: 'Delete {label} Overlay',
  deleteLabelSegment: 'Delete {label} Segment',
  deleteLabelGroup: 'Delete {label} Group',
  resizeLabelSegment: 'Resize {label} Segment',
  resize: 'Resize',
  changePreview: 'Change Preview',
  selectNextOverlay: 'Cycle Highlighted Overlay',
  jumpNextGroupSegment: 'Jump to Next Segment',
  IncreaseUDRSize: 'Increase UDR Size',
  selectDeselectGroup: 'Redact/Un-redact Group',
  decreaseUDRSize: 'Decrease UDR Size',
  close: 'Close',
  reRunEngine: 'RERUN ENGINE',
  continue: 'Continue',
  DetectHeadsandObjects: 'Detect Heads and Objects',
  DetectHeadsandObjectsDes:
    'Running Head and Object Detection will replace any of the previous head and object detection results. Do you wish to continue?',
  detectObjectsTitle: 'Detect Objects',
  detectHeadObjectsDes:
    'Warning! Running object detection again will replace any of the previous detection results. Do you wish to continue? \n Select which object types you would like detected (At least one option must be selected)',
  object: 'Object',
  auto: 'Auto',
  time: 'Time',
  segmentType: 'Type',
  interpolation: 'INTERPOLATION',
  manual: 'Manual',
  saveChangesMessage: 'Would you like to save the changes?',
  selectedObjectProcessing: 'Selected Object is processing',
  ConfirmUnlinkingModalTitle: 'Confirm Unlinking From CMS',
  ConfirmUnlinkingModalContent:
    'You are about to unlink {tdoCount} files from CMS. Your media assets will still reside in CMS but you will no longer be able to access them within Redact.',
  unlink: 'Unlink',
  uploadFailed: 'upload failed',
  processingNewMedia: 'Processing New Media...',
  reqID: 'Req ID',
  fileRedacted: 'File Redacted',
  redactingFile: 'Redacting File',
  playVideo: 'Play Video',
  noFilesUploaded: 'No Files Uploaded',
  uploadFilesToStarted: 'Upload your files to get started',
  uploadFile: 'Upload File',
  noResultsFound: 'No Results Found',
  notFoundDes: 'Please a try a different selection.',
  confirmDeletionModalTitle: 'Confirm Deletion',
  confirmDeletionModalContent:
    'You are about to delete {tdoCount} files. You will not be able to undo this action.',
  confirmMultiDeletionModalTitle: 'Multiple File Deletion',
  confirmMultiDeletionModalContent:
    'You are about to delete {tdoCount} files. Are you sure?',
  filePickerRunHeadTooltip: 'Detect heads, laptops, and license plates',
  filePickerRunPersonTooltip: 'Detect persons, laptops, and license plates',
  filePickerRunHead: 'Run Head and Object Detection',
  filePickerRunPerson: 'Run Person and Object Detection',
  runTranscription: 'Run Transcription',
  ingest: 'Ingest',
  ingestFromUrl: 'Ingest from URL',
  govqaID: 'GovQA Request ID: {govQARequestId}',
  foiaXpressID: 'FOIAXpress Request ID: {foiaXpressRequestId}',
  casepointID: 'Casepoint ID: {casepointRequestId}',
  nuixID: 'NUIX ID: {nuixRequestId}',
  exterroID: 'Exterro ID: {exterroRequestId}',
  engineProcessingTimeDependDes:
    'This may take some time depending on video length.',
  engineProcessingWaitDes1: 'You can wait for the processing to finish here',
  engineProcessingWaitDes2: 'or go back to the',
  homePage: 'HOMEPAGE',
  set: 'Set',
  unredact: 'Unredact',
  unredactAll: 'Unredact All',
  editNote: 'Edit Note',
  deleteNote: 'Delete Note',
  addNote: 'Add Note',
  redact: 'Redact',
  redactAll: 'Redact All',
  maskVoice: 'Mask Voice',
  unmaskVoice: 'Un-Mask Voice',
  notesFormHelpTextLabels: 'Separate each label by a comma and/or space.',
  notesFormLabels: 'Labels',
  notesFormNotes: 'Notes',
  searchViewPlaceholder: 'Hit Enter to search...',
  forbidden: 'Forbidden',
  logoutMessage: 'Please wait while you are redirected to the login page.',
  logoutWait: 'Logging out...',
  tdosGridHasMore: 'Scroll down to load more media...',
  pageNotFound: 'Page Not Found',
  pageNotFoundDes:
    "The page you are looking for has been moved, deleted or doesn't exist.",
  overviewTitle: 'Upload Media for redaction',
  action: 'action',
  auditLogDialogTitle: 'Case Audit Log',
  id: 'id',
  approve: 'Approve',
  approveCaseTitle: 'Approve Case',
  approveCaseContent: 'Are you sure you want to approve this case?',

  archive: 'Archive',
  archiveCaseTitle: 'Archive Case',
  archiveCaseContent:
    'Are you sure you want to archive this case? Once archived, the case will be suppressed from view on the dashboard unless "View All" toggle is enabled.',

  removeFilterTitle: 'Removing From Output',
  removeFilterHeads:
    'Please confirm: redacted Heads are NOT to be included in the final video.',
  removeFilterUdrs:
    'Please confirm: redacted UDRs are NOT to be included in the final video.',
  removeFilterLicensePlates:
    'Please confirm: redacted License Plates are NOT to be included in the final video.',
  removeFilterLaptops:
    'Please confirm: redacted Laptops are NOT to be included in the final video.',
  removeFilterNotepads:
    'Please confirm: redacted Notepads are NOT to be included in the final video.',
  removeFilterCards:
    'Please confirm: redacted ID Cards are NOT to be included in the final video.',
  removeFilterPersons:
    'Please confirm: redacted Persons are NOT to be included in the final video.',
  removeFilterConfirmDialog:
    "Are you sure you'd like to remove this filter from output? Once removed, the filter state cannot be recovered.",

  reopen: 'Reopen',
  reopenCaseTitle: 'Reopen Case',
  reopenCaseContent: 'Are you sure you want to reopen this case?',

  date: 'date',
  deleteCaseTitle: 'Delete Case',
  deleteCaseContent:
    'Are you sure you want to delete this case? Once deleted, the case cannot be recovered.',
  deleteCaseRequest:
    'You must remove all media from this case before deleting.',

  deleteTdoTitle: 'Delete Selected',
  deleteTdoContent: 'Are you sure you want to delete the selected media?',

  export: 'Export',
  exportCaseTitle: 'Export Case',
  exportCaseContent: 'Are you sure you want to export this case?',

  hash: 'hash',

  process: 'Process',
  processCaseTitle: 'Process Case',
  processCaseContent:
    'Run POI search on all videos. Please ensure you have uploaded all POI images and videos. Do you wish to continue?',

  logs: 'Logs',
  user: 'user',

  userProfileMenu: 'User Profile Menu',
  signOut: 'Sign Out',
  casesCreated: 'Cases Created',
  numOfDaysOld: '# Days',
  averageTimeProcessing: 'Average Time Processing',
  casesInQueue: 'Cases In Queue',
  currentFailed: 'Current Failed',
  statusDropdown: 'Status Dropdown',
  allStatuses: 'All Statuses',
  showArchived: 'Show Archived',
  newCase: 'New Case',
  newImage: 'New Image',
  hideArchived: 'Hide Archived',
  deleting: 'Deleting',
  archived: 'Archived',
  new: 'New',
  pending: 'Pending',
  processing: 'Processing',
  processed: 'Processed',
  error: 'Error',
  readyForReview: 'Ready for Review',
  approved: 'Approved',
  readyForExport: 'Ready for Export',
  deleted: 'Deleted',
  deleteFolder: 'Delete Folder',
  submit: 'Submit',
  caseName: 'Case Name',
  caseNameRequired: 'Case Name Required',
  folderName: 'Folder Name',
  folderNameRequired: 'Folder Name Required',
  deselectAll: 'Deselect All',
  selectAll: 'Select All',
  dropToUploadMedia: 'Drop to Upload Media',
  dragAndDrop: 'Drag and Drop',
  orClickToAddMedia: 'or Click to Add Media',
  viewAuditLog: 'View Audit Log',
  processCase: 'Process Case',
  approveCase: 'Approve Case',
  reopenCase: 'Reopen Case',
  archiveCase: 'Archive Case',
  deleteCase: 'Delete Case',
  exportCase: 'Export Case',
  caseReviewManagement: 'Case Review Management',
  addMedia: 'Add Media',
  searchPlaceholder: 'Search...',
  searchCodePlaceholder: 'Search for code',
  loadingCodes: 'Loading Codes...',
  noCodes: 'No Codes Available...',
  daysOld: 'Days Old',
  casesApproved: 'Cases Approved',
  casesReopened: 'Cases Reopened',
  noItemsToDisplay: 'No Items to Display',
  lastUpdated: 'Last Updated',
  age: 'Age',
  status: 'Status',
  mediaDetails: 'Media Details',
  caseDetails: 'Case Details',
  caseActions: 'Case Actions',
  caseMedia: 'Case Media',
  of: 'of',
  perPage: 'Per Page',
  details: 'Details',
  archiveCaseConfirmDialog:
    'Are you sure you\'d like to archive this case? Once archived, the case will be suppressed from view on the dashboard unless "View All" toggle is enabled.',
  deleteCaseConfirmDialog:
    "Are you sure you'd like to delete this case? Once deleted, the case cannot be recovered.",
  back: 'Back',
  deleteMessage:
    "Are you sure you'd like to delete this? Once deleted, it cannot be recovered.",
  moveToVirtualFolderNotAllowed:
    'Folders or Requests can not be nested within input or output folders.',
  invalidFileMove:
    'Invalid move, files are allowed to move into requests only.',
  activeCases: 'Active Cases',
  approvedCases: 'Approved Cases',
  archivedCases: 'Archived Cases',
  reviewCases: 'Under Review Cases',
  noCases: 'No Cases',
  openCase: 'Open Case',
  sourceMedia: 'Source Media',
  redactedMedia: 'Redacted Media',
  noMedia: 'No Media',

  exportPending: 'This case will be exported momentarily.',
  exportRunning: 'This case is being exported right now!',
  exportComplete:
    'This case is ready to download! Please click "Download" below.',
  exportFailed: 'This case failed to export.',
  exportDefault: 'Please wait for your export to begin.',

  readyForReviewTitle: 'Ready to Review Case',
  readyForReviewTitleContent: 'Are you sure this case is ready to review?',

  caseIsNew: 'Case is new',
  caseIsProcessing: 'Case is processing',
  caseHasAnError: 'Case has an error',
  caseIsReadyForReview: 'Case is ready for review',
  caseHasBeenApproved: 'Case has been approved',
  caseIsReadyForExport: 'Case is ready for export',
  caseHasBeenDeleted: 'Case has been deleted',
  caseStatusWasNotUpdated: 'Case status was not updated',

  isBeingProcessed: 'is being processed',
  couldNotBeProcessed: 'could not be processed',

  newCaseCreated: 'New Case Created',
  caseDeleted: 'Case Deleted',
  caseArchived: 'Case Archived',
  noNotifications: 'No Notifications',

  displayUnselectedOverlays: 'Show Non-redacted Overlays',
  displayUnselectedOverlaysTitle: 'Show Non-redacted Overlays',
  displayUnselectedOverlaysContent:
    'Warning! Previewing non-redacted overlays may result in a rendering lag. A lag may occur when there are a large number of visible overlays.',

  timelineUdr: 'UDR',
  timelineHead: 'HEAD',
  timelinePerson: 'PERSON',
  timelinePoi: 'POI ',
  timelinePlate: 'PLATE',
  timelineVehicle: 'VEHICLE',
  timelineLaptop: 'LAPTOP ',
  timelineNotepad: 'NOTEPAD (PII)',
  timelineCard: 'ID CARD (PII)',
  timelineTranscription: 'AUDIO',
  timelineComments: 'COMMENTS',
  created: 'Created',
  running: 'Running',
  view: 'View',
  navigationDiscardWarning:
    'You have unsaved changes on this page. Do you want to leave this page and discard your changes or stay on this page?',
  mostRecent: 'Most Recent',
  all: 'All',
  head: 'Head',
  face: 'Head',
  poi: 'POI',
  udr: 'UDR',
  laptop: 'Laptop',
  manualInterp: 'Manual',
  licensePlate: 'License plate',
  plate: 'Plate',
  plateOrVehicle: 'Plate / Vehicle',
  vehicle: 'Vehicle',
  notepad: 'Notepad (PII)',
  card: 'ID Card (PII)',
  person: 'Person',
  fetchDetAssets: 'Fetching object detection assets...',
  procDetAssets: 'Processing object detection assets...',
  caseStatusChanged: 'Case status has been changed',
  caseStatusNotChanged: 'Case status was not changed',
  caseNotDeleted: 'Case was not deleted',
  fileDeleted: 'File deleted',
  fileFailedDeleted: 'File failed to delete',
  fileFailedCreateTDO: 'File failed create export TDO',
  exportTdoNotCreated: 'Export TDO could not be created',
  caseCouldNotExported: 'Case could not be exported',
  failedToFetchCase: 'Failed to fetch case',
  caseNameChange: 'Case name changed to {caseName}',
  caseNameChangeFail: 'Failed to change case name: {error}',
  notiDismissFail: 'Notification could not be dismissed',
  notiClearFail: 'Notification could not be cleared',
  failedToFetchMedia: 'Failed to fetch media',
  failedToFetchComments: 'Failed to fetch comments',
  fileUploadSuccess: 'File uploaded successfully',
  fileUploadFail: 'File upload failed',
  latestRedactDeleted: 'Latest redacted file has been successfully deleted',
  latestRedactDeletedFail: 'Latest redacted file deletion failed',
  downloadMediaFail: 'Attempt to download media failed',
  procNotFinishedWait:
    'This file has not finished processing. Wait for ingestion to complete.',
  engineRetryError: 'An error occurred while retrying engine.',
  successfulRedact: 'File redacted successfully. Click View File to review.',
  engineJobFailureMsg:
    'Engine failed to run with the following engine id: {engineId}',

  noOutputFilesToExport: 'No output files to export.',
  casemediatitle: 'Case Media',
  viewAllCases: 'View All Cases',
  addCase: 'Add Case',
  addFolder: 'Add Folder',
  createdSuccessfully: 'created successfully',
  failedToCreate: 'could not be created',
  caseDashboard: 'Case Dashboard',
  viewAllFolders: 'View All Folders',
  searchFoldersAndCases: 'Search Folders and Cases...',
  searchResults: 'Search Results',
  search: 'Search',
  name: 'Name',
  dateCreated: 'Date Created',

  noCommentsFound: 'No comments found in this media.',
  addNewComment: 'Add New Comment',
  timeSyncCommentTooltip: 'Jump To Frame',
  timeSyncMediaTime: 'Media time {time}',
  postedOn: 'Posted on',
  editedOn: 'Edited on',
  comment: 'Comment',
  successfullySavedComment: 'Successfully saved comment.',
  failedToSaveComment: 'Failed to save comment.',
  failedToFetchUser: 'Failed to fetch user.',

  read: 'Read',
  markAsRead: 'Mark as read',
  edit: 'Edit',
  hide: 'Hide',
  unhide: 'Unhide',
  filterDetections: 'Filter Detections',
  removeFromOutput: 'Remove From Output',
  viewHidden: 'View Hidden',
  viewFilter: 'View Filter',
  hidden: 'Hidden',
  hideFilter: 'Hide Filter',
  sortByTimestamp: 'Sort by Timestamp',
  sortByDateCreated: 'Sort by Date Created',
  viewPeopleFilter: 'View User Filter',
  hidePeopleFilter: 'Hide User Filter',
  sortAscend: 'Sort Ascend',
  sortDescend: 'Sort Descend',
  commenter: 'Commenter',
  legacyMediasLinkText: 'View Unassigned Media',
  legacyMediasTitle: 'Unassigned Media',
  profileName: 'Profile Name',
  isDefault: 'Is Default',
  createdBy: 'Created By',
  modifiedBy: 'Modified By',
  createdOn: 'Created On',
  modifiedOn: 'Modified On',

  redaction: 'Redaction',
  headDetection: 'Head Detection',
  bodyDetection: 'Body Detection',
  personLocation: 'Person Location',
  personOfInterest: 'Person of Interest',

  menuOptionDeleteFolder: 'Delete',
  menuOptionRenameFolder: 'Rename',
  menuOptionFolderDetails: 'Folder Details',
  menuOptionMoveFolder: 'Move',
  menuOptionDownloadAll: 'Download All',

  menuOptionEditFileAttributes: 'Edit Attributes',
  menuOptionAddTagFile: 'Add Tag',
  menuOptionReviewFile: 'Review File',
  menuOptionViewLogFile: 'View Log',
  menuOptionCopyFile: 'Copy File',
  menuOptionPlayFile: 'Play File',
  menuOptionDownloadFile: 'Download File',
  menuOptionDeleteFile: 'Delete File',
  addRequest: 'Add Request',
  searchFoldersAndRequests: 'Search Folders and Requests...',
  requestName: 'Request Name',
  requestNameRequired: 'Request Name required',
  moveFolder: 'Move To',
  confirmMove: 'Move',
  noFoldersFound: 'No folders found.',

  moveFolderSuccess: 'Moving folder or request is successful.',
  moveFolderFailure: 'Failed moving folder or request.',
  renameSuccess: 'Rename completed successfully.',
  folderCannotNestedMoreThanTwoLevel:
    'Folders can not be nested more two level.',
  requestShouldBeOneLevelDeep:
    'Requests should be nested at least one level deep within a master folder.',
  containsNestedFolders:
    'Selected folder has nested folders. Folders can not be nested more two level.',
  foldersShouldNestedInRequest: 'Folders can not be nested within requests.',
  requestCannotNestedInRequests: 'Requests can not be nested within request.',
  nameExistsInFolder:
    'This name  already exist for this selected folder. Sorry, duplicate names not allowed.',
  nameExistsCheckFailed: 'Failed name exists check for',
  errorAddingMedia: 'An error occurred while adding the media.',
  errorUpdatingMedia: 'An error occurred while updating media.',
  errorRemovingMedia: 'An error occurred while removing the media.',
  errorRemovingFiles: 'An error occurred while removing one or more files.',
  errorPreloadingMedia: 'An error occurred while pre-loading the media list.',
  errorUnlinkingFiles: 'An error occurred while unlinking one or more files.',
  invalidRequestToIngest:
    'File should be uploaded to request. Please select a valid request to ingest.',
  selectRequestToIngest: 'Select a request to ingest',
  confirmSelectRequest: 'Select',
  noRedactPermission: 'Please make sure user has REDACT editor role.',
  fetchRootFolderFailed: 'Unable to fetch folders.',
  createRootFolderFailed: 'Failed to create org root folder.',
  sourceAndDestSame: 'Selected folder cannot be moved within itself.',
  folderAlreadyExitsInParent: 'Selected folder already exists within parent.',
  requestAlreadyExitsInParent: 'Selected request already exists within parent.',
  inValidSource: 'Invalid move. Please try again.',
  inValidDest: 'Invalid target folder. Please try again.',
  code: 'Code',
  codeName: 'Code Name',
  description: 'Description',
  codeFontColor: 'Code Font Color',
  redactions: 'Redactions',
  redactionCodes: 'Redaction Codes',
  addNewCode: 'Add New Code',
  settingsProfile: 'Profiles',
  settingsProfileTitle: 'Settings Profile: ',
  addNewSettingsProfile: 'Add New Profile',
  deleteSettingsProfile: 'Delete Profile',
  fetchProfileListFailure:
    'Unexpected error occurred while fetching profile list.',
  createSettingsProfileFailure:
    'Unexpected error occurred while creating settings profile.',
  updateSettingsProfileFailure:
    'Unexpected error occurred while updating settings profile.',
  deleteSettingsProfileFailure:
    'Unexpected error occurred while deleting settings profile.',

  codeColor: 'Code Color',
  createdDateTime: 'Date Added',
  modifiedDateTime: 'Date Modified',
  definingRedactedAreas: 'Defining Redacted Areas',
  redactionCodeDetails: 'Redaction Code Details',
  colorDescription:
    'Choose a color for your Redaction Code font when it is displayed on screen.',
  saveRedactionCodeSuccess: 'Saved Redaction Code successfully.',
  savedRedactionCodeFailure: 'An error occurred while saving redaction code.',
  fetchRedactionCodesFailure:
    'Failed to retrieve redaction codes: {responseCode}',
  deleteRedactionCode: 'Delete Redaction Code',
  deleteRedactionCodeSuccess: 'Deleted redaction code successfully.',
  deleteRedactionCodeFailure:
    'An error occurred while deleting redaction code.',
  checkRedactionCodeExistsSuccess:
    'Failed to save. Looks like Redaction Code or Code Name already exists. Please verify and use different Code or Code Name.',
  checkRedactionCodeExistsFailure:
    'An error occurred while performing redaction code exists validation.',
  illuminateFolderNameMsg:
    "Folder name 'Illuminate App' is not allowed in root level as it's reserved for Illuminate app only. Please provide different folder name.",
  moveFileToCaseSuccess: 'File moved successfully.',
  moveFileToCaseFailure:
    'Unexpected error occurred while moving the file. Please try again later.',
  menuOptionMoveFile: 'Move File',
  sourceAndDestCaseOfFileSame:
    'Unable to move as both source and destination request are the same.',
  tdoIsLocked:
    'This file is currently being edited by {name}. Please wait or refresh the page to try again.',
  cannotDeleteFileInUse: 'Cannot delete -- file currently in use.',
  cannotDeleteFileJobsInProcessing:
    'Cannot delete as some of the case files are currently in processing state. Please try again later.',
  downloadAllJobFailure:
    'Unexpected error occurred while downloading all output files. Please try again later.',
  downloadAllComplete: 'Download All completed.',
  editNameTDOSuccess: 'TDO name updated successfully.',
  editNameTDOFailed: 'TDO update failed.',

  noRedactionsTitle: 'No Redactions Selected',
  noRedactionsText:
    'No redactions have been selected for this file. If this is correct, please confirm to continue.',
  detectHeadObjects: 'Head and Objects',
  detectPersonObjects: 'Person and Objects',

  undo: 'Undo last action',
  redo: 'Redo previous action',
  cannotUndo: 'Cannot undo last action',
  cannotRedo: 'Cannot redo previous action',
  groupingTools: 'Grouping Tools',
  mergeUnmergeSelect: 'Merge/Un-merge Select',
  mergeUnmerge: 'Merge/Un-merge',
  selectGroupRange: 'Select Group Range',

  processTranscriptionSuccess: 'A transcription process has completed.',
  mergedGroup: 'Merged Group',
  mixed: 'Mixed',
  allSegments: 'All segments',
  pinToTop: 'Pin to Top',
  unpin: 'Unpin',
  mergeWithPinnedGroup: 'Merge with Pinned Group',
  mergeWithNamedGroup: 'Merge with Named Group',
  merge: 'Merge',
  unmerge: 'Unmerge',
  clear: 'Clear',
  select: 'Select',
  deselect: 'Deselect',

  mergeConfirm: 'Merge confirmation',
  unmergeConfirm: 'Unmerge confirmation',
  mergeConfirmText: 'Creating new merged group. Please confirm group name:',
  mergeMultipleText:
    'Selection includes multiple named groups! Please select name for the merged group:',
  unmergeConfirmText:
    'Moving segment(s) to a new group. Please confirm group name',
  mergeTextBox: 'Merged Group',
  apply: 'Apply',
  groupNameEnterText: 'Enter a group name',
  unMergeFromGroup: 'Un-Merge from Group',
  selectSegment: 'Select Segment',
  deselectSegment: 'Deselect Segment',
  deleteSegment: 'Delete Segment',
  selected: 'Selected',
  and: 'and',
  group: 'Group',
  segment: 'Segment',

  rotationPreview: 'Rotation Preview',
  rotateConfirmTitle: 'Rotation Confirmation',
  rotateConfirmDes:
    'Warning! Rotating will remove all existing UDRs and detected objects! \nYou can resume editing after the video has finished processing.',
  selectDetectOption: 'Select any object types you would like detected.',
  rotateVideoSuccess: 'Processing Video Rotation.',
  rotateVideoFailure: 'Unexpected error occurred while rotating video.',
  errorFetchingAuditLog:
    'Unexpected error fetching audit logs. Please try again.',
  errorFetchingTDOState: 'Error fetching latest TDO state',
  errorFetchingGovQAIntegrationInfo: 'Could not fetch GovQA Integration Info.',
  missingGovQAIntegrationConfig:
    'GovQA integration configuration is missing information.',
  errorFetchingExternalIntegrationInfo:
    'Could not fetch External Integration Info.',
  missingExternalIntegrationConfig:
    'External integration configuration is missing information.',
  errorFetchingFOIAXpressIntegrationInfo:
    'Could not fetch FOIAXpress Integration Info.',
  missingFOIAXpressIntegrationConfig:
    'FOIAXpress integration configuration is missing information.',
  errorFetchingCasepointIntegrationInfo:
    'Could not fetch Casepoint Integration Info.',
  missingCasepointIntegrationConfig:
    'Casepoint integration configuration is missing information.',
  errorFetchingExterroIntegrationInfo:
    'Could not fetch Exterro Integration Info.',
  missingExterroIntegrationConfig:
    'Exterro integration configuration is missing information.',
  errorFetchingNuixIntegrationInfo: 'Could not fetch Nuix Integration Info',
  missingNuixIntegrationConfig:
    'Nuix integration configuration is missing information.',
  errorUnknown: 'Unknown error',
  errorSendingFile: 'Could not send file. Please contact your administrator.',
});

export default {
  [LOCALES.ENGLISH]: {
    ...getCommonMessages(),
  },
  [LOCALES.REDACT_ENGLISH]: {
    ...getCommonMessages(),

    // Override common values
    caseActions: 'Request Actions',
    casesCreated: 'Request Created',
    casesApproved: 'Request Approved',
    casesReopened: 'Request Reopened',
    caseName: 'Request Name',
    caseNameRequired: 'Request Name Required',
    caseDeleted: 'Request Deleted',
    caseArchived: 'Request Archived',
    caseDashboard: 'Request Dashboard',
    archiveCase: 'Archive Request',
    deleteCase: 'Delete Request',
    deleteCaseConfirmDialog:
      "Are you sure you'd like to delete this request? Once deleted, the request cannot be recovered.",
    archiveCaseConfirmDialog:
      'Are you sure you\'d like to archive this request? Once archived, the request will be suppressed from view on the dashboard unless "View All" toggle is enabled.',
  },
};

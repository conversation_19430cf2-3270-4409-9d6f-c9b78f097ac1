import {
  select,
  take,
  all,
  fork,
  cancel,
  put,
  takeLatest,
} from 'typed-redux-saga/macro';
import { isEqual } from 'lodash';
import { redirect, Location } from 'redux-first-router';
import { modules } from '@veritone/glc-redux';

import { getPendo, getSegment } from '@helpers/analyticsHelper';

import { ROUTE_FORBIDDEN, selectRoutesMap, selectPreviousRoute } from './index';
import { BOOT_FINISHED, bootDidFinish } from '../app';
import { Task } from 'redux-saga';
import { Routes } from '@redact/pages/routes';

const {
  user: { LOGOUT, userIsAuthenticated },
} = modules;

export default function* routes() {
  yield* all([
    fork(watchRouteSagas),
    fork(redirectToForbiddenRouteOnApiAuthErrors),
  ]);
}

// setup sagas on application boot
export let currentRouteTask: Task<any>;
export function* watchRouteSagas() {
  const routesMap = yield* select(selectRoutesMap);

  // watch routing actions -- spawn route sagas when the route mounts, and
  // cancel them when the route exits.
  yield* takeLatest(Object.keys(routesMap), watchRouteSagasHandle(routesMap));
}
export const selectPendoSegment = (state: any) => {
  getPendo(state);
  getSegment(state, window.location.pathname);
};
export const watchRouteSagasHandle = (routesMap: Routes) =>
  function* (currentRoute: Location) {
    const hasBooted = yield* select(bootDidFinish);
    if (!hasBooted) {
      yield* take(BOOT_FINISHED);
    }

    const userIsAuthed = yield* select(userIsAuthenticated);
    const currentRouteMap = routesMap[currentRoute.type];
    if (typeof currentRouteMap === 'object') {
      if (currentRouteMap?.requiresAuth && !userIsAuthed) {
        // do not run sagas for inaccessible routes
        return;
      }

      const previousRoute = yield* select(selectPreviousRoute);

      if (
        currentRoute.type === previousRoute.type &&
        isEqual(currentRoute.payload, previousRoute.payload)
        // todo: etc? query?
      ) {
        // no route change; leave sagas alone
        // fixme -- seems like this might not work. prev/current are never same?
        return;
      }

      /**
       * Poor getPendo() code failes if User not available. At least only inits once.
       * getSegment() does init and path hit. At least only inits once (sort of).
       */
      yield* select(selectPendoSegment);
      if (currentRouteTask) {
        yield* cancel(currentRouteTask);
      }

      if (currentRouteMap.saga) {
        currentRouteTask = yield* fork(currentRouteMap.saga);
      }
    }
  };

function* redirectToForbiddenRouteOnApiAuthErrors() {
  const forbiddenStatusCodes = [401, 403];

  yield* takeLatest(
    function ({
      payload,
    }: { payload?: { name: string; status: number } } = {}) {
      if (payload) {
        const { name, status } = payload;
        return name === 'ApiError' && forbiddenStatusCodes.includes(status);
      } else {
        return false;
      }
    } as any, // TODO: Fix this
    redirectToForbiddenHandle
  );
}

export function* redirectToForbiddenHandle() {
  // ignore if user is not logged in
  if (yield* select(userIsAuthenticated)) {
    yield* put(redirect(ROUTE_FORBIDDEN()));
  } else {
    yield* put({ type: LOGOUT });
  }
}

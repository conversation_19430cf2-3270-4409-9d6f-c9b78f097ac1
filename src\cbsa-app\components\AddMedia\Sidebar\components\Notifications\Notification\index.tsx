import cn from 'classnames';
import moment from 'moment/moment';
import { useIntl } from 'react-intl';
import { useStyles } from './styles';
import { Close } from '@mui/icons-material';
import { AIWareNotification } from '@cbsa-modules/addMedia';

const Notification = ({ notification, action }: Props) => {
  const intl = useIntl();
  const classes = useStyles();
  const {
    data: { createdDateTime, message, notificationType, status },
  } = notification;

  return (
    <div className={classes.notification}>
      <div
        className={cn('Indicator', {
          failed: status === 'failed',
        })}
      />
      <div className={'Status'}>
        <div className={'Message'}>
          {`${intl.formatMessage({
            id: notificationType,
          })} ${intl.formatMessage({ id: message })}`}
        </div>
        <div className={'TimeAgo'}>{moment.utc(createdDateTime).fromNow()}</div>
      </div>
      <Close className={'Close'} onClick={() => action(notification)} />
    </div>
  );
};

interface Props {
  notification: AIWareNotification;
  action: (n: AIWareNotification) => any;
}

export default Notification;

import { all, fork, put, select, takeLatest } from 'typed-redux-saga/macro';

import {
  selectAllowDownload,
  // @ts-expect-error Types are invalid - needs fixing
  TRY_USER_PERMISSION,
  // @ts-expect-error Types are invalid - needs fixing
  TryUserPermissionPayload,
} from '@user-permissions';

import { actionOpenUpgradeAccount } from '../actions';

interface Action<P> {
  type: string;
  payload: P;
}

export function* userPermissionsSagas() {
  yield* all([fork(onFetchUserSuccess)]);
}

function* onFetchUserSuccess() {
  yield* takeLatest<Action<TryUserPermissionPayload>>(
    TRY_USER_PERMISSION,
    function* ({ payload }) {
      const [allow, _, actions] = yield* select(selectAllowDownload);
      if (payload === 'allowDownload' && !allow && actions) {
        yield* put(actionOpenUpgradeAccount());
      }
    }
  );
}

import {
  But<PERSON>,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogContentText,
  IconButton,
  ThemeProvider,
  TextField,
} from '@mui/material';
import RemoveIcon from '@mui/icons-material/Close';
import { Fragment, ChangeEvent, useState } from 'react';

import { buttonTheme } from '@redact/materialUITheme';
import * as styles from './styles.scss';

const InviteAppModalView = ({ onSendInvite }: InviteAppModalViewPropTypes) => {
  const [textValue, setTextValue] = useState('');
  const [emails, setEmails] = useState<ReadonlyArray<string>>([]);
  const [errMsg, setErrMsg] = useState<string | null>(null);

  const onClear = () => {
    setEmails([]);
    setTextValue('');
    setErrMsg(null);
  };

  const onChange = ({ target }: ChangeEvent<HTMLTextAreaElement>) => {
    const es = target.value.split(',').map((e) => e.trim().toLowerCase());
    const isValid = es.reduce(
      (valid, email) => valid && isValidEmail(email),
      true
    );
    setErrMsg(
      isValid ? null : 'One or more email addresses entered are not valid.'
    );
    setEmails(es);
    setTextValue(target.value.toLowerCase());
  };

  const handleSendInvite = () => {
    onSendInvite(emails);
  };

  return (
    <Fragment>
      <DialogContent className={styles.closeDialogContent}>
        <DialogContentText
          className={styles.closeDialogContentText}
          align="center"
        >
          Invite your coworkers and associates
        </DialogContentText>
        <TextField
          className={styles.textField}
          autoFocus
          margin="dense"
          label="Email Address"
          type="text"
          onChange={onChange}
          value={textValue}
          error={!!errMsg}
          helperText={errMsg}
        />
        <IconButton onClick={onClear} size="large">
          <RemoveIcon color="secondary" fontSize="small" />
        </IconButton>
      </DialogContent>
      <DialogActions className={styles.closeDialogAction}>
        <ThemeProvider theme={buttonTheme}>
          <Button
            className={styles.inviteButton}
            variant="contained"
            color="primary"
            onClick={handleSendInvite}
            data-veritone-element="send-invite-email-button"
            disabled={!!errMsg || emails.length === 0}
          >
            INVITE
          </Button>
        </ThemeProvider>
      </DialogActions>
    </Fragment>
  );
};

export default InviteAppModalView;

export interface InviteAppModalViewPropTypes {
  readonly onSendInvite: (emails: ReadonlyArray<string>) => void;
  readonly onClose: () => void;
}

const VALID_EMAIL_REGEX = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/i;

const isValidEmail = (email: string) => VALID_EMAIL_REGEX.test(email);

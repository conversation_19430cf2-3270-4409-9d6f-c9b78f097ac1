import { search } from '@worker';
import * as actions from '../actions';
import { findLast, omit } from 'lodash';
import { defaultState, MainStore } from '../store';
import { produce, produceWithPatches, WritableDraft } from 'immer';
import {
  FILTER_PARAMETER_TYPE,
  FILTER_PARAMETER_TYPES,
} from '@helpers/constants';
import { frameAlignStartTimeMs, frameAlignStopTimeMs } from '@utils';
import { CaseReducer, createReducer, Draft } from '@reduxjs/toolkit';
import {
  ClusterItemGroup,
  ClusterSegment,
  UDRsPolyAssetGroupSeriesItem,
} from '../models';
import { removeSliceFromUdrGroupSeries } from '@worker/reducers/overlay/udrs';

type Re<P = unknown> = CaseReducer<
  typeof defaultState,
  { payload: P; type: string }
>;

const onSetGlobalSettings: Re<actions.SetGlobalSettingsRequest> = (
  state,
  action
) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      if (draft.tdo) {
        Object.assign(draft.tdo.details.settings, action.payload);
      }
    }
  );

  return produce(nextState, (draft) => {
    if (patches.length && inversePatches.length) {
      draft.history.past.push({ patches, inversePatches });
      draft.history.future = [];
    }
  });
};

const onOverlayPreview: Re<actions.SetOverlayPreviewRequest> = (
  state,
  action
) => ({
  ...state,
  ...action.payload,
});

const onSetSelectedGroups: Re<actions.SetSelectedGroupsRequest> = (
  state,
  action
) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      Object.assign(draft.selectedPolyGroups, action.payload.selected);
    }
  );

  return produce(nextState, (draft) => {
    draft.tdoIsChanged = true;
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const onSetMergeSelectClusterIds: Re<
  actions.SetMergeSelectClusterIdsRequest
> = (state, action) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      const { clusterIds, selected } = action.payload;

      const detectionGroupIds: string[] = [];
      const udrGroupIds: string[] = [];

      clusterIds.forEach((clusterId) => {
        const cluster = state.clusterMap[clusterId];
        if (cluster) {
          detectionGroupIds.push(...cluster.detectionGroupIds);
          udrGroupIds.push(...cluster.udrGroupIds);
        } else {
          // possible clusterId is a groupId
          const groupId = clusterId;

          // check if is a detection group
          if (state.detectionClusterGroups[groupId]) {
            detectionGroupIds.push(groupId);
          }

          // check if is a udr group
          if (state.udrClusterGroups[groupId]) {
            udrGroupIds.push(groupId);
          }
        }
      });

      onSetMergeSelectClusterIdsHelper(
        draft,
        clusterIds,
        selected,
        detectionGroupIds,
        udrGroupIds
      );
    }
  );

  return produce(nextState, (draft) => {
    draft.tdoIsChanged = true;
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const removeMergeSegments = ({
  draft,
  segmentIds,
  segments,
}: {
  draft: Draft<MainStore>;
  segments?: ClusterSegment[];
  segmentIds?: string[];
}) => {
  const ids: string[] = segmentIds || segments?.map((seg) => seg.id) || [];
  ids.forEach((id) => {
    delete draft.clusterMergeSegments[id];
  });
};

const onSetMergeGroupId: Re<actions.SetMergeGroupIdRequest> = (
  state,
  action
) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      const { groupId } = action.payload;
      const group =
        draft.detectionClusterGroups[groupId] ??
        draft.udrClusterGroups[groupId];

      if (!group) {
        return;
      }

      const clusterId = group.clusterId;
      const clusterMapItem = draft.clusterMap[clusterId];

      const detectionClusterGroups =
        clusterMapItem?.detectionGroupIds.map(
          (id) => draft.detectionClusterGroups[id]!
        ) || [];
      const udrClusterGroups =
        clusterMapItem?.udrGroupIds.map((id) => draft.udrClusterGroups[id]!) ||
        [];
      const clusterGroups = detectionClusterGroups.concat(udrClusterGroups);

      const isSelectedGroup = draft.clusterMergeGroupIds[groupId];

      if (!isSelectedGroup) {
        Object.assign(draft.clusterMergeGroupIds, { [groupId]: true });

        if (clusterGroups.length <= 1) {
          Object.assign(draft.clusterMergeGroupIds, { [clusterId]: true });
        } else {
          const groupSegmentIds = group.segments.map((seg) => seg.id);
          const segmentIds = clusterGroups.reduce<string[]>(
            (acc, { segments }) => [...acc, ...segments.map(({ id }) => id)],
            []
          );
          const allSegments = segmentIds?.every(
            (segmentId) =>
              groupSegmentIds.includes(segmentId) ||
              draft.clusterMergeSegments[segmentId]
          );
          if (allSegments) {
            Object.assign(draft.clusterMergeGroupIds, { [clusterId]: true });
            clusterGroups.forEach((group) => {
              draft.clusterMergeGroupIds[group.id] = true;
            });
            removeMergeSegments({ draft, segmentIds });
          } else {
            group.segments.forEach((segment) => {
              Object.assign(draft.clusterMergeSegments, {
                [segment.id]: segment,
              });
            });
          }
        }
      } else {
        if (clusterGroups.length <= 1) {
          delete draft.clusterMergeGroupIds[groupId];
          removeMergeSegments({ draft, segments: group.segments });
        } else {
          for (const { id, segments } of clusterGroups) {
            const isCurrentGroup = id === groupId;
            const isSelectCluster = draft.clusterMergeGroupIds[clusterId];

            if (isCurrentGroup && isSelectCluster) {
              segments.forEach((segment) => {
                const { id } = segment;
                Object.assign(draft.clusterMergeSegments, { [id]: segment });
              });
            } else if (
              (isCurrentGroup && !isSelectCluster) ||
              (!isCurrentGroup && isSelectCluster)
            ) {
              delete draft.clusterMergeGroupIds[id];
              removeMergeSegments({ draft, segments });
            }
          }
        }
        delete draft.clusterMergeGroupIds[group.clusterId];
      }
    }
  );

  return produce(nextState, (draft) => {
    draft.tdoIsChanged = true;
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

export const onSetMergeSelectClusterIdsHelper = (
  draft: Draft<MainStore>,
  clusterIds: string[],
  selected: boolean,
  detectionGroupIds: string[],
  udrGroupIds: string[]
) => {
  const clusterGroups: ClusterItemGroup[] = [];

  let group: ClusterItemGroup | undefined;

  detectionGroupIds.forEach((groupId) => {
    group = draft.detectionClusterGroups[groupId];
    if (group) {
      clusterGroups.push(group);
    }
  });

  udrGroupIds.forEach((groupId) => {
    group = draft.udrClusterGroups[groupId];
    if (group) {
      clusterGroups.push(group);
    }
  });

  if (selected) {
    /* remove individual selected segments if entire group is selected */
    clusterGroups.forEach((clusterGroup) =>
      clusterGroup.segments.forEach(
        (segment) => delete draft.clusterMergeSegments[segment.id]
      )
    );

    /* mark cluster and groups as selected */
    Object.assign(
      draft.clusterMergeGroupIds,
      Object.fromEntries(clusterIds.map((clusterId) => [clusterId, selected]))
    );
    clusterGroups.forEach((group) => {
      Object.assign(draft.clusterMergeGroupIds, { [group.id]: selected });
    });
  } else {
    /* Remove group/cluster */
    clusterIds.forEach(
      (clusterId) => delete draft.clusterMergeGroupIds[clusterId]
    );

    clusterGroups.forEach((group) => {
      delete draft.clusterMergeGroupIds[group.id];
    });
  }
};

const onSetClusterMergeSegments: Re<actions.SetClusterMergeSegmentsRequest> = (
  state,
  action
) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      const { id, groupId, segment } = action.payload;
      const group =
        draft.detectionClusterGroups[groupId] ??
        draft.udrClusterGroups[groupId];
      const clusterId = group?.clusterId;

      if (clusterId && draft.clusterMergeGroupIds[clusterId]) {
        /* Check if group/cluster is selected */
        const detectionClusterGroups = Object.values(
          draft.detectionClusterGroups
        ).filter((group) => group.clusterId === clusterId);
        const udrClusterGroups = Object.values(draft.udrClusterGroups).filter(
          (group) => group.clusterId === clusterId
        );
        const segments = [
          ...(detectionClusterGroups ?? []),
          ...(udrClusterGroups ?? []),
        ].reduce<ClusterSegment[]>(
          (acc, { segments }) => [
            ...acc,
            ...segments.map((segment) => segment),
          ],
          []
        );

        segments.forEach((segment) => {
          if (id !== segment.id) {
            Object.assign(draft.clusterMergeSegments, {
              [segment.id]: segment,
            });
          }
        });
        delete draft.clusterMergeGroupIds[clusterId];
        detectionClusterGroups.forEach((group) => {
          delete draft.clusterMergeGroupIds[group.id];
        });
        udrClusterGroups.forEach((group) => {
          delete draft.clusterMergeGroupIds[group.id];
        });
      } else if (clusterId && segment) {
        /* Check if all segments selected */
        const detectionClusterGroups = Object.values(
          draft.detectionClusterGroups
        ).filter((group) => group.clusterId === clusterId);
        const udrClusterGroups = Object.values(draft.udrClusterGroups).filter(
          (group) => group.clusterId === clusterId
        );
        const segmentIds = [
          ...(detectionClusterGroups ?? []),
          ...(udrClusterGroups ?? []),
        ].reduce<string[]>(
          (acc, { segments }) => [...acc, ...segments.map(({ id }) => id)],
          []
        );
        const allSegments = !!segmentIds?.every(
          (segmentId) =>
            id === segmentId || draft.clusterMergeSegments[segmentId]
        );

        if (allSegments) {
          Object.assign(draft.clusterMergeGroupIds, { [clusterId]: true });
          detectionClusterGroups.forEach((group) => {
            draft.clusterMergeGroupIds[group.id] = true;
          });
          udrClusterGroups.forEach((group) => {
            draft.clusterMergeGroupIds[group.id] = true;
          });
          segmentIds?.forEach((segmentId) => {
            delete draft.clusterMergeSegments[segmentId];
          });
        } else {
          Object.assign(draft.clusterMergeSegments, { [id]: segment });
        }
      } else if (segment) {
        /* Add segment */
        Object.assign(draft.clusterMergeSegments, { [id]: segment });
      } else {
        /* Remove segment */
        delete draft.clusterMergeSegments[id];
      }
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const onClearClusterMerge: Re = (state) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.clusterMergeGroupIds = {};
      draft.clusterMergeSegments = {};
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const onInitSetSelectedGroups: Re<actions.InitSetSelectedGroupsRequest> = (
  state,
  action
) => ({
  ...state,
  selectedPolyGroups: action.payload.selected,
});

const onSetExpandedGroups: Re<actions.SetExpandedGroupsRequest> = (
  state,
  action
) => ({
  ...state,
  expandedPolyGroups: {
    ...state.selectedPolyGroups,
    ...action.payload.expanded,
  },
});

const onSetSortBy: Re<actions.SetSortByRequest> = (state, action) => ({
  ...state,
  sortPolyGroupsBy: action.payload,
});

const onSetDisplayUnselectedOverlays: Re<boolean> = (state, action) => ({
  ...state,
  displayUnselectedOverlays: action.payload,
});

const onSetSelectedGroupsByClusterId: Re<string> = (state) => ({
  ...state,
  tdoIsChanged: true,
});

const onShowGlobalSettingsModal: Re<{
  readonly visible: boolean;
}> = (state, { payload }) => ({
  ...state,
  showGlobalSettingsModal: payload.visible,
});

function onChangeUdrsPolyAssetGroupSeriesItemHelper(
  state: WritableDraft<MainStore>,
  payload: {
    id: string;
    groupId: string;
    changeType: 'start' | 'end' | 'stick';
    seriesItem: UDRsPolyAssetGroupSeriesItem;
  },
  submit: boolean
) {
  let startTimeMs = payload.seriesItem.startTimeMs;
  let stopTimeMs = payload.seriesItem.stopTimeMs;

  const fps = state.tdo?.details.veritoneFile?.videoFrameRate;

  if (fps) {
    // aligned time
    const alignedStartTimeMs = frameAlignStartTimeMs(startTimeMs, fps);

    // align stopTimeMs to extend to include the frame that displays at the "raw" stopTimeMs
    let alignedStopTimeMs = frameAlignStartTimeMs(stopTimeMs, fps) + 1000 / fps; // go to start of frame then add frame duration
    alignedStopTimeMs = frameAlignStopTimeMs(alignedStopTimeMs, fps); // get the exact alignment
    alignedStopTimeMs = Math.ceil(alignedStopTimeMs - 1); // back off so displays as 1ms less the start time for next frame

    if (submit) {
      startTimeMs = alignedStartTimeMs;
      stopTimeMs = alignedStopTimeMs;
    } else {
      // if not ready to submit prevent alignment from moving in opposite direction of drag
      startTimeMs = Math.max(startTimeMs, alignedStartTimeMs);
      stopTimeMs = Math.min(stopTimeMs, alignedStopTimeMs);
    }
  }

  const { currentPosition, udrCollection } = state;

  const polys = udrCollection?.collection
    ? search(udrCollection.collection, currentPosition)
    : [];

  const collectionBoundingPoly = polys.find((poly) => poly.id === payload.id);

  const group = state.udrsState.localUDRAsset.boundingPolys[payload.groupId];
  const origGroup = state.udrsState.udrAsset.boundingPolys[payload.groupId];

  if (!group || !origGroup) {
    return state;
  }

  let udrsState = {
    ...state.udrsState,
    udrBoundingPolyBeingUpdated: !state.udrsState.udrBoundingPolyBeingUpdated
      ? collectionBoundingPoly
      : state.udrsState.udrBoundingPolyBeingUpdated,
    sprayPaintEdgeUdr:
      state.udrsState.sprayPaintEdgeUdr ||
      origGroup.series.find(({ id }) => id === payload.id),
  };

  const origEdgeSeriesItem = udrsState.sprayPaintEdgeUdr;

  let series;

  let highlightedOverlay = state.highlightedOverlay;

  if (
    payload.seriesItem.object.overlayObjectType === 'spray_paint_udr' &&
    origEdgeSeriesItem
  ) {
    const { changeType } = payload;

    const shouldExtendFirstUDR =
      changeType === 'start' && startTimeMs < origEdgeSeriesItem.startTimeMs;

    const shouldExtendLastUDR =
      changeType === 'end' && stopTimeMs > origEdgeSeriesItem.stopTimeMs;

    highlightedOverlay = {
      id: payload.id,
      timeMs: startTimeMs,
      groupId: payload.groupId,
      type: 'spray_paint_udr',
    };

    if (shouldExtendFirstUDR) {
      series = origGroup.series.map((s) => {
        if (s.id === origEdgeSeriesItem.id) {
          return { ...s, startTimeMs: startTimeMs };
        }
        return s;
      });
      // NOTE: when extending spray paint udr group series need to set udrBoundingPolyBeingUpdated,
      // so it stay rendered on the media player
      const localPolys = udrCollection?.collection
        ? search(udrCollection.collection, origEdgeSeriesItem.startTimeMs)
        : [];
      udrsState.udrBoundingPolyBeingUpdated = localPolys.find(
        (poly) => poly.id === origEdgeSeriesItem.id
      );

      highlightedOverlay = { ...highlightedOverlay, id: origEdgeSeriesItem.id };
    } else if (shouldExtendLastUDR) {
      series = origGroup.series.map((s) => {
        if (s.id === origEdgeSeriesItem.id) {
          return { ...s, stopTimeMs: stopTimeMs };
        }
        return s;
      });

      const localPolys = udrCollection?.collection
        ? search(udrCollection.collection, origEdgeSeriesItem.stopTimeMs)
        : [];
      udrsState.udrBoundingPolyBeingUpdated = localPolys.find(
        (poly) => poly.id === origEdgeSeriesItem.id
      );

      highlightedOverlay = { ...highlightedOverlay, id: origEdgeSeriesItem.id };
    } else {
      let startTimeSliceRemove;
      let endTimeSliceRemove;

      if (changeType === 'start') {
        startTimeSliceRemove = origEdgeSeriesItem.startTimeMs;
        endTimeSliceRemove = startTimeMs;
      } else {
        startTimeSliceRemove = stopTimeMs;
        endTimeSliceRemove = origEdgeSeriesItem.stopTimeMs;
      }

      series = removeSliceFromUdrGroupSeries(
        origGroup.series,
        startTimeSliceRemove,
        endTimeSliceRemove
      );

      // NOTE need to set highlightedOverlay id with the edge item, in case currently selected udr
      // got deleted
      let newEdgeItem;

      if (changeType === 'start') {
        newEdgeItem = series.find(
          ({ startTimeMs: itemStart, stopTimeMs: itemStop }) =>
            Math.ceil(startTimeMs) >= itemStart && startTimeMs <= itemStop
        );
      } else {
        newEdgeItem = findLast(
          series,
          ({ startTimeMs: itemStart, stopTimeMs: itemStop }) =>
            stopTimeMs >= itemStart && Math.floor(stopTimeMs) <= itemStop
        );
      }

      highlightedOverlay = newEdgeItem
        ? { ...highlightedOverlay, id: newEdgeItem.id }
        : highlightedOverlay;

      // NOTE: when shrinking spray paint udr group series we don't need to display overlays
      // that are not at the current position, we only need to do that if we extend spray paint udr group
      udrsState.udrBoundingPolyBeingUpdated = undefined;
    }
  } else {
    series = group.series.map((s) => {
      if (s.id === payload.id) {
        if (submit) {
          return { ...payload.seriesItem, startTimeMs, stopTimeMs };
        } else {
          return payload.seriesItem;
        }
      }
      return s;
    });
  }

  const newGroup = {
    ...group,
    series,
  };

  udrsState = {
    ...udrsState,
    ...(submit && {
      udrBoundingPolyBeingUpdated: undefined,
      sprayPaintEdgeUdr: undefined,
    }),
    localUDRAsset: {
      boundingPolys: {
        ...state.udrsState.localUDRAsset.boundingPolys,
        [group.groupId]: newGroup,
      },
    },
  };

  if (!highlightedOverlay) {
    return state;
  }

  return {
    ...state,
    udrsState,
    highlightedOverlay,
  };
}

const onChangeUdrsPolyAssetGroupSeriesItemSubmit: Re<{
  id: string;
  groupId: string;
  changeType: 'start' | 'end' | 'stick';
  seriesItem: UDRsPolyAssetGroupSeriesItem;
}> = (state, { payload }) =>
  onChangeUdrsPolyAssetGroupSeriesItemHelper(state, payload, true);

const onChangeUdrsPolyAssetGroupSeriesItem: Re<{
  id: string;
  groupId: string;
  seriesItem: UDRsPolyAssetGroupSeriesItem;
  changeType: 'start' | 'end' | 'stick';
}> = (state, { payload }) =>
  onChangeUdrsPolyAssetGroupSeriesItemHelper(state, payload, false);

const onUpdateStartEndLiveTrackingUdr: Re<{
  startPayload: {
    id: string;
    groupId: string;
    seriesItem: UDRsPolyAssetGroupSeriesItem;
  };
  endPayload: {
    id: string;
    groupId: string;
    seriesItem: UDRsPolyAssetGroupSeriesItem;
  };
}> = (state) => ({
  ...state,
});

const onUDRSelectedFromTimeline: Re<{
  id: string;
  groupId: string;
  seriesItem: UDRsPolyAssetGroupSeriesItem;
}> = (state, { payload }) => {
  const highlightedOverlay = {
    id: payload.id,
    timeMs: payload.seriesItem.startTimeMs,
    groupId: payload.groupId,
    type: 'udr',
  };

  let newState = {
    ...state,
    highlightedOverlay,
    currentPosition: payload.seriesItem.startTimeMs,
  };

  const selectedUDRGroupId = state.udrsState.selectedUDRGroupId;

  if (selectedUDRGroupId && selectedUDRGroupId !== payload.groupId) {
    newState = {
      ...newState,
      udrsState: {
        ...newState.udrsState,
        selectedUDRGroupId: undefined,
      },
    };
  }

  return newState;
};

const onSetRegularModePlaybackSpeed: Re<number> = (
  state,
  { payload: regularModePlaybackSpeed }
) => ({
  ...state,
  udrsState: {
    ...state.udrsState,
    regularModePlaybackSpeed,
  },
});

const onSetFilterToggleAll: Re<actions.FILTER_TOGGLE_ALL_REQUEST> = (
  state,
  { payload }
) => {
  const { filter, preventUndo } = payload;

  const updatedShow = { ...state.filterParameters.show };

  FILTER_PARAMETER_TYPES.forEach((type) => {
    // if (type === 'manual' && ) {
    //   updatedShow[type] =
    //     (state.tdo?.details?.settings?.manualInterpolationMax ??
    //       DEFAULT_GLOBAL_SETTINGS.manualInterpolationMax) > 0 && payload;
    // }
    updatedShow[type] = filter && !state.filterParameters.remove[type];
  });

  // workaround for when drawing udr when udrs are filtered so undo doesn't require two clicks
  if (preventUndo) {
    return {
      ...state,
      filterParameters: {
        show: updatedShow,
        remove: state.filterParameters.remove,
      },
    };
  }

  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      Object.assign(draft.filterParameters.show, updatedShow);
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

/** previous more complicated version
const onSetFilterToggleAll: Re<actions.FILTER_TOGGLE_ALL_REQUEST> = (
  state,
  { payload }
) => {
  const { filter, isUndo } = payload;
  const updatedShow = { ...state.filterParameters.show };

  FILTER_PARAMETER_TYPES.forEach((type) => {
    // if (type === 'manual' && ) {
    //   updatedShow[type] =
    //     (state.tdo?.details?.settings?.manualInterpolationMax ??
    //       DEFAULT_GLOBAL_SETTINGS.manualInterpolationMax) > 0 && payload;
    // }
    updatedShow[type] = filter && !state.filterParameters.remove[type];
  });

  return produce(state, (draft) => {
    Object.assign(draft.filterParameters.show, updatedShow);

    if (!isUndo) {
      draft.history.past.push({
        action: {
          creator: actions.SET_FILTER_TOGGLE_ALL,
          payload: { ...payload, isUndo: true },
        },
        inverseAction: {
          creator: actions.SET_FILTER_TOGGLE_ALL,
          payload: { filter: !filter, isUndo: true },
        },
      });
      draft.history.future = [];
    }
  });

};
*/

/**
const onSetShowFilterType: Re<actions.FILTER_TYPE_REQUEST> = (
  state,
  { payload }
) => {
  const { filterType, value, isUndo, isDropDown } = payload;

  const updatedShow = {
    ...state.filterParameters.show,
    [filterType]: value,
  };

  // set all other keys to false - used by cbsa since is a drop down
  // clean this up later
  if (isDropDown) {
    Object.keys(updatedShow).forEach((key) => {
      if (key !== filterType) {
        updatedShow[key as FILTER_PARAMETER_TYPE] = false;
      }
    });
  }

  return produce(state, (draft) => {
    Object.assign(draft.filterParameters.show, updatedShow);

    if (!isUndo) {
      draft.history.past.push({
        action: {
          creator: actions.SET_SHOW_FILTER_TYPE,
          payload: { ...payload, isUndo: true },
        },
        inverseAction: {
          creator: actions.SET_SHOW_FILTER_TYPE,
          payload: { filterType, value: !value, isUndo: true },
        },
      });
      draft.history.future = [];
    }
  });
};
*/

const onSetShowFilterType: Re<actions.FILTER_TYPE_REQUEST> = (
  state,
  { payload }
) => {
  const { filterType, value, isDropDown, preventUndo } = payload;

  const updatedShow = {
    ...state.filterParameters.show,
    [filterType]: value,
  };

  // set all other keys to false - used by cbsa since is a drop down
  // clean this up later
  if (isDropDown) {
    Object.keys(updatedShow).forEach((key) => {
      if (key !== filterType) {
        updatedShow[key as FILTER_PARAMETER_TYPE] = false;
      }
    });
  }

  // workaround for when drawing udr when udrs are filtered so undo doesn't require two clicks
  if (preventUndo) {
    return {
      ...state,
      filterParameters: {
        show: updatedShow,
        remove: state.filterParameters.remove,
      },
    };
  }

  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      Object.assign(draft.filterParameters.show, updatedShow);
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const onSetRemoveFilterType: Re<actions.FILTER_TYPE_REQUEST> = (
  state,
  { payload }
) => {
  const { filterType, value } = payload;
  const remove = {
    ...state.filterParameters.remove,
    [filterType]: value,
  };
  const show = {
    ...state.filterParameters.show,
    [filterType]: false,
  };

  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      Object.assign(draft.filterParameters, { remove, show });
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const onSetTrimInterval: Re<{
  startTimeMs: number;
  stopTimeMs: number;
}> = (state, { payload: trimInterval }) => ({
  ...state,
  tdo: state.tdo && {
    ...state.tdo,
    details: {
      ...state.tdo.details,
      redact: {
        ...state.tdo.details.redact,
        trimInterval,
      },
    },
  },
  tdoIsChanged: true,
});

const onRemoveTrimTimes: Re = (state) => ({
  ...state,
  tdo: state.tdo && {
    ...state.tdo,
    details: {
      ...state.tdo.details,
      redact: omit(state.tdo.details.redact, 'trimInterval'),
    },
  },
  tdoIsChanged: true,
});

const onUpdateTdoReadonly: Re<{ tdoReadonly: boolean; name?: string }> = (
  state,
  { payload }
) => ({
  ...state,
  tdoReadonly: {
    isLocked: payload.tdoReadonly,
    name: payload.name,
  },
});

const onSetPrevMarkerTime: Re<number> = (state, action) => ({
  ...state,
  prevMarkerTime: Math.max(action.payload, 0),
});

const onSetDraggedOverlayId: Re<string | null> = (state, action) => ({
  ...state,
  draggedOverlayId: action.payload,
});

const onSetIsSaveFailed: Re<boolean> = (state, action) => ({
  ...state,
  isSaveFailed: action.payload,
});

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(actions.SET_GLOBAL_SETTINGS, onSetGlobalSettings)
    .addCase(actions.SET_OVERLAY_PREVIEW, onOverlayPreview)
    .addCase(actions.SET_SELECTED_GROUPS, onSetSelectedGroups)
    .addCase(actions.SET_MERGE_SELECT_CLUSTER_IDS, onSetMergeSelectClusterIds)
    .addCase(actions.SET_MERGE_GROUP_ID, onSetMergeGroupId)
    .addCase(actions.SET_CLUSTER_MERGE_SEGMENTS, onSetClusterMergeSegments)
    .addCase(actions.CLEAR_CLUSTER_MERGE, onClearClusterMerge)
    .addCase(actions.INIT_SET_SELECTED_GROUPS, onInitSetSelectedGroups)
    .addCase(actions.SET_EXPANDED_GROUPS, onSetExpandedGroups)
    .addCase(actions.SET_SORT_BY, onSetSortBy)
    .addCase(
      actions.SET_DISPLAY_UNSELECTED_OVERLAYS,
      onSetDisplayUnselectedOverlays
    )
    .addCase(
      actions.SET_SELECTED_GROUPS_BY_GROUP_ID,
      onSetSelectedGroupsByClusterId
    )

    .addCase(actions.GLOBAL_SETTINGS_MODAL_SHOW_HIDE, onShowGlobalSettingsModal)
    .addCase(
      actions.CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM,
      onChangeUdrsPolyAssetGroupSeriesItem
    )
    .addCase(
      actions.CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM_SUBMIT,
      onChangeUdrsPolyAssetGroupSeriesItemSubmit
    )
    .addCase(
      actions.UPDATE_START_END_UDRS_LIVE_TRACKING,
      onUpdateStartEndLiveTrackingUdr
    )
    .addCase(actions.UDR_SELECTED_FROM_TIMELINE, onUDRSelectedFromTimeline)
    .addCase(
      actions.SET_REGULAR_MODE_PLAYBACK_SPEED,
      onSetRegularModePlaybackSpeed
    )
    .addCase(actions.SET_FILTER_TOGGLE_ALL, onSetFilterToggleAll)
    .addCase(actions.SET_SHOW_FILTER_TYPE, onSetShowFilterType)
    .addCase(actions.SET_REMOVE_FILTER_TYPE, onSetRemoveFilterType)

    .addCase(actions.SET_TRIM_INTERVAL, onSetTrimInterval)
    .addCase(actions.REMOVE_TRIM_INTERVAL, onRemoveTrimTimes)
    .addCase(actions.UPDATE_TDO_READONLY, onUpdateTdoReadonly)
    .addCase(actions.SET_PREV_MARKER_TIME, onSetPrevMarkerTime)
    .addCase(actions.SET_DRAGGED_OVERLAY_ID, onSetDraggedOverlayId)
    .addCase(actions.SET_IS_SAVE_FAILED, onSetIsSaveFailed);
});

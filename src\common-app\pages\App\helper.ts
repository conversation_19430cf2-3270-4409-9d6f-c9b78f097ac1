import { LOCALES } from '@i18n';

export enum ApplicationName {
  REDACT = 'redact',
  CBSA = 'cbsa',
}

export const isRedact = (appName?: string) =>
  appName === ApplicationName.REDACT;

export const getRedactLanguage = (language: string) =>
  ({
    [LOCALES.ENGLISH]: LOCALES.REDACT_ENGLISH,
    [LOCALES.FRENCH]: LOCALES.REDACT_FRENCH,
  })[language] || language;

export const getAppSpecificLanguage = (appName: string, language: string) =>
  isRedact(appName) ? getRedactLanguage(language) : language;

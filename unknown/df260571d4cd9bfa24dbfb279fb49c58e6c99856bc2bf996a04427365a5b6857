{"compilerOptions": {"module": "commonjs", "esModuleInterop": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "target": "es2023", "strict": true, "noUncheckedIndexedAccess": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "sourceMap": true, "outDir": "dist", "baseUrl": ".", "paths": {"*": ["node_modules/*"]}, "types": []}, "include": ["src/**/*"], "exclude": ["node_modules/*"], "ts-node": {"files": true}}
// import React from 'react';
// import moment from 'moment';
// import cloneDeep from 'lodash';
// import TdoWrapper from './index';
// import { IN_REDACTION_TAG_KEY } from '@common-modules/mediaDetails';
// import { Grid } from '@mui/material';
//
// describe('TdoWrapper', () => {
//   const modifiedDateTime = new Date();
//   const basicItem = {
//     id: 12345,
//     thumbnailUrl: 'https://dummyimage.com/600x400/000/fff',
//     primaryAsset: {
//       signedUri:
//         'https://www.sample-videos.com/video/mp4/720/big_buck_bunny_720p_1mb.mp4',
//     },
//     details: {
//       veritoneFile: {
//         filename: 'testFileName',
//       },
//       tags: [
//         {
//           value: IN_REDACTION_TAG_KEY,
//           redactionStatus: 'draft',
//         },
//       ],
//     },
//     modifiedDateTime,
//     assets: {
//       records: [],
//     },
//   };
//   const ingestedItem = cloneDeep(basicItem);
//   ingestedItem.details.govQARequestID = '1234567890';
//
//   it('should render tdo basic details on the card', () => {
//     const wrapper = shallow(<TdoWrapper item={basicItem} />);
//
//     const player = wrapper.find(`Player`);
//     expect(player).toHaveLength(1);
//     expect(player.props().poster === basicItem.thumbnailUrl);
//     expect(player.props().src === basicItem.primaryAsset.signedUri);
//
//     expect(wrapper.contains(`BigPlayButton`));
//
//     const nameWrapper = wrapper.find(`TdoNameWrapper`);
//     expect(nameWrapper).toHaveLength(1);
//     expect(nameWrapper.props().name === basicItem.name);
//
//     const mainGrid = wrapper.find(Grid).first();
//     expect(mainGrid.html()).toContain('draft');
//     expect(mainGrid.html()).toContain(
//       moment(basicItem.modifiedDateTime).format('ddd MMM DD, YYYY hh:mm A')
//     );
//   });
//
//   it('should render tdo request id when ingested', () => {
//     const wrapper = shallow(<TdoWrapper item={ingestedItem} />);
//
//     const player = wrapper.find(`Player`);
//     expect(player).toHaveLength(1);
//     expect(player.props().poster === basicItem.thumbnailUrl);
//     expect(player.props().src === basicItem.primaryAsset.signedUri);
//
//     expect(wrapper.contains(`BigPlayButton`));
//
//     const nameWrapper = wrapper.find(`TdoNameWrapper`);
//     expect(nameWrapper).toHaveLength(1);
//     expect(nameWrapper.props().name === basicItem.name);
//
//     const mainGrid = wrapper.find(Grid).first();
//     expect(mainGrid.html()).toContain('draft');
//     expect(mainGrid.html()).toContain(
//       moment(basicItem.modifiedDateTime).format('ddd MMM DD, YYYY hh:mm A')
//     );
//     expect(mainGrid.html()).toContain('Req ID: 1234567890');
//   });
// });

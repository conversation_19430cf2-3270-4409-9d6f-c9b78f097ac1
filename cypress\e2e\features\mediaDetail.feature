Feature: Verify Media Detail Page

  Background: Navigate to Media Detail page

  @e2e @mdp-general
  Scenario: Verify Media details page
    Given The user uploads file "lucy.mp4" with transcription "off"
    And The user is on "lucy.mp4" File Detail Screen
    Then The header should contain File name "lucy.mp4"
    And Status should be "Draft"
    And The header should contain Editor tab, Redacted Files tab
    And The header should contain Undo - Redo, Notifications, Keyboard shortcut, Setting, Help center, Close icons
    Then The body should contain Video play screen, Filter Detections, Timeline
    And The body should contain Video tab, Audio tab, Comments tab
    Then The footer should contain Preview object in video, Show Unselected toggle
    And The footer should contain Last saved: time, Save, Redact file button, Down arrow button
    When The user clicks on Notifications icon
    Then The notifications panel is opened
    When The user clicks on Keyboard shortcut icon
    Then The keyboard shortcuts panel is opened
    When The user clicks on Setting icon
    Then The settings panel is opened
        # When The user clicks on Help center icon
        # Then The help center panel is opened
    When The user clicks on Close icon
    Then The Media Details Page is closed and return to the Landing page

  @e2e @mdp-general
  <PERSON><PERSON>rio: Verify user can change file name
    Given The user uploads file "upload_test.mp4" with transcription "off"
    And The user is on "upload_test.mp4" File Detail Screen
    When The user clicks on the file name and changes the file name to "Changed File Name"
    And The user clicks on accept icon
    Then The header should contain File name "Changed File Name"

  @e2e @mdp-general
  Scenario: Verify user can cancel the name change
    Given The user is on "fileId_lucy.mp4" File Detail Screen
    When The user clicks on the file name and changes the file name to "Changed File Name"
    And The user clicks on deny icon
    Then The header should contain File name "lucy.mp4"

  @e2e @mdp-general
  Scenario: Verify user can change the file status
    Given The user is on "fileId_lucy.mp4" File Detail Screen
    Then Status should be "Draft"
    When The user clicks on the status dropdown
    Then Show a dropdown list with statuses
      | statusName     |
      | Draft          |
      | Pending Review |
      | Complete       |
    When The user selects "Pending Review" status
    Then Status should be "Pending Review"
    When The user clicks on the status dropdown
    When The user selects "Complete" status
    Then Status should be "Complete"
    When The user clicks on the status dropdown
    When The user selects "Draft" status
    Then Status should be "Draft"

  @e2e @mdp-general
  Scenario: Verify user can run "Detect heads and objects" in editor tab
    Given The user is on "fileId_lucy.mp4" File Detail Screen
    When The user is on Video Tab
    Then Video tab have message "Automatically detect and place bounding boxes on objects within this video, making redaction faster and easier for you."
    When The user clicks on Detect Objects button
    And The user selects Head and Objects engine
    Then Video tab have the new message

  @e2e @mdp-general
  Scenario: Verify user cannot run "Detect heads and objects" engine again in editor tab
    Given The user is on "fileId_lucy.mp4" File Detail Screen
    When The user is on Video Tab
    Then The tab has list of objects detected
    And The button is disabled and cannot make action

  @e2e @mdp-general
  Scenario: Verify user can close the Media Details Page in Editor tab
    Then The user is on "lucy.mp4" File Detail Screen
    When The user clicks on Close icon
    Then The Media Details Page is closed and return to the Landing page

  @e2e @mdp-general
  Scenario: Verify user can change Redaction effects for object in right panel
    When The user uploads file "lucy.mp4" with transcription "head, person"
    Then The user is on "lucy.mp4" File Detail Screen
    When The user clicks on Redaction effects in right panel
    Then Redaction effects pop-up is opened
    And Fill default is blur, Blur level default is 10, Restore default, Cancel and Save button
    When The user clicks on Object type
    Then Display a dropdown list containing: "UDR, Head, Person"
    When The user clicks fill redaction
    Then Display a dropdown list containing: "Blur, Blackfill, Outline"
    When The user clicks on Object type
    And The user selects "Head"
    When The user clicks fill redaction
    And The user selects "Blackfill"
    And The user clicks Save button
    And The user clicks on Redaction effects in right panel
    Then New redaction effect "Blackfill" is added to the "Head" object type
    When The user clicks on Object type
    And The user selects "Person"
    When The user clicks fill redaction
    And The user selects "Outline"
    And The user clicks Save button
    And The user clicks on Redaction effects in right panel
    Then New redaction effect "Outline" is added to the "Person" object type
    When The user clicks on Object type
    And The user selects "UDR"
    When The user clicks fill redaction
    And The user selects "Blur"
    And The user clicks Save button
    And The user clicks on Redaction effects in right panel
    Then New redaction effect "Blur" is added to the "UDR" object type

  @e2e @mdp-general
  Scenario: Verify user can't edit blur level number >10 in right panel
    Then The user is on "lucy.mp4" File Detail Screen
    When The user clicks on Redaction effects in right panel
    And The user fills blur level with number "15"
    Then The user can't fill in number > 10

  @e2e @mdp-general
  Scenario: Verify user can Restore default of redaction effect for object in right panel
    Then The user is on "lucy.mp4" File Detail Screen
    When The user selects checkbox for UDR 1 in the right panel
    And The user clicks on Redaction effects in right panel
    When The user clicks on Object type
    And The user selects "Head"
    When The user clicks fill redaction
    And The user selects "Blackfill"
    And The user clicks on Restore default button
    When The user clicks on Redaction effects in right panel
    Then Fill default is blur, Blur level default is 10, Restore default, Cancel and Save button
    When The user clicks on Object type
    And The user selects "Person"
    When The user clicks fill redaction
    And The user selects "Outline"
    And The user clicks on Restore default button
    When The user clicks on Redaction effects in right panel
    Then Fill default is blur, Blur level default is 10, Restore default, Cancel and Save button

  @e2e @mdp-general
  Scenario: Verify user can Add Redaction code for object
    Then The user is on "lucy.mp4" File Detail Screen
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Add Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page" from dropdown list
    And The user presses the "Add" button in "Add Code" popup
    Then The new redaction code "3000" is added to the selected object

  @e2e @mdp-general
  Scenario: Verify user can change redaction code number for object
    Then The user is on "lucy.mp4" File Detail Screen
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Add Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page" from dropdown list
    And The user presses the "Add" button in "Add Code" popup
    Then The new redaction code "3000" is added to the selected object
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Edit Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page Edit" from dropdown list
    And The user presses the "Save" button in "Edit Code" popup
    Then The new redaction code "8000" is added to the selected object

  @e2e @mdp-general
  Scenario: Verify user can change redaction code color for object
    Then The user is on "lucy.mp4" File Detail Screen
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Add Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page" from dropdown list
    And The user presses the "Add" button in "Add Code" popup
    Then The new redaction code "3000" is added to the selected object
    And The new code color "3000" is "rgb(255, 255, 255)"
    When The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Edit Redaction Code" in UDR menu
    And The user selects color "rgb(253, 216, 53)"
    And The user presses the "Save" button in "Edit Code" popup
    Then The new redaction code "3000" is added to the selected object
    And The new code color "3000" is "rgb(253, 216, 53)"

  @e2e @mdp-general
  Scenario: Verify user can restore default color of redaction code
    Then The user is on "lucy.mp4" File Detail Screen
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Add Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page" from dropdown list
    And The user presses the "Add" button in "Add Code" popup
    Then The new redaction code "3000" is added to the selected object
    When The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Edit Redaction Code" in UDR menu
    And The user selects color "rgb(253, 216, 53)"
    And The user presses the "Save" button in "Edit Code" popup
    When The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Edit Redaction Code" in UDR menu
    And The user presses the "Restore Default Color" button in "Edit Code" popup
    And The user presses the "Save" button in "Edit Code" popup
    Then The new redaction code "3000" is added to the selected object
    And The new code color "3000" is "rgb(255, 255, 255)"

  @e2e @mdp-general
  Scenario: Verify user can remove redaction code for object
    Then The user is on "lucy.mp4" File Detail Screen
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Add Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page" from dropdown list
    And The user presses the "Add" button in "Add Code" popup
    Then The new redaction code "3000" is added to the selected object
    When The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Remove Redaction Code" in UDR menu
    Then Open a pop-up "You are about to delete the redaction code. Are you sure?"
    When The user clicks on Confirm button with text "Delete"
    Then Code "3000" is deleted

  @e2e @mdp-general
  Scenario: Verify user can filter detections in right panel
    Then The user is on "lucy.mp4" File Detail Screen
    When The user clicks on Filter Detections in right panel
    Then Display a pop-up with have list of all the detections that are checked
    And Have Remove From Output column, default: unselect
    When The user unchecked UDR detection
    Then The "UDR" detection is removed from the timeline
    And The "UDR" detection is unchecked in the right panel
    When The user clicks on Filter Detections in right panel
    When The user unchecked Head detection
    Then The "Head" detection is removed from the timeline
    And The "Head" detection is unchecked in the right panel
    When The user clicks on Filter Detections in right panel
    And The user unchecked Head detection
    When The user unchecked Person detection
    Then The "Person" detection is removed from the timeline
    And The "Person" detection is unchecked in the right panel

  @e2e @mdp-general
  Scenario: Verify user can remove detection from output
    Then The user is on "lucy.mp4" File Detail Screen
    When The user clicks on Filter Detections in right panel
    When The user remove UDR detection
    Then Display "Removing From Output" pop-up- Content: "Please confirm: redacted UDRs are NOT to be included in the final video."
    When The user clicks on Confirm button with text "Remove"
    Then The "UDR" detection is removed from the timeline
    And The "UDR" detection is unchecked in the right panel
    When The user remove Head detection
    Then Display "Removing From Output" pop-up- Content: "Please confirm: redacted Heads are NOT to be included in the final video."
    When The user clicks on Confirm button with text "Remove"
    Then The "Head" detection is removed from the timeline
    And The "Head" detection is unchecked in the right panel
    When The user remove Person detection
    Then Display "Removing From Output" pop-up- Content: "Please confirm: redacted Persons are NOT to be included in the final video."
    When The user clicks on Confirm button with text "Remove"
    Then The "Person" detection is removed from the timeline
    And The "Person" detection is unchecked in the right panel

  @e2e @mdp-general
  Scenario: Verify user can resize person group
    Then The user is on "lucy.mp4" File Detail Screen
    When The user selects checkbox for UDR 1 in the right panel
    And The user resizes the bounding
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Resize Person Segment" in UDR menu
    Then The object during that period is resized

  @e2e @mdp-general
  Scenario: Verify user can delete person in Frame
    Then The user is on "lucy.mp4" File Detail Screen
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Delete Person in Frame" in UDR menu
    Then The deleted person is not displayed in media file

  @e2e @mdp-general
  Scenario: Verify user can delete person group
    Then The user is on "lucy.mp4" File Detail Screen
    When Store the number of "person" detection to "totalMatchingDetectionName"
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Delete Person Group" in UDR menu
    Then Selected person group is deleted
    When The user clicks on Close icon
    Then The deleted person is not displayed in media file

  @e2e @mdp-general
  Scenario: Verify user can delete person Overlay
    Then The user is on "lucy.mp4" File Detail Screen
    When Store the number of "person" detection to "totalMatchingDetectionName"
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Delete Person Overlay" in UDR menu
    Then The deleted person is not displayed in media file

  @e2e @mdp-general
  Scenario: Verify user can delete person Segment
    Then The user is on "lucy.mp4" File Detail Screen
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Delete Person Segment" in UDR menu
    Then The deleted person is not displayed in media file

  @e2e @mdp-general
  Scenario: Verify Warning modal when user adds/edits redaction code/redaction effect for merged group
    When The user uploads file "lucy.mp4" with transcription "head, person"
    Then The user is on "lucy.mp4" File Detail Screen
    And The user plays the video to 1 seconds
    When The user created a merged group
    Then A merged group is created
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Add Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page" from dropdown list
    And The user presses the "Add" button in "Add Code" popup
    Then Show warning modal: Header: "Add Redaction Code" Body:- "Warning! Group is assigned to a Merged Group!" - "You are about to add/edit a redaction code. Apply change to"
    And Button: Cancel in white container, Group,  Merged Group in color "rgb(0, 92, 126)"
    When The user clicks on "Group" button
    Then The UDR 1 contains Redaction Code "3000" and text color "rgb(255, 255, 255)"
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Edit Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page Edit" from dropdown list
    And The user presses the "Save" button in "Edit Code" popup
    Then Show warning modal: Header: "Edit Redaction Code" Body:- "Warning! Group is assigned to a Merged Group!" - "You are about to add/edit a redaction code. Apply change to"
    And Button: Cancel in white container, Group,  Merged Group in color "rgb(0, 92, 126)"
    When The user clicks on "Group" button
    Then The UDR 1 contains Redaction Code "8000" and text color "rgb(255, 255, 255)"
    When The user clicks on Close icon
    And The user cancel the changes
    Then The user is on "lucy.mp4" File Detail Screen
    When The user created a merged group
    Then A merged group is created
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Add Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page" from dropdown list
    And The user presses the "Add" button in "Add Code" popup
    Then Show warning modal: Header: "Add Redaction Code" Body:- "Warning! Group is assigned to a Merged Group!" - "You are about to add/edit a redaction code. Apply change to"
    And Button: Cancel in white container, Group,  Merged Group in color "rgb(0, 92, 126)"
    When The user clicks on "Merged Group" button
    Then The UDR 1 contains Redaction Code "3000" and text color "rgb(255, 255, 255)"
    When The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Edit Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page Edit" from dropdown list
    And The user presses the "Save" button in "Edit Code" popup
    Then Show warning modal: Header: "Edit Redaction Code" Body:- "Warning! Group is assigned to a Merged Group!" - "You are about to add/edit a redaction code. Apply change to"
    And Button: Cancel in white container, Group,  Merged Group in color "rgb(0, 92, 126)"
    When The user clicks on "Merged Group" button
    Then The UDR 1 contains Redaction Code "8000" and text color "rgb(255, 255, 255)"

  @e2e @mdp-general
  Scenario: Verify Warning modal when user removes redaction code/effect of merged group
    Then The user is on "lucy.mp4" File Detail Screen
    When The user created a merged group
    Then A merged group is created
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Add Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page" from dropdown list
    And The user presses the "Add" button in "Add Code" popup
    Then Show warning modal: Header: "Add Redaction Code" Body:- "Warning! Group is assigned to a Merged Group!" - "You are about to add/edit a redaction code. Apply change to"
    And Button: Cancel in white container, Group,  Merged Group in color "rgb(0, 92, 126)"
    When The user clicks on "Merged Group" button
    Then The UDR 1 contains Redaction Code "3000" and text color "rgb(255, 255, 255)"
    When The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Remove Redaction Code" in UDR menu
    Then Show warning modal: Header: "Remove Redaction Code" Body:- "Warning! Group is assigned to a Merged Group!" - "You are about to delete the redaction code. Apply change to"
    And Button: Cancel in white container, Group,  Merged Group in color "rgb(158, 0, 0)"
    When The user clicks on "Merged Group" button
    Then The UDR 1 should not contain Redaction Code "3000"
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Add Redaction Code" in UDR menu
    And The user selects code name "Redaction Code For Media Detail Page" from dropdown list
    And The user presses the "Add" button in "Add Code" popup
    Then Show warning modal: Header: "Add Redaction Code" Body:- "Warning! Group is assigned to a Merged Group!" - "You are about to add/edit a redaction code. Apply change to"
    And Button: Cancel in white container, Group,  Merged Group in color "rgb(0, 92, 126)"
    When The user clicks on "Merged Group" button
    Then The UDR 2 contains Redaction Code "3000" and text color "rgb(255, 255, 255)"
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Remove Redaction Code" in UDR menu
    Then Show warning modal: Header: "Remove Redaction Code" Body:- "Warning! Group is assigned to a Merged Group!" - "You are about to delete the redaction code. Apply change to"
    And Button: Cancel in white container, Group,  Merged Group in color "rgb(158, 0, 0)"
    When The user clicks on "Group" button
    Then The UDR 2 should not contain Redaction Code "3000"
    And The UDR 1 contains Redaction Code "3000" and text color "rgb(255, 255, 255)"

  @e2e @mdp-general
  Scenario: Verify Warning modal when user changes shape of a segment of merged group
    Then The user is on "lucy.mp4" File Detail Screen
    When The user created a merged group
    Then A merged group is created
    When The user selects checkbox for UDR 1 in the right panel
    When The user clicks on Shape menu on UDR 1
    Then Show warning modal: Header: "Change Shape" Body: - "Warning! Group is assigned to a Merged Group!" - "Shape Type"- 3 radio buttons: Ellipse, Rectangle, Object Type Default Shape - "You are about edit the shape type. Apply change to", Button: Cancel in white container, Group, Merged Group in light blue container
    When The user selects "ellipse" shape
    And The user clicks on "Merged Group" button
    Then The shape of the UDR 1 is "ellipse"
    When The user clicks on Shape menu on UDR 1
    When The user selects "rectangle" shape
    And The user clicks on "Merged Group" button
    Then The shape of the UDR 1 is "rectangle"

  @e2e @mdp-general
  Scenario: Verify Warning modal when user deletes a segment of merged group
    Then The user is on "lucy.mp4" File Detail Screen
    When The user created a merged group
    Then A merged group is created
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "person"
    And The user clicks on "Delete Person Group" in UDR menu
    Then Show warning modal: Header: "Delete Group" Body:- "Warning! Group is assigned to a Merged Group!" - "You are about to delete the group. Apply change to"
    And Button: Cancel in white container, Group, Merged Group in red container
    When The user clicks on "Merged Group" button
    Then The merged group is deleted
    When The user created a 2nd merged group
    When The user selects checkbox for UDR 1 in the right panel
    And The user opens UDR menu for UDR 1 with type "head"
    And The user clicks on "Delete Head Group" in UDR menu
    When The user clicks on "Merged Group" button
    Then The merged group is deleted

  @e2e @mdp-general
  Scenario: Delete multiple test files
    Given The user deletes files
      | lucy.mp4        |
      | fileId_lucy.mp4 |
      | upload_test.mp4 |

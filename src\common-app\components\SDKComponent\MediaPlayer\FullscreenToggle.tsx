import cn from 'classnames';

const FullscreenToggle = ({ className, onClick, isFullscreen }: Props) => (
  <button
    className={cn(
      className,
      {
        'video-react-icon-fullscreen-exit': isFullscreen,
        'video-react-icon-fullscreen': !isFullscreen,
      },
      'video-react-fullscreen-control video-react-control video-react-button video-react-icon'
    )}
    type="button"
    tabIndex={0}
    onClick={onClick}
  >
    <span className="video-react-control-text">Non-Fullscreen</span>
  </button>
);

FullscreenToggle.displayName = 'FullscreenToggle';

interface Props {
  className?: string;
  onClick: () => void;
  isFullscreen: boolean;
}

export default FullscreenToggle;

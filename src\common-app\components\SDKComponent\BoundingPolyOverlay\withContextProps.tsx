import { Consumer, FC } from 'react';
import { OverlayPosition, OverlayPropType } from './OverlayTypes';

const withContextProps =
  (
    ContextConsumer: Consumer<{
      top: number;
      left: number;
      height: number;
      width: number;
    }>,
    mapContextToProps: (context: OverlayPosition) => {
      overlayPositioningContext: OverlayPosition;
    }
  ) =>
  (Component: FC<OverlayPropType>) => {
    const ComponentWithContextProps = (props: OverlayPropType) => (
      <ContextConsumer>
        {(context: OverlayPosition) => (
          <Component {...props} {...mapContextToProps(context)} />
        )}
      </ContextConsumer>
    );
    ComponentWithContextProps.displayName = Component.name;
    return ComponentWithContextProps;
  };

export default withContextProps;

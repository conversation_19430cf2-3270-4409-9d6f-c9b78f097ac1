import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { fetchCaseFileListQuery } from '../../src/api/queries';
import { caseFileList } from '../../src/controllers/caseFileList';
import { CaseId } from '../../src/model/brands';

const limit = 30;
const offset = 0;

describe('caseFileList', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('retrieve the list of files w/ caseId', async () => {
    const params = { caseId: '123456789' as CaseId };
    const req = getMockReq({
      params,
      query: {
        limit: limit.toString(),
        offset: offset.toString(),
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.resolve({ case: { files: { records: [] } } }));

    await caseFileList(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, fetchCaseFileListQuery(params.caseId, limit, offset));
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('fails to retrieve the list of files w/o caseId', async () => {
    const req = getMockReq({
      params: { caseId: '' },
      query: {
        limit: limit.toString(),
        offset: offset.toString(),
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await caseFileList(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.caseIdRequired });
  });

  it('fails to retrieve the list of files w/ bad limit', async () => {
    const req = getMockReq({
      params: { caseId: '123456789' },
      query: {
        limit: 'bad limit',
        offset: offset.toString(),
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await caseFileList(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.limitNotNumber });
  });

  it('fails to retrieve the list of files w/ large limit', async () => {
    const req = getMockReq({
      params: { caseId: '123456789' },
      query: {
        limit: '10000',
        offset: offset.toString(),
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await caseFileList(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.maxLimitReached });
  });

  it('fails to retrieve the list of files w/ bad offset', async () => {
    const req = getMockReq({
      params: { caseId: '123456789' },
      query: {
        limit: limit.toString(),
        offset: 'bad offset',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await caseFileList(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.offsetNotNumber });
  });

  it('fails to retrieve the list of files w/ error', async () => {
    const params = { caseId: '123456789' as CaseId };
    const req = getMockReq({
      params,
      query: {
        limit: limit.toString(),
        offset: offset.toString(),
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.reject());

    await caseFileList(req, res);
    expect(error).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, fetchCaseFileListQuery(params.caseId, limit, offset));
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
  });
});

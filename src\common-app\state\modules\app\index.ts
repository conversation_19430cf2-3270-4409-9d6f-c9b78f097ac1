import { createAction, createReducer } from '@reduxjs/toolkit';
import { createSelector } from 'reselect';
import {
  FETCH_APPLICATION_CONFIGS_SUCCESS,
  SHOW_OR_HIDE_APPBAR_MENU,
} from './actions';
import { isPlainObject, isString } from 'lodash';

export const BOOT = createAction<Record<string, any>>(
  'boot saga: sequence all the stuff needed to start the app'
);
export const BOOT_FINISHED = createAction('boot saga finished');

export interface AppBarState {
  isBooting: boolean;
  bootDidFinish: boolean;
  isAppBarMenuOpen: boolean;
  applicationConfigs?: {
    configType: string;
    configKey: string;
    value: string | null;
    valueJSON: Record<string, unknown> | null;
  }[];
}

const defaultState: AppBarState = {
  isBooting: false,
  bootDidFinish: false,
  isAppBarMenuOpen: false,
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(BOOT, (state) => ({
      ...state,
      isBooting: true,
      bootDidFinish: false,
    }))
    .addCase(BOOT_FINISHED, (state) => ({
      ...state,
      isBooting: false,
      bootDidFinish: true,
    }))
    .addCase(SHOW_OR_HIDE_APPBAR_MENU, (state) => ({
      ...state,
      isAppBarMenuOpen: !state.isAppBarMenuOpen,
    }))
    .addCase(FETCH_APPLICATION_CONFIGS_SUCCESS, (state, action) => ({
      ...state,
      applicationConfigs: action.payload.applicationConfig.records,
    }));
});

export default reducer;
export const namespace = 'app';

export const boot = (options: Record<string, any> = {}) => BOOT(options);

export const bootFinished = () => BOOT_FINISHED();

const local = (state: any) => state[namespace] as AppBarState;

export const isBooting = (state: any) => local(state)?.isBooting;
export const bootDidFinish = (state: any) => local(state)?.bootDidFinish;
export const selectIsAppBarMenuOpen = (state: any) =>
  local(state)?.isAppBarMenuOpen;
export const selectRawApplicationConfigs = (state: any) =>
  local(state)?.applicationConfigs;
export const selectApplicationConfigs = createSelector(
  selectRawApplicationConfigs,
  (rac) =>
    rac?.reduce<Record<string, unknown>>((acc, config) => {
      const value = appConfigValue(config);
      if (!value) {
        return acc;
      }
      return {
        ...acc,
        [config.configKey]: value,
      };
    }, {}) || {}
);

export function appConfigValue(
  config: NonNullable<ReturnType<typeof selectRawApplicationConfigs>>[0]
) {
  if (config.configType === 'JSON') {
    // Work around SDK incorrect usage of value rather than valueJSON for JSON config keys
    return getBrokenJSONAppConfigValue(config);
  } else {
    return config.value;
  }
}

function getBrokenJSONAppConfigValue(
  appConfig: NonNullable<ReturnType<typeof selectRawApplicationConfigs>>[0]
) {
  if (!appConfig?.valueJSON && !appConfig?.value) {
    return undefined;
  }
  // Default to valueJSON if it exists, otherwise try to parse value as JSON to handle platform UI deficiency
  if (appConfig.valueJSON) {
    return appConfig.valueJSON;
  } else if (isString(appConfig.value)) {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const value = JSON.parse(appConfig.value);
      if (isPlainObject(value)) {
        // Relatively safe cast...
        return value as Record<string, any>;
      } else {
        throw new Error('Invalid JSON structure in app config');
      }
    } catch (_e) {
      console.error('Invalid JSON string in app config:', appConfig.value);
    }
  }
}

import { all, fork, take } from 'typed-redux-saga/macro';

import { KILL_ONBOARDING } from '../actions';
import { initSagas } from './init';
import { invitesSagas } from './invites';
import { organizationSagas } from './organization';
import { trackingUserInfoSagas } from './trackingUserInfo';
import { upgradeSagas } from './upgrade';

export function* userOnboardingSagas() {
  const isInit = yield* initSagas();
  if (isInit) {
    const allEffects = yield* all([
      fork(upgradeSagas),
      fork(invitesSagas),
      fork(organizationSagas),
      fork(trackingUserInfoSagas),
    ]);
    yield* take(KILL_ONBOARDING);
    allEffects.forEach((e) => e.cancel());
  }
}

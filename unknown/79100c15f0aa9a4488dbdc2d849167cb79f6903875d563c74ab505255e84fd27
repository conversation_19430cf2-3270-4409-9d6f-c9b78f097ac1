import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';
import Select, { SelectProps } from '@mui/material/Select';
import { Controller } from 'react-hook-form';
import MenuItem from '@mui/material/MenuItem';

const SelectInput = ({
  controlName,
  control,
  label,
  options,
  ...custom
}: SelectInputProps) => (
  <Controller<{ [controlName]: string }>
    // TODO: Fix the type of control
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    control={control}
    name={controlName}
    rules={{ required: 'Required' }}
    render={({ field, fieldState }) => (
      <FormControl fullWidth>
        {label && (
          <InputLabel shrink error={fieldState.isTouched && !!fieldState.error}>
            {label}
          </InputLabel>
        )}
        <Select
          error={fieldState.isTouched && !!fieldState.error}
          {...field}
          {...custom}
          value={field.value}
        >
          {options.map((option) => (
            <MenuItem key={option} value={option}>
              {option}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    )}
  />
);

type SelectInputProps = SelectProps & {
  control: any;
  controlName: string;
  setValue: any;
  trigger: any;
  readonly label?: string;
  readonly options: Array<string>;
};

export default SelectInput;

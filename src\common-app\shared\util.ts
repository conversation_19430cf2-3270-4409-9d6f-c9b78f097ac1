import { BlurLevel } from '@common/state/modules/mediaDetails/models';
import { clamp, isString } from 'lodash';
import { NonEmptyArray } from 'ts-essentials';

// URI Parser from https://stackoverflow.com/a/39308026
export const ParseURI = (url: string) => {
  const m = url.match(
    /^(([^:\/?#]+:)?(?:\/\/(([^\/?#:]*)(?::([^\/?#:]*))?)))?([^?#]*)(\?[^#]*)?(#.*)?$/
  );
  if (!m) {
    return null;
  }

  const r = {
    hash: m[8] || '', // #asd
    host: m[3] || '', // localhost:257
    hostname: m[4] || '', // localhost
    href: m[0] || '', // http://localhost:257/deploy/?asd=asd#asd
    origin: m[1] || '', // http://localhost:257
    pathname: m[6] || (m[1] ? '/' : ''), // /deploy/
    port: m[5] || '', // 257
    protocol: m[2] || '', // http:
    search: m[7] || '', // ?asd=asd
  };

  if (r.protocol.length === 2) {
    r.protocol = 'file:///' + r.protocol.toUpperCase();
    r.origin = r.protocol + '//' + r.host;
  }
  r.href = r.origin + r.pathname + r.search + r.hash;
  return r;
};

// export type GUID = string & { isGuid: true };
export type GUID = string;
function asGuid(guid: string): GUID {
  return guid;
}

// http://stackoverflow.com/questions/105034/create-guid-uuid-in-javascript

export function guid() {
  function s4() {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  }

  return asGuid(`${s4()}-${s4()}-${s4()}`);
}

export interface ExtendedError extends Error {
  errors: any[];
}

export const extendedError = (msg: string, errors: any[]) => {
  const err = new Error(msg) as ExtendedError;
  err.errors = errors;
  return err;
};

export const notUndefined = <T>(item: T): item is NonNullable<T> => !!item;

export function numberToBlurLevel(level: number): BlurLevel;
export function numberToBlurLevel(level: undefined): undefined;
export function numberToBlurLevel(
  level: number | undefined
): BlurLevel | undefined;
export function numberToBlurLevel(level: number | undefined) {
  if (!level) {
    return undefined;
  }

  return clamp(Math.round(level), 1, 10) as BlurLevel;
}

export function loopedNext<T>(arr: T[], index: number) {
  if (!arrayHasContents(arr)) {
    throw new Error('Array is empty');
  }

  if (index === arr.length - 1) {
    return arr[0];
  }
  // Casting is safe due to previous checks - ??
  return arr[index + 1] as T;
}

type Tuple<T, N extends number> = N extends N
  ? number extends N
    ? T[]
    : _TupleOf<T, N, []>
  : never;
type _TupleOf<T, N extends number, R extends unknown[]> = R['length'] extends N
  ? R
  : _TupleOf<T, N, [T, ...R]>;

export function arrayHasContents<T>(
  arr: readonly T[] | undefined
): arr is NonEmptyArray<T> {
  return !!arr && arr.length > 0;
}

export function arrayHasLength<T, LENGTH extends number>(
  arr: readonly T[] | undefined,
  i: LENGTH
): arr is Tuple<T, LENGTH> {
  return !!arr && arr.length === i;
}

export function isObjectWithProperty<T extends string>(
  obj: unknown,
  prop: T
): obj is { [key in T]: unknown } {
  return typeof obj === 'object' && obj !== null && prop in obj;
}

export function isObjectWithStringProperty<T extends string>(
  obj: unknown,
  prop: T
): obj is { [key in T]: string } {
  return isObjectWithProperty(obj, prop) && isString(obj[prop]);
}

const ELASTIC_SEARCH_SPECIAL_CHARS = '$[](){}+-~*&:';

export const escapeSpecialElasticSearchChars = (strToEscape: string) => {
  const regex = new RegExp(
    `[${ELASTIC_SEARCH_SPECIAL_CHARS.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`,
    'g'
  );
  return strToEscape.replace(regex, (match) => `\\${match}`);
};

import {
  ShapeType,
  BoundingPoly,
  BoundingPolyR<PERSON>t,
  UDRsBasePoly,
  BlurLevel,
  RedactionConfig,
  OverlayObjectType,
} from '@common-modules/mediaDetails/models';
import { DETECTION_OBJECT_TYPE } from '@helpers/constants';
import { IndividualRedactionCode } from '@redact/state/modules/redactionCodes/models';

export interface BlurEngineBoundingPolyObject {
  readonly overlayObjectType: OverlayObjectType; // engine probably doesn't really need this - is used as a backup to type
  readonly boundingPoly: BoundingPolyRect;
  readonly isDeleted: boolean;
  readonly type: DETECTION_OBJECT_TYPE;
}

// Interface for sending detection redaction off to blur engine
export interface BlurEngineDetectionRedactionItem {
  readonly id?: string;
  readonly object: BlurEngineBoundingPolyObject;
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
  // Optional Custom Effects
  readonly shapeType?: ShapeType;
  readonly blurLevel?: BlurLevel;
  readonly blackFill?: boolean;
  readonly outline?: boolean;
  // remove codeTxt, codeColor, codeLocation once blur engine is updated to accept code object below
  readonly codeTxt?: string;
  readonly codeLocation?: string;
  readonly codeColor?: string;
  readonly code?: {
    readonly text: string;
    readonly color: string;
    readonly location: string;
  };
}
export interface BlurEngineUdrBoundingPolys {
  readonly [groupId: string]: null | BlurEngineUDRsPolyAsset;
}

export interface BlurEngineUDRsPolyAssetGroupSeriesItem {
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
  readonly object: Pick<
    BoundingPoly['object'],
    'overlayObjectType' | 'boundingPoly'
  >;
}

export interface BlurEngineUDRsPolyAsset {
  readonly basePoly?: UDRsBasePoly; // don't need this - but required to make decimalPrecisionSavingBoundingPoly typing work
  readonly series: ReadonlyArray<BlurEngineUDRsPolyAssetGroupSeriesItem>;
  readonly redactionCode?: IndividualRedactionCode;
  // Optional Custom Effects
  readonly shapeType?: ShapeType;
  readonly redactionConfig?: RedactionConfig;
  readonly codeTxt?: string;
  readonly codeLocation?: string;
  readonly codeColor?: string;
}

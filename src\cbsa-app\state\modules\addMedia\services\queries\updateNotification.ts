import { AIWareNotification } from '../../AddMediaStore';

export interface UPDATE_NOTIFICATION_QUERY_RESPONSE {
  createStructuredData: AIWareNotification;
}
export const UPDATE_NOTIFICATION_QUERY = `
  mutation updateNotification($id: ID!, $schemaId: ID!, $data: JSONData!) {
    createStructuredData(input: {
      id: $id,
      schemaId: $schemaId,
      data: $data,
    }) {
      id
      data
    }
  }`;

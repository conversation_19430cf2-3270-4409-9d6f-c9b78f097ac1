import { CLOSE_SEND_EMAIL_INVITE, OPEN_SEND_EMAIL_INVITE } from '../actions';
import { OnboardingState } from '../models';
import { defaultState } from '../store';
import { CaseReducer, createReducer } from '@reduxjs/toolkit';

type Re<P = unknown> = CaseReducer<
  OnboardingState,
  { payload: P; type: string }
>;

const onActionOpenInviteApp: Re<void> = (state) => ({
  ...state,
  isInviteAppOpen: true,
});

const onActionCloseInviteApp: Re<void> = (state) => ({
  ...state,
  isInviteAppOpen: false,
});

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(OPEN_SEND_EMAIL_INVITE, onActionOpenInviteApp)
    .addCase(CLOSE_SEND_EMAIL_INVITE, onActionCloseInviteApp);
});

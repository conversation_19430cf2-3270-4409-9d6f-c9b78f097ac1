import 'moment/locale/fr';
import moment from 'moment';
import { create } from 'jss';
import {
  LOCALSTORAGE_APP_NAME_KEY,
  LOCALSTORAGE_LANGUAGE_KEY,
} from '@common/i18n/createIntl';
import '@fontsource/roboto/latin.css';
import { I18nProvider, LOCALES } from '@i18n';
import RootRouter from '@common-pages/RootRouter';
import { StylesProvider, jssPreset } from '@mui/styles';
import { useState, useEffect, useCallback, StrictMode } from 'react';
import ScopedCssBaseline from '@mui/material/ScopedCssBaseline';
import { ApplicationName, getAppSpecificLanguage } from './helper';
import createGenerateClassName from '@mui/styles/createGenerateClassName';

/* Class names will prefix this seed to avoid name collisions with AIWARE.js */
const generateClassName = createGenerateClassName({
  seed: 'redact-jss',
});

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const jss = create({
  ...jssPreset(),
  insertionPoint: 'jss-insertion-point',
});

const getLoadLanguage = () =>
  window.localStorage.getItem(LOCALSTORAGE_LANGUAGE_KEY);

const defaultLocale = LOCALES.ENGLISH;

const App = ({ appName }: { appName?: string }) => {
  const [language, setLang] = useState(
    getAppSpecificLanguage(
      appName || '',
      getLoadLanguage() || window.navigator.language
    )
  );

  const updateLanguage = useCallback(
    (newLang: string) => {
      const [_, locale] = Object.entries({
        en: LOCALES.ENGLISH,
        fr: LOCALES.FRENCH,
      }).find(([key]) => newLang.includes(key)) || [undefined, defaultLocale];
      const appLocale = getAppSpecificLanguage(appName || '', locale);

      setLang(appLocale);
      moment.locale(locale);
      window.localStorage.setItem(LOCALSTORAGE_LANGUAGE_KEY, appLocale);
    },
    [appName]
  );

  useEffect(() => {
    if (appName) {
      window.localStorage.setItem(LOCALSTORAGE_APP_NAME_KEY, appName);
    }

    updateLanguage(language);

    /* Handle aiWARE preferredLanguage */
    const aiWARE = async () => {
      while (
        // TODO: Is there a better way to get the preferred language?
        !window.aiware ||
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        !window.aiware?.store.getState().auth?.user?.preferredLanguage
      ) {
        await delay(500);
      }

      window.aiware.on(
        'languageChange',
        function (
          error?: any,
          data?: {
            preferredLanguage: 'en' | 'fr';
          }
        ) {
          if (!error && data?.preferredLanguage) {
            updateLanguage(data.preferredLanguage);
          }
        }
      );
    };

    /* Run async function */
    // TODO: Do we really need to call `then` here?
    // eslint-disable-next-line promise/valid-params
    void aiWARE().then();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <StrictMode>
      <StylesProvider jss={jss} generateClassName={generateClassName}>
        <I18nProvider language={language}>
          <ScopedCssBaseline />
          <RootRouter appName={ApplicationName.REDACT} />
        </I18nProvider>
      </StylesProvider>
    </StrictMode>
  );
};

export default App;

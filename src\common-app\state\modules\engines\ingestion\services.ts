import { JobStatus, modules } from '@veritone/glc-redux';

import { selectConfigEngines } from '../selectors';
import callGraphQLApi from '@helpers/callGraph<PERSON>Api';
import {
  NOOP,
  QUERY_ENGINE_RESULTS_SUCCESS,
  QUERY_ENGINE_RESULTS_FAILURE,
  CANCEL_JOB_FAILURE,
  CANCEL_JOB_SUCCESS,
  CREATE_ENGINE_JOB_SUCCESS,
  CREATE_ENGINE_JOB_FAILURE,
  CHECK_JOB_STATUS_FAILURE,
  CHECK_JOB_STATUS_SUCCESS,
} from './actions';
import { TDOId } from '@common-modules/universal/models/Brands';
import { selectIngestionOptions } from './selectors';
import { selectGlobalSettings } from '@common-modules/mediaDetails';
import { selectFeatureFlags } from '@common/user-permissions';
import { ClusterSimThresholdType } from '../../mediaDetails/models';
import { generateTasksAndRoutes } from '@helpers/engineJobHelper';
import { UnknownAction } from 'redux-saga';

const { user } = modules;

/**
 * Load engine results from the JSON file.
 * @param {{ signedUri: string }} payload
 */
// export const getEngineResultsFromURIService = ({ signedUri }) => async (
//   dispatch,
//   getState
// ) => {
//   return await callApi({
//     signedUri,
//     actionTypes: [
//       NOOP,
//       GET_ENGINE_RESULTS_FROM_URI_SUCCESS,
//       GET_ENGINE_RESULTS_FROM_URI_FAILURE,
//     ],
//     dispatch,
//     getState,
//     bailout: undefined,
//   });
// };

/**
 * Query for the engine results to get the JSON data URI.
 * @param {{ tdoId: TDOId }} payload
 */
// export const queryEngineResultsRecordService = ({ tdoId }) => async (
//   dispatch,
//   getState
// ) => {
//   const query = `
//     query($id: ID!) {
//       temporalDataObject(id: $id) {
//         assets(type: "redacted-transcription-result" orderBy: createdDateTime limit: 1) {
//           records {
//             signedUri
//           }
//         }
//       }
//     }
//   `;

//   return await callGraphQLApi({
//     actionTypes: [
//       NOOP,
//       QUERY_ENGINE_RESULTS_RECORD_SUCCESS,
//       QUERY_ENGINE_RESULTS_RECORD_FAILURE,
//     ],
//     query,
//     variables: { id: tdoId },
//     dispatch,
//     getState,
//   });
// };

export interface QueryEngineResultsServiceResponse {
  engineResults: {
    records: {
      tdoId: TDOId;
      engineId: string;
      startOffsetMs: number;
      stopOffsetMs: number;
      jsondata: any;
    }[];
  };
}
export const queryEngineResultsService =
  ({
    tdoId,
    engines,
    mediaDuration,
  }: {
    tdoId: TDOId;
    engines: string[];
    mediaDuration: number;
  }) =>
  async (dispatch: (a: UnknownAction) => void, getState: () => any) => {
    const query = `
      query($tdoId: ID!, $engines: [ID!], $startOffset: Int, $stopOffset: Int) {
        engineResults(tdoId: $tdoId, engineIds: $engines, startOffsetMs: 0, stopOffsetMs: $stopOffset) {
          records {
            tdoId
            engineId
            startOffsetMs
            stopOffsetMs
            jsondata
          }
        }
      }
    `;
    return await callGraphQLApi<QueryEngineResultsServiceResponse>({
      actionTypes: [
        NOOP,
        QUERY_ENGINE_RESULTS_SUCCESS,
        QUERY_ENGINE_RESULTS_FAILURE,
      ],
      query,
      variables: { tdoId, engines, stopOffset: mediaDuration },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

/**
 * Create an ingestion job.
 * @param {{ engineId: string; payload: { targetId: string; url: string } }} payload
 * {app: "redact"} Used for email notifications flow in Automate to filter tasks only from REDACT app. DONOT REMOVE.
 */
export const createEngineJobService =
  ({
    tdoId,
    tdoName,
    payload,
  }: {
    tdoId: TDOId;
    tdoName: string;
    payload: {
      // TODO: Type the acceptable fields for payload which are sent to the ingestion engine
      // targetId: string;
      url: string;
      urlHeaders?: string;
      faceDetectionThreshold?: number;
      legacyClustering?: boolean;
      clusterSimThreshold?: ClusterSimThresholdType;
      fileType?: string;
    };
  }) =>
  async (dispatch: (a: UnknownAction) => void, getState: () => any) => {
    // TODO: can we properly type getState?
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const state = getState();
    const {
      glcIngestionEngineId,
      fastChunkerEngineId,
      transcriptionEngineId,
      transcriptionEngineOptions,
      detectionEngineId,
      defaultClusterId,
      outputWriterEngineId,
      voiceMaskEngineId,
    } = selectConfigEngines(state);

    // const { email, organization } = user.selectUser(state);
    const { email } = user.selectUser(state);
    const {
      runHead,
      runPerson,
      runTranscription,
    }: {
      runHead: boolean;
      runPerson: boolean;
      runTranscription: boolean;
    } = selectIngestionOptions(state);
    const {
      detectionRate,
      faceDetectionThreshold,
      videoType,
    }: {
      detectionRate: number;
      faceDetectionThreshold: number;
      videoType: string;
    } = selectGlobalSettings(state);
    const featureFlags = selectFeatureFlags(state);

    const ingestionPayload = {
      ...payload,
      user: email,
      // chunkSize: 10000000, deprecated!
      zeropadOffsetAudio: true,
    };

    const runDetection = payload.fileType?.includes('audio')
      ? false
      : runHead || runPerson;

    const [tasks, routes] = generateTasksAndRoutes({
      ingestionPayload,
      glcIngestionEngineId,
      outputWriterEngineId,
      fastChunkerEngineId,
      transcriptionEngineId,
      transcriptionEngineOptions,
      runTranscription,
      runDetection,
      voiceMaskEngineId:
        voiceMaskEngineId || '8e610957-c97d-425f-a2ab-7fc667d2e93a',
      voiceChange: featureFlags.voiceChange,
      ...(runDetection && {
        detectionOptions: {
          engineId: detectionEngineId,
          confidenceThreshold: Number(
            (faceDetectionThreshold / 100).toFixed(2)
          ),
          videoType,
          detectionRate,
          detectHead: runHead,
          detectNotepad: featureFlags.detectNotepads,
          detectCard: featureFlags.detectCards,
          detectPerson: runPerson,
          legacyClustering: payload.legacyClustering,
          clusterSimThreshold: payload.clusterSimThreshold,
        },
      }),
    });

    const input = {
      targetId: tdoId,
      clusterId: defaultClusterId || '',
      tasks,
      routes,
    };

    const query = `mutation($input: CreateJob!) {
      createJob(input: $input) {
        id
        targetId
        status
      }
    }`;

    return await callGraphQLApi<{
      createJob: {
        id: string;
        targetId: TDOId;
        status: string;
      };
    }>({
      actionTypes: [NOOP, CREATE_ENGINE_JOB_SUCCESS, CREATE_ENGINE_JOB_FAILURE],
      query,
      variables: { id: tdoId, tdoName, input },
      dispatch,
      getState,
    });
  };

/**
 * TODO THIS NEEDS TO BE UPDATED TO USE NEW ENGINES!
 * IDEALLY REUSE createEngineJobService and pass in trim params
 * Create an ingestion job.
 * @param {{ tdoId: TDOId; payload: { targetId: string; url: string }, runHead: boolean, runTranscription: boolean }} payload
 * {app: "redact"} Used for email notifications flow in Automate to filter tasks only from REDACT app. DONOT REMOVE.
 */
export const createEngineJobForTrimTDO =
  ({
    tdoId,
    payload,
    runHead,
    runTranscription,
  }: {
    tdoId: TDOId;
    payload: {
      targetId: string;
      url: string;
    };
    runHead: boolean;
    runTranscription: boolean;
  }) =>
  async (dispatch: (a: UnknownAction) => void, getState: () => any) => {
    console.log(
      ' tdoId, payload, runHead, runTranscription',
      tdoId,
      payload,
      runHead,
      runTranscription
    );
    // TODO: can we properly type getState?
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const state = getState();
    const {
      webstreamAdapterEngineId: engineId,
      transcriptionEngineId,
      transcriptionEngineOptions,
      detectionEngineId,
      streamIngestorEngineId,
      streamIngestorPlaybackSegmentCreatorId,
      defaultClusterId,
    } = selectConfigEngines(state);
    const email = user.selectUser(state).email;
    // const { runHead, runTranscription } = selectIngestionOptions(state);
    const { detectionRate, faceDetectionThreshold, videoType } =
      selectGlobalSettings(state);
    const query = trimJobV3f({
      defaultClusterId,
      streamIngestorPlaybackSegmentCreatorId,
      streamIngestorEngineId,
      detectionEngineId,
      transcriptionEngineId,
      transcriptionEngineOptions,
      faceDetectionThreshold,
      videoType,
      detectionRate,
      runHead,
      runTranscription,
    });

    return await callGraphQLApi<{
      createJob: {
        id: string;
        targetId: TDOId;
        status: string;
      };
    }>({
      actionTypes: [NOOP, CREATE_ENGINE_JOB_SUCCESS, CREATE_ENGINE_JOB_FAILURE],
      query,
      variables: {
        id: tdoId,
        engineId,
        payload: {
          ...payload,
          user: email,
        },
      },
      dispatch,
      getState,
    });
  };

const trimJobV3f = ({
  defaultClusterId,
  streamIngestorPlaybackSegmentCreatorId,
  streamIngestorEngineId,
  detectionEngineId,
  transcriptionEngineId,
  transcriptionEngineOptions,
  faceDetectionThreshold,
  videoType,
  detectionRate,
  runHead,
  runTranscription,
}: {
  defaultClusterId?: string;
  streamIngestorPlaybackSegmentCreatorId: string | undefined;
  streamIngestorEngineId: string | undefined;
  detectionEngineId: string;
  transcriptionEngineId: string;
  transcriptionEngineOptions: Record<string, unknown>;
  faceDetectionThreshold: number;
  videoType: string;
  detectionRate: number;
  runHead: boolean;
  runTranscription: boolean;
}) => `
    mutation ($id: ID!, $engineId: ID!, $payload: JSONData!){
    createJob(input: {
        targetId: $id,
        clusterId: "${defaultClusterId ?? ''}",
        tasks: [
          {
            engineId: $engineId,
            payload: $payload
          },
          {
            engineId: "${streamIngestorPlaybackSegmentCreatorId}" # playback
          },
          ${
            runHead
              ? `{
                  engineId: "${streamIngestorEngineId}",
                  payload: {
                    ffmpegTemplate: "video",
                    comment: "stream ingestor"
                    app: "redact"
                    chunkOverlap: 3
                    customFFMPEGProperties:{
                      chunkSizeInSeconds: "60"
                      outputChunkDuration: "1m"
                      chunkOverlapDuration: "3s"
                      chunkOverlap: "3s"
                      extractFramesPerSec: "1"
                    }
                  }
                },
                {
                  engineId: "${detectionEngineId}",
                  payload: {
                    confidenceThreshold: ${(
                      faceDetectionThreshold / 100
                    ).toFixed(2)},
                    videoType: "${videoType}",
                    stepSizeDetection: ${detectionRate},
                    app: "redact"
                  },
                },`
              : ''
          }
          ${
            runHead
              ? `{
                  engineId: "${detectionEngineId}",
                  payload: {
                    confidenceThreshold: ${(
                      faceDetectionThreshold / 100
                    ).toFixed(2)},
                    videoType: "${videoType}",
                    stepSizeDetection: ${detectionRate},
                    app: "redact"
                  },
                }`
              : ''
          }
          ${
            runTranscription
              ? `
              {
                engineId: "${streamIngestorEngineId}",
                payload: {
                  ffmpegTemplate: "audio",
                  comment: "stream ingestor"
                  app: "redact"
                  customFFMPEGProperties:{
                    outputChunkDuration: "2m"
                    chunkSizeInSeconds: "120"
                  }
                }
              },{
                engineId: "${transcriptionEngineId}",
                payload: ${JSON.stringify(Object.assign({}, transcriptionEngineOptions, { diarise: 'false', app: 'redact' }))}
              }`
              : ''
          }
        ]
      }) {
        id
        targetId
        status
      }
    }
  `;

/**
 * Cancel a running job.
 * @param {{ jobId: string }} action
 */
export interface CancelJobServiceResponse {
  cancelJob: {
    id: string;
    message: string;
  };
}
export const cancelJobService =
  ({ jobId }: { jobId: string }) =>
  async (dispatch: (a: UnknownAction) => void, getState: () => any) => {
    const query = `
      mutation cancelJob($id: ID!) {
        cancelJob(id: $id){
          id
          message
        }
      }
    `;

    return await callGraphQLApi({
      actionTypes: [NOOP, CANCEL_JOB_SUCCESS, CANCEL_JOB_FAILURE],
      query,
      variables: { id: jobId },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

/**
 * Get the status of a job.
 * @param {{ jobId: string }} action
 */
export interface CheckJobStatusServiceResponse {
  job: {
    id: string;
    targetId: string;
    status: JobStatus;
  };
}
export const checkJobStatusService =
  ({ jobId }: { jobId: string }) =>
  async (dispatch: (a: UnknownAction) => void, getState: () => any) => {
    const query = `query($jobId: ID!) {
      job(id: $jobId) {
        id
        targetId
        status
      }
    }`;

    return await callGraphQLApi<CheckJobStatusServiceResponse>({
      actionTypes: [NOOP, CHECK_JOB_STATUS_SUCCESS, CHECK_JOB_STATUS_FAILURE],
      query,
      variables: { jobId },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

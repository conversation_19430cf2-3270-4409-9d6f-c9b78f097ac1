import { useStyles } from './styles';
import { ClickAwayListener, ThemeProvider } from '@mui/material';
import { useIntl } from 'react-intl';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const DetailsContent = ({ isOpen, onClose, onView }: Props) => {
  const intl = useIntl();
  const classes = useStyles();

  return isOpen ? (
    <ClickAwayListener onClickAway={onClose}>
      <div className={classes.details}>
        <div
          className={'Option'}
          onClick={() => {
            onView();
            onClose();
          }}
        >
          {intl.formatMessage({ id: 'view' })}
        </div>
      </div>
    </ClickAwayListener>
  ) : (
    <></>
  );
};

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onView: () => void;
}

const Details = ({ isOpen, onClose, onView }: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <DetailsContent isOpen={isOpen} onClose={onClose} onView={onView} />
  </ThemeProvider>
);

export default Details;

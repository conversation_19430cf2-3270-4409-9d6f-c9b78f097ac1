import { Case, CaseStatus } from '@cbsa-modules/universal';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraph<PERSON>Api';
import { ChangeCaseStatusQueryResponse } from '../services/queries/changeCaseStatus';
import { createAction } from '@reduxjs/toolkit';

export const CHANGE_CASE_STATUS = createAction<{
  caseDetails: Case;
  caseStatus: CaseStatus;
}>('CBSA/addMedia/APPROVE_CASE');
export const CHANGE_CASE_STATUS_SUCCESS = createGraphQLSuccessAction<
  ChangeCaseStatusQueryResponse,
  Record<string, any>,
  true
>('CBSA/AddMedia/APPROVE_CASE_SUCCESS');
export const CHANGE_CASE_STATUS_FAILURE = createGraphQLFailureAction(
  'CBSA/AddMedia/APPROVE_CASE_FAILURE'
);

export const changeCaseStatus = ({
  caseDetails,
  caseStatus,
}: {
  caseDetails: Case;
  caseStatus: CaseStatus;
}) => CHANGE_CASE_STATUS({ caseDetails, caseStatus });

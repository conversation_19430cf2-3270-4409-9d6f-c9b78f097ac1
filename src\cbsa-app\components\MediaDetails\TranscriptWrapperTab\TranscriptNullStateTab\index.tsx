import { ConnectedProps, connect } from 'react-redux';

import { processEngineRequestAction } from '@common-modules/engines/transcription';
import {
  selectTdo,
  selectDisableTranscriptionButton,
  LocalState,
} from '@common-modules/mediaDetails';

import { TranscriptNullStateTabPropTypes } from './TranscriptNullStateTabPropTypes';
import TranscriptNullStateTabView from './TranscriptNullStateTabView';

const DetectNullStateTab = ({
  tdo,
  buttonTranscriptionDisable,
  onStartJob,
  videoLoaded,
}: TranscriptNullStateTabPropTypes) => {
  const handleOnStartJob = () => {
    if (!buttonTranscriptionDisable && tdo?.primaryAsset) {
      onStartJob({
        tdoId: tdo.id,
        tdoName: tdo.name,
        targetId: tdo.id,
        url: tdo.primaryAsset.signedUri,
      });
    }
  };

  return (
    <TranscriptNullStateTabView
      onStartJob={handleOnStartJob}
      isDisable={buttonTranscriptionDisable}
      videoLoaded={videoLoaded}
    />
  );
};

const mapState = (state: LocalState) => ({
  tdo: selectTdo(state),
  buttonTranscriptionDisable: selectDisableTranscriptionButton(state),
});
const mapDispatch = {
  onStartJob: processEngineRequestAction,
};
const connector = connect(mapState, mapDispatch);
export type TranscriptNullStateTabPropsFromRedux = ConnectedProps<
  typeof connector
>;

export default connector(DetectNullStateTab);

import { useIntl } from 'react-intl';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { I18nTranslate } from '@common/i18n';
import TextField from '@mui/material/TextField';
import ConfirmDialog from '@common-components/ConfirmDialog';
import { putNewCase } from '@cbsa-modules/mainPage/actions';
import { NewCaseDialogProps } from '@cbsa-modules/mainPage/MainPageStore';

const NewCaseDialog = (props: NewCaseDialogProps) => {
  const intl = useIntl();
  const dispatch = useDispatch();
  const [newCaseName, setNewCaseName] = useState('');

  const { showNewCaseDialog, setShowNewCaseDialog } = props;

  const canSubmit = newCaseName.trim().length > 0;

  return (
    <ConfirmDialog
      data-testid="confirm-dialog"
      isOpen={showNewCaseDialog}
      title={I18nTranslate.TranslateMessage('newCase')}
      content={
        <TextField
          variant="outlined"
          value={newCaseName}
          style={{ width: '40vw' }}
          onChange={(event) => {
            setNewCaseName(event.target.value);
          }}
          error={!canSubmit}
          label={
            canSubmit
              ? I18nTranslate.TranslateMessage('caseName')
              : I18nTranslate.TranslateMessage('caseNameRequired')
          }
        />
      }
      confirmText={intl.formatMessage({ id: 'submit' })}
      onConfirm={() => {
        // This emits an event for a saga listener
        dispatch(putNewCase(newCaseName));

        setNewCaseName('');
        setShowNewCaseDialog(false);
      }}
      disableConfirmButton={!canSubmit}
      cancelText={intl.formatMessage({ id: 'cancel' })}
      onCancel={() => {
        setNewCaseName('');
        setShowNewCaseDialog(false);
      }}
    />
  );
};

export default NewCaseDialog;

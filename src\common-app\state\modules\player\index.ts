import { UnknownAction, createAction } from '@reduxjs/toolkit';
import { playerReducer } from 'video-react';

interface PlayerState {
  currentSrc: string | null;
  duration: number;
  currentTime: number;
  seekingTime: number;
  buffered: null;
  waiting: boolean;
  seeking: boolean;
  paused: boolean;
  autoPaused: boolean;
  ended: boolean;
  playbackRate: number;
  muted: boolean;
  volume: number;
  readyState: number;
  networkState: number;
  videoWidth: number;
  videoHeight: number;
  hasStarted: boolean;
  userActivity: boolean;
  isActive: boolean;
  isFullscreen: boolean;
  activeTextTrack: null;
  sessionCurrentTime: number;
  isFullLoad: boolean;
  textTracks: undefined;
}

export const namespace = 'player';
interface LocalState {
  [namespace]: PlayerState;
}
export const RESET_PLAYER_STORE = createAction('player/RESET_PLAYER_STORE');
export const UPDATE_SESSION_CURRENT_TIME = createAction<{ time: number }>(
  'player/UPDATE_SESSION_CURRENT_TIME'
);
export const FULL_LOAD_PLAYER = createAction<boolean>(
  'player/FULL_LOAD_PLAYER'
);
export const SET_HAS_STARTED = createAction<boolean>('player/SET_HAS_STARTED');
export const RESET_TEXT_TRACKS = createAction('player/RESET_TEXT_TRACKS');

export const actionResetPlayerStore = () => RESET_PLAYER_STORE();
export const actionUpdateSessionCurrentTime = (time: number) =>
  UPDATE_SESSION_CURRENT_TIME({ time });
export const actionFullLoadPlayer = (isFullLoad: boolean) =>
  FULL_LOAD_PLAYER(isFullLoad);
export const actionSetHasStartedPlayer = (isStarted: boolean) =>
  SET_HAS_STARTED(isStarted);
export const selectPlayerState = (state: LocalState) => state[namespace];
export const selectSessionCurrentTime = (state: LocalState) =>
  state[namespace].sessionCurrentTime;
export const selectPlayer = (state: LocalState) => state[namespace];
export const selectFullLoadPlayer = (state: LocalState) =>
  state[namespace].isFullLoad;
export const playerDefaultState: PlayerState = {
  currentSrc: null,
  duration: 0,
  currentTime: 0,
  seekingTime: 0,
  buffered: null,
  waiting: false,
  seeking: false,
  paused: true,
  autoPaused: false,
  ended: false,
  playbackRate: 1,
  muted: false,
  volume: 1,
  readyState: 0,
  networkState: 0,
  videoWidth: 0,
  videoHeight: 0,
  hasStarted: false,
  userActivity: true,
  isActive: false,
  isFullscreen: false,
  activeTextTrack: null,
  sessionCurrentTime: 0,
  isFullLoad: false,
  textTracks: undefined,
};

export const extendedPlayerReducer = (
  state: typeof playerDefaultState,
  action: UnknownAction
) => {
  // TODO: Fix types
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const existingState = playerReducer(state, action);

  switch (true) {
    case RESET_PLAYER_STORE.match(action):
      return { ...playerDefaultState };
    case UPDATE_SESSION_CURRENT_TIME.match(action): {
      const { time } = action.payload;
      // TODO: fix types
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return { ...existingState, sessionCurrentTime: time };
    }
    case FULL_LOAD_PLAYER.match(action):
      // TODO: Fix types
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return {
        ...existingState,
        isFullLoad: action.payload,
      };
    case SET_HAS_STARTED.match(action):
      // TODO: Fix types
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return {
        ...existingState,
        hasStarted: action.payload,
      };

    case RESET_TEXT_TRACKS.match(action):
      // TODO: Fix types
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return {
        ...existingState,
        textTracks: undefined,
      };
    default:
      // TODO: Fix type
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      return existingState;
  }
};

# Steps To Enable Object Level Permission for Org.

1. Create a new Org using Admin app.
2. Enable Admin and Redact Applications.
3. Turn on "Enable Object Level Permission" option in "SYSTEM SETTINGS" tab and click Save.
4. Select the newly created Org and Click "Users".
5. Add user with admin access and provide "Default App Access" for Redact.
6. To enable folder view for the org refer (https://github.com/veritone/redact-app/blob/master/docs/EnableFolderView.md)
7. Login to the staging environment using the admin credentials.
8. Click the "Utilities" icon from App Bar.
9. Click the "Admin Center" icon from the utilities side panel.
10. Click "Users" to invite new users to this org.
11. Click "Groups" to create new group and assign members using users created in step 7.
12. Click "Permission Sets" to create new permission sets and assign required permissions.
13. Click "System Access" to assign the permissions to the group.



import moment from 'moment';
import { useState } from 'react';
import * as styles from './styles.scss';
import Done from '@mui/icons-material/Done';
import Tooltip from '@mui/material/Tooltip';
import Clear from '@mui/icons-material/Clear';
import cutIcons from '@resources/images/cut_icon.svg';
import { useDispatch, useSelector } from 'react-redux';
import { getApiTrimTemplate } from '@helpers/apiStreamer';
import TrimDialog from '../TrimConfirmationDialog/TrimDialog';
import TrimAndProcessDialog from '../TrimConfirmationDialog/TrimAndProcessDialog';
import {
  toggleTrimTool,
  isShowTrimTool,
  createTDOForTrimVideo,
} from '@common-modules/trim';
import {
  selectClusterList,
  selectDetectionRunning,
  selectAudioTabState,
  selectTdo,
} from '@common-modules/mediaDetails';
import { RootState } from '@redact/bootstrap';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { AnyAction } from 'redux-saga';
import { TabState } from '@common/state/modules/mediaDetails/models';

const CutIcon = ({ startTime, endTime }: Props) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, unknown, AnyAction>>();
  const tdo = useSelector(selectTdo);
  const list = useSelector(selectClusterList);
  const showTrimTool = useSelector(isShowTrimTool);
  const audioState = useSelector(selectAudioTabState);
  const apiTrimTemplate = useSelector(getApiTrimTemplate);
  const runningTasks = useSelector(selectDetectionRunning);
  const [faceDetectionDone, setFaceDetectionDone] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [audioTranscriptionDone, setAudioTranscriptionDone] = useState(false);

  const handleOnclickIcons = () => {
    let faceDetectionDone = false;
    let audioTranscriptionDone = false;

    /* Checking if the audio transcription engine has already been run */
    if (audioState !== TabState.NOT_RUN) {
      audioTranscriptionDone = true;
    }

    /* Checking if the Head detection engine has already been run */
    if (list.length || runningTasks.length) {
      faceDetectionDone = true;
    }

    setFaceDetectionDone(faceDetectionDone);
    setAudioTranscriptionDone(audioTranscriptionDone);
    dispatch(toggleTrimTool());
  };

  const handleDoneClick = () => setShowConfirmationModal(true);

  const handleClearClick = () => dispatch(toggleTrimTool());

  const handleClose = () => setShowConfirmationModal(false);

  const handleCreateTDOForTrimVideo = (
    runFaceDetection: boolean,
    runAudioTranscription: boolean
  ) => {
    const startTimeFormatted = moment
      .utc(tdo?.startDateTime)
      .add(startTime, 'seconds')
      .toDate()
      .toISOString();
    const endTimeFormatted = moment
      .utc(tdo?.startDateTime)
      .add(endTime, 'seconds')
      .toDate()
      .toISOString();
    void dispatch(
      createTDOForTrimVideo(
        {
          startTime: startTime,
          endTime: endTime,
          startDateTime: startTimeFormatted,
          endDateTime: endTimeFormatted,
          url: apiTrimTemplate
            .replace('[START_TIME]', startTimeFormatted)
            .replace('[END_TIME]', endTimeFormatted)
            .replace('[MEDIA_ID]', tdo?.id ?? ''),
          name:
            tdo?.name +
            `_trim(${Math.round(startTime * 1000) / 1000}s-${
              Math.round(endTime * 1000) / 1000
            }s)`,
          type: 'video/mp4',
          hasFaceDetectionEngineResults: faceDetectionDone,
          hasAudioTranscriptionEngineResults: audioTranscriptionDone,
          sourceTDO: tdo?.id,
        },
        runFaceDetection,
        runAudioTranscription
      )
    );
  };

  /* Trim video only */
  const handleTrim = () => {
    handleCreateTDOForTrimVideo(false, false);
    handleClose();
  };

  /* Trim the video and createJob to run Head detection or/and audio transcription */
  const handleTrimAndProcess = (
    runFaceDetection: boolean,
    runAudioTranscription: boolean
  ) => {
    handleCreateTDOForTrimVideo(runFaceDetection, runAudioTranscription);
    handleClose();
  };

  return (
    <>
      <div className={styles.trimWrapper}>
        {showTrimTool && (
          <div className={styles.checkCancelTrimWrapper}>
            <div
              onClick={handleDoneClick}
              className={`${styles.icon} ${styles.doneWrapper}`}
            >
              <Done color="primary" data-testid="icon-done" />
            </div>
            <div
              onClick={handleClearClick}
              className={`${styles.icon} ${styles.clearWrapper}`}
            >
              <Clear color="primary" data-testid="icon-clear" />
            </div>
          </div>
        )}
        <div onClick={handleOnclickIcons} className={styles.cutIconsWrapper}>
          <Tooltip
            title={`Trim & Process`}
            placement="top"
            data-testid="trim-icon-tooltip"
          >
            <img src={cutIcons} />
          </Tooltip>
        </div>
      </div>
      {faceDetectionDone && audioTranscriptionDone ? (
        <TrimDialog
          showConfirmationModal={showConfirmationModal}
          handleClose={handleClose}
          handleTrim={handleTrim}
        />
      ) : (
        <TrimAndProcessDialog
          showConfirmationModal={showConfirmationModal}
          faceDetectionDone={faceDetectionDone}
          audioTranscriptionDone={audioTranscriptionDone}
          handleClose={handleClose}
          handleTrimAndProcess={handleTrimAndProcess}
          handleTrim={handleTrim}
        />
      )}
    </>
  );
};

interface Props {
  startTime: number;
  endTime: number;
}

export default CutIcon;

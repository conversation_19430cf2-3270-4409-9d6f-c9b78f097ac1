import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import Dialog from '@common-components/ConfirmDialog';

const DeleteCaseDialog = ({ canDelete, isOpen, onConfirm, onClose }: Props) => {
  const intl = useIntl();

  return (
    <Dialog
      isOpen={isOpen}
      title={I18nTranslate.TranslateMessage('deleteCaseTitle')}
      content={I18nTranslate.TranslateMessage(
        canDelete ? 'deleteCaseContent' : 'deleteCaseRequest'
      )}
      confirmText={(canDelete && intl.formatMessage({ id: 'delete' })) || ''}
      onConfirm={onConfirm}
      cancelText={intl.formatMessage({ id: 'cancel' })}
      onCancel={onClose}
      warning
    />
  );
};
interface Props {
  readonly canDelete: boolean;
  readonly isOpen: boolean;
  readonly onConfirm: () => void;
  readonly onClose: () => void;
}

export default DeleteCaseDialog;

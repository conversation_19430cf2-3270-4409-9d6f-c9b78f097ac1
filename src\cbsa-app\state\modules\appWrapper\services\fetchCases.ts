import {
  FET<PERSON>_CASES_FAILURE,
  FETCH_CASES_SUCCESS,
} from '@cbsa-modules/appWrapper';
import callGraphQLApi from '@helpers/callGraphQLApi';
import { NOOP } from '@cbsa-modules/universal/actions';
import { Thunk, lookupLatestCaseSchemaId } from '@cbsa-modules/universal';
import {
  FETCH_CASES_QUERY,
  FetchCasesQueryResponse,
} from '@cbsa-modules/appWrapper/services/queries/fetchCases';

export const fetchCases: Thunk<{
  readonly offset: number;
  readonly limit: number;
  readonly query: {
    field: string;
    operator: string;
    value: string;
  }[];
}> =
  ({ offset, limit, query }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    return await callGraph<PERSON><PERSON>pi<FetchCasesQueryResponse>({
      actionTypes: [NOOP, <PERSON>ETCH_CASES_SUCCESS, FETCH_CASES_FAILURE],
      query: FETCH_CASES_QUERY,
      variables: { offset, limit, schemaId, query },
      dispatch,
      getState,
    });
  };

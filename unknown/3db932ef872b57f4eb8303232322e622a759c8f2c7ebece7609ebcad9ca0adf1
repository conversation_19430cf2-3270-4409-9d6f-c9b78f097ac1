import TextField, { TextFieldProps } from '@mui/material/TextField';
import {
  Control,
  Controller,
  FieldValues,
  UseFormSetValue,
} from 'react-hook-form';

const getRulesByFieldType = (fieldType: string) => {
  const commonRules: {
    required: string;
    pattern?: { value: RegExp; message: string };
  } = { required: 'Required' };
  if (fieldType === 'email') {
    commonRules.pattern = {
      value: /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/i,
      message: 'Invalid Email',
    };
  }
  return commonRules;
};

const TextInput = ({
  control,
  controlName,
  trigger,
  label,
  type = 'text',
  ...custom
}: TextInputProps) => (
  <Controller
    name={controlName}
    control={control}
    rules={getRulesByFieldType(type)}
    render={({ field, fieldState }) => (
      <TextField
        type={type}
        {...field}
        {...custom}
        label={label}
        error={fieldState.isTouched && !!fieldState.error}
        helperText={fieldState.isTouched && fieldState.error?.message}
        onKeyUp={() => trigger?.(controlName)}
        onKeyDown={() => trigger?.(controlName)}
      />
    )}
  />
);

type TextInputProps = {
  control: Control<FieldValues, any>;
  controlName: string;
  setValue: UseFormSetValue<FieldValues>;
  label: string;
  type?: string;
  trigger?: (t: string) => void;
} & TextFieldProps;

export default TextInput;

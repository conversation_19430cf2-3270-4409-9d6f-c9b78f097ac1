import { all, fork, put, select, takeEvery } from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';

import {
  actionCloseUpgradeAccount,
  MAKE_PURCHASE,
  OPEN_FASTSPRING,
} from '../actions';
import {} from '../selectors';
import { serviceMakePurchase } from '../services';

const { user } = modules;

// interface Action<P> {
//   type: string;
//   payload: P;
// }

export function* upgradeSagas() {
  yield* all([fork(onActionMakePurchase), fork(onActionOpenFastSpring)]);
}

function* onActionMakePurchase() {
  yield* takeEvery(MAKE_PURCHASE, function* ({ payload }) {
    if (payload) {
      yield* put(serviceMakePurchase(payload));
    }
  });
}

function* onActionOpenFastSpring() {
  yield* takeEvery(OPEN_FASTSPRING, function* () {
    const orgId = yield* select(user.selectUserOrganizationId);
    const u = yield* select(user.selectUser);
    const s = {
      reset: true,
      products: [
        {
          path: 'sub-mt-1',
          quantity: 1,
        },
      ],
      tags: {
        orgId,
      },
      paymentContact: {
        email: u.email,
        firstName: u.kvp?.firstName || '',
        lastName: u.kvp?.lastName || '',
      },
      checkout: true,
    };
    if ((<any>window).fastspring?.builder) {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      (<any>window).fastspring.builder.push(s);
      yield* put(actionCloseUpgradeAccount());
    }
  });
}

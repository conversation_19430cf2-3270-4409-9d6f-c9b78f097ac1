import { Lens } from 'monocle-ts';

import * as Actions from './actions';
import {
  defaultState,
  defaultJobSlice,
  JobState,
  TranscriptionStore,
} from './store';
import { TDOId } from '../../universal/models/Brands';
import { createReducer } from '@reduxjs/toolkit';
import { GetActionCreatorPayloadT } from '@utils';
import type { JobStatus } from 'veritone-types';

const engineGroup = (tdoId: TDOId) =>
  Lens.fromNullableProp<TranscriptionStore>()(tdoId, {});
const engineJob = (jobId: string) =>
  Lens.fromNullableProp<NonNullable<TranscriptionStore['']>>()(
    jobId,
    defaultJobSlice()
  );
const getJob = (tdoId: TDOId, jobId: string) =>
  engineGroup(tdoId).compose(engineJob(jobId)).get;
const modifyJob = (
  tdoId: TDOId,
  jobId: string,
  fn: (j: JobState) => JobState
) => engineGroup(tdoId).compose(engineJob(jobId)).modify(fn);

const processEngineRequest = (
  state: TranscriptionStore,
  { payload }: { payload: { tdoId: TDOId } }
) => engineGroup(payload.tdoId).modify((s) => s)(state);

const processEngineRequestSuccess = (
  state: TranscriptionStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
    endedOn: new Date(),
    error: undefined,
  }))(state);

const processEngineRequestFailure = (
  state: TranscriptionStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string; errorMsg: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
    status: 'failed',
    endedOn: new Date(),
    error: payload.errorMsg,
  }))(state);

const createJobSuccess = (
  state: TranscriptionStore,
  {
    payload,
  }: {
    payload: GetActionCreatorPayloadT<typeof Actions.CREATE_ENGINE_JOB_SUCCESS>;
  }
) =>
  modifyJob(payload.createJob.targetId, payload.createJob.id, (_job) => ({
    jobId: payload.createJob.id,
    isRunning: true,
    status: 'pending',
    startedOn: new Date(),
    endedOn: undefined,
    error: undefined,
  }))(state);

const startPollingEngineResults = (
  state: TranscriptionStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    jobId: payload.jobId,
    isRunning: true,
    status: 'pending',
    error: undefined,
  }))(state);

const stopPollingEngineResults = (
  state: TranscriptionStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
  }))(state);

const checkJobStatusSuccess = (
  state: TranscriptionStore,
  {
    payload,
  }: { payload: { job: { targetId: TDOId; id: string; status: JobStatus } } }
) =>
  payload.job.status ===
  getJob(payload.job.targetId, payload.job.id)(state).status
    ? state
    : modifyJob(payload.job.targetId, payload.job.id, (job) => ({
        ...job,
        status: payload.job.status,
      }))(state);

const cancelJob = (
  state: TranscriptionStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
    status: 'cancelled',
    endedOn: new Date(),
    error: undefined,
  }))(state);

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(Actions.PROCESS_ENGINE_REQUEST, processEngineRequest)
    .addCase(
      Actions.PROCESS_ENGINE_REQUEST_SUCCESS,
      processEngineRequestSuccess
    )
    .addCase(
      Actions.PROCESS_ENGINE_REQUEST_FAILURE,
      processEngineRequestFailure as any // TODO: Fix this
    )
    .addCase(Actions.CREATE_ENGINE_JOB_SUCCESS, createJobSuccess)
    .addCase(Actions.START_POLLING_ENGINE_RESULTS, startPollingEngineResults)
    .addCase(Actions.STOP_POLLING_ENGINE_RESULTS, stopPollingEngineResults)
    .addCase(Actions.CHECK_JOB_STATUS_SUCCESS, checkJobStatusSuccess)
    .addCase(Actions.CANCEL_JOB, cancelJob);
});

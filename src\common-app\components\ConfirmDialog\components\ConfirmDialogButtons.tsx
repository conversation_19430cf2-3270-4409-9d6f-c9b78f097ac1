import { Button as <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';
import React from 'react';

const style = {
  button: {
    primary: {
      '&.MuiButton-root': {
        backgroundColor: 'primary.main',
        borderRadius: '3px',
        color: 'white',
        height: '50px',
        margin: '0',
        flex: 1,
        '&:hover': {
          background: '#003F57',
        },
      },
      '&.Mui-disabled': {
        color: 'rgba(0, 0, 0, 0.26)',
      },
    },
    secondary: {
      '&.MuiButton-root': {
        background: 'transparent',
        borderWidth: '2px',
        borderStyle: 'solid',
        borderColor: 'primary.main',
        borderRadius: '3px',
        color: 'primary.main',
        height: '50px',
        margin: '0',
        flex: 1,
        '&:hover': {
          backgroundColor: 'rgba(0, 92, 126, 0.04)',
        },
      },
    },
    warning: {
      '&.MuiButton-root': {
        background: '#9E0000',
        borderRadius: '3px',
        color: 'white',
        height: '50px',
        margin: '0',
        flex: 1,
        '&:hover': {
          background: '#6C0101',
        },
      },
    },
  },
};

const Button = ({ children, disabled, onClick, variant }: Props) => (
  <MuiButton
    disabled={disabled}
    onClick={onClick}
    data-testid="confirm-dialog-button"
    sx={style.button[`${variant ?? 'primary'}`]}
  >
    {children}
  </MuiButton>
);

interface Props {
  children: string | React.JSX.Element;
  disabled?: boolean;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'warning';
}

export default Button;

import Grid from '@mui/material/Grid2';

import SearchBox from '@common-components/CaseDashboard/CasesMangement/SearchBox';
import StatusFilter from '@common-components/CaseDashboard/CasesMangement/StatusFilter';
import ArchiveSwitch from './ArchiveSwitch';
import NewCaseButton from './NewCaseButton';

// import makeStyles from '@mui/styles/makeStyles';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

// TODO: Remove disable when grid styling is handled
// eslint-disable-next-line
const CasesManagementContent = () => {
  // const classes = useStyles();

  return (
    <Grid>
      <Grid
        container
        // TODO: update to use styled instead
        // classes={{ root: classes.managementRow }}
      >
        <SearchBox />
        <StatusFilter />
        <ArchiveSwitch />
        <NewCaseButton />
      </Grid>
    </Grid>
  );
};

const CasesManagement = () => (
  <ThemeProvider theme={defaultTheme}>
    <CasesManagementContent />
  </ThemeProvider>
);

export default CasesManagement;

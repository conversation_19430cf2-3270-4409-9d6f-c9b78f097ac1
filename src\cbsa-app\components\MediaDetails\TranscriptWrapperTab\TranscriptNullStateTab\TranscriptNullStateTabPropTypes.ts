import { TranscriptNullStateTabPropsFromRedux } from '.';
// import { DetailTDO } from '@common-modules/mediaDetails/models';
// import { processEngineRequestAction } from '@common-modules/engines/transcription';

// export interface TranscriptNullStateTabPropTypes {
//   readonly tdo: DetailTDO | null;
//   readonly buttonTranscriptionDisable: boolean;
//   readonly onStartJob: typeof processEngineRequestAction;
//   readonly videoLoaded: boolean;
// }

export type TranscriptNullStateTabPropTypes =
  TranscriptNullStateTabPropsFromRedux & {
    readonly videoLoaded: boolean;
  };

import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import { validateTokenQuery } from '../../src/api/queries';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { validateToken } from '../../src/validations/validator';

describe('validator', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, next, mockClear } = getMockRes();

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('authorizes w/ valid API token', async () => {
    const req = getMockReq({
      headers: {
        authorization: 'Bearer abcdef:1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqr',
      },
    });

    await validateToken(req, res, next);
    expect(error).not.toHaveBeenCalled();
    expect(next).toBeCalled();
  });

  it('authorizes w/ valid session token', async () => {
    const headers = {
      authorization: 'Bearer abcdefgh-1234-5678-90ab-cdefghijklmn',
    };
    const req = getMockReq({
      headers,
    });

    callGQL.mockImplementation(() => Promise.resolve({
      validateToken: {
        token: 'token',
      },
    }));

    await validateToken(req, res, next);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, validateTokenQuery(headers.authorization.split(' ')[1]!));
    expect(next).toBeCalled();
  });

  it('logs an error w/o valid token', async () => {
    const req = getMockReq({
      headers: {
        authorization: 'Bearer token',
      },
    });

    await validateToken(req, res, next);
    expect(error).toHaveBeenCalledTimes(1);
    expect(error).toHaveBeenCalledWith(Messages.InvalidToken);
  });
});

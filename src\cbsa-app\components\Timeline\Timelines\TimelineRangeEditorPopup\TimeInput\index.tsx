import { clamp, padStart } from 'lodash';
import moment from 'moment';
import * as React from 'react';

import * as styles from './styles.scss';

const formatTime = (timeMs: number) => {
  const t = moment.duration(timeMs);
  return {
    hours: t.hours(),
    minutes: t.minutes(),
    seconds: t.seconds(),
    milliseconds: t.milliseconds(),
  };
};

const toMs = ({
  hours,
  minutes,
  seconds,
  milliseconds,
}: ReturnType<typeof formatTime>) =>
  hours * 3_600_000 + minutes * 60_000 + seconds * 1_000 + milliseconds;

class TimeInput extends React.Component<TimeInputProps, any> {
  state = {
    ...formatTime(this.props.value),
  };

  static getDerivedStateFromProps(props: TimeInputProps) {
    return formatTime(props.value);
  }

  onChangeLocal =
    (name: string, min: number, max: number) =>
    (evt: React.ChangeEvent<HTMLInputElement>) => {
      const val = clamp(parseInt(evt.target.value, 10) || 0, min, max);
      const timeMs = { ...this.state, [name]: val };
      this.setState({ [name]: val }, () => this.props.onChange(toMs(timeMs)));
    };

  onFocus = (evt: React.FocusEvent<HTMLInputElement>) => {
    evt.target.select();
  };

  render() {
    const { hours, minutes, seconds, milliseconds } = this.state;
    const { label, textHelper } = this.props;

    return (
      <React.Fragment>
        <div
          className={styles.timeInputTitle}
          data-testid="timeline-range-editor-popup-time-input-title"
        >
          {label}
          <span className={styles.timeInputTextHelper}>{textHelper}</span>
        </div>
        <div
          className={styles.groupInput}
          data-testid="timeline-range-editor-popup-group-input"
        >
          <input
            data-testid="timeline-range-editor-popup-input"
            type="text"
            value={padStart(String(hours), 2, '0')}
            onChange={this.onChangeLocal('hours', 0, hours)}
            onFocus={this.onFocus}
          />
          {':'}
          <input
            data-testid="timeline-range-editor-popup-input"
            type="text"
            value={padStart(String(minutes), 2, '0')}
            onChange={this.onChangeLocal('minutes', 0, 59)}
            onFocus={this.onFocus}
          />
          {':'}
          <input
            data-testid="timeline-range-editor-popup-input"
            type="text"
            value={padStart(String(seconds), 2, '0')}
            onChange={this.onChangeLocal('seconds', 0, 59)}
            onFocus={this.onFocus}
          />
          {'.'}
          <input
            data-testid="timeline-range-editor-popup-input"
            type="text"
            value={padStart(String(milliseconds), 3, '0')}
            onChange={this.onChangeLocal('milliseconds', 0, 999)}
            onFocus={this.onFocus}
          />
        </div>
      </React.Fragment>
    );
  }
}

export default TimeInput;

export interface TimeInputProps {
  value: number;
  label: string;
  textHelper: string;
  onChange: (timeMs: number) => void;
}

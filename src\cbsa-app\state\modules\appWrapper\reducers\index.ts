import { AppWrapperStore } from '@cbsa-modules/appWrapper';
import { defaultState } from '@cbsa-modules/appWrapper/store';

import {
  FETCH_CASES,
  FETCH_CASES_SUCCESS,
  FETCH_CASES_FAILURE,
  <PERSON>ETCH_MEDIA,
  <PERSON>ETCH_MEDIA_SUCCESS,
  FETCH_MEDIA_FAILURE,
  QUEUE_FILE,
  PROCESS_FILE,
  UPLOAD_FILE_SUCCESS,
  UPLOAD_FILE_FAILURE,
  RETRY_FAILED_FILE,
  REMOVE_SUCCESSFUL_FILE,
} from '../actions';
import { fetchCases, fetchCasesSuccess, fetchCasesFailure } from './fetchCases';
import { fetchMedia, fetchMediaSuccess, fetchMediaFailure } from './fetchMedia';
import {
  queueFile,
  processFile,
  uploadFileSuccess,
  uploadFileFailure,
  retryFailedFile,
  removeSuccessfulFile,
} from './uploadMedia';
import { CaseReducer, createReducer } from '@reduxjs/toolkit';

export type Re<P = unknown> = CaseReducer<
  AppWrapperStore,
  { payload: P; type: string }
>;

const reducers = createReducer(defaultState, (builder) => {
  builder
    .addCase(FETCH_CASES, fetchCases)
    .addCase(FETCH_CASES_SUCCESS, fetchCasesSuccess)
    .addCase(FETCH_CASES_FAILURE, fetchCasesFailure)
    .addCase(FETCH_MEDIA, fetchMedia)
    .addCase(FETCH_MEDIA_SUCCESS, fetchMediaSuccess)
    .addCase(FETCH_MEDIA_FAILURE, fetchMediaFailure)
    .addCase(QUEUE_FILE, queueFile)
    .addCase(PROCESS_FILE, processFile)
    .addCase(UPLOAD_FILE_SUCCESS, uploadFileSuccess)
    .addCase(UPLOAD_FILE_FAILURE, uploadFileFailure)
    .addCase(RETRY_FAILED_FILE, retryFailedFile)
    .addCase(REMOVE_SUCCESSFUL_FILE, removeSuccessfulFile);
});

export default reducers;

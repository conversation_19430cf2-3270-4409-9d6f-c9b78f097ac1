import { clamp } from 'lodash';
import React from 'react';

export function getMousePosition(
  e: MouseEvent | React.MouseEvent<HTMLElement>,
  clamp_element: HTMLElement | null = null
) {
  let el = e.target as HTMLElement,
    el_left = 0,
    el_top = 0,
    x = e.clientX,
    y = e.clientY;

  if (clamp_element) {
    const bounds = clamp_element.getBoundingClientRect();
    x = clamp(x, bounds.left, bounds.right);
    y = clamp(y, bounds.top, bounds.bottom);
    el = clamp_element;
  }

  while (el.offsetParent) {
    el_left += el.offsetLeft;
    el_top += el.offsetTop;
    el = el.offsetParent as HTMLElement;
  }

  el = clamp_element || (e.target as HTMLElement);
  while (el.parentNode) {
    el_left -= el.scrollLeft;
    el_top -= el.scrollTop;
    el = el.parentNode as HTMLElement; // TODO: Why is this safe?
  }

  x -= el_left;
  y -= el_top;

  return { x, y };
}

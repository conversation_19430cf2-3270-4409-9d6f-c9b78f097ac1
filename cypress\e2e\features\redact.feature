Feature: Redact

  @e2e @redacted-files
  Scenario: Prepare Test Data upload
    Given The user uploads file "upload_test.mp4" with transcription "transcription"
    And The user uploads file "lucy.mp4" with transcription "off"

  @e2e @redacted-files
  Scenario: Redact and verify a word in the transcript and file
    Given The user goes to test file "upload_test.mp4"
    Given I have redacted the transcription
    When I redact the file using coordinates 100, 100, 200, 200
    Then I should see the redacted file and be able to download it

  @e2e @redacted-files
  Scenario: Redacted file without redaction
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    When The user clicks the "Redact File" button
    Then The confirmation dialog is displayed
    When The user confirms "Redact File" to start redaction
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user should see the video player in Redacted Files tab
    Then The user clicks 'Download Media' button in Redacted Files tab
    # wait for download
    Then The report should have name "lucy[NO_REDACTIONS].v1.mp4"

  @e2e @redacted-files
  Scenario: Redacted file after making some redactions
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And Presses the "Save" button
    When The user clicks the "Redact File" button
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user should see the video player in Redacted Files tab

  @e2e @redacted-files
  Scenario: Set and clear trim range
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    When The user clicks on down button next to Redact Files button
    Then The user should see the menu displayed
    When The user choose "Set Trim Range" option
    Then The user should see the trim range dialog displayed
    When The user enter "Start" time at "01" "seconds"
    And The user enter "End" time at "02" "seconds"
    And The user click button Set to set time for trim
    Then The button "Redact File" changes to "Trim and Redact"
    When The user clicks the "Trim and Redact" button
    Then The confirmation dialog is displayed
    When The user confirms "Trim and Redact" to start redaction
    And The user waits for redaction to complete
    When The user clicks on down button next to Redact Files button
    Then The user should see the menu displayed
    When The user choose "Clear Trim Range" option
    And Presses the "Save" button
    Then The button "Trim and Redact" changes to "Redact File"
    When The user deletes Redact File "lucy.mp4" if exist

  @e2e @redacted-files
  Scenario: Verify Start and End time range
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    When The user clicks on down button next to Redact Files button
    Then The user should see the menu displayed
    Then The user choose "Set Trim Range" option
    Then The user should see the trim range dialog displayed
    Then The user enter "Start" time at "50" "seconds"
    Then The error message "Start cannot be after end" should be displayed for "Start"
    Then The user enter "End" time at "00" "seconds"
    Then The user enter "End" time at "00" "milliseconds"
    And The error message "Start cannot be after end" should be displayed for "End"

  @e2e @redacted-files
  Scenario: Trim and redact file without any redaction
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    When The user clicks on down button next to Redact Files button
    Then The user should see the menu displayed
    Then The user choose "Set Trim Range" option
    Then The user should see the trim range dialog displayed
    Then The user enter "Start" time at "01" "seconds"
    Then The user enter "End" time at "02" "seconds"
    Then The user click button Set to set time for trim
    Then The button "Redact File" changes to "Trim and Redact"
    When The user clicks the "Trim and Redact" button
    Then The confirmation dialog is displayed
    When The user confirms "Trim and Redact" to start redaction
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user should see the video player in Redacted Files tab
    And The user navigates to Editor tab
    When The user clicks on down button next to Redact Files button
    Then The user should see the menu displayed
    When The user choose "Clear Trim Range" option
    And Presses the "Save" button
    Then The user removes current tdoFile

  @e2e @redacted-files
  Scenario: Version number is visible beneath the Latest Redacted File
    And The user uploads file "lucy.mp4" with transcription "off"
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And Presses the "Save" button
    When The user clicks the "Redact File" button
    And The user waits for redaction to complete
    When The user navigates to Redacted Files tab
    Then The user should see the version 1 in redacted files

  @e2e @redacted-files @error
  Scenario: Version number is visible beneath the Latest Redacted File when using trim ranges
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And Presses the "Save" button
    When The user clicks on down button next to Redact Files button
    Then The user should see the menu displayed
    Then The user choose "Set Trim Range" option
    Then The user should see the trim range dialog displayed
    Then The user enter "Start" time at "01" "seconds"
    Then The user enter "End" time at "02" "seconds"
    Then The user click button Set to set time for trim
    Then The button "Redact File" changes to "Trim and Redact"
    When The user clicks the "Trim and Redact" button
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user should see the version 1 in redacted files

  @e2e @redacted-files
  Scenario: Verify user can trim and redact file with redactions
    Given The user goes to test file "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user clicks on down button next to Redact Files button
    Then The user should see the menu displayed
    Then The user choose "Set Trim Range" option
    Then The user should see the trim range dialog displayed
    Then The user enter "Start" time at "01" "seconds"
    Then The user enter "End" time at "03" "seconds"
    Then The user click button Set to set time for trim
    Then The button "Redact File" changes to "Trim and Redact"
    When The user clicks the "Trim and Redact" button
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user should see the video player in Redacted Files tab
    Then The user removes current tdoFile

  @e2e @redacted-files
  Scenario: Download redaction report
    Given The user uploads file "lucy.mp4" with transcription "off"
    Given The user goes to test file "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And Presses the "Save" button
    When The user clicks on down button next to Redact Files button
    Then The user should see the menu displayed
    Then The user choose "Download Redaction Report" option
    Then The user removes current tdoFile

  @e2e @redacted-files
  Scenario: Verify user can delete the latest redacted file when the file was redacted only 1 time
    And The user uploads file "lucy.mp4" with transcription "off"
    Given The user goes to test file "lucy.mp4"
    And The user clicks the "Redact File" button
    Then The confirmation dialog is displayed
    When The user confirms "Redact File" to start redaction
    And The user waits for redaction to complete
    When The user navigates to Redacted Files tab
    Then The user should see the version 1 in redacted files
    When The user opens delete Redact modal
    Then Delete modal shows 0 previous versions remaining
    When The user confirms deletion of Redact File
    Then The user removes current tdoFile

  @e2e @redacted-files
  Scenario: Verify user can delete the latest redacted file when the file was redacted 2 time
    And The user uploads file "lucy.mp4" with transcription "off"
    Given The user goes to test file "lucy.mp4"
    And The user clicks the "Redact File" button
    Then The confirmation dialog is displayed
    When The user confirms "Redact File" to start redaction
    And The user waits for redaction to complete
    And The user clicks the "Redact File" button
    Then The confirmation dialog is displayed
    When The user confirms "Redact File" to start redaction
    And The user waits for redaction to complete
    When The user navigates to Redacted Files tab
    Then The user should see the version 2 in redacted files
    When The user opens delete Redact modal
    Then Delete modal shows 1 previous versions remaining
    When The user confirms deletion of Redact File
    Then The user removes current tdoFile
#################################

  @e2e @redacted-files
  Scenario: Verify Redacted files tab
    Given The user uploads file "lucy.mp4" with transcription "off"
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    Then The Redacted Files tab should be disabled
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And Presses the "Save" button
    When The user clicks the "Redact File" button
    And The user waits for redaction to complete
    Then The Redacted Files tab should have color "rgb(255, 255, 255)"
    And The user navigates to Redacted Files tab
    Then The Redacted Files tab should have color "rgb(249, 160, 44)"
    Then The user should see all elements in the Redacted Files tab

  @e2e @redacted-files
  Scenario: Verify user can delete the latest redacted file when the file was redacted only 1 time
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    Then The Redacted Files tab should be disabled
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And Presses the "Save" button
    When The user clicks the "Redact File" button
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user clicks delete button in Redacted Files tab
    Then The delete confirm dialog should display
    Then The user choose "delete" to delete redacted file
    And The user navigates to Editor tab
    Then The Redacted Files tab should be disabled
    Then The user removes current tdoFile

  @e2e @redacted-files
  Scenario: Verify user can download media in Redacted files
    Given The user uploads file "lucy.mp4" with transcription "transcription"
    Given The user goes to test file "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And Presses the "Save" button
    When The user clicks the "Redact File" button
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user clicks 'Download Media' button in Redacted Files tab
    Then The user clicks 'DOWNLOAD AUDIT LOG' button in Redacted Files tab
    Then The user clicks 'Download All' button in Redacted Files tab
    Then The user clicks 'Download Redacted Transcript' button in Redacted Files tab
    Then The report should have name "lucy[REDACTED].v1.txt" with detail "Stitch Fix"
    Then The user removes current tdoFile

  @e2e @redacted-files
  Scenario: Verify number of version is updated after each time file was redact
    And The user uploads file "lucy.mp4" with transcription "off"
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    Then The Redacted Files tab should be disabled
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And Presses the "Save" button
    When The user clicks the "Redact File" button
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user should see the version 1 in redacted files
    And The user navigates to Editor tab
    Then The user removes all redacted files
    Then The Redacted Files tab should be disabled
    When The user draws "1st" UDR at coordinates 150, 150, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user clicks the "Redact File" button
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user should see the version 2 in redacted files

  @e2e @redacted-files
  Scenario: Verify preview video in Redacted Files tab
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And Presses the "Save" button
    When The user clicks the "Redact File" button
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    Then The user should see the video player in Redacted Files tab
    When The user clicks on the preview video
    And The video should contain the redacted UDR
    Then The user removes current tdoFile

  @e2e @redacted-files
  Scenario: Verify the view count updates when playing preview video
    And The user uploads file "lucy.mp4" with transcription "off"
    Given The user goes to test file "lucy.mp4"
    Then The user removes all redacted files
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    And Presses the "Save" button
    And The user clicks the "Redact File" button
    And The user waits for redaction to complete
    And The user navigates to Redacted Files tab
    When The user clicks on the preview video
    Then The user see number of view is 1
    Then The user close the redacted files
    Given The user goes to test file "lucy.mp4"
    And The user navigates to Redacted Files tab
    When The user clicks on the preview video
    Then The user see number of view is 2
    Then The user removes current tdoFile

  @e2e @redacted-files
  Scenario: Delete multiple test files
    Given The user deletes files
      | lucy.mp4        |
      | upload_test.mp4 |
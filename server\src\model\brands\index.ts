interface Flavoring<FlavorT> {
    _type?: FlavorT;
}
export type Flavor<T, FlavorT> = T & Flavoring<FlavorT>;
export type TDOId = Flavor<string, 'Tdo'>;
interface Branding<BrandT> {
    _type: BrandT;
  }
export type Brand<T, BrandT> = T & Branding<BrandT>;
export type TreeObjectId = Brand<string, 'TreeObject'>;
export type CaseId = Brand<string, 'Case'>;
export type FolderId = Brand<string, 'Folder'> | CaseId;

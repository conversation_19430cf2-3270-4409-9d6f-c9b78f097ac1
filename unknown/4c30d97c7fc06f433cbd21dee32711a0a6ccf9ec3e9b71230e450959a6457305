.media {
  gap: 10px;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: space-between;

  &Group {
    gap: 10px;
    display: flex;
    overflow: hidden;
    align-items: center;
  }

  &Icon {
    color: #4e4e4e;
    font-size: 30px;
  }

  &Name {
    color: #4e4e4e;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;

    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  &Status {
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    padding: 4px 5px;

    &.new {
      background: rgb(0, 92, 126, 0.2);
      color: #005c7e;
    }

    &.processing {
      background: rgb(213, 114, 0, 0.2);
      color: #d57200;
    }

    &.complete {
      background: rgb(67, 160, 71, 0.2);
      color: #43a047;
    }

    &.error {
      background: rgb(213, 0, 14, 0.2);
      color: #d5000e;
    }
  }

  &:hover {
    text-decoration: none;

    .media {
      &Icon {
        color: #005c7e;
      }

      &Name {
        color: #005c7e;
        font-weight: bold;
      }
    }
  }
}

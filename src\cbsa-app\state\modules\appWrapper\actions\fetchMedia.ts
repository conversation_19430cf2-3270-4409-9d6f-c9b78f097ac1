import { TreeObjectId } from '@common/state/modules/universal/models/Brands';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { FETCH_MEDIA_RESPONSE } from '../services/queries/fetchMedia';
import { createAction } from '@reduxjs/toolkit';
export const FETCH_MEDIA = createAction<{
  caseId: TreeObjectId;
  offset: number;
  limit: number;
}>('CBSA/FETCH_MEDIA');
export const FETCH_MEDIA_SUCCESS =
  createGraphQLSuccessAction<FETCH_MEDIA_RESPONSE>('CBSA/FETCH_MEDIA_SUCCESS');
export const FETCH_MEDIA_FAILURE = createGraphQLFailureAction(
  'CBSA/FETCH_MEDIA_FAILURE'
);

export const fetchMedia = ({
  caseId,
  offset,
  limit,
}: {
  caseId: TreeObjectId;
  offset: number;
  limit: number;
}) => FETCH_MEDIA({ caseId, offset, limit });

import { omitBy } from 'lodash';
import {
  all,
  fork,
  put,
  select,
  takeEvery,
  takeLatest,
} from 'typed-redux-saga/macro';

import {
  IN_SET_AUDIO_REDACTIONS,
  OUT_UDR_CLUSTER_GROUPS,
  OUT_DETECTION_CLUSTER_GROUPS,
  WORKER_WARN_FAILURE,
  WorkerWarnFailurePayload,
} from '@worker';
import {
  actionInitSetSelectedGroups,
  ADD_REDACTION_CODE_TO_TRANSCRIPTION,
  DELETE_NOTES,
  REDACT_SLICE,
  REDACT_WORDS,
  REMOVE_REDACTION_CODE_FROM_TRANSCRIPTION,
  UNREDACT_ALL,
  UNREDACT_SLICE,
  UNREDACT_WORDS,
  UPDATE_NOTES,
} from '../actions';
import { selectAudioRedactions, selectSelected } from '../selectors';
import { enqueueSnackbar } from '@common-modules/snackbar';

export function* workerOutSaga() {
  yield* all([
    fork(onActionOutTimeline),
    fork(onActionAudioRedactionUpdate),
    fork(WatchWorkerSaveAssetFailure),
  ]);
}

function* WatchWorkerSaveAssetFailure() {
  yield* takeEvery(WORKER_WARN_FAILURE, workerWarnFailure);
}

function* workerWarnFailure(action: { payload: WorkerWarnFailurePayload }) {
  const { message } = action.payload;
  yield* put(
    enqueueSnackbar({
      message,
      variant: 'warning',
    })
  );
}

function* onActionOutTimeline() {
  yield* takeEvery(
    [OUT_UDR_CLUSTER_GROUPS, OUT_DETECTION_CLUSTER_GROUPS],
    function* () {
      const selected = yield* select(selectSelected);
      yield* put(
        actionInitSetSelectedGroups({
          selected: omitBy(selected, (v) => v !== true),
        })
      );
    }
  );
}

function* onActionAudioRedactionUpdate() {
  yield* takeLatest(
    [
      REDACT_WORDS,
      UNREDACT_WORDS,
      REDACT_SLICE,
      UNREDACT_SLICE,
      UNREDACT_ALL,
      UPDATE_NOTES,
      DELETE_NOTES,
      ADD_REDACTION_CODE_TO_TRANSCRIPTION,
      REMOVE_REDACTION_CODE_FROM_TRANSCRIPTION,
    ],
    function* () {
      const audioRedactions = yield* select(selectAudioRedactions);
      yield* put(IN_SET_AUDIO_REDACTIONS({ audioRedactions }));
    }
  );
}

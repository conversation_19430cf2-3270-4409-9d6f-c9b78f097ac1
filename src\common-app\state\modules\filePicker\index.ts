import { clamp, get } from 'lodash';
import { UploadResult } from './saga';
import { CLOSE_FILEPICKER } from '@redact-modules/mainPage';
import { CaseId } from '@common-modules/universal/models/Brands';
import { createAction, createReducer } from '@reduxjs/toolkit';
import { ArrayOrSingle } from 'ts-essentials';

export const PICK_START = createAction(
  'PICK_START',
  (meta: { id: string }) => ({
    payload: undefined,
    meta,
  })
);
export const PICK_END = createAction('PICK_END', (meta: { id: string }) => ({
  payload: undefined,
  meta,
}));
export const UPLOAD_REQUEST = createAction(
  'UPLOAD_REQUEST',
  (
    payload: {
      id: string;
      files: ArrayOrSingle<File>;
      callback: () => void;
      govQARequestId?: string;
      foiaXpressRequestId?: string;
      casepointRequestId?: string;
      nuixRequestId?: string;
      exterroRequestId?: string;
      caseId?: CaseId;
      settingsProfileId?: string;
    },
    meta: { id: string }
  ) => ({
    payload,
    meta,
  })
);

export const UPLOAD_PROGRESS = createAction(
  'UPLOAD_PROGRESS',
  (progressPercent: number, meta: { fileKey: string; id: string }) => ({
    payload: progressPercent,
    meta,
  })
);
export const UPLOAD_COMPLETE = createAction(
  'UPLOAD_COMPLETE',
  (
    uploadResult: UploadResult[],
    meta: {
      warning: any;
      error: unknown;
      id: string;
      caseId?: CaseId;
      settingsProfileId?: string;
      govQARequestId?: string;
      foiaXpressRequestId?: string;
      casepointRequestId?: string;
      nuixRequestId?: string;
      exterroRequestId?: string;
    }
  ) => ({
    payload: uploadResult,
    meta,
  })
);

export const namespace = 'filePicker';

type FileState = 'selecting' | 'uploading' | 'complete';

const defaultState: DefaultStateTypes = {};

export interface DefaultStateTypes {
  [pickedId: string]: PickerStateTypes;
}

const defaultPickerState: PickerStateTypes = {
  open: false,
  state: 'selecting' as FileState,
  progressPercentByFileKey: {},
  success: false,
  error: null,
  warning: null,
  uploadResult: null,
};

export interface PickerStateTypes {
  open: boolean;
  state: FileState;
  progressPercentByFileKey: Record<string, any>;
  success: boolean | null;
  error: string | null;
  warning: string | null;
  uploadResult: UploadResult[] | null;
}

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(CLOSE_FILEPICKER, () => defaultState)
    .addCase(PICK_START, (state, { meta: { id } }) => ({
      ...state,
      [id]: {
        ...defaultPickerState,
        open: true,
        state: 'selecting',
      },
    }))
    .addCase(PICK_END, (state, { meta: { id } }) => ({
      ...state,
      [id]: {
        ...defaultPickerState,
        ...state[id],
        open: false,
      },
    }))
    .addCase(UPLOAD_REQUEST, (state, { meta: { id } }) => ({
      ...state,
      [id]: {
        ...defaultPickerState,
        ...state[id],
        state: 'uploading',
        progressPercentByFileKey: {},
        success: null,
        error: null,
        warning: null,
        uploadResult: null,
      },
    }))
    .addCase(UPLOAD_PROGRESS, (state, { payload, meta: { fileKey, id } }) => ({
      // todo: status message
      ...state,
      [id]: {
        ...defaultPickerState,
        ...state[id],
        progressPercentByFileKey: {
          ...state.progressPercentByFileKey,
          [fileKey]: payload,
        },
      },
    }))
    .addCase(
      UPLOAD_COMPLETE,
      (state, { payload, meta: { warning, error, id } }) => {
        const errorMessage = get(error, 'message', error); // Error or string
        return {
          ...state,
          [id]: {
            ...defaultPickerState,
            ...state[id],
            success: !(warning || error) || null,
            error: typeof errorMessage === 'string' ? errorMessage : null,
            warning: typeof warning === 'string' ? warning : null,
            state: 'complete',
            uploadResult: payload,
          },
        };
      }
    );
});

export const pick = (id: string) => PICK_START({ id });

export const endPick = (id: string) => PICK_END({ id });

export const uploadRequest = (payload: {
  id: string;
  files: ArrayOrSingle<File>;
  callback: () => void;
  govQARequestId?: string;
  foiaXpressRequestId?: string;
  casepointRequestId?: string;
  nuixRequestId?: string;
  exterroRequestId?: string;
  caseId?: CaseId;
  settingsProfileId?: string;
}) => UPLOAD_REQUEST(payload, { id: payload.id });

export const uploadProgress = (
  id: string,
  fileKey: string,
  progressPercent: number
) => UPLOAD_PROGRESS(clamp(Math.round(progressPercent), 100), { fileKey, id });

export const uploadComplete = (
  id: string,
  result: UploadResult[],
  { warning, error }: { warning: any; error: any },
  requestIds: {
    govQARequestId?: string;
    foiaXpressRequestId?: string;
    casepointRequestId?: string;
    nuixRequestId?: string;
    exterroRequestId?: string;
  },
  caseId?: CaseId,
  settingsProfileId?: string
) =>
  UPLOAD_COMPLETE(result, {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    warning,
    error,
    id,
    ...requestIds,
    caseId,
    settingsProfileId,
  });

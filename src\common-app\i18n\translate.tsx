import { ComponentProps } from 'react';
import {
  FormattedMessage,
  FormattedDate,
  FormatDateOptions,
  useIntl,
} from 'react-intl';

const TranslateMessage = (
  id: string,
  values: ComponentProps<typeof FormattedMessage>['values'] = {}
  // eslint-disable-next-line formatjs/enforce-default-message
) => <FormattedMessage id={id} values={{ ...values }} />;

const TranslateDate = (value: string | number | Date | undefined) => {
  const options: FormatDateOptions = {
    year: 'numeric',
    month: 'short',
    weekday: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  };
  return <FormattedDate value={value} {...options} />;
};

const Intl = () => {
  const intl = useIntl();
  return intl;
};

export default { TranslateMessage, TranslateDate, Intl };

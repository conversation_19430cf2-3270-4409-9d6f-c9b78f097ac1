import moment from 'moment';
import call<PERSON>rap<PERSON><PERSON><PERSON><PERSON> from '@helpers/callGraph<PERSON><PERSON><PERSON>';
import { Case, Thunk, lookupLatestCaseSchemaId } from '@cbsa-modules/universal';
import {
  DELETE_CASE_FOLDER_QUERY,
  DeleteCaseFolderQueryResponse,
  UPDATE_STATUS_OF_CASE_SDO_TO_DELETED_QUERY,
} from './queries/deleteCase';
import {
  DELETE_CASE_FAILURE,
  DELETE_CASE_SUCCESS,
} from '@cbsa-modules/addMedia';
import { NOOP } from '@cbsa-modules/universal/actions';

export const deleteCase: Thunk<{
  readonly caseDetails: Case;
}> =
  ({ caseDetails }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const { createdDateTime, id, name: caseName, treeObjectId } = caseDetails;

    await callGraph<PERSON><PERSON><PERSON><DeleteCaseFolderQueryResponse>({
      actionTypes: [NOOP, NOOP, NOOP],
      query: DELETE_CASE_FOLDER_QUERY,
      variables: { id: treeObjectId },
      dispatch,
      getState,
    });

    const {
      createStructuredData: { data: sdoStatusUpdateResults },
    } = (await callGraphQLApi({
      actionTypes: [NOOP, NOOP, DELETE_CASE_FAILURE],
      query: UPDATE_STATUS_OF_CASE_SDO_TO_DELETED_QUERY,
      variables: {
        id,
        schemaId,
        data: {
          caseName,
          status: 'deleted',
          folderTreeObjectId: treeObjectId,
          createdDateTime,
          modifiedDateTime: moment(new Date()).toISOString(),
          priority: 1,
          processingTime: 3600,
        },
      },
      dispatch,
      getState,
    })) as {
      createStructuredData: {
        data: Case;
      };
    };

    dispatch(DELETE_CASE_SUCCESS(sdoStatusUpdateResults));
  };

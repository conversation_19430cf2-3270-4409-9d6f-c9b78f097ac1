import { isEmpty } from 'lodash';
import { createFolderAdapter } from './createFolder';
import Config from '../../apiConfig.json';
import { getSchemaIdAdapter } from './getSchema';
import { CreateFolderRequest, RequestHeader } from '../model/requests';
import { getRootFolderAdapter } from './getRootFolder';
import {
  createContentTemplateQuery,
  createRequestSDOQuery,
} from '../api/queries';
import { callGQL } from '../api/callGraphql';
import {
  CreateContentTemplateResponse,
  CreateRequestSDOResponse,
} from '../model/responses';
import { Messages } from '../errors/messages';
import { Logger } from '../logger';
import { CaseId, FolderId } from '../model/brands';
import { isFolderId } from '../validations/helpers';

export const createCaseAdapter =  async (
    headers: RequestHeader,
    request: CreateFolderRequest
  ) => {
    const { name, description, parentFolderId, userId } = request;
    let rootFolderId: FolderId | undefined = undefined;
    if (!isFolderId(parentFolderId)) {
      rootFolderId = (await getRootFolderAdapter(headers)) as FolderId | undefined;
    } else {
       rootFolderId = parentFolderId;
    }
    if (rootFolderId) {
      const createFolder = await createFolderAdapter(headers, {
        name,
        description: isEmpty(description) ? name : description,
        parentFolderId: rootFolderId,
        userId: userId
      });

      const schemaId = await getSchemaIdAdapter(headers, Config.caseRegistryId);

      if (createFolder?.treeObjectId && schemaId) {
        const requestSdoId = await callGQL<CreateRequestSDOResponse>(
          headers,
          createRequestSDOQuery,
          {
            name,
            folderTreeObjectId: createFolder.treeObjectId,
            currentTime: new Date().toISOString(),
            schemaId,
          }
        )
          .then((res) => res.createStructuredData?.id)
          .catch((err) => {
            Logger.error(Messages.createCaseSdo + JSON.stringify(err));
          });

        if (requestSdoId) {
          const templateId = await callGQL<CreateContentTemplateResponse>(
            headers,
            createContentTemplateQuery,
            {
              schemaId: schemaId,
              folderId: createFolder?.treeObjectId,
              sdoId: requestSdoId,
            }
          )
            .then((res) => res.createFolderContentTemplate?.id)
            .catch((err) => {
              Logger.error(Messages.createContentTemplate + JSON.stringify(err));
            });
          if (templateId) {
            // Since template has been created this folder is now a case
            return createFolder.id as CaseId;
          }
        }
      }
    }
    return undefined;
}
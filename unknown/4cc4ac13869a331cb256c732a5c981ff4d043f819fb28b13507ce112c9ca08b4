import { all, fork, put, select, takeEvery } from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';

import { SEND_EMAIL_INVITE } from '../actions';
import { sendEmailInvite } from '../services';

// interface Action<P> {
//   type: string;
//   payload: P;
// }

const {
  config: { getConfig },
  user: { selectUser },
} = modules;

export function* invitesSagas() {
  yield* all([fork(onActionSendEmailInvite)]);
}

function* onActionSendEmailInvite() {
  yield* takeEvery(SEND_EMAIL_INVITE, function* ({ payload }) {
    const currentUser = yield* select(selectUser);
    const { signUpRoute } = yield* select(getConfig<Window['config']>);
    const { emails } = payload;
    const firstName = currentUser.kvp?.firstName || 'Veritone Redact';
    const emailFromAddress = '<EMAIL>';
    const replyTo = '<EMAIL>';
    const subject = 'Welcome to Veritone';
    if (signUpRoute) {
      for (const email of emails) {
        const message = emailInviteTemplate(firstName, signUpRoute);
        yield* put(
          sendEmailInvite({
            email,
            emailFromAddress,
            replyTo,
            subject,
            message,
          })
        );
      }
    }
  });
}

const emailInviteTemplate = (name: string, signUpLink: string) => `
<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta http-equiv='Content-Type' content='text/html; charset=utf-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1' />
    <meta http-equiv='X-UA-Compatible' content='IE=edge' />
</head>

    <style type='text/css'>
        /* CLIENT-SPECIFIC STYLES */
        body,
        table,
        td,
        a {
            -webkit-text-size-adjust: 100%;
            text-size-adjust: 100%;
        }

        table,
        td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            -ms-interpolation-mode: bicubic;
        }

        /* RESET STYLES */
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        table {
            border-collapse: collapse !important;
        }

        body {
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            width: 800px !important;
        }

        /* iOS BLUE LINKS */
        a[x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: none !important;
            font-size: inherit !important;
            font-family: inherit !important;
            font-weight: inherit !important;
            line-height: inherit !important;
        }

        /* MEDIA QUERIES */
        @media screen and (max-width: 480px) {
            .mobile-hide {
                display: none !important;
            }
            .mobile-center {
                text-align: center !important;
            }
        }

        /* ANDROID CENTER FIX */
        div[style*='margin: 16px 0;'] {
            margin: 0 !important;
        }
    </style>

    <body style='margin: 0 !important; padding: 0 !important; width: 800px; background-color: #fafafa '>
        <div style='width: 800px'>
            <!--[if (gte mso 9)|(IE)]>
            <table align='center' border='0' cellspacing='0' cellpadding='0'>
                <tr>
                    <td align='center' valign='top' width='800'>
            <![endif]-->
            <table align='center' border='0' cellpadding='0' cellspacing='0' style='max-width:800px;'>
                <tr style='background-color: #ffffff'>
                    <div valign='top' style='margin-top: 24px; margin-bottom: 19px; background-repeat: no-repeat;background-size: cover;height:45px;width: 250px; background-image:url(https://static.veritone.com/logo/veritone-logo.png);'>
                        <!--[if (gte mso 9)|(IE)]>
                        <center>
                        <v:image xmlns:v='urn:schemas-microsoft-com:vml' fill='true' stroke='false' style=' border: 0;display: inline-block; width: 250px; height: 45px;' src='https://static.veritone.com/logo/veritone-logo.png' />
                        <v:fill  opacity='0%' color='#f7901e'  />
                        <v:textbox inset='0,0,0,0'>
                        </v:textbox>
                        </v:fill>
                        </v:image>
                        </center>
                        <![endif]-->
                    </div>
                </tr>
            </table>

            <table align='center' border='0' cellpadding='0' cellspacing='0' style='max-width:800px; border-top: 5px solid #0288D1;'>
                <tr>
                    <td align='left' style='font-family: Roboto; font-weight: 400; line-height: 24px; padding: 10px 81px 0px 81px;'>
                        <p style='color: #4a4a4a;font-size: 28px;margin-bottom: 60px; text-align: center; font-weight: 500'>
                            You received an invitation to sign up for Redact
                        </p>
                            <span style='color: #999999;font-size: 18px;margin-bottom: 10px; font-family: Roboto'>
                                Hi&nbsp;,
                            </span>
                            <p style='color: #999999;font-size: 18px;margin-bottom: 10px; font-family: Roboto'>
                                Your friend, ${name}, invited you to try Veritone Redact. Click on the button below to sign up.
                            </p>
                    </td>
                </tr>
                <tr>
                    <td align='center' style='padding-top: 30px;'>
                        <!--[if (gte mso 9)|(IE)]>
                                <v:roundrect xmlns:v='urn:schemas-microsoft-com:vml' xmlns:w='urn:schemas-microsoft-com:office:word' href='<%= data.inviteTokenUrl %>' style='height:50px;v-text-anchor:middle;width:300px;' arcsize='8%' stroke='f' fillcolor='#2196F3'>
                                    <w:anchorlock/>
                                    <center>
                                <![endif]-->
                            <a href='${signUpLink}' style='margin-bottom: 100px; background-color:#2196F3;border-radius:4px;color:#ffffff;display:inline-block;font-family:Roboto;font-size:14px;font-weight:500;line-height:50px;text-align:center;text-decoration:none;width:180px;-webkit-text-size-adjust:none;'>
                                SIGN UP
                            </a>
                        <!--[if (gte mso 9)|(IE)]>
                                    </center>
                                </v:roundrect>
                                <![endif]-->
                    </td>
                </tr>
                <!--[if (gte mso 9)|(IE)]>
                <tr>
                    <td style='padding-top: 30px;'>
                    </td>
                </tr>
                <![endif]-->
                <tr>
                    <td align='center' style='font-family: Roboto; font-weight: 400; line-height: 24px; padding: 10px 81px 0px 81px;'>
                        <span style='color: rgba(0,0,0,0.54);font-size: 14px;margin-top: 100px; text-align: center'>
                            If this message was received in error, please disregard or contact: <EMAIL>
                        </span>
                    </td>
                </tr>
            </table>

            <table align='center' border='0' cellpadding='0' cellspacing='0' width='100%' style='max-width:800px; margin-top: 60px; background-color: #ffffff;'>
                <tr>
                    <td align='center' style='font-family: Roboto; font-size: 18px;font-weight: 400; line-height: 24px; padding-top: 15px'>
                        <span style='font-size: 14px;line-height: 18px;color: rgba(0,0,0,0.54);margin-bottom: 0'>
                            Copyright © 2019 Veritone Inc.<br> 575 Anton Blvd Suite 100, Costa Mesa, CA 92626<br> All Rights Reserved<br>
                        </span>
                    </td>
                </tr>
            </table>
            <!--[if (gte mso 9)|(IE)]>
            </td></tr></table>
            <![endif]-->
        </div>

    </body>

</html>
`;

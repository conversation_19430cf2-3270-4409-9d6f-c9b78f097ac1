import { FILTER_PARAMETER_TYPE } from '@helpers/constants';
import { ClusterSegment } from '@common-modules/mediaDetails/models/ClusterItem';

const OverlayPreviewOptions = [
  'redacted',
  'outline',
  'black_fill',
  'none',
] as const;

export type OverlayPreviewOptionsType = (typeof OverlayPreviewOptions)[number];

export function isOverlayPreviewOptionsType(
  preview: string
): preview is OverlayPreviewOptionsType {
  return OverlayPreviewOptions.includes(preview as any);
}

export interface HighlightedOverlay {
  readonly id: string;
  readonly timeMs: number;
  readonly groupId: string;
  readonly type: string;
  readonly newSprayPaintEditId?: string | undefined;
}

export interface OverlayPreviewOption {
  readonly value: OverlayPreviewOptionsType;
  readonly label: 'Redacted' | 'Outline' | 'Blackfill' | 'None';
}
export interface FilterParameters {
  readonly show: Record<FILTER_PARAMETER_TYPE, boolean>;
  readonly remove: Record<FILTER_PARAMETER_TYPE, boolean>;
}

export interface ViewSettings {
  readonly playbackSpeed: number; // 0.25 - 10
  readonly playbackDirection: 'forward' | 'reverse';
  // readonly overlayPreview: 'outline' | 'black_fill' | 'none';
  readonly overlayPreview: OverlayPreviewOptionsType;
  readonly selectedPolyGroups: {
    readonly [id: string]: boolean | undefined;
  };
  readonly clusterMergeGroupIds: {
    readonly [id: string]: boolean;
  };
  readonly clusterMergeSegments: {
    readonly [id: string]: ClusterSegment;
  };
  readonly expandedPolyGroups: {
    readonly [id: string]: boolean | undefined;
  };
  readonly sortPolyGroupsBy: {
    readonly column: 'startTimeMs' | 'autoInterp' | 'manualInterp';
    readonly direction: 'asc' | 'desc';
  };
  readonly filterParameters: FilterParameters;
  readonly showGlobalSettingsModal: boolean;

  readonly highlightedOverlay?: HighlightedOverlay;
  readonly displayUnselectedOverlays?: boolean;
  readonly prevMarkerTime: number;
  readonly draggedOverlayId: string | null;
}

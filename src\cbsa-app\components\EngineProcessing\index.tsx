import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';
import * as styles from './styles.scss';
import { I18nTranslate } from '@i18n';

interface EngineProcessingPropTypes {
  readonly tabName?: string;
  readonly title?: string;
  readonly onGoHomePage?: () => void;
  readonly textColor?: string;
  readonly progressSize?: number;
}

const EngineProcessing = ({
  tabName = ' ',
  title = 'Engine processing...',
  onGoHomePage,
  textColor = 'black',
  progressSize = 60,
}: EngineProcessingPropTypes) => (
  <Grid
    id="engine-processing-root"
    container
    direction="column"
    justifyContent="flex-start"
    alignItems="stretch"
    data-testid="engine-processing-root"
  >
    <Grid data-testid="engine-processing-tab-name" className={styles.tabName}>
      {tabName}
    </Grid>
    <Grid container justifyContent="center">
      <CircularProgress
        data-testid="engine-processing-circular-progress"
        variant="indeterminate"
        disableShrink
        thickness={1}
        size={progressSize}
        classes={{
          colorPrimary: styles.progress,
        }}
      />
    </Grid>
    <Grid style={{ color: textColor }} className={styles.processingText}>
      <Typography
        data-testid="engine-processing-title"
        align="center"
        variant="h6"
        color={'secondary'}
      >
        {title}
      </Typography>
      <Typography
        data-testid="engine-processing-message"
        align="center"
        variant="body2"
        color={'secondary'}
        data-test="typo-body2"
      >
        {I18nTranslate.TranslateMessage('engineProcessingTimeDependDes')}
      </Typography>
      {onGoHomePage ? (
        <Typography
          data-testid="engine-processing-goto-home"
          align="center"
          variant="body2"
          color={'secondary'}
          data-test="typo-body2"
        >
          {I18nTranslate.TranslateMessage('engineProcessingWaitDes1')}
          <br />
          {I18nTranslate.TranslateMessage('engineProcessingWaitDes2')}{' '}
          <span
            className={styles.homeButton}
            onClick={onGoHomePage}
            data-veritone-element="loading-state-home-page-button"
            data-testid="loading-state-home-page-button"
          >
            {I18nTranslate.TranslateMessage('homePage')}
          </span>
        </Typography>
      ) : null}
    </Grid>
  </Grid>
);

export default EngineProcessing;

import { has, uniq } from 'lodash';
import {
  all,
  fork,
  put,
  select,
  takeEvery,
  takeLatest,
} from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';

import { CREATE_ENGINE_JOB as CREATE_OT_JOB } from '../../engines/optical-tracking/actions';
import { CREATE_ENGINE_JOB as CREATE_TRANSCRIPTION_JOB } from '../../engines/transcription/actions';
import {
  CHANGE_CODE,
  CHANGE_SHAPE,
  IN_DOWNLOAD_ASSET,
  IN_REDACT_MEDIA,
  IN_SEND_TO_HEAD_DETECTION,
  SET_DETECTION_TYPE_REDACTION,
  SET_INDIVIDUAL_REDACTION,
} from '@worker';

import {
  DELETE_SELECTED_TDOS,
  UNLINK_SELECTED_TDOS,
  UPSERT_ORGANIZATION_SDO_SUCCESS,
} from '@redact-modules/mainPage/actions';
import {
  actionLog<PERSON>uditEvent,
  DELETE_LAST_ASSET,
  FETCH_UPDATE_ASSET_ACTION,
  LOG_AUDIT_EVENT,
  NEW_OVERLAY,
  PLAY_REDACTED_ASSET,
  REDACT_WORDS,
  UNREDACT_WORDS_LOG,
  RedactWordsRequest,
  RESIZE_OVERLAY_SEGMENT,
  DELETE_DETECTION_OVERLAY,
  DELETE_DETECTION_IN_FRAME,
  DELETE_DETECTION_SEGMENT,
  DELETE_DETECTION_GROUP,
  DELETE_UDR_OVERLAY,
  DELETE_UDR_IN_FRAME,
  DELETE_UDR_SEGMENT,
  DELETE_UDR_GROUP,
  ADD_REDACTION_CODE_TO_TRANSCRIPTION,
  REMOVE_REDACTION_CODE_FROM_TRANSCRIPTION,
  REDACT_SLICE,
  UNREDACT_SLICE,
  actionDisableUnredactSliceLog,
  // UnredactWordsRequest,
} from '../actions';
import {
  selectCurrentPosition,
  selectDisableUnredactAuditLog,
  selectIsFileStatusChanged,
  selectTdoIsChanged,
  selectUdrAsset,
  selectWordsRedacted,
} from '../selectors';
import { sendEvent } from '../services';
import moment from 'moment';
import { GetActionCreatorPayloadT } from '@utils';

const {
  config: { getConfig },
  user: { selectUser },
} = modules;

interface Action<P, M = unknown> {
  type: string;
  payload: P;
  meta: M;
}

export function* auditLogsSaga() {
  yield* all([
    fork(onActionLogAuditEvent),
    fork(watchExternalMediaSentToRedact),
    fork(watchUpdateFileStage),
    fork(watchPlayRedactedAsset),
    fork(watchDeleteLastAsset),
    fork(watchDownloadAsset),
    fork(watchRedactMediaFile),
    fork(watchOpticalTrackingJob),
    fork(watchHeadDetectionJob),
    fork(watchTranscriptionJob),
    fork(watchUnlinkTDOs),
    fork(watchDeleteTDOs),
    fork(watchSelectWordToRedact),
    fork(watchIndividualBlurJob),
    fork(watchIndividualCodeJob),
    fork(watchBulkDetectionRedactionJob),
    fork(watchAddRedactionCodeToTranscription),
    fork(watchRemoveRedactionCodeFromTranscription),
    fork(watchSelectWordsToUnRedact),
    fork(watchTimelineRedact),
    fork(watchTimelineUnredact),
    fork(watchResizeOverlay),
    fork(watchDeleteUDRGroups),
    fork(watchDeleteUDR),
    fork(watchNewOverlay),
    fork(watchDeleteDetection),
    fork(watchChangeShapeBoundingBox),
  ]);
}

export function* onActionLogAuditEvent() {
  yield* takeEvery(LOG_AUDIT_EVENT, function* (action) {
    const { eventSchemaId } = yield* select(getConfig<Window['config']>);
    yield* put(sendEvent(eventSchemaId, action));
  });
}

// ////////////////////////////////////////
// Watch for events to log

function* watchExternalMediaSentToRedact() {
  yield* takeLatest(UPSERT_ORGANIZATION_SDO_SUCCESS, function* (action) {
    if (!has(action, 'meta.variables.id')) {
      yield* put(actionLogAuditEvent('User sent media file to Redact app'));
    }
  });
}

function* watchUpdateFileStage() {
  yield* takeLatest(FETCH_UPDATE_ASSET_ACTION, watchUpdateFileStageHandle);
}

export function* watchUpdateFileStageHandle() {
  const isFileStatusChanged: boolean = yield* select(selectIsFileStatusChanged);
  if (isFileStatusChanged) {
    yield* put(actionLogAuditEvent('Change status'));
  }
  const isTdoChanges: boolean = yield* select(selectTdoIsChanged);
  if (isTdoChanges) {
    yield* put(actionLogAuditEvent('Save'));
  }
}

function* watchPlayRedactedAsset() {
  yield* takeLatest(PLAY_REDACTED_ASSET, function* () {
    yield* put(actionLogAuditEvent('View redacted asset'));
  });
}

function* watchDeleteLastAsset() {
  yield* takeLatest(DELETE_LAST_ASSET, function* () {
    yield* put(actionLogAuditEvent('Delete redacted asset'));
  });
}

export function* watchDownloadAsset() {
  yield* takeLatest(IN_DOWNLOAD_ASSET, function* () {
    yield* put(actionLogAuditEvent('Download redacted asset'));
  });
}

export function* redactMediaFile() {
  yield* put(actionLogAuditEvent('Redact Media File'));
  const words = yield* select(selectWordsRedacted);
  if (words.length > 0) {
    yield* put(actionLogAuditEvent('Redact Audio File'));
  }
}

export function* watchRedactMediaFile() {
  yield* takeLatest(IN_REDACT_MEDIA, redactMediaFile);
}

function* watchHeadDetectionJob() {
  yield* takeLatest(IN_SEND_TO_HEAD_DETECTION, function* () {
    yield* put(actionLogAuditEvent('Head Detection Run'));
  });
}
function* watchOpticalTrackingJob() {
  yield* takeLatest(CREATE_OT_JOB, function* ({ payload }) {
    yield* put(
      actionLogAuditEvent('Optical Tracking Run: ' + JSON.stringify(payload))
    );
  });
}
function* watchIndividualCodeJob() {
  yield* takeLatest(CHANGE_CODE, function* ({ payload }) {
    const currentPosition: number = yield* select(selectCurrentPosition);

    // when multiple groups this gets complicated
    const numGroups =
      payload.udrGroupIds.length + payload.detectionGroups.length;

    if (numGroups === 1) {
      const prevCode = yield* select(selectUdrAsset);

      // use legacy audit log messages
      const groupId =
        payload.udrGroupIds.length > 0
          ? payload.udrGroupIds[0]
          : payload.detectionGroups[0]!.id; // one guarnteed to exist from numGroups check above

      const previousRedactionCode = groupId
        ? prevCode.boundingPolys?.[groupId]?.redactionCode
        : undefined;

      if (payload.redactionCode) {
        if (previousRedactionCode) {
          yield* put(
            actionLogAuditEvent(
              `Activity: "Redaction Code Updated"\nNew Redaction Code:{ Code: ${JSON.stringify(
                payload.redactionCode?.code
              )} Color: ${JSON.stringify(
                payload.redactionCode?.codeColor
              )} Location: ${JSON.stringify(
                payload.redactionCode?.codeLocation
              )} }\nOld Redaction Code:{ Code: ${JSON.stringify(
                previousRedactionCode.code
              )} Color: ${JSON.stringify(
                previousRedactionCode.codeColor
              )} Location: ${JSON.stringify(
                previousRedactionCode.codeLocation
              )} }\nTime point in media: ${currentPosition} ms`
            )
          );
        } else {
          yield* put(
            actionLogAuditEvent(
              `Activity: "Redaction Code Added"\nRedaction Code:{ Code: ${JSON.stringify(
                payload.redactionCode?.code
              )} Color: ${JSON.stringify(
                payload.redactionCode?.codeColor
              )} Location: ${JSON.stringify(
                payload.redactionCode?.codeLocation
              )} }\nTime point in media: ${currentPosition} ms`
            )
          );
        }
      } else if (previousRedactionCode) {
        yield* put(
          actionLogAuditEvent(
            `Activity: "Redaction Code Deleted"\nOld Redaction Code:{ Code: ${JSON.stringify(
              previousRedactionCode.code
            )} Color: ${JSON.stringify(
              previousRedactionCode.codeColor
            )} Location: ${JSON.stringify(
              previousRedactionCode.codeLocation
            )} }\nTime point in media: ${currentPosition} ms`
          )
        );
      }
    } else {
      // merged group
      if (payload.redactionCode) {
        yield* put(
          actionLogAuditEvent(
            `Activity: "Redaction Code Added/Updated for Merged Group"\nRedaction Code:{ Code: ${JSON.stringify(
              payload.redactionCode?.code
            )} Color: ${JSON.stringify(
              payload.redactionCode?.codeColor
            )} Location: ${JSON.stringify(
              payload.redactionCode?.codeLocation
            )} }\nTime point in media: ${currentPosition} ms`
          )
        );
      } else {
        yield* put(
          actionLogAuditEvent(
            `Activity: "Redaction Code Deleted for Merged Group \nTime point in media: ${currentPosition} ms`
          )
        );
      }
    }
  });
}

function* watchIndividualBlurJob() {
  yield* takeLatest(SET_INDIVIDUAL_REDACTION, function* ({ payload }) {
    const currentUser = yield* select(selectUser);
    yield* put(
      actionLogAuditEvent(
        `Datetime: ${moment()}
          User Info: ${currentUser.email} ${currentUser.kvp?.firstName}  ${
            currentUser.kvp?.lastName
          } ${currentUser.userId}
          Activity: "Redaction Configuration changed: ${JSON.stringify(
            payload
          )}"
          Time Range:
            Start Time(ms): ${payload.startTimeMs}
            Stop Time(ms): ${payload.stopTimeMs}`
      )
    );
  });
}

function* watchBulkDetectionRedactionJob() {
  yield* takeLatest(SET_DETECTION_TYPE_REDACTION, function* ({ payload }) {
    const currentUser = yield* select(selectUser);
    yield* put(
      actionLogAuditEvent(
        `Datetime: ${moment()}
          User Info: ${currentUser.email} ${currentUser.kvp?.firstName}  ${
            currentUser.kvp?.lastName
          } ${currentUser.userId}
          Activity: "Redaction Configuration changed: ${JSON.stringify(
            payload
          )}"`
      )
    );
  });
}
function* watchNewOverlay() {
  yield* takeLatest([NEW_OVERLAY], function* ({ payload }) {
    yield* put(
      actionLogAuditEvent('Add Bounding Box: ' + JSON.stringify(payload))
    );
  });
}

function* watchResizeOverlay() {
  yield* takeLatest([RESIZE_OVERLAY_SEGMENT], function* ({ payload }) {
    yield* put(
      actionLogAuditEvent('Resize Overlay Segment: ' + JSON.stringify(payload))
    );
  });
}

function* watchDeleteDetection() {
  yield* takeLatest(
    [DELETE_DETECTION_OVERLAY, DELETE_DETECTION_IN_FRAME],
    function* ({ payload }) {
      yield* put(
        actionLogAuditEvent('Delete Bounding Box: ' + JSON.stringify(payload))
      );
    }
  );
}

function* watchDeleteUDR() {
  yield* takeLatest(
    [DELETE_UDR_OVERLAY, DELETE_UDR_IN_FRAME],
    function* ({ payload }) {
      yield* put(actionLogAuditEvent('Delete UDR: ' + JSON.stringify(payload)));
    }
  );
}

function* watchDeleteUDRGroups() {
  yield* takeLatest(
    [
      DELETE_DETECTION_SEGMENT,
      DELETE_UDR_SEGMENT,
      DELETE_DETECTION_GROUP,
      DELETE_UDR_GROUP,
    ],
    function* ({ payload }) {
      yield* put(
        actionLogAuditEvent(
          'Delete UDR Segment/Group: ' + JSON.stringify(payload)
        )
      );
    }
  );
}

function* watchTranscriptionJob() {
  yield* takeLatest(CREATE_TRANSCRIPTION_JOB, function* () {
    yield* put(actionLogAuditEvent('Transcribe Audio'));
  });
}

function* watchUnlinkTDOs() {
  yield* takeLatest(UNLINK_SELECTED_TDOS, function* () {
    yield* put(actionLogAuditEvent('Unlink TDOs from CMS'));
  });
}

function* watchDeleteTDOs() {
  yield* takeLatest(DELETE_SELECTED_TDOS, function* () {
    yield* put(actionLogAuditEvent('Delete TDOs'));
  });
}

function* handleAudioTranscriptionLog(
  // RedactWordsRequest and UnredactWordsRequest are identical
  action: Action<RedactWordsRequest /* | UnredactWordsRequest */>
) {
  const { payload } = action;
  let uniqueWordsMsg = 'Words set for redaction';
  let detailsMsg = 'Audio Redaction Details';
  if (UNREDACT_WORDS_LOG.match(action)) {
    uniqueWordsMsg = 'Words set for UnRedaction';
    detailsMsg = 'Audio UnRedaction Details';
  }
  const words = uniq(payload.map((t) => t.words)).join(', ');
  yield* put(actionLogAuditEvent(`${uniqueWordsMsg}: ${words}`));
  const wordsWithTimestamp = payload.map((t) =>
    JSON.stringify({
      words: t.words,
      startTimeMs: t.startTimeMs,
      stopTimeMs: t.stopTimeMs,
    })
  );
  yield* put(
    actionLogAuditEvent(`${detailsMsg}: \n ${wordsWithTimestamp.join(',\n')}`)
  );
}

function* watchSelectWordToRedact() {
  yield* takeLatest(REDACT_WORDS, handleAudioTranscriptionLog);
}

function* watchSelectWordsToUnRedact() {
  yield* takeLatest(UNREDACT_WORDS_LOG, handleAudioTranscriptionLog);
}

function* watchAddRedactionCodeToTranscription() {
  yield* takeLatest(
    ADD_REDACTION_CODE_TO_TRANSCRIPTION,
    function* ({ payload }) {
      const { word } = payload;
      const message = word.redactionNotes?.redactionCode
        ? 'Redaction Code Updated'
        : 'Redaction Code Added';

      yield* put(actionLogAuditEvent(`${message} ${JSON.stringify(payload)}`));
    }
  );
}

function* watchRemoveRedactionCodeFromTranscription() {
  yield* takeLatest(
    REMOVE_REDACTION_CODE_FROM_TRANSCRIPTION,
    function* ({ payload }) {
      yield* put(
        actionLogAuditEvent(`Redaction Code Deleted ${JSON.stringify(payload)}`)
      );
    }
  );
}

function* handleTimelineRedactionLogs(
  action: Action<GetActionCreatorPayloadT<typeof REDACT_SLICE>>
) {
  const { payload } = action;
  if ((payload.redact.length || 0) > 0) {
    const timeStamps = payload.redact.map((t) =>
      JSON.stringify({
        startTimeMs: t[0],
        stopTimeMs: t[1],
      })
    );
    yield* put(
      actionLogAuditEvent(
        `Timeline Audio Redaction Details: \n ${timeStamps.join(',\n')}`
      )
    );
    yield* put(actionDisableUnredactSliceLog(false));
  }
}

function* handleTimelineUnredactionLogs(
  action: Action<GetActionCreatorPayloadT<typeof UNREDACT_SLICE>>
) {
  const { payload } = action;
  const isDisableAuditLog = yield* select(selectDisableUnredactAuditLog);
  if ((payload.length || 0) > 0) {
    const timeStamps = payload.map((t) =>
      JSON.stringify({
        startTimeMs: t[0],
        stopTimeMs: t[1],
      })
    );
    if (!isDisableAuditLog) {
      yield* put(
        actionLogAuditEvent(
          `Timeline Audio UnRedaction Details: \n ${timeStamps.join(',\n')}`
        )
      );
      if (REDACT_SLICE.match(action)) {
        yield* put(actionDisableUnredactSliceLog(false));
      }
    }
  }
}

function* watchTimelineRedact() {
  yield* takeLatest([REDACT_SLICE], handleTimelineRedactionLogs);
}

function* watchTimelineUnredact() {
  yield* takeLatest([UNREDACT_SLICE], handleTimelineUnredactionLogs);
}

function* watchChangeShapeBoundingBox() {
  yield* takeLatest(CHANGE_SHAPE, function* ({ payload }) {
    yield* put(
      actionLogAuditEvent(
        'Change Shape of Bounding Box: ' + JSON.stringify(payload)
      )
    );
  });
}

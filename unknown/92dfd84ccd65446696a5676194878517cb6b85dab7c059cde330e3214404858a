import { createAction, createReducer, UnknownAction } from '@reduxjs/toolkit';
import callGraphQLApi from 'src/helpers/callGraph<PERSON>Api';

export const SHOW_TRIM_TOOL = createAction('SHOW_TRIM_TOOL');
export const HIDE_TRIM_TOOL = createAction('HIDE_TRIM_TOOL');

export const CREATE_TDO_FOR_TRIM_VIDEO = 'CREATE_TDO_FOR_TRIM_VIDEO';
export const CREATE_TDO_FOR_TRIM_VIDEO_SUCCESS =
  'CREATE_TDO_FOR_TRIM_VIDEO_SUCCESS';
export const CREATE_TDO_FOR_TRIM_VIDEO_FAILURE =
  'CREATE_TDO_FOR_TRIM_VIDEO_FAILURE';

export const SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO =
  'SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO';
export const SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO_SUCCESS =
  'SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO_SUCCESS';
export const SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO_FAILURE =
  'SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO_FAILURE';

const defaultState = {
  isShowTrimTool: false,
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(SHOW_TRIM_TOOL, (state) => ({
      ...state,
      isShowTrimTool: true,
    }))
    .addCase(HIDE_TRIM_TOOL, (state) => ({
      ...state,
      isShowTrimTool: false,
    }));
});

export default reducer;
export const namespace = 'trim';
interface LocalState {
  [namespace]: {
    isShowTrimTool: boolean;
  };
}
export const local = (state: LocalState) => state[namespace];

export const toggleTrimTool =
  () => (dispatch: (a: UnknownAction) => void, getState: () => any) => {
    const { isShowTrimTool } = local(getState());
    dispatch(isShowTrimTool ? HIDE_TRIM_TOOL() : SHOW_TRIM_TOOL());
  };

export const createTDOForTrimVideo =
  (trimFile: any, runFaceDetection: boolean, runAudioTranscription: boolean) =>
  async (dispatch: (a: UnknownAction) => void, getState: () => any) => {
    const query = `
      mutation {
        createTDOWithAsset(input: {
          startDateTime: "${trimFile.startDateTime}",
          stopDateTime: "${trimFile.endDateTime}",
          uri: "${trimFile.url}",
          name: "${trimFile.name}",
          contentType: "${trimFile.contentType}",
          assetType: "media"
          addToIndex: true,
          details: {
            veritoneProgram: {
              sourceType: "trimmed"
            }
          }
        }) {
          id,
          name
          primaryAsset(assetType:"media") {
            signedUri
          }
        }
      }
      `;
    const body = new FormData();
    body.append('query', query);
    body.append('filename', trimFile.name);
    if (trimFile.blob) {
      body.append('file', trimFile.blob);
    }
    return await callGraphQLApi({
      actionTypes: [
        CREATE_TDO_FOR_TRIM_VIDEO,
        CREATE_TDO_FOR_TRIM_VIDEO_SUCCESS,
        CREATE_TDO_FOR_TRIM_VIDEO_FAILURE,
      ],
      variables: {
        // TODO: Fix types here
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        trimFile,
        runFaceDetection,
        runAudioTranscription,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        hasFaceDetectionEngineResults: trimFile.hasFaceDetectionEngineResults,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        hasAudioTranscriptionEngineResults:
          trimFile.hasAudioTranscriptionEngineResults,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        sourceTDO: trimFile.sourceTDO,
      },
      body: body, // TODO: This isn't supported by callGraphQLApi
      bailout: undefined,
      dispatch,
      getState,
    } as any); // TODO: Fix this
  };

export const isShowTrimTool = (state: any) => local(state).isShowTrimTool;

import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import * as Services from '../services';
import { TreeObjectId } from '@cbsa-modules/universal';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { put, takeEvery, takeLatest } from 'typed-redux-saga/macro';

export function* deleteCase() {
  yield* takeLatest(Actions.DELETE_CASE, function* ({ payload }) {
    const { caseDetails } = payload;
    yield* put(Services.deleteCase({ caseDetails }));
  });
}

export function* deleteCaseSuccess() {
  yield* takeEvery(Actions.DELETE_CASE_SUCCESS, function* ({ payload }) {
    const intl = sagaIntl();

    const cbsa_hideOnceFolders =
      (JSON.parse(
        window.localStorage.getItem('cbsa_hideOnceFolders') || '[]'
      ) as TreeObjectId[]) || [];
    if (payload?.folderTreeObjectId) {
      if (!cbsa_hideOnceFolders.includes(payload.folderTreeObjectId)) {
        cbsa_hideOnceFolders.push(payload.folderTreeObjectId);
        window.localStorage.setItem(
          'cbsa_hideOnceFolders',
          JSON.stringify(cbsa_hideOnceFolders)
        );
      }

      yield* put(
        enqueueSnackbar({
          message: intl.formatMessage({ id: 'caseHasBeenDeleted' }),
          variant: 'success',
        })
      );

      window.location.href = '/';
    }
  });
}

export function* deleteCaseFailure() {
  yield* takeEvery(Actions.DELETE_CASE_FAILURE, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({ id: 'caseNotDeleted' }),
        variant: 'error',
      })
    );
  });
}

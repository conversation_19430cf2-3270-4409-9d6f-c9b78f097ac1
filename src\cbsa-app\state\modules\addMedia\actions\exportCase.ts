import { TDOId, TreeObjectId } from '@cbsa-modules/universal';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import {
  CreateExportTdoResponse,
  StartExportJobResponse,
} from '../services/queries/exportCase';
import { createAction } from '@reduxjs/toolkit';

export const CREATE_EXPORT_TDO = createAction<{
  caseId: TreeObjectId;
}>('CBSA/CREATE_EXPORT_TDO');
export const CREATE_EXPORT_TDO_SUCCESS =
  createGraphQLSuccessAction<CreateExportTdoResponse>(
    'CBSA/CREATE_EXPORT_TDO_SUCCESS'
  );
export const CREATE_EXPORT_TDO_FAILURE = createGraphQLFailureAction(
  'CBSA/CREATE_EXPORT_TDO_FAILURE'
);

export const START_EXPORT_JOB = createAction<{
  caseId: TreeObjectId;
  tdoId: TDOId;
}>('CBSA/START_EXPORT_JOB');
export const START_EXPORT_JOB_SUCCESS =
  createGraphQLSuccessAction<StartExportJobResponse>(
    'CBSA/START_EXPORT_JOB_SUCCESS'
  );
export const START_EXPORT_JOB_FAILURE = createGraphQLFailureAction(
  'CBSA/START_EXPORT_JOB_FAILURE'
);

export const createExportTdo = (caseId: TreeObjectId) =>
  CREATE_EXPORT_TDO({ caseId });

export const startExportJob = ({
  caseId,
  tdoId,
}: {
  caseId: TreeObjectId;
  tdoId: TDOId;
}) => START_EXPORT_JOB({ caseId, tdoId });

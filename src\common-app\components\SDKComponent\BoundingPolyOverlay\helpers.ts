import {
  BoundingPolyRect,
  Point,
  PolyCenter,
  ShapeType,
} from '@common-modules/mediaDetails/models';

export function pixelXYWidthHeightToPercentagePoly(
  { x, y, width, height }: PolyCenter,
  contentWidth: number,
  contentHeight: number
): BoundingPolyRect {
  // translate from internal (x, y, width, height) format to veritone's
  // percentage-based vertex format
  return [
    // top-left
    {
      x: x / contentWidth,
      y: y / contentHeight,
    },
    // top-right
    {
      x: (x + width) / contentWidth,
      y: y / contentHeight,
    },
    // bottom-right
    {
      x: (x + width) / contentWidth,
      y: (y + height) / contentHeight,
    },
    // bottom-left
    {
      x: x / contentWidth,
      y: (y + height) / contentHeight,
    },
  ];
}

export function percentagePolyToPixelXYWidthHeight(
  poly: Point[],
  contentWidth: number,
  contentHeight: number,
  scale: number
) {
  const pixelBoundingBox = scaleBoundingBox(boundingBox(poly), scale).map(
    ({ x, y }) => percentageToPixelCoords({ x, y, contentWidth, contentHeight })
  ) as ReturnType<typeof scaleBoundingBox>;

  return pixelBoundingBoxToXYWidthHeight(pixelBoundingBox);
}

function boundingBox(vertices: Point[]): BoundingPolyRect {
  const xValues = vertices.map(({ x }) => x);
  const yValues = vertices.map(({ y }) => y);

  const minX = Math.min.apply(null, xValues);
  const maxX = Math.max.apply(null, xValues);
  const minY = Math.min.apply(null, yValues);
  const maxY = Math.max.apply(null, yValues);

  return [
    // top-left
    { x: minX, y: minY },
    // top-right
    { x: maxX, y: minY },
    // bottom-right
    { x: maxX, y: maxY },
    // bottom-left
    { x: minX, y: maxY },
  ];
}

function scaleBoundingBox(
  [topLeft, topRight, bottomRight, bottomLeft]: BoundingPolyRect,
  scale: number
): BoundingPolyRect {
  const dx = ((topRight.x - topLeft.x) * scale) / 100 / 2;
  const dy = ((bottomLeft.y - topLeft.y) * scale) / 100 / 2;

  const minX = Math.max(0, Math.min(1, topLeft.x - dx));
  const maxX = Math.max(0, Math.min(1, topRight.x + dx));
  const minY = Math.max(0, Math.min(1, topLeft.y - dy));
  const maxY = Math.max(0, Math.min(1, bottomRight.y + dy));

  return [
    // top-left
    { x: minX, y: minY },
    // top-right
    { x: maxX, y: minY },
    // bottom-right
    { x: maxX, y: maxY },
    // bottom-left
    { x: minX, y: maxY },
  ];
}

function percentageToPixelCoords({
  x,
  y,
  contentWidth,
  contentHeight,
}: Point & {
  contentWidth: number;
  contentHeight: number;
}) {
  return {
    x: x * contentWidth,
    y: y * contentHeight,
  };
}

function pixelBoundingBoxToXYWidthHeight([
  topLeft,
  topRight,
  _bottomRight,
  bottomLeft,
]: BoundingPolyRect): PolyCenter {
  return {
    x: topLeft.x,
    y: topLeft.y,
    width: topRight.x - topLeft.x,
    height: bottomLeft.y - topLeft.y,
  };
}

export const borderRadiusByPosition = ({
  defaultShape,
  position,
  shape,
  size,
  videoDimensions,
}: {
  defaultShape: ShapeType;
  position?: { x: number; y: number };
  shape?: ShapeType;
  size: { height: number; width: number };
  videoDimensions?: { height?: number; width?: number };
}) => {
  if ((shape ?? defaultShape) === 'ellipse') {
    if (
      position?.x !== undefined &&
      position?.y !== undefined &&
      videoDimensions?.width &&
      videoDimensions?.height
    ) {
      const x = Math.round(position.x);
      const y = Math.round(position.y);
      const width = Math.floor(videoDimensions.width - size.width);
      const height = Math.floor(videoDimensions.height - size.height);

      let topLeft = '50%';
      let topRight = '50%';
      let bottomRight = '50%';
      let bottomLeft = '50%';

      if (x === 0) {
        topLeft = '0';
        bottomLeft = '0';
      }
      if (x >= width) {
        topRight = '0';
        bottomRight = '0';
      }

      if (y === 0) {
        topLeft = '0';
        topRight = '0';
      }

      if (y >= height) {
        bottomRight = '0';
        bottomLeft = '0';
      }

      return `${topLeft} ${topRight} ${bottomRight} ${bottomLeft}`;
    }
    return '50%';
  }
  return 0;
};

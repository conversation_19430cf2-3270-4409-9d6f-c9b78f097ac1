import shaka from 'shaka-player';
import {
  CollectionWrapper,
  ProgressResponse,
  GroupedBoundingPoly,
  DataFetchedForDetectionType,
  ClusterMapItem,
} from '@worker';
import { namespace } from './store';
import { SummaryTDO } from '@cbsa-modules/universal';
import {
  AudiowaveFrame,
  ClusterItemGroup,
  DetailTDO,
  MediaComment,
  MediaUser,
  TranscriptionViewable,
  TranscriptionViewState,
  UDRsPolyAsset,
  UDRsPolyAssetGroupSeriesItem,
} from './models';
import { Patch } from 'immer';
import { ActionCreatorWithPayload } from '@reduxjs/toolkit';
import { GetActionCreatorPayloadT } from '@utils';
import {
  REMOVE_COMMENT,
  SET_FILTER_TOGGLE_ALL,
  SET_SHOW_FILTER_TYPE,
  UPDATE_COMMENT,
} from '@common-modules/mediaDetails/actions';
import {
  CANCEL_JOB,
  PROCESS_ENGINE_REQUEST_OVERLAY,
} from '@common-modules/engines/optical-tracking';
import { ExternalIntegrationConfig } from '@helpers/externalIntegrationApi';

export interface DetectionAssetStoreSlice {
  readonly [namespace]: DetectionAssetState;
}

export interface DetectionAssetState {
  readonly loaded: boolean;

  readonly facesSelectedEngineId: string | null;

  readonly detectionClusterGroups: { [groupId: string]: ClusterItemGroup };
  readonly udrClusterGroups: { [groupId: string]: ClusterItemGroup };

  readonly clusterMap: {
    readonly [clusterId: string]: ClusterMapItem;
  };

  readonly isFetchingClusterListData: boolean;
  readonly detectionCollections?: {
    [key: string]: CollectionWrapper | undefined;
  };
  readonly transcription?: TranscriptionViewable;
  readonly transcriptionView: TranscriptionViewState;
  readonly progress: ProgressResponse;

  readonly tdo: DetailTDO | null;
  readonly tdoIsChanged: boolean;

  readonly currentPosition: number;

  readonly disableUnredactAuditLog: boolean; // this indicator is required to skip unredact slice audit log during timeline redaction flow.
  readonly detectionSeriesLengthWithDeleted: undefined | number;
}

export interface FaceDetectionInfo {
  isFetching: boolean;
  isProcessing: boolean;
  isReady: boolean;
  statusMessage: string;
}

interface BasicIntegration {
  authErrorMessage?: string;
  errorMessage?: string;
  successMessage?: string;

  isFormShown: boolean;
  isSendingFile: boolean;
  isStatusDialogShown: boolean;
}
export type GovQAIntegration = BasicIntegration & {
  sessionId?: string;
};

export type FOIAXpressIntegration = BasicIntegration;
export type ExternalIntegration = BasicIntegration & {
  config?: ExternalIntegrationConfig;
};
export type CasepointIntegration = BasicIntegration;
export type ExterroIntegration = BasicIntegration;
export type NuixIntegration = BasicIntegration;

export interface DeletedOverlaysCache {
  overlayIdsMap: { [key: string]: number };
  overlaySubsegmentIdsMap: { [key: string]: number };
}

export interface Extra {
  [param: string]: any;
  readonly user?: MediaUser;
  faceDetection: FaceDetectionInfo;
  readonly udrCollection?: CollectionWrapper;
  udrsState: {
    sprayPaintEdgeUdr?: UDRsPolyAssetGroupSeriesItem;
    udrBoundingPolyBeingUpdated?: GroupedBoundingPoly;
    selectedUDRGroupId?: string;
    udrAsset: UDRsPolyAsset;
    localUDRAsset: UDRsPolyAsset;
    regularModePlaybackSpeed: number;
  };

  readonly hasAudio: boolean;
  readonly videoLoaded: boolean;
  readonly dataFetchedForDetectionType: DataFetchedForDetectionType;

  integrations: {
    govQa: GovQAIntegration;
    foiaXpress: FOIAXpressIntegration;
    casepoint: CasepointIntegration;
    exterro: ExterroIntegration;
    nuix: NuixIntegration;
    externalIntegration: ExternalIntegration;
  };

  audiowaves: AudiowaveFrame[];

  deletedOverlaysCache: DeletedOverlaysCache;

  caseMedia: {
    caseId: string;
    caseName: string;
    images: SummaryTDO[];
    media: SummaryTDO[];
  };

  isCaseMediaDrawerOpen: boolean;

  mediaComments: { [key: string]: MediaComment };
  mediaCommentsUsers: { [key: string]: MediaUser };
  isHideComment: boolean;
  isFetchingComments: boolean;
  selectedMediaComments: string[];
  lastSaveMediaDataDisplayTime?: string;
  isSaveRunning: boolean;
  isSaveFailed: boolean;
  isBasicUserQuerySupported: boolean;
  isPlayerSeeked: boolean;

  tdoReadonly: {
    isLocked: boolean;
    name?: string;
  };
  thumbnailTracks?: shaka.Track | null;
  shakaPlayer?: shaka.Player | null;
}

export interface Undo {
  history: {
    past: MaxDepthArray<
      | {
          patches: Patch[];
          inversePatches: Patch[];
        }
      | {
          action:
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof UPDATE_COMMENT>
                >;
                payload: GetActionCreatorPayloadT<typeof UPDATE_COMMENT>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<
                    typeof PROCESS_ENGINE_REQUEST_OVERLAY
                  >
                >;
                payload: GetActionCreatorPayloadT<
                  typeof PROCESS_ENGINE_REQUEST_OVERLAY
                >;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof SET_FILTER_TOGGLE_ALL>
                >;
                payload: GetActionCreatorPayloadT<typeof SET_FILTER_TOGGLE_ALL>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof SET_SHOW_FILTER_TYPE>
                >;
                payload: GetActionCreatorPayloadT<typeof SET_SHOW_FILTER_TYPE>;
              };
          inverseAction:
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof UPDATE_COMMENT>
                >;
                payload: GetActionCreatorPayloadT<typeof UPDATE_COMMENT>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof REMOVE_COMMENT>
                >;
                payload: GetActionCreatorPayloadT<typeof REMOVE_COMMENT>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof CANCEL_JOB>
                >;
                payload: GetActionCreatorPayloadT<typeof CANCEL_JOB>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof SET_FILTER_TOGGLE_ALL>
                >;
                payload: GetActionCreatorPayloadT<typeof SET_FILTER_TOGGLE_ALL>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof SET_SHOW_FILTER_TYPE>
                >;
                payload: GetActionCreatorPayloadT<typeof SET_SHOW_FILTER_TYPE>;
              };
        }
      | null
    >;
    future: Array<
      | {
          patches: Patch[];
          inversePatches: Patch[];
        }
      | {
          action:
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof UPDATE_COMMENT>
                >;
                payload: GetActionCreatorPayloadT<typeof UPDATE_COMMENT>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<
                    typeof PROCESS_ENGINE_REQUEST_OVERLAY
                  >
                >;
                payload: GetActionCreatorPayloadT<
                  typeof PROCESS_ENGINE_REQUEST_OVERLAY
                >;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof SET_FILTER_TOGGLE_ALL>
                >;
                payload: GetActionCreatorPayloadT<typeof SET_FILTER_TOGGLE_ALL>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof SET_SHOW_FILTER_TYPE>
                >;
                payload: GetActionCreatorPayloadT<typeof SET_SHOW_FILTER_TYPE>;
              };
          inverseAction:
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof UPDATE_COMMENT>
                >;
                payload: GetActionCreatorPayloadT<typeof UPDATE_COMMENT>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof REMOVE_COMMENT>
                >;
                payload: GetActionCreatorPayloadT<typeof REMOVE_COMMENT>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof CANCEL_JOB>
                >;
                payload: GetActionCreatorPayloadT<typeof CANCEL_JOB>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof SET_FILTER_TOGGLE_ALL>
                >;
                payload: GetActionCreatorPayloadT<typeof SET_FILTER_TOGGLE_ALL>;
              }
            | {
                creator: ActionCreatorWithPayload<
                  GetActionCreatorPayloadT<typeof SET_SHOW_FILTER_TYPE>
                >;
                payload: GetActionCreatorPayloadT<typeof SET_SHOW_FILTER_TYPE>;
              };
        }
      | null
    >;
  };
}

export class MaxDepthArray<T> extends Array<T> {
  public static readonly MAX_DEPTH = 20;
  constructor(...elements: T[]) {
    super(...elements);
  }

  public push(value: T): number {
    while (this.length >= MaxDepthArray.MAX_DEPTH) {
      super.shift();
    }
    return super.push(value);
  }
}

Feature: File Detail

  Background: Navigate to a video
    Given The user uploads file "lucy.mp4" with transcription "off"   
    Given The user is on "lucy.mp4" File Detail Screen

  @e2e @mdp @regression
  Scenario Outline: Verify <tabName> File Detail Tabs
    When The user clicks on <tabName> Tab
    Then The user is on <tabName> Tab
    When The user clicks on <tabName> Tab
    Then The user is on <tabName> Tab

    Examples:
      | tabName |
      | 'video' |
      | 'audio' |

  @e2e @mdp
  Scenario: Delete multiple test files
    Given The user deletes files
      | lucy.mp4 |
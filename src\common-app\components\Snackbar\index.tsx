import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useSnackbar, SnackbarKey } from 'notistack';
import { noop } from 'lodash';

import { selectSnackbars, removeSnackbar } from '@common-modules/snackbar';
import ButtonAction from './Button';

let displayed: ReadonlyArray<SnackbarKey> = [];

const Notifier = () => {
  const dispatch = useDispatch();
  const notifications = useSelector(selectSnackbars);
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  const storeDisplayed = (id: Snackbar<PERSON>ey) => {
    displayed = [...displayed, id];
  };

  const removeDisplayed = (id: Snackbar<PERSON>ey) => {
    displayed = [...displayed.filter((key: SnackbarKey) => id !== key)];
  };

  useEffect(() => {
    notifications.forEach(
      ({
        key,
        message,
        variant,
        dismissed = false,
        handleAction = noop,
        action = 'OK',
        ...options
      }) => {
        if (dismissed) {
          // dismiss snackbar using notistack
          closeSnackbar(key);
          return;
        }

        // do nothing if snackbar is already displayed
        if (displayed.includes(key)) {
          return;
        }

        // display snackbar using notistack
        enqueueSnackbar(message, {
          key,
          variant,
          ...options,
          action: (
            <ButtonAction
              keyNoti={key}
              handleAction={handleAction}
              action={action}
            />
          ),
          onExited: (_, myKey: SnackbarKey) => {
            // remove this snackbar from redux store
            dispatch(removeSnackbar(myKey));
            removeDisplayed(myKey);
          },
        });

        // keep track of snackbars that we've displayed
        storeDisplayed(key);
      }
    );
  }, [notifications, closeSnackbar, enqueueSnackbar, dispatch]);

  return null;
};

export default Notifier;

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'notistack';
import { v4 as uuid } from 'uuid';

import { SnackbarAddInterface, SnackbarInterface } from './models';
import { createAction } from '@reduxjs/toolkit';
export const ENQUEUE_SNACKBAR = createAction<{
  notification: SnackbarInterface;
}>('ENQUEUE_SNACKBAR');
export const CLOSE_SNACKBAR = createAction<{
  dismissAll: boolean;
  key?: SnackbarKey;
}>('CLOSE_SNACKBAR');
export const REMOVE_SNACKBAR = createAction<{
  key: SnackbarKey;
}>('REMOVE_SNACKBAR');

export const enqueueSnackbar = (notification: SnackbarAddInterface) =>
  ENQUEUE_SNACKBAR({
    notification: {
      ...notification,
      key: uuid(),
    },
  });

export const closeSnackbar = (key: <PERSON>nack<PERSON><PERSON><PERSON>) =>
  CLOSE_SNACKBAR({
    dismissAll: !key, // dismiss all if no key has been defined
    key,
  });

export const removeSnackbar = (key: Snackbar<PERSON><PERSON>) =>
  REMOVE_SNACKBAR({
    key,
  });

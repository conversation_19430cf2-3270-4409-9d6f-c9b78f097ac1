import { createSelector } from 'reselect';
import { TDOId } from '@common-modules/universal/models/Brands';
import { defaultJobSlice, IngestionStore, namespace } from './store';
import { selectCurrentTdoId } from '../../mediaDetails/selectors';

export const selectStore = (state: { [namespace]: IngestionStore }) =>
  state[namespace];

export const selectAllEngineStatuses = (tdoId: TDOId) =>
  createSelector(selectStore, (s) => s.tdos[tdoId] || {});

export const selectAllEngineStatusesByCurrentTdoId = createSelector(
  selectStore,
  selectCurrentTdoId,
  (s, tdoId) => (tdoId ? s.tdos[tdoId] || {} : {})
);

export const selectEngineStatus = (tdoId: TDOId, jobId: string) =>
  createSelector(
    selectAllEngineStatuses(tdoId),
    (s) => s[jobId] || defaultJobSlice()
  );

export const selectEngineStatusByCurrentTdoId = (jobId: string) =>
  createSelector(
    selectAllEngineStatusesByCurrentTdoId,
    (s) => s[jobId] || defaultJobSlice()
  );

export const selectIngestionOptions = createSelector(
  selectStore,
  ({
    runHead,
    runPerson,
    runTranscription,
  }: {
    runHead: boolean;
    runPerson: boolean;
    runTranscription: boolean;
  }) => ({
    runHead,
    runPerson,
    runTranscription,
  })
);

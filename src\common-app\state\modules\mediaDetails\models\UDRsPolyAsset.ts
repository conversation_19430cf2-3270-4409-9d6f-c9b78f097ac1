import { BoundingPoly } from './BoundingPoly';
import { ShapeType } from '@common-modules/mediaDetails/models/GlobalSettings';
import { IndividualRedactionCode } from '@redact/state/modules/redactionCodes/models';
import { RedactionConfig } from './RedactionPolys';
import { ElementOf } from 'ts-essentials';

/**
 * UDRs as saved in the asset and store.
 * `null` = the group has been deleted.
 */
export interface UDRsPolyAsset {
  readonly selectedUDRGroupId?: string;
  boundingPolys: {
    readonly [groupId: string]: null | UDRsPolyAssetGroup;
  };

  // NOTE: lastSprayPaintSeriesItemId is needed to select correct UDR on the media player
  // after saving spray painted UDRs
  lastSprayPaintSeriesItemId?: string;
}

export interface UDRsPolyAssetGroupSeriesItem {
  readonly id: string;
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
  readonly object: Pick<
    BoundingPoly['object'],
    'id' | 'overlayObjectType' | 'boundingPoly'
  >;
  readonly sprayPaintEditId?: string;
}

/**
 * `series` - Active polys. Likely engine results.
 * `basePoly` - UDR processed by OT engine. Resubmit on successive runs
 */
export interface UDRsPolyAssetGroup {
  readonly userLabel: string;
  readonly groupId: string;
  readonly clusterId?: string;
  readonly status: UDRsPolyAssetStatus;
  readonly jobId?: string;
  readonly basePoly: UDRsBasePoly;
  readonly series: ReadonlyArray<UDRsPolyAssetGroupSeriesItem>;
  readonly shapeType?: ShapeType;
  redactionCode?: IndividualRedactionCode;
  readonly redactionConfig?: RedactionConfig;
  version?: number;
}

export type UDRsBasePoly = ElementOf<UDRsPolyAssetGroup['series']>;

export enum UDRsPolyAssetStatus {
  SINGLE = 'single',
  PENDING = 'pending',
  GROUP = 'group',
}

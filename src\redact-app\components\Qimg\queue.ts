export interface QueueItem<P = unknown> {
  readonly priority: number;
  readonly insertOrder?: number;
  readonly key: string;
  readonly payload: P;
}

export default class QueueManager<P = unknown> {
  private waitingQueue: Required<QueueItem<P>>[] = [];
  private runningQueue: Required<QueueItem<P>>[] = [];
  private resultCache: Record<string, QueueItem<P>> = {};
  private INSERT_ORDER = 0;

  private CACHE_SIZE_LIMIT = 50; // max number of items to cache - we could use use bigger cache after implementing a clear on tdo open/close and switch to a sized based cache instead. If platform caching works becomes more effective then possibly no longer worth further improving
  private cacheOrder: string[] = [];

  add(item: QueueItem<P>): boolean {
    // add item to waiting queue (as long as not in cache)
    if (item.key && !this.getCachedItem(item.key)) {
      this.waitingQueue.push({ ...item, insertOrder: this.INSERT_ORDER++ });

      // do LIFO to prioritize newest items - could make this a parameter
      this.waitingQueue.sort(
        (a, b) => b.priority - a.priority || b.insertOrder - a.insertOrder // FIFO
        // (a, b) => b.priority - a.priority || a.insertOrder - b.insertOrder // LIFO - if we are slow to remove stale items from queue loading most recent (bottom thumbanils) could be better
      );
      return true;
    }
    return false;
  }

  checkForDuplicate(key: string): boolean {
    // check if an item is already in the queue
    let index = this.waitingQueue.findIndex((i) => i.key === key);
    if (index !== -1) {
      return true;
    }
    index = this.runningQueue.findIndex((i) => i.key === key);
    if (index !== -1) {
      return true;
    }
    return false;
  }

  remove(key: string): boolean {
    // remove item from queue
    const index = this.waitingQueue.findIndex((i) => i.key === key);
    if (index !== -1) {
      this.waitingQueue.splice(index, 1);
    }
    return index !== -1;
  }

  removeFromCache(key: string): boolean {
    const cachedItem = this.resultCache[key];
    if (cachedItem) {
      delete this.resultCache[key];
      return true;
    }
    return false;
  }

  nextItem() {
    // find next queued item and move to the runningQueue
    const item = this.waitingQueue.pop();
    if (item) {
      // move to the running queue
      this.runningQueue.push(item);

      // return item in case caller needs to do something with it
      return item;
    }
  }

  completeItem(key: string, cache?: boolean) {
    // item has finishing running
    const index = this.runningQueue.findIndex((i) => i.key === key);
    if (index === -1) {
      return;
    }

    const item = this.runningQueue[index];
    if (!item) {
      return;
    }

    if (cache) {
      this.resultCache[item.key] = item;
      this.cacheOrder.push(item.key);

      // clean up cache if getting too large
      if (this.cacheOrder.length > this.CACHE_SIZE_LIMIT) {
        const popCacheKey = this.cacheOrder.shift();
        if (popCacheKey) {
          this.removeFromCache(popCacheKey);
        }
      }
    }

    this.runningQueue.splice(index, 1);
  }

  getCachedItem(key: string): QueueItem<P> | undefined {
    return this.resultCache[key];
  }

  waitingCount() {
    return this.waitingQueue.length;
  }

  runningCount() {
    return this.runningQueue.length;
  }

  clear() {
    Object.keys(this.resultCache).forEach((k) => delete this.resultCache[k]);
    this.cacheOrder = [];
  }
}

import { forEach, map } from 'lodash';
import Konva from 'konva';
import {
  UDRResizeContainerMap,
  UDRResizeContainer,
} from './UDRGroupRowComponent';

export function renderTimeline(
  layer: Konva.Layer,
  udrResizeContainerMap: UDRResizeContainerMap,
  opts: {
    onClick: (id: string) => any;
  }
) {
  layer.destroyChildren();

  const rectsData = map(
    udrResizeContainerMap,
    (udrResizeContainer: UDRResizeContainer) => {
      const { startPx, isSelected } = udrResizeContainer;

      const rect = new Konva.Rect({
        x: startPx,
        y: 0,
        fill: 'rgb(255, 235, 59)',
        stroke: 'rgb(204, 183, 2)',
        opacity: isSelected ? 1 : 0.1,
      });

      return { rect, ...udrResizeContainer };
    }
  );

  forEach(rectsData, ({ rect, udrId, startPx, stopPx }) => {
    rect.setSize({ width: stopPx - startPx, height: 32 });
    layer.add(rect);
    rect.draw();

    rect.on('click', () => {
      opts.onClick(udrId);
    });
  });

  return {
    layer,
    removeEventListeners: () => {
      forEach(rectsData, ({ rect }) => {
        rect.off('click');
      });
    },
  };
}

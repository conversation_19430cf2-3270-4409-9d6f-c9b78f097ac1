import { DETECTION_OBJECT_TYPE } from '@helpers/constants';
import { ShapeType } from '@common-modules/mediaDetails/models/GlobalSettings';
import { RedactionConfig } from '@common-modules/mediaDetails/models/RedactionPolys';
import { IndividualRedactionCode } from '@redact/state/modules/redactionCodes/models';

export type OverlayObjectType =
  | 'detection'
  | 'udr'
  | 'interpolation'
  | 'spray_paint_udr';

export interface BoundingPolyObject {
  readonly id: string;
  readonly label: string; // TODO should eventually remove this
  readonly overlayObjectType: OverlayObjectType;
  readonly uri?: string;
  readonly boundingPoly: BoundingPolyRect;
  readonly isDeleted: boolean;
  readonly isEditable?: boolean;
  readonly confidence: number;
  // readonly type: DETECTION_OBJECT_TYPE;
  type: DETECTION_OBJECT_TYPE; // HACK need to modify the type in detection.ts for poim
}

export interface BoundingPoly {
  readonly id: string;
  // readonly engineId: string; // no reason to save this
  readonly subsegmentId: string;
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
  readonly size: { width: number; height: number };
  readonly object: BoundingPolyObject;
  shapeType?: ShapeType;
  redactionCode?: IndividualRedactionCode;
  redactionConfig?: RedactionConfig;
  version?: number;
}

export type BoundingPolyRect = [Point, Point, Point, Point];

export interface Point {
  readonly x: number;
  readonly y: number;
}

export interface EditablePoint {
  x: number;
  y: number;
}

export type PolyCenter = Point & {
  readonly width: number;
  readonly height: number;
};

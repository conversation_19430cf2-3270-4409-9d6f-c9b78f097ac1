import { connect } from 'react-redux';
import { createSelector } from 'reselect';

import { actionOpenUpgradeAccount } from '../../actions';
import {
  selectHasBusinessSubscription,
  selectHasEnterprise,
  selectHasFreeTrial,
  selectMediaProcessedMs,
  selectMediaProcessedRemainingMs,
} from '../../selectors';
import UserUpgradeBarView from './UserUpgradeBarView';

const UserUpgradeBar = ({
  isEnterprise,
  isFreeTrialUser,
  isSubscriber,
  mediaProcessedMs,
  mediaProcessingRemainingMs,
  onUpgradeAccount,
}: UserUpgradeBarPropTypes) =>
  !isEnterprise ? (
    <UserUpgradeBarView
      {...{
        isEnterprise,
        isFreeTrialUser,
        isSubscriber,
        mediaProcessedMs,
        mediaProcessingRemainingMs,
        onUpgradeAccount,
      }}
    />
  ) : null;

export default connect(
  createSelector(
    selectHasEnterprise,
    selectHasFreeTrial,
    selectHasBusinessSubscription,
    selectMediaProcessedMs,
    selectMediaProcessedRemainingMs,
    (
      isEnterprise,
      isFreeTrialUser,
      isSubscriber,
      mediaProcessedMs,
      mediaProcessingRemainingMs
    ) => ({
      isEnterprise,
      isFreeTrialUser,
      isSubscriber,
      mediaProcessedMs,
      mediaProcessingRemainingMs,
    })
  ),
  {
    onUpgradeAccount: actionOpenUpgradeAccount,
  }
)(UserUpgradeBar);

export interface UserUpgradeBarPropTypes {
  readonly isEnterprise: boolean;
  readonly isFreeTrialUser: boolean;
  readonly isSubscriber: boolean;
  readonly mediaProcessedMs: number;
  readonly mediaProcessingRemainingMs: number;
  readonly onUpgradeAccount: typeof actionOpenUpgradeAccount;
}

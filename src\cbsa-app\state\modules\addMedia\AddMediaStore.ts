import type {
  SummaryTDO,
  StructuredDataObject,
  AuditEvent,
  Case,
  TDOId,
  TreeObjectId,
} from '@cbsa-modules/universal';

type NotificationStatus = 'new' | 'dismissed' | 'failed';

type RedactJobType =
  | 'redaction'
  | 'headDetection'
  | 'bodyDetection'
  | 'personLocation'
  | 'personOfInterest';

export interface AIWareNotification {
  readonly id: string;
  readonly data: {
    readonly batchId: string;
    readonly message: string;
    readonly caseName: string;
    readonly createdDateTime: string;
    readonly status: NotificationStatus;
    readonly folderTreeObjectId: TreeObjectId;
    readonly notificationType: RedactJobType;
  };
}

export interface AddMediaStore {
  readonly caseDetails?: Case | null;
  readonly tdos: { readonly [id: string]: SummaryTDO };
  readonly sdos: { readonly [id: string]: StructuredDataObject };
  readonly notifications: Array<AIWareNotification>;
  readonly selectedTdoImages: { readonly [id: number]: string };
  readonly selectedTdoMedia: { readonly [id: TDOId]: boolean };
  readonly loaders: {
    readonly isLoadingCaseDetails: boolean;
    readonly isLoadingCaseTdosInitial: boolean;
    readonly isUpdatingCaseName: boolean;
    readonly isProcessingCase: boolean;
    readonly isDeletingCase: boolean;
  };
  readonly auditEvents: {
    readonly [tdoId: TDOId]: AuditEvent[];
  };
  readonly auditLogQueryPending: number;
}

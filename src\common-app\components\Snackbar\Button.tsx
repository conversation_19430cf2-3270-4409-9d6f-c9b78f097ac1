import { useDispatch } from 'react-redux';
import { Button } from '@mui/material';
import { Snackbar<PERSON>ey } from 'notistack';
import { closeSnackbar } from '@common-modules/snackbar';
import * as styles from './index.scss';

const ButtonAction = ({
  handleAction,
  keyNoti,
  action,
}: {
  keyNoti: Snackbar<PERSON>ey;
  action: string;
  handleAction: () => any;
}) => {
  const dispatch = useDispatch();

  const handleClick = () => {
    handleAction();
    dispatch(closeSnackbar(keyNoti));
  };

  return (
    <Button
      className={styles.snackbarButton}
      color="primary"
      size="small"
      onClick={handleClick}
    >
      {action}
    </Button>
  );
};

export default ButtonAction;

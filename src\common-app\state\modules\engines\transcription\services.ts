const { user } = modules;
import { modules } from '@veritone/glc-redux';
import { TDOId } from '@common-modules/universal/models/Brands';
import { selectConfigEngines } from '../selectors';
import callGraph<PERSON><PERSON><PERSON>, {
  GQLApiDispatch,
  GQLApiGetState,
} from '@helpers/callGraphQLApi';

import {
  NOOP,
  QUERY_ENGINE_RESULTS_SUCCESS,
  QUERY_ENGINE_RESULTS_FAILURE,
  CANCEL_JOB_FAILURE,
  CANCEL_JOB_SUCCESS,
  CREATE_ENGINE_JOB_SUCCESS,
  CREATE_ENGINE_JOB_FAILURE,
  CHECK_JOB_STATUS_FAILURE,
  CHECK_JOB_STATUS_SUCCESS,
} from './actions';
import { DAG_REFERENCE_IDS } from '@helpers/constants';

/**
 * Load engine results from the JSON file.
 * @param {{ signedUri: string }} payload
 */
// export const getEngineResultsFromURIService = ({ signedUri }) => async (
//   dispatch,
//   getState
// ) => {
//   return await callApi({
//     signedUri,
//     actionTypes: [
//       NOOP,
//       GET_ENGINE_RESULTS_FROM_URI_SUCCESS,
//       GET_ENGINE_RESULTS_FROM_URI_FAILURE,
//     ],
//     dispatch,
//     getState,
//     bailout: undefined,
//   });
// };

/**
 * Query for the engine results to get the JSON data URI.
 * @param {{ tdoId: TDOId }} payload
 */
// export const queryEngineResultsRecordService = ({ tdoId }) => async (
//   dispatch,
//   getState
// ) => {
//   const query = `
//     query($id: ID!) {
//       temporalDataObject(id: $id) {
//         assets(type: "redacted-transcription-result" orderBy: createdDateTime limit: 1) {
//           records {
//             signedUri
//           }
//         }
//       }
//     }
//   `;

//   return await callGraphQLApi({
//     actionTypes: [
//       NOOP,
//       QUERY_ENGINE_RESULTS_RECORD_SUCCESS,
//       QUERY_ENGINE_RESULTS_RECORD_FAILURE,
//     ],
//     query,
//     variables: { id: tdoId },
//     dispatch,
//     getState,
//   });
// };

export interface QueryEngineResultsServiceResponse {
  engineResults: {
    records: {
      tdoId: string;
      engineId: string;
      startOffsetMs: number;
      stopOffsetMs: number;
      jsondata: Record<string, any>;
    }[];
  };
}
export const queryEngineResultsService =
  ({
    tdoId,
    engines,
    mediaDuration,
  }: {
    tdoId: TDOId;
    engines: string[];
    mediaDuration: number;
  }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const query = `
      query($tdoId: ID!, $engines: [ID!], $startOffset: Int, $stopOffset: Int) {
        engineResults(tdoId: $tdoId, engineIds: $engines, startOffsetMs: 0, stopOffsetMs: $stopOffset) {
          records {
            tdoId
            engineId
            startOffsetMs
            stopOffsetMs
            jsondata
          }
        }
      }
    `;
    return await callGraphQLApi<QueryEngineResultsServiceResponse>({
      actionTypes: [
        NOOP,
        QUERY_ENGINE_RESULTS_SUCCESS,
        QUERY_ENGINE_RESULTS_FAILURE,
      ],
      query,
      variables: { tdoId, engines, stopOffset: mediaDuration },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

/**
 * Create an transcription job.
 * @param {{ engineId: string; payload: { targetId: string; url: string } }} payload
 */
export const createEngineJobService =
  ({
    tdoId,
    tdoName,
    payload,
  }: {
    tdoId: TDOId;
    tdoName: string;
    payload: {
      targetId: string;
      url: string;
    };
  }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    // TODO: type the return of getState properly
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const state = getState();
    const {
      glcIngestionEngineId,
      transcriptionEngineId,
      transcriptionEngineOptions,
      defaultClusterId,
      outputWriterEngineId,
    } = selectConfigEngines(state);

    const email = user.selectUser(state).email;

    // TODO replace this with the generalized generateTasksAndRoutes
    const query = transcriptionJobV3f({
      defaultClusterId,
      payload,
      glcIngestionEngineId,
      outputWriterEngineId,
    });

    return await callGraphQLApi<{
      createJob: {
        id: string;
        targetId: TDOId;
        status: string;
      };
    }>({
      actionTypes: [NOOP, CREATE_ENGINE_JOB_SUCCESS, CREATE_ENGINE_JOB_FAILURE],
      query,
      variables: {
        id: tdoId,
        tdoName,
        engineId: transcriptionEngineId,
        payload: Object.assign({}, transcriptionEngineOptions, {
          ...payload,
          diarise: 'false',
          user: email,
          // Used for email notifications flow in Automate to filter tasks only from REDACT app. DONOT REMOVE.
          app: 'redact',
        }),
      },
      dispatch,
      getState,
    });
  };

const transcriptionJobV3f = ({
  defaultClusterId,
  payload,
  glcIngestionEngineId,
  outputWriterEngineId,
}: {
  defaultClusterId?: string;
  payload: {
    targetId: string;
    url: string;
  };
  glcIngestionEngineId?: string;
  outputWriterEngineId: string;
}) => `
    mutation ($id: ID!, $engineId: ID!, $payload: JSONData!){
     createJob(input: {
        targetId: $id,
        clusterId: "${defaultClusterId ?? ''}",
        tasks: [
          ${ingestionTask({
            glcIngestionEngineId,
            payload,
          })}
          {
            engineId: $engineId,
            payload: $payload,
            executionPreferences: {
              maxEngines: 10
              parentCompleteBeforeStarting: true
              priority: -5
            }
            ioFolders: [
              {
                referenceId: "transcriptionInputFolder"
                mode: chunk
                type: input
              },
              {
                referenceId: "transcriptionOutputFolder"
                mode: chunk
                type: output
              }
            ]
          },
          {
            engineId: "${outputWriterEngineId}"  ## output writer for SM
            executionPreferences: {
              parentCompleteBeforeStarting: true
              priority: -15
            }
            ioFolders: [
              {
                referenceId: "${DAG_REFERENCE_IDS['owFromTranscription']}"
                mode: chunk
                type: input
              }
            ]
          }
        ],
        routes: [
          {  ## chunkAudio --> Transcription
            parentIoFolderReferenceId: "chunkAudioOutputFolder"
            childIoFolderReferenceId: "transcriptionInputFolder"
            options: {}
          },
          {  ## Transcription --> output writer
            parentIoFolderReferenceId: "transcriptionOutputFolder"
            childIoFolderReferenceId: "${
              DAG_REFERENCE_IDS['owFromTranscription']
            }"
            options: {}
          }
        ]
      }) {
        id
        targetId
        status
      }
    }
  `;

// using glc ingestion engine for chunking avoids some issues with the glc chunker (and uses the higher quality aac version for audio only files)
export const ingestionTask = ({
  glcIngestionEngineId,
  payload,
}: {
  glcIngestionEngineId?: string;
  payload: {
    targetId: string;
    url: string;
  };
}) => `
    {
      engineId: "${glcIngestionEngineId}"
      payload: {
        comment: "Fast Audio Chunk"
        app: "redact"
        chunkType: "audio"
        url: "${payload.url}"
        chunkDuration: 180
        chunkOverlap: 3
        playback: false
        zeropadOffsetAudio: true
      }
      executionPreferences: {
        parentCompleteBeforeStarting: true
        priority: -20
      }
      ioFolders: [
        {
          referenceId: "chunkAudioOutputFolder"
          mode: chunk
          type: output
        }
      ]
    }
  `;

/**
 * Cancel a running job.
 * @param {{ jobId: string }} action
 */
export interface CancelJobServiceResponse {
  cancelJob: {
    id: string;
    message: string;
  };
}
export const cancelJobService =
  ({ jobId }: { jobId: string }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const query = `
      mutation cancelJob($id: ID!) {
        cancelJob(id: $id){
          id
          message
        }
      }
    `;

    return await callGraphQLApi<CancelJobServiceResponse>({
      actionTypes: [NOOP, CANCEL_JOB_SUCCESS, CANCEL_JOB_FAILURE],
      query,
      variables: { id: jobId },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

/**
 * Get the status of a job.
 * @param {{ jobId: string }} action
 */
export const checkJobStatusService =
  ({ jobId }: { jobId: string }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const query = `query($jobId: ID!) {
      job(id: $jobId) {
        id
        targetId
        status
      }
    }`;

    return await callGraphQLApi({
      actionTypes: [NOOP, CHECK_JOB_STATUS_SUCCESS, CHECK_JOB_STATUS_FAILURE],
      query,
      variables: { jobId },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

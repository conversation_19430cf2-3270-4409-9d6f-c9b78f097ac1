import { CaseId, TDOId } from '@common-modules/universal/models/Brands';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { createAction } from '@reduxjs/toolkit';
import { FetchCaseMediaResponse } from '../services';
export const FETCH_CASE_MEDIA = 'CBSA/FETCH_CASE_MEDIA';
export const FETCH_CASE_MEDIA_SUCCESS =
  createGraphQLSuccessAction<FetchCaseMediaResponse>(
    'CBSA/FETCH_CASE_MEDIA_SUCCESS'
  );
export const FETCH_CASE_MEDIA_FAILURE = createGraphQLFailureAction(
  'CBSA/FETCH_CASE_MEDIA_FAILURE'
);

export const DELETE_CASE_DRAWER_TDO_REQUEST =
  createAction<DeleteCaseDrawerPayload>(
    'REDACT/DELETE_CASE_DRAWER_TDO_REQUEST'
  );
export const DELETE_CASE_DRAWER_TDO_SUCCESS =
  createAction<DeleteCaseDrawerPayload>(
    'REDACT/DELETE_CASE_DRAWER_TDO_SUCCESS'
  );

export const fetchCaseMedia = (caseId: CaseId) => ({
  type: FETCH_CASE_MEDIA,
  payload: { caseId },
});

export interface DeleteCaseDrawerPayload {
  tdoId: TDOId;
  fileName: string;
  isActiveFile: boolean;
}
export const actionDeleteCaseDrawerTdo = (payload: DeleteCaseDrawerPayload) =>
  DELETE_CASE_DRAWER_TDO_REQUEST(payload);

export const actionDeleteCaseDrawerSuccess = (
  payload: DeleteCaseDrawerPayload
) => DELETE_CASE_DRAWER_TDO_SUCCESS(payload);

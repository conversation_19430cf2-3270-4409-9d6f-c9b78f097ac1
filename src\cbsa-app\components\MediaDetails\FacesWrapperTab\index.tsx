import { ThemeProvider, StyledEngineProvider } from '@mui/material';
import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { createSelector } from 'reselect';

import EngineProcessing from '@cbsa-components/EngineProcessing';
import ClustersResultsTab from '@cbsa-components/MediaDetails/FacesWrapperTab/ClustersResultsTab';
import TabErrorState from '@cbsa-components/MediaDetails/FacesWrapperTab/FacesErrorStateTab';
import TabNullState from '@cbsa-components/MediaDetails/FacesWrapperTab/FacesNullStateTab';
import {
  REDIRECT_TO_HOME,
  selectClusterList,
  selectDetectionFailed,
  selectDetectionRunning,
  selectDisableFacesButton,
  actionUnDisableFacesButton,
  actionFetchEngineResultsAfterDetect,
} from '@common-modules/mediaDetails';

import { buttonTheme } from '@cbsa/styles/materialThemes';

const FacesWrapperTab = () => {
  const { runningTasks, failedTasks, list, buttonDetectFaceDisable } =
    useSelector(componentSelectors);
  const dispatch = useDispatch();
  const [checkRunningTasks, setCheckRunningTasks] = useState<boolean>(false);
  useEffect(() => {
    if (runningTasks.length && buttonDetectFaceDisable) {
      dispatch(componentActions.unDisableFacesButton());
    }
  });

  useEffect(() => {
    if (runningTasks.length && !checkRunningTasks) {
      setCheckRunningTasks(true);
    } else if (!runningTasks.length && checkRunningTasks) {
      setCheckRunningTasks(false);
      dispatch(componentActions.fetchEngineResults());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, runningTasks]);

  if (list.length > 0) {
    return (
      <StyledEngineProvider injectFirst>
        <ThemeProvider theme={buttonTheme}>
          <ClustersResultsTab />
        </ThemeProvider>
      </StyledEngineProvider>
    );
  }

  if (runningTasks.length > 0) {
    return (
      <EngineProcessing
        tabName="Object Detection"
        title="Detecting Objects..."
        onGoHomePage={() => {
          dispatch(componentActions.onGoHomePage());
        }}
      />
    );
  }

  if (failedTasks.length > 0) {
    return <TabErrorState />;
  }

  return <TabNullState />;
};

const componentSelectors = createSelector(
  selectDetectionRunning,
  selectDetectionFailed,
  selectClusterList,
  selectDisableFacesButton,
  (runningTasks, failedTasks, list, buttonDetectFaceDisable) => ({
    runningTasks,
    failedTasks,
    list,
    buttonDetectFaceDisable,
  })
);

const componentActions = {
  onGoHomePage: () => REDIRECT_TO_HOME(),
  unDisableFacesButton: actionUnDisableFacesButton,
  fetchEngineResults: actionFetchEngineResultsAfterDetect,
};

export default FacesWrapperTab;

import { find } from 'lodash';
import { getOffset } from '@helpers/mouseUtils';
import {
  Fragment,
  memo,
  MouseEvent,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { AudioRedactionSlice } from '@common-modules/mediaDetails/models';
import TimelineRangeEditorPopup from '../TimelineRangeEditorPopup';
import { mapRender } from './mapRender';
import { renderer } from './renderer';
import { TranscriptionPropTypes } from './TranscriptionPropTypes';

function getClickedMediaTime(
  evt: MouseEvent,
  currentTarget: Element,
  startWindowMs: number,
  stopWindowMs: number
): number {
  const offset = getOffset(evt);
  const clientRect = currentTarget.getBoundingClientRect();
  const offsetRatio = offset.x / clientRect.width;
  const range = stopWindowMs - startWindowMs;
  return Math.floor(startWindowMs + offsetRatio * range);
}

const Transcription = (props: TranscriptionPropTypes) => {
  const {
    mediaDuration,
    list: transcriptRedactions,
    selected: selectedTimeSlices,
    onUnredactSlice,
    onRedactSlice,
    onSelectSlice,
    onDeselectAll,
    transcriptionList,
  } = props;
  const ref = useRef<HTMLDivElement>(null);

  const [afterFirstRender, setAfterFirstRender] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState(false);
  const [timeRange, setTimeRange] = useState<AudioRedactionSlice>([
    0,
    0,
    undefined,
  ]);
  const [anchorPosition, setAnchorPosition] = useState({ top: 0, left: 0 });
  const [sliceClicked, setSliceClicked] = useState<AudioRedactionSlice>([
    0,
    0,
    undefined,
  ]);
  const [allowMove, setAllowMove] = useState<boolean>(false);
  const [timeClickMs, setTimeClickMs] = useState<number>(0);
  // Used for re-rendering if user changes the zoom level of the browser window
  const [effectiveWindowPixelWidth, setEffectiveWindowPixelWidth] =
    useState<number>(window.innerWidth);

  const { render, destroy } = useMemo(
    () => renderer(ref, mapRender),
    [afterFirstRender, effectiveWindowPixelWidth] // eslint-disable-line react-hooks/exhaustive-deps
  );

  useLayoutEffect(() => render(props, timeRange));

  useEffect(() => {
    setAfterFirstRender(true);
    return () => destroy();
  }, [destroy]);

  useEffect(() => {
    const slice = selectedTimeSlices[0];
    if (slice) {
      setTimeRange(slice);
    }
  }, [selectedTimeSlices]);

  const setWindowSize = () => {
    setEffectiveWindowPixelWidth(window.innerWidth);
  };

  useEffect(() => {
    window.addEventListener('resize', setWindowSize);

    return () => window.removeEventListener('resize', setWindowSize);
  }, []);

  const onSetRangeTime = (range: AudioRedactionSlice) => {
    onRedactSlice({
      redact: [range],
      unredact: range[1] !== sliceClicked[0] ? [sliceClicked] : undefined,
    });
    handleManualRedactClose();
    onDeselectAll();
  };

  const handleUnredactSlice = (range: AudioRedactionSlice) => {
    onUnredactSlice([range]);
    handleManualRedactClose();
  };

  const handleManualRedactClose = () => {
    setIsOpen(false);
    setTimeRange([0, 0, undefined]);
    onDeselectAll();
  };

  const onMouseUp = () => {
    setAllowMove(false);
  };

  const onMouseDown = (evt: MouseEvent) => {
    const { nativeEvent, currentTarget } = evt;
    // Time click (ms)
    const { startWindowMs, stopWindowMs } = props;
    const clickedMediaTime = getClickedMediaTime(
      evt,
      currentTarget,
      startWindowMs,
      stopWindowMs
    );
    // Find slice was clicked
    const sliceClickedExisted =
      find(
        selectedTimeSlices,
        ([s, e]) => clickedMediaTime >= s && clickedMediaTime <= e
      ) ||
      find(
        transcriptRedactions,
        ([s, e]) => clickedMediaTime >= s && clickedMediaTime <= e
      );

    if (sliceClickedExisted) {
      setSliceClicked(sliceClickedExisted);
      setTimeClickMs(sliceClickedExisted[0]);
    } else {
      setSliceClicked([
        clickedMediaTime,
        Math.min(clickedMediaTime + 500, mediaDuration * 1000),
        undefined,
      ]);
      setTimeClickMs(clickedMediaTime);
    }

    setAnchorPosition({
      top: currentTarget.getBoundingClientRect().top - 135,
      left: nativeEvent.clientX,
    });
    setIsOpen(true);
    setTimeRange(
      sliceClickedExisted || [
        clickedMediaTime,
        Math.min(clickedMediaTime + 500, mediaDuration * 1000),
        undefined,
      ]
    );
    setAllowMove(true);
  };

  const createNewSelectSlice = (time: number): AudioRedactionSlice => {
    // const newSlice = Object.assign([], sliceClicked);
    if (time < sliceClicked[0]) {
      return Object.assign([], sliceClicked, [time, sliceClicked[0]]);
    } else {
      return Object.assign([], sliceClicked, [sliceClicked[0], time]);
    }
    // if (time < newSlice[0]) {
    //   newSlice[1] = newSlice[0];
    //   newSlice[0] = time;
    // } else {
    //   newSlice[1] = time;
    // }
    // return newSlice;
  };

  const onChangeTimeRange = (time: number) => {
    // const newTimeRange = Object.assign([], timeRange);
    let newTimeRange: AudioRedactionSlice;
    if (time < timeClickMs) {
      newTimeRange = [time, timeClickMs, timeRange[2]];
    } else {
      newTimeRange = [timeClickMs, time, timeRange[2]];
    }
    // if (time < timeClickMs) {
    //   newTimeRange[0] = time;
    //   newTimeRange[1] = timeClickMs;
    // } else {
    //   newTimeRange[0] = timeClickMs;
    //   newTimeRange[1] = time;
    // }
    setTimeRange(newTimeRange);
  };

  const onMouseMove = (evt: MouseEvent) => {
    if (!allowMove) {
      return;
    }

    const { currentTarget } = evt;

    const { startWindowMs, stopWindowMs } = props;
    const clickedMediaTime = getClickedMediaTime(
      evt,
      currentTarget,
      startWindowMs,
      stopWindowMs
    );

    if (transcriptionList.length > 0) {
      // Transcription has been run
      const newSlice = createNewSelectSlice(clickedMediaTime);
      return onSelectSlice(newSlice);
    }
    // Transcription has not been run
    return onChangeTimeRange(clickedMediaTime);
  };

  const onMouseLeave = () => {
    setAllowMove(false);
  };

  return (
    <Fragment>
      <div
        id="transcription-container"
        data-testid="timeline-transcription"
        ref={ref}
        onMouseUp={onMouseUp}
        onMouseDown={onMouseDown}
        onMouseMove={onMouseMove}
        onMouseLeave={onMouseLeave}
        style={allowMove ? { zIndex: 102 } : {}}
      />
      <TimelineRangeEditorPopup
        minMs={0}
        maxMs={mediaDuration * 1000}
        timeRange={timeRange}
        anchorPosition={anchorPosition}
        isOpen={isOpen}
        hideGroupButton={allowMove}
        onCancel={handleManualRedactClose}
        onSetRangeTime={onSetRangeTime}
        onUnredactSlice={handleUnredactSlice}
      />
    </Fragment>
  );
};

export default memo(Transcription);

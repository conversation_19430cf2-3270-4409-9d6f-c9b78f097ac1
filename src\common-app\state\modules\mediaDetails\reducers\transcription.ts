import { get, omit, sortBy, uniqBy } from 'lodash';
import { CaseReducer, createReducer } from '@reduxjs/toolkit';

import { notNil } from '@utils';
import * as actions from '../actions';
import {
  AudioRedactionSlice,
  AudioRedactionDetails,
  TranscriptionViewableWords,
} from '../models';
import { defaultState } from '../store';
import { produce, produceWithPatches } from 'immer';

type Re<P = unknown> = CaseReducer<
  typeof defaultState,
  { payload: P; type: string }
>;

const onSelectWords: Re<actions.SelectWordsRequest> = (state, { payload }) => ({
  ...state,
  transcriptionView: {
    ...state.transcriptionView,
    selected: uniqBy(
      [...state.transcriptionView.selected, ...payload],
      'id'
    ).sort((a, b) => a.startTimeMs - b.stopTimeMs),
  },
});

const onSelectRangeWords: Re<actions.SelectRangeWordsRequest> = (
  state,
  { payload }
) => {
  const [{ startTimeMs }, { stopTimeMs }] = sortBy(
    payload,
    'startTimeMs'
  ) as typeof payload; // Work around sortBy not being typed well for tuples
  return {
    ...state,
    transcriptionView: {
      ...state.transcriptionView,
      selected: (state.transcription?.transcription || [])
        .filter(
          (t) => t.startTimeMs >= startTimeMs && t.stopTimeMs <= stopTimeMs
        )
        .sort((a, b) => a.startTimeMs - b.startTimeMs),
    },
  };
};

const onDeselectWords: Re<actions.DeselectWordsRequest> = (
  state,
  { payload }
) => ({
  ...state,
  transcriptionView: {
    ...state.transcriptionView,
    selected: state.transcriptionView.selected.filter(
      (s) => !payload.includes(s)
    ),
  },
});

const onDeselectAll: Re<actions.DeselectAllRequest> = (state) => ({
  ...state,
  transcriptionView: {
    ...state.transcriptionView,
    selected: [],
  },
});

export const onRedactWords: Re<actions.RedactWordsRequest> = (
  state,
  { payload }
) => {
  const [newState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.tdoIsChanged = true;
      draft.transcriptionView.redactions = normalizeRedactionSlices(
        draft.transcriptionView.redactions,
        payload.map<AudioRedactionSlice>(
          ({ startTimeMs, stopTimeMs, words }) => [
            startTimeMs,
            stopTimeMs,
            {
              // TODO: type things properly
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              user: get(draft, 'user.email', 'Unknown'),
              modifiedDateTime: new Date().toISOString(),
              labels: [],
              notes: words,
              hasAuditEvent: true,
            },
          ]
        )
      );
    }
  );

  return produce(newState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const onUnredactWords: Re<actions.UnredactWordsRequest> = (
  state,
  { payload }
) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.tdoIsChanged = true;
      draft.transcriptionView.redactions = removeRedactionSlices(
        draft.transcriptionView.redactions,
        payload.map<AudioRedactionSlice>(({ startTimeMs, stopTimeMs }) => [
          startTimeMs,
          stopTimeMs,
          null,
        ])
      );
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const onRedactSlice: Re<actions.RedactSliceRequest> = (state, { payload }) => {
  const { redact, unredact } = payload;
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.tdoIsChanged = true;
      if (unredact) {
        draft.transcriptionView.redactions = removeRedactionSlices(
          draft.transcriptionView.redactions,
          unredact
        );
      }
      draft.transcriptionView.redactions = normalizeRedactionSlices(
        draft.transcriptionView.redactions,
        redact.map<AudioRedactionSlice>(([startTimeMs, stopTimeMs]) => [
          startTimeMs,
          stopTimeMs,
          {
            // TODO: type things properly
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            user: get(draft, 'user.email', 'Unknown'),
            modifiedDateTime: new Date().toISOString(),
            labels: [],
            notes: 'Timeline redaction',
            hasAuditEvent: true,
          },
        ])
      );
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const onSelectSlice: Re<actions.SelectSliceRequest> = (state, { payload }) => {
  if (!state.transcription) {
    return state;
  }
  return {
    ...state,
    transcriptionView: {
      ...state.transcriptionView,
      selected: [
        ...normalizeSelectSlices(payload, state.transcription.transcription),
      ],
    },
  };
};

const onUnredactSlice: Re<actions.UnredactSliceRequest> = (
  state,
  { payload }
) =>
  produce(state, (draft) => {
    draft.tdoIsChanged = true;
    draft.transcriptionView.redactions = removeRedactionSlices(
      draft.transcriptionView.redactions,
      payload
    );
  });

const onUnredactAll: Re<actions.UnredactAllRequest> = (state) => ({
  ...state,
  tdoIsChanged: true,
  transcriptionView: {
    ...state.transcriptionView,
    redactions: [],
  },
});

const onViewNotes: Re<actions.ViewNotesRequest> = (
  state,
  { payload: [s, e] }
) => ({
  ...state,
  tdoIsChanged: true,
  transcriptionView: {
    ...state.transcriptionView,
    editNotes: state.transcriptionView.redactions.find(
      (r) => s >= r[0] && e <= r[1]
    ),
  },
});

const onCloseNotes: Re<void> = (state) => ({
  ...state,
  tdoIsChanged: true,
  transcriptionView: {
    ...state.transcriptionView,
    editNotes: undefined,
    currentEditWords: undefined,
  },
});

const onUpdateNotes: Re<actions.UpdateNotesRequest> = (
  state,
  { payload: [s, e, n] }
) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.tdoIsChanged = true;
      draft.transcriptionView.redactions =
        draft.transcriptionView.redactions.map((r) =>
          s >= r[0] && e <= r[1]
            ? [
                r[0],
                r[1],
                {
                  ...r[2],
                  ...n,
                  modifiedDateTime: new Date().toISOString(),
                  // TODO: type things properly
                  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                  user: get(state, 'user.email', 'Unknown'),
                },
              ]
            : r
        );
    }
  );

  return produce(nextState, (draft) => {
    draft.transcriptionView.editNotes = undefined;
    draft.transcriptionView.currentEditWords = undefined;
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

export const onDeleteNotes: Re<actions.DeleteNotesRequest> = (
  state,
  { payload: [s, e] }
) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.tdoIsChanged = true;
      draft.transcriptionView.redactions =
        draft.transcriptionView.redactions.map((r) => {
          if (r[2]) {
            r[2].notes = '';
            r[2].labels = [];
          }
          return s >= r[0] && e <= r[1] ? [r[0], r[1], r[2]] : r;
        });
      draft.editNotes = undefined;
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

export const onAddRedactionCodeToTranscription: Re<
  actions.AddRedactionCodeToTranscriptionRequest
> = (state, { payload }) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.tdoIsChanged = true;
      draft.transcriptionView.redactions = insertRedactionCode(
        draft.transcriptionView.redactions,
        payload,
        get(draft, 'user.email', 'Unknown')
      );
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

export const onRemoveRedactionCodeFromTranscription: Re<
  actions.RemoveRedactionCodeFromTranscriptionRequest
> = (state, { payload }) => {
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.tdoIsChanged = true;
      draft.transcriptionView.redactions = removeRedactionCode(
        state.transcriptionView.redactions,
        payload
      );
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

const disableUnredactSliceLog: Re<boolean> = (state, { payload }) => ({
  ...state,
  disableUnredactAuditLog: payload,
});

const setCurrentWordsForNotes: Re<string> = (state, { payload }) => ({
  ...state,
  tdoIsChanged: true,
  transcriptionView: {
    ...state.transcriptionView,
    currentEditWords: payload,
  },
});

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(actions.SELECT_WORDS, onSelectWords)
    .addCase(actions.SELECT_RANGE_WORDS, onSelectRangeWords)
    .addCase(actions.DESELECT_WORDS, onDeselectWords)
    .addCase(actions.DESELECT_ALL, onDeselectAll)
    .addCase(actions.REDACT_WORDS, onRedactWords)
    .addCase(actions.UNREDACT_WORDS, onUnredactWords)
    .addCase(actions.REDACT_SLICE, onRedactSlice)
    .addCase(actions.SELECT_SLICE, onSelectSlice)
    .addCase(actions.UNREDACT_SLICE, onUnredactSlice)
    .addCase(actions.UNREDACT_ALL, onUnredactAll)
    .addCase(actions.VIEW_NOTES, onViewNotes)
    .addCase(actions.CLOSE_NOTES, onCloseNotes)
    .addCase(actions.UPDATE_NOTES, onUpdateNotes)
    .addCase(actions.DELETE_NOTES, onDeleteNotes)
    .addCase(
      actions.ADD_REDACTION_CODE_TO_TRANSCRIPTION,
      onAddRedactionCodeToTranscription
    )
    .addCase(
      actions.REMOVE_REDACTION_CODE_FROM_TRANSCRIPTION,
      onRemoveRedactionCodeFromTranscription
    )
    .addCase(actions.DISABLE_UNREDACT_SLICE_LOG, disableUnredactSliceLog)
    .addCase(actions.SET_CURRENT_WORDS_FOR_NOTES, setCurrentWordsForNotes);
});

/**
 * A redaction slice or gap cannot be less than in MS.
 */
const SLICE_MIN_MS = 33;

/**
 * Takes a list of redacted slices and merges overlapping times.
 * Note: Slices are inclusive.
 */
const normalizeRedactionSlices = (
  slices: Array<AudioRedactionSlice>,
  newSlices: Array<AudioRedactionSlice> = []
): Array<AudioRedactionSlice> => {
  const sortedSlices = sortBy(slices.concat(newSlices), '0');
  if (sortedSlices.length < 2) {
    return sortedSlices;
  }
  // const normalized: AudioRedactionSlice[] = [];
  let start = Infinity;
  let end = 0;
  let notes: AudioRedactionSlice[2];
  const normalized = sortedSlices.reduce<AudioRedactionSlice[]>(
    (acc, slice, i, arr) => {
      start = Math.min(start, slice[0]);
      end = Math.max(end, slice[1]);
      notes = mergeNotes([notes, slice[2]]);
      const nextSlice = arr[i + 1];
      if (nextSlice && nextSlice[0] > end + SLICE_MIN_MS) {
        if (end - start > SLICE_MIN_MS) {
          acc.push([start, end, notes]);
          notes = undefined;
        }
        start = Infinity;
        end = 0;
      }
      return acc;
    },
    []
  );

  // for (let i = 0; i < sortedSlices.length; i++) {
  //   const slice = sortedSlices[i];
  //   start = Math.min(start, slice[0]);
  //   end = Math.max(end, slice[1]);
  //   notes = mergeNotes([notes, slice[2]]);
  //   if (sortedSlices[i + 1]?.[0] > end + SLICE_MIN_MS) {
  //     if (end - start > SLICE_MIN_MS) {
  //       normalized.push([start, end, notes]);
  //       notes = undefined;
  //     }
  //     start = Infinity;
  //     end = 0;
  //   }
  // }
  normalized.push([start, end, notes]);
  return normalized;
};

const normalizeSelectSlices = (
  range: AudioRedactionSlice,
  transcriptionText: Array<TranscriptionViewableWords> = []
): Array<TranscriptionViewableWords> => {
  const startTimeMs = range[0];
  const stopTimeMs = range[1];
  const finalSelected: Array<TranscriptionViewableWords> = [];
  transcriptionText.forEach((w) => {
    if (w.startTimeMs > startTimeMs) {
      if (w.startTimeMs < stopTimeMs) {
        finalSelected.push(w);
      }
    } else if (w.stopTimeMs > startTimeMs) {
      finalSelected.push(w);
    }
  });
  return finalSelected;
};

const removeRedactionSlices = (
  initSlices: Array<AudioRedactionSlice>,
  initCutSlices: Array<AudioRedactionSlice>
): Array<AudioRedactionSlice> => {
  const slices = sortBy(initSlices, '0');
  const cutSlices = normalizeRedactionSlices(initCutSlices);
  const normalized: AudioRedactionSlice[] = [];
  for (const slice of slices) {
    const newSlices: AudioRedactionSlice[] = [];
    let [start, , notes] = slice;
    const [, end] = slice;
    for (const [cs, ce, cn] of cutSlices) {
      // cut after slice
      if (cs >= end) {
        newSlices.push([start, end, notes]);
        break;
      }
      // if cut before slice.
      if (ce < start) {
        newSlices.push([start, end, notes]);
        continue;
      }
      // If in slice
      if (cs > start) {
        newSlices.push([start, cs, mergeNotes([notes, cn])]);
      }
      // If left trim
      if (ce < end) {
        start = ce;
        notes = mergeNotes([notes, cn]);
        newSlices.push([start, end, notes]);
      }
    }
    // Add new slices
    for (const slice of newSlices) {
      normalized.push(slice);
    }
  }
  return normalizeRedactionSlices(normalized);
};

const mergeNotes = (
  notes: Array<AudioRedactionDetails | null | undefined>
): AudioRedactionDetails | undefined => {
  const ns = notes.filter(notNil);
  if (ns.length === 0) {
    return;
  }

  return ns.reduce((note, n) => ({
    ...note,
    modifiedDateTime:
      n.modifiedDateTime.localeCompare(note.modifiedDateTime) === -1
        ? note.modifiedDateTime
        : n.modifiedDateTime,
    labels: [...note.labels, ...n.labels],
    notes: `${note.notes} ${n.notes}`.slice(0, 500),
    redactionCode: note?.redactionCode || n?.redactionCode,
  }));
};

const insertRedactionCode = (
  slices: Array<AudioRedactionSlice>,
  payload: actions.AddRedactionCodeToTranscriptionRequest,
  user: string
) => {
  const { word, redactionCode } = payload;
  const index = slices.findIndex(
    ([s, e, _n]) => s <= word.startTimeMs && e >= word.stopTimeMs
  );
  const slice = slices[index];
  if (slice) {
    return replaceSliceAt(
      slices,
      index,
      replaceSliceNote(
        slice,
        slice[2]
          ? { ...slice[2], redactionCode }
          : {
              user,
              modifiedDateTime: new Date().toISOString(),
              labels: [],
              notes: '',
              hasAuditEvent: true,
              redactionCode,
            }
      )
    );
  }
  return slices;
};

const removeRedactionCode = (
  slices: Array<AudioRedactionSlice>,
  word: actions.RemoveRedactionCodeFromTranscriptionRequest
) => {
  const index = slices.findIndex(
    ([s, e, _n]) => s <= word.startTimeMs && e >= word.stopTimeMs
  );
  const slice = slices[index];
  if (slice) {
    return replaceSliceAt(
      slices,
      index,
      replaceSliceNote(slice, omit(slice[2], 'redactionCode'))
    );
  }
  return slices;
};

const replaceSliceAt = (
  arr: Array<AudioRedactionSlice>,
  index: number,
  value: AudioRedactionSlice
): Array<AudioRedactionSlice> => [
  ...arr.slice(0, index),
  value,
  ...arr.slice(index + 1),
];

const replaceSliceNote = (
  arr: AudioRedactionSlice,
  value: AudioRedactionDetails | null | undefined
): AudioRedactionSlice => [arr[0], arr[1], value];

import 'jest';
import { Lo<PERSON> } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { createFolder } from '../../src/controllers/createFolder';
import { checkFolderExistsQuery, checkNameExistsQuery, createFolderQuery } from '../../src/api/queries';

const limit = 100;
const offset = 0;

describe('createFolder', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('creates a folder', async () => {
    const req = getMockReq({
      body: {
        parentFolderId: '123456789',
        name: 'name',
        description: 'description',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() => Promise.resolve({
      folder: {
        id: '123456789',
        folderPath: [],
        contentTemplates: [],
      },
    }));

    callGQL.mockImplementationOnce(() => Promise.resolve({
      folder: {
        childFolders: {
          records: ['folder1'],
        },
      },
    }));

    callGQL.mockImplementationOnce(() => Promise.resolve({
      createFolder: {
        id: req.body.id,
        name: req.body.name,
      },
    }));

    await createFolder(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(3);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, checkFolderExistsQuery(req.body.parentFolderId));
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, checkNameExistsQuery(req.body.parentFolderId, req.body.name, limit, offset));
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createFolderQuery,
      {
        description: req.body.description,
        name: req.body.name,
        parentFolderId: req.body.parentFolderId,
        userId: undefined,
      });
    expect(res.status).toHaveBeenCalledWith(StatusCodes.InsertedSuccess);
  });

  it('fails to create a folder w/o name', async () => {
    const req = getMockReq({
      body: {
        parentFolderId: '123456789',
        name: '',
        description: 'description',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await createFolder(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.NameIsRequired })
  });
});

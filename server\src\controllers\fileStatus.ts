import express from 'express';
import { fetchIngestedFileStatusQuery } from '../api/queries';
import { callGQL } from '../api/callGraphql';
import { pick } from 'lodash';
import { IngestedFileStatusResponse } from '../model/responses';
import { Logger } from '../logger';
import { StatusCodes } from '../errors/statusCodes';
import { Messages } from '../errors/messages';
import { isTdoId } from '../validations/helpers';

export const fileStatus = async (
  req: express.Request,
  res: express.Response
) => {
  const tdoId = req.params.tdoId;
  if (!isTdoId(tdoId)) {
    return res.status(StatusCodes.BadRequest).json({ error: Messages.tdoIdRequired });
  }
  const headers = pick(req.headers, ['authorization']);
  const query = fetchIngestedFileStatusQuery(tdoId);
  try {
    const tdoResponse = await callGQL<IngestedFileStatusResponse>(headers, query);
    const { id, name, /* primaryAsset, */ jobs } = tdoResponse.temporalDataObject;
    const response = {
      id,
      name,
      // signedUri: primaryAsset?.signedUri,
      status: jobs?.records?.[0]?.status
    }
    return res.status(StatusCodes.Success).json(response);
  } catch(err) {
    Logger.error(err);
    return res.status(StatusCodes.BadRequest).send(err);
  }
};

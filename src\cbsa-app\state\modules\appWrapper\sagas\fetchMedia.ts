import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import * as Services from '../services';
import { put, takeLatest } from 'typed-redux-saga/macro';
import { enqueueSnackbar } from '@common-modules/snackbar';

export function* fetchMediaHandler() {
  yield* takeLatest(Actions.FETCH_MEDIA, function* ({ payload }) {
    const { caseId, offset, limit } = payload;
    if (caseId) {
      yield* put(
        Services.fetchMedia({
          offset,
          limit,
          caseId,
        })
      );
    }
  });
}

export function* fetchMediaFailureHandler() {
  yield* takeLatest(Actions.FETCH_MEDIA_FAILURE, function* () {
    const intl = sagaIntl();
    yield enqueueSnackbar({
      message: intl.formatMessage({ id: 'failedToFetchMedia' }),
      variant: 'error',
    });
  });
}

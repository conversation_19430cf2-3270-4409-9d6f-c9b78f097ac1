import IconButton from '@mui/material/IconButton';
import DeleteIcon from '@mui/icons-material/Delete';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

import { MouseEventHandler } from 'react';
import OverlayToolBar from './OverlayToolBar';

const OverlayActionsMenu = ({
  visible,
  onMinimize,
  onDelete,
  onConfirm,
  menuItems,
  bottomOffset,
  focusedBoundingBoxId,
}: Props) => (
  <OverlayToolBar
    visible={visible}
    onMinimize={onMinimize}
    menuItems={menuItems}
    bottomOffset={bottomOffset}
    focusedBoundingBoxId={focusedBoundingBoxId}
  >
    <IconButton
      onClick={onDelete}
      data-veritone-element="overlay-toolbar-delete-icon"
      data-testid="overlay-toolbar-delete-icon"
      data-test="overlay-action-icon-button"
      size="large"
    >
      <DeleteIcon data-testid="icon-delete" />
    </IconButton>
    {onConfirm && (
      <IconButton
        onClick={onConfirm}
        data-test="overlay-action-icon-button"
        data-testid="overlay-toolbar-confirm-icon"
        size="large"
      >
        <CheckCircleIcon data-testid="icon-circle" />
      </IconButton>
    )}
  </OverlayToolBar>
);

interface Props {
  visible: boolean;
  onMinimize: MouseEventHandler<HTMLElement>;
  onDelete: MouseEventHandler<HTMLElement>;
  onConfirm?: MouseEventHandler<HTMLElement>;
  menuItems: {
    label: string;
    onClick: () => void;
  }[];
  bottomOffset: number;
  focusedBoundingBoxId?: string;
}

export default OverlayActionsMenu;

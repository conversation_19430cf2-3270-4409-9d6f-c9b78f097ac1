import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { I18nTranslate } from '@common/i18n';

const ConfirmDialog = ({
  isOpen,
  onContinue,
  onClose,
}: ConfirmDialogPropTypes) => (
  <Dialog
    open={isOpen}
    onClose={(_event, reason) => {
      if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
        onClose();
      }
    }}
    maxWidth={false}
    data-veritone-component="confirm-face-detection-modal"
  >
    <DialogTitle>
      {I18nTranslate.TranslateMessage('DetectHeadsandObjects')}
    </DialogTitle>
    <DialogContent>
      {I18nTranslate.TranslateMessage('DetectHeadsandObjectsDes')}
    </DialogContent>
    <DialogActions>
      <Button
        onClick={onClose}
        color="primary"
        data-veritone-element="confirm-face-detection-modal-cancel-button"
      >
        {I18nTranslate.TranslateMessage('cancel')}
      </Button>
      <Button
        type="submit"
        color="primary"
        onClick={onContinue}
        data-veritone-element="confirm-face-detection-modal-ok-button"
      >
        {I18nTranslate.TranslateMessage('continue')}
      </Button>
    </DialogActions>
  </Dialog>
);

export default ConfirmDialog;

export interface ConfirmDialogPropTypes {
  readonly isOpen: boolean;
  readonly onContinue: () => void;
  readonly onClose: () => void;
}

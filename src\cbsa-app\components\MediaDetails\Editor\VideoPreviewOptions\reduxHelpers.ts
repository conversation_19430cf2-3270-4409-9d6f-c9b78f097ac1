import { createSelector } from 'reselect';

import {
  actionOverlayPreview,
  selectContentTypeHasVideo,
  selectOverlayPreview,
} from '@common-modules/mediaDetails';

export const componentSelectors = createSelector(
  selectOverlayPreview,
  selectContentTypeHasVideo,
  (overlayPreview, hasVideo) => ({
    overlayPreview,
    hasVideo,
  })
);

export const componentActions = {
  onOverlayPreviewChange: actionOverlayPreview,
};

// export type VideoPreviewOptionsPropTypes = ReturnType<typeof mapStateToProps> &
//   typeof mapDispatchToProps;

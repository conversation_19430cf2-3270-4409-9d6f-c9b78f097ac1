import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import { getSdoIdQuery } from '../../src/api/queries';
import * as getSchema from '../../src/adapters/getSchema';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { deleteCase } from '../../src/controllers/deleteCase';
import * as isLocked from '../../src/adapters/checkCaseLock';
import * as deleteSdo  from '../../src/adapters/deleteSdo';
import { CaseId } from '../../src/model/brands';

describe('deleteCase', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  const checkFolderIsLocked = jest.spyOn(isLocked, 'checkCaseLockAdapter');
  checkFolderIsLocked.mockImplementation(() => Promise.resolve( { isFailed: false, isLocked: false, isJobRunning: false}));

  const getSchemaId = jest.spyOn(getSchema, 'getSchemaIdAdapter');
  getSchemaId.mockImplementation(() => Promise.resolve('schemaId'));

  const deleteSdoAdapter = jest.spyOn(deleteSdo, 'deleteSdoAdapter');
  deleteSdoAdapter.mockImplementation(() => Promise.resolve('123456789'));
  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('deletes a case w/ caseId', async () => {
    const params = { caseId: '123456789' as CaseId };
    const req = getMockReq({
      params,
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() => Promise.resolve({
      folder: {
        id: '123456789',
        contentTemplates: [{ sdoId: '123456789' }],
      },
    }));

    callGQL.mockImplementationOnce(() => Promise.resolve({ deleteStructuredData: { id: '123456789' } }));

    callGQL.mockImplementationOnce(() => Promise.resolve('123456789'));

    callGQL.mockImplementationOnce(() => Promise.resolve({ deleteFolder: { id: '123456789' } }));

    await deleteCase(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(3);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, getSdoIdQuery(params.caseId));
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, getSdoIdQuery(params.caseId));
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, getSdoIdQuery(params.caseId));
    expect(isLocked.checkCaseLockAdapter).toHaveBeenCalledTimes(1);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('fails to delete a folder w/o caseId', async () => {
    const params = { caseId: '' };
    const req = getMockReq({
      params,
      headers: {
        authorization: 'Bearer token',
      },
    });

    await deleteCase(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.caseIdRequired });
  });
});

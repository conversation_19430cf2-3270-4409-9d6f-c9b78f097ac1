import { forOwn } from 'lodash';
import { guid } from './util';
import VeritoneApp from './VeritoneApp';
import React, { ComponentProps, JSXElementConstructor } from 'react';

export default function widget<
  C extends keyof React.JSX.IntrinsicElements | JSXElementConstructor<any>,
>(Component: C) {
  return class Widget {
    _elId: string;
    _props: Omit<ComponentProps<C>, 'elId' | 'widgetId'>;
    _app:
      | {
          _register: (a: any) => any;
          _unregister: (a: any) => any;
        }
      | undefined
      | null;
    _id: string;
    constructor({
      elId,
      widgetId,
      ...props
    }: ComponentProps<C> & { elId: string; widgetId: string }) {
      // TODO: types need fixing
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      this._elId = elId;
      this._props = props;
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      this._app = VeritoneApp(null, { _isWidget: true });
      this._id = guid();

      if (this._app) {
        this._app._register(this);
      }
    }

    destroy() {
      this._app?._unregister(this);
    }

    setRefProperties(ref: C) {
      // allow access of ref properties on the widget itself
      // (should only be used by consumers to call component's API)
      forOwn(ref, (value, key) => {
        try {
          Object.defineProperty(this, key, { value });
        } catch (_e) {
          /* */
        }
      });
    }

    get Component() {
      return Component;
    }

    get props() {
      return {
        ...this._props,
        _widgetId: this._id,
      };
    }
  };
}

import { useSelector } from 'react-redux';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';

import { local } from '@common-modules/mediaDetails';

import RouteLoadingScreen from '@common-components/RouteLoadingScreen';
// import React from 'react';

const OpticalTrackingModal = () => {
  const { pollingOpticalTrackingAreProcessing } =
    useSelector(componentSelector);

  return (
    <Dialog
      open={pollingOpticalTrackingAreProcessing}
      onClose={(_evt, reason) => {
        if (reason === 'backdropClick' || reason === 'escapeKeyDown') {
          return;
        }
      }}
      maxWidth={false}
      data-veritone-component="optical-tracking-modal"
    >
      <DialogContent>
        <div style={{ height: '135px' }}>
          {/* <RouteLoadingScreen style={{ height: '130px' }} minDelay={500} /> */}
          <RouteLoadingScreen />
        </div>
      </DialogContent>
      <DialogTitle>Selected Object is processing</DialogTitle>
    </Dialog>
  );
};

const componentSelector = (state: any) => ({
  pollingOpticalTrackingAreProcessing:
    local(state).pollingOpticalTrackingAreProcessing,
});

export default OpticalTrackingModal;

import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles((theme) => ({
  notifications: {
    '& .Header': {
      display: 'flex',
      alignItems: 'center',
      margin: '15px 0 10px 0',
      justifyContent: 'space-between',

      '& .Title': {
        fontSize: '14px',
        fontWeight: 'bold',
        textTransform: 'uppercase',
        color: theme.palette.primary.main,
      },

      '& .Action': {
        fontSize: '12px',
        cursor: 'pointer',
        textAlign: 'right',
        color: theme.palette.primary.main,
      },
    },

    '& .None': {
      fontSize: '12px',
    },
  },
}));

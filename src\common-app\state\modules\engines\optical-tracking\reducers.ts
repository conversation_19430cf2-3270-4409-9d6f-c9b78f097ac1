import { Lens } from 'monocle-ts';

import * as Actions from './actions';
import {
  defaultState,
  defaultJobSlice,
  OpticalTrackingStore,
  JobState,
} from './store';
import { createReducer } from '@reduxjs/toolkit';
import { TDOId } from '../../universal/models/Brands';
import { JobStatus } from '@veritone/glc-redux';

const engineGroup = (tdoId: TDOId) =>
  Lens.fromNullableProp<OpticalTrackingStore>()(tdoId, {});
const engineJob = (jobId: string) =>
  Lens.fromNullableProp<NonNullable<OpticalTrackingStore['']>>()(
    jobId,
    defaultJobSlice()
  );
const getJob = (tdoId: TDOId, jobId: string) =>
  engineGroup(tdoId).compose(engineJob(jobId)).get;
const modifyJob = (
  tdoId: TDOId,
  jobId: string,
  fn: (j: JobState) => JobState
) => engineGroup(tdoId).compose(engineJob(jobId)).modify(fn);

const processEngineRequest = (state: OpticalTrackingStore) => ({ ...state });

const processEngineRequestSuccess = (
  state: OpticalTrackingStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
    endedOn: new Date(),
    error: undefined,
  }))(state);

const processEngineRequestFailure = (
  state: OpticalTrackingStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string; errorMsg: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
    status: JobStatus.Failed,
    endedOn: new Date(),
    error: payload.errorMsg,
  }))(state);

const createJobSuccess = (
  state: OpticalTrackingStore,
  { payload }: { payload: { createJob: { id: string; targetId: TDOId } } }
) => {
  const res = modifyJob(
    payload.createJob.targetId,
    payload.createJob.id,
    (_job) => ({
      jobId: payload.createJob.id,
      isRunning: true,
      status: JobStatus.Pending,
      startedOn: new Date(),
      endedOn: undefined,
      error: undefined,
    })
  )(state);
  return res;
};

const startPollingEngineResults = (
  state: OpticalTrackingStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    jobId: payload.jobId,
    isRunning: true,
    status: JobStatus.Pending,
    error: undefined,
  }))(state);

const stopPollingEngineResults = (
  state: any,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
  }))(state);

const checkJobStatusSuccess = (
  state: OpticalTrackingStore,
  {
    payload,
  }: { payload: { job: { targetId: TDOId; id: string; status: JobStatus } } }
) =>
  payload.job.status ===
  getJob(payload.job.targetId, payload.job.id)(state).status
    ? state
    : modifyJob(payload.job.targetId, payload.job.id, (job) => ({
        ...job,
        status: payload.job.status,
      }))(state);

const cancelJob = (
  state: OpticalTrackingStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
    status: JobStatus.Cancelled,
    endedOn: new Date(),
    error: undefined,
  }))(state);

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(Actions.PROCESS_ENGINE_REQUEST, processEngineRequest)
    .addCase(
      Actions.PROCESS_ENGINE_REQUEST_SUCCESS,
      processEngineRequestSuccess
    )
    .addCase(
      Actions.PROCESS_ENGINE_REQUEST_FAILURE,
      processEngineRequestFailure
    )
    .addCase(Actions.CREATE_ENGINE_JOB_SUCCESS, createJobSuccess)
    .addCase(Actions.START_POLLING_ENGINE_RESULTS, startPollingEngineResults)
    .addCase(Actions.STOP_POLLING_ENGINE_RESULTS, stopPollingEngineResults)
    .addCase(Actions.CHECK_JOB_STATUS_SUCCESS, checkJobStatusSuccess)
    .addCase(Actions.CANCEL_JOB, cancelJob);
});

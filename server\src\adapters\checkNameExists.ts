import { CheckNameExistsResponse } from '../model/responses';
import { CheckNameExistsRequest, RequestHeader } from '../model/requests';
import { callGQL } from '../api/callGraphql';
import { checkNameExistsQuery } from '../api/queries';
import { Messages } from '../errors/messages'
import { Logger } from '../logger';

export const checkNameExistsAdapter = async (
  headers: RequestHeader,
  request: CheckNameExistsRequest,
) => {
  const defaultLimit = 100;
  const defaultOffset = 0;
  const getNameMatches = async(offset: number): Promise<boolean> => {
      try {
        const query = checkNameExistsQuery(request.folderId, request.name, defaultLimit, offset);
        const response = await callGQL<CheckNameExistsResponse>(headers, query);
        if (response.folder.childFolders?.records?.length > 0) {
          const anyMatches = response.folder.childFolders.records.filter(f => {
            return f.name === request.name && f.parent?.id === request.folderId;
          });
          if (anyMatches?.length > 0 ) {
            return true;
          } else {
            if (response.folder.childFolders.records?.length === defaultLimit) {
              return await getNameMatches(offset + defaultLimit);
            } else {
              return false;
            }
          }
        } else {
          return false;
        }
    } catch (err) {
        Logger.error(Messages.checkFolderExistsFail + JSON.stringify(err));
        return false;
    }
  }
  return await getNameMatches(defaultOffset);
};

import { selectApplicationConfigs } from '@common/state/modules/app';
import { selectFeatureFlags } from '@common/user-permissions';

// Minimal config with all required fields for type compatibility
const minimalConfig = {
  applicationId: 'app-1',
  veritoneAppId: 'veritone-app-1',
  nodeEnv: 'test',
  taskTablePartitionActiveDate: '',
  jobTablePartitionActiveDate: '',
  recordingAssetTablePartitionActiveDate: '',
  recordingWeekConfig: { weeklyIdsDateActive: '', weekOffset: 0 },
  switchAppRoute: '',
  loginRoute: '',
  apiRoot: '',
  graphQLEndpoint: '',
  searchEndpoint: '',
  corsightEndpoint: '',
  OAuthClientID: '',
  pendoKey: '',
  segmentWriteKey: '',
  useOAuthGrant: '',
  defaultClusterId: '',
  defaultClusterIdAzureStage: '',
  defaultClusterIdAzureProd: '',
  defaultClusterIdAWSDev: '',
  defaultClusterIdAWSStage: '',
  defaultClusterIdAWSProd: '',
  defaultClusterIdAzureUK: '',
  defaultClusterIdAzureCanada: '',
  defaultClusterIdTW: '',
  redactNotificationSchemaId: '',
  eventSchemaId: '',
  tdoRedactStateSchemaId: '',
  caseRegistryId: '',
  caseNotificationRegistryId: '',
  uploadSizeLimit: '',
  featureFlags: {
    detectNotepads: true,
    detectCards: false,
    redactionCodes: true,
  },
  redactionCodesRegistryId: 'reg-123',
  auth: { userTokenCookieName: '' },
  mandrillAPIKey: '',
  publicDnsZoneName: '',
  jwt: { secret: '', ttl: '' },
  aiwareJSPath: '',
  aiwareJSVersion: '',
  glcApiRoot: '',
  webstreamAdapterEngineId: '',
  streamIngestorEngineId: '',
  streamIngestorDASHPlaybackSegmentCreatorId: '',
  streamIngestorPlaybackSegmentCreatorId: '',
  legacyRedactEngineId: '',
  redactEngineId: '',
  voiceMaskEngineId: '',
  downloadEngineCategoryId: '',
  downloadEngineId: '',
  opticalTrackingEngineCatagoryId: '',
  opticalTrackingEngineCategoryId: '',
  opticalTrackingEngineId: '',
  detectionCategory: '',
  detectionEngineId: '',
  transcriptionCategoryId: '',
  transcriptionEngineId: '',
  poiDetectionEngineId: '',
  segmentIoId: '',
  segmentIoKey: '',
  allowedOriginHosts: [],
  log: {
    console: { color: false, enable: false, level: '' },
    file: false,
    fileName: '',
    level: '',
    'log.console': false,
    'log.file': false,
    'log.level': '',
    'log.fileName': '',
    'log.profile': false,
    profile: false,
    'log.consoleColor': false,
    'log.remote': false,
    loggly: { enable: false },
  },
  errorTracking: {},
  sso: {},
  ssoEnabled: false,
  ssoProvider: '',
  ssoRedirectUrl: '',
  ssoLogoutUrl: '',
  aiware: { enabled: false, organizationApplicationId: '' },
  mediaFormats: [],
  services: { coreAdminUri: '' },
  fastChunkerEngineId: '',
  glcIngestionEngineId: '',
  requestQuoteUrl: '',
  newRelic: {
    enable: false,
    errorCollectorIgnoreCodes: '',
    licenseKey: '',
    logLevel: '',
  },
  requireComplexPassword: false,
  port: 3000,
  skipMiddleware: false,
  skipAuth: false,
  skipSSO: false,
  skipErrorTracking: false,
  skipPendo: false,
  skipSegment: false,
};

const baseAppConfigs = {
  featureFlags: { detectCards: true },
  transcriptionEngineId: 'engineId',
}

describe('selectFeatureFlags', () => {
  const configWithAllFields = {
    ...minimalConfig,
  };

  it('merges config and appConfigs featureFlags, and sets redactionCodes correctly', () => {
    const result = selectFeatureFlags.resultFunc(
      configWithAllFields,
      baseAppConfigs
    );
    expect(result.detectNotepads).toBe(true);
    expect(result.detectCards).toBe(true); // org overrides config
    expect(result.redactionCodes).toBe(true);
  });


  it('sets redactionCodes to false if redactionCodesRegistryId is missing', () => {
    const config = {
      ...configWithAllFields,
      redactionCodesRegistryId: undefined,
    };
    const result = selectFeatureFlags.resultFunc(config, baseAppConfigs);
    expect(result.redactionCodes).toBe(false);
  });

  it('defaults redactionCodes to true if not present in featureFlags and registryId exists', () => {
    const config = { ...configWithAllFields, featureFlags: {} };
    const result = selectFeatureFlags.resultFunc(config, baseAppConfigs);
    expect(result.redactionCodes).toBe(true);
  });

  it('does not override redactionCodes if present in featureFlags and registryId exists', () => {
    const config = {
      ...configWithAllFields,
      featureFlags: { redactionCodes: false },
    };
    const result = selectFeatureFlags.resultFunc(config, baseAppConfigs);
    expect(result.redactionCodes).toBe(false);
  });
});

describe('selectApplicationConfigs', () => {
  const resultFunc = selectApplicationConfigs.resultFunc;

  it('parses JSON value if valueJSON is null', () => {
    const appConfigs = [
      {
        configType: 'JSON',
        configKey: 'featureFlags',
        value: '{"detectCards":true}',
        valueJSON: null,
      },
    ];
    const result = resultFunc(appConfigs);
    // @ts-expect-error Acceptable here, as we are testing the resultFunc
    expect(result.featureFlags.detectCards).toBe(true);
  });

  it('ignores invalid JSON', () => {
    const appConfigs = [
      {
        configType: 'JSON',
        configKey: 'featureFlags',
        value: '{detectCards}',
        valueJSON: null,
      },
    ];
    const result = resultFunc(appConfigs);
    expect(result).toEqual({});
  });
});

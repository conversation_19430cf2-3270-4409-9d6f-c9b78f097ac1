import * as React from 'react';

import Grid from '@mui/material/Grid2';
import IconButton from '@mui/material/IconButton';
import Input from '@mui/material/Input';
import InputAdornment from '@mui/material/InputAdornment';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';
import RemoveIcon from '@mui/icons-material/Close';
import DownIcon from '@mui/icons-material/KeyboardArrowDown';
import UpIcon from '@mui/icons-material/KeyboardArrowUp';
import SearchIcon from '@mui/icons-material/Search';

import defaultTheme from '@redact/materialUITheme';
import * as styles from '../styles.scss';

const onInputChange =
  (onChange: (term: string) => void) =>
  (evt: React.ChangeEvent<HTMLInputElement>) =>
    onChange(evt.target.value);

const onKeyUp =
  (onSearch: () => void) => (evt: React.KeyboardEvent<HTMLInputElement>) => {
    // TODO: cancelBubble is deprecated - remove once stopPropagation is confirmed to be enough
    // eslint-disable-next-line @typescript-eslint/no-deprecated
    evt.nativeEvent.cancelBubble = true;
    evt.nativeEvent.stopPropagation();

    if (evt.key === 'Enter') {
      evt.stopPropagation();
      onSearch();
    }
  };

const SearchView = ({
  term,
  focus,
  onSetFocus,
  numResults,
  onChange,
  onClear,
  onSearch,
}: SearchViewPropTypes) => {
  const handleClickUpIcon = () => {
    onSetFocus(-1);
  };
  const handleClickDownIcon = () => {
    onSetFocus(1);
  };

  return (
    <div className={styles.transcriptionSearch} data-testid="search-view">
      <StyledEngineProvider injectFirst>
        <ThemeProvider theme={defaultTheme}>
          <Grid
            container
            alignItems="center"
            data-testid="mui-theme-provider-content"
          >
            <Grid>
              <SearchIcon color="secondary" />
            </Grid>
            <Grid container alignItems="stretch" style={{ flex: '1' }}>
              <Input
                placeholder=" Hit Enter to search..."
                color="secondary"
                value={term}
                onChange={onInputChange(onChange)}
                onKeyUp={onKeyUp(onSearch)}
                endAdornment={
                  numResults >= 0 ? (
                    <InputAdornment color="secondary" position="end">
                      {focus + 1}/{numResults}
                    </InputAdornment>
                  ) : null
                }
                classes={{ underline: styles.searchUnderline }}
                style={{ flex: '1', color: '#ccc' }}
              />
            </Grid>
            <Grid>
              <IconButton
                data-testid="search-view-icon-button-first"
                onClick={handleClickUpIcon}
                size="large"
              >
                <UpIcon color="secondary" fontSize="small" />
              </IconButton>
              <IconButton onClick={handleClickDownIcon} size="large">
                <DownIcon color="secondary" fontSize="small" />
              </IconButton>
              <IconButton
                data-testid="search-view-icon-button-last"
                onClick={onClear}
                size="large"
              >
                <RemoveIcon color="secondary" fontSize="small" />
              </IconButton>
            </Grid>
          </Grid>
        </ThemeProvider>
      </StyledEngineProvider>
    </div>
  );
};

export default SearchView;

export interface SearchViewPropTypes {
  readonly term: string;
  readonly focus: number;
  readonly numResults: number;
  readonly onSetFocus: (dir: number) => void;
  readonly onChange: (term: string) => void;
  readonly onClear: () => void;
  readonly onSearch: () => void;
}

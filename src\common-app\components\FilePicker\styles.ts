import { Theme } from '@mui/material';
import { makeStyles, createStyles } from '@mui/styles';

const DEFAULT_SELECT_INPUT = 200;
const FILE_PICKER_LIST_HEIGHT = 220;
const PADDING_BOTTOM_DEFAULT_ERROR_MESSAGE = '3.25em';
const DEFAULT_MAX_WIDTH_MENU_ITEM = 280;

export const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    tooltip: {
      fontSize: 14,
    },
    folderPathBreadcrumb: {
      padding: '10px 10px 5px 20px',
    },
    filePickerCheckboxes: {
      position: 'absolute',
      bottom: 0,
      left: 6,
    },
    filePickerCheckboxesWithBottom: {},
    filePickerOption: {
      height: 25,
      '& .MuiSvgIcon-root': {
        fontSize: '1.2857142857142856rem',
      },
    },
    filePickerContent: {
      paddingLeft: 5,
    },
    transcriptionIcon: {
      paddingLeft: 2,
      fontSize: '1.4rem',
      verticalAlign: 'sub',
    },
    additionalID: {
      height: 25,
      marginTop: 10,
      paddingLeft: 10,
      fontSize: '0.75rem',
      color: '777',
    },
    filePickerPaperOverride: {
      maxWidth: '100% !important',
      maxHeight: '100% !important',
      position: 'relative',
    },
    filePickerHeader: {
      width: '100%',
      minHeight: 62,
      display: 'flex',
      backgroundColor: '#FAFAFA',
      borderBottom: '1px solid #e0e0e0',
      padding: '0 10px 0 20px',
    },
    filePickerTitle: {
      flex: 'auto',
      fontSize: 18,
      paddingTop: 20,
      alignSelf: 'start',
    },
    filePickerCloseButton: {
      padding: 8,
      alignSelf: 'center',
    },
    filePickerFooterCancelButton: {
      marginRight: 16,
    },
    filePickerThemeOverride: {
      '& .filePickerList': {
        height: FILE_PICKER_LIST_HEIGHT,
      },
      '& .file-picker-error-message': {
        paddingBottom: PADDING_BOTTOM_DEFAULT_ERROR_MESSAGE,
      },
      '& .MuiButton-containedPrimary:hover': {
        backgroundColor: theme.palette.primary.main,
      },
      '& .MuiTabs-indicator': {
        backgroundColor: theme.palette.primary.main,
      },
    },
    additionalFooterPadding: {
      '& .picker-footer-container': {
        padding: '44px 20px',
        width: '100%',
      },
    },
    selectInput: {
      '& .MuiSelect-select': {
        padding: '5px 10px 5px 10px',
      },
      maxWidth: DEFAULT_SELECT_INPUT,
    },
    selectProfileLabel: {
      paddingLeft: 10,
      paddingRight: 6,
    },
    menuItem: {
      '&.MuiMenuItem-root': {
        display: 'block',
        whiteSpace: 'nowrap',
        maxWidth: DEFAULT_MAX_WIDTH_MENU_ITEM,
        overflow: 'hidden',
        textOverflow: 'ellipsis',
      },
    },
  })
);

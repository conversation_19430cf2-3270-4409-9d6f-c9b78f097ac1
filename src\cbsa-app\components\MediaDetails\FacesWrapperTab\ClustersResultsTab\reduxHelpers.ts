import { createSelector } from 'reselect';

import { actionSendToHeadDetection } from '@common-modules/facesTabModule';
import {
  actionSeekVideo,
  actionSetSelectedGroups,
  actionSetSortBy,
  FACE_HIGHLIGHT,
  selectClusterList,
  selectDetectionRunning,
  selectGlobalSettings,
  selectHighlightedOverlay,
  selectSelected,
  selectSortBy,
  actionChangeClusterLabel,
  selectDisableFacesButton,
  selectDataFetchedForDetectionType,
  selectFilterParameters,
} from '@common-modules/mediaDetails';
import { OBJECT_TYPE } from '@helpers/constants';

export const componentSelectors = createSelector(
  selectClusterList,
  selectFilterParameters,
  selectSortBy,
  selectHighlightedOverlay,
  selectSelected,
  selectDetectionRunning,
  selectGlobalSettings,
  selectDisableFacesButton,
  selectDataFetchedForDetectionType,
  (
    clusterList,
    filterParameters,
    sortBy,
    highlightedOverlay,
    selected,
    detectionRunning,
    settings,
    buttonDetectFaceDisable,
    dataFetchedForDetectionType
  ) => ({
    clusterList,
    filterParameters,
    sortBy,
    highlightedOverlay,
    selected,
    detectionRunning,
    settings,
    buttonDetectFaceDisable,
    dataFetchedForDetectionType,
  })
);

export const componentActions = {
  setSortBy: actionSetSortBy,
  setSelectedGroups: actionSetSelectedGroups,
  changeClusterLabel: actionChangeClusterLabel,
  detectFaces: actionSendToHeadDetection,
  seekMediaTime: actionSeekVideo,
  onFaceHighlight: (payload: {
    id: string;
    timeMs: number;
    type: OBJECT_TYPE;
    groupId: string;
  }) => FACE_HIGHLIGHT(payload),
};

import { RefObject, useEffect, useRef, useState } from 'react';

const INITIAL_POSITION = { width: 0, height: 0 };

export const useRefSize = <T extends HTMLDivElement | HTMLCanvasElement>(
  ref: RefObject<T | null>
) => {
  const [dimensions, setDimensions] = useState(INITIAL_POSITION);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  useEffect(() => {
    resizeObserverRef.current = new ResizeObserver((entries = []) => {
      entries.forEach((entry) => {
        const { width, height } = entry.contentRect;
        setDimensions({ width, height });
      });
    });

    if (ref.current) {
      resizeObserverRef.current.observe(ref.current);
    }

    return () => {
      resizeObserverRef.current?.disconnect?.();
    };
  }, [ref]);

  return dimensions;
};

import { namespace } from './index';
import type { AddMediaStore } from '@cbsa-modules/addMedia';
import { createSelector } from 'reselect';

const selectStore = (store: { [namespace]: AddMediaStore }): AddMediaStore =>
  store[namespace];

export const selectSelectedTdoImages = createSelector(
  selectStore,
  (s) => s.selectedTdoImages
);

export const selectSelectedTdoMedia = createSelector(
  selectStore,
  (s) => s.selectedTdoMedia
);

export const selectCaseDetails = createSelector(
  selectStore,
  (s) => s.caseDetails
);

export const selectTDOs = createSelector(selectStore, (s) => s.tdos);

export const selectAuditEvents = createSelector(
  selectStore,
  (s) => s.auditEvents
);

export const selectAuditEventsLoading = createSelector(
  selectStore,
  (s) => s.auditLogQueryPending > 0
);

export const selectNotifications = createSelector(
  selectStore,
  (s) => s.notifications
);

export const selectLoaders = createSelector(selectStore, (s) => s.loaders);

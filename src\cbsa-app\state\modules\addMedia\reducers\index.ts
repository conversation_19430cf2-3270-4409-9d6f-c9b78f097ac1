import { omit } from 'lodash';
import { AddMediaStore } from '@cbsa-modules/addMedia';
import { defaultState } from '@cbsa-modules/addMedia/store';
import {
  GET_TDO_AUDIT_LOGS_SUCCESS,
  DELETE_CASE,
  DELETE_CASE_SUCCESS,
  DELETE_CASE_FAILURE,
  FETCH_TDOS_SUCCESS,
  FETCH_CASE_NOTIFICATIONS_SUCCESS,
  PROCESS_CASE,
  PROCESS_CASE_SUCCESS,
  PROCESS_CASE_FAILURE,
  UPDATE_CASE_NAME,
  UPDATE_CASE_NAME_SUCCESS,
  UPDATE_CASE_NAME_FAILURE,
  CLEAR_NOTIFICATION_SUCCESS,
  DISMISS_NOTIFICATION_SUCCESS,
  TOGGLE_TDO_IMAGE,
  TOGGLE_TDO_MEDIA,
  DELETE_TDO_SUCCESS,
  SET_LOADERS,
  SET_CASE_DETAILS,
} from '../actions';
import { getTdo<PERSON>uditLogSuccess } from './auditLog';
import {
  deleteCase,
  deleteCaseSuccessful,
  deleteCaseFailure,
} from './deleteCase';
import {
  fetchTdosSuccess,
  fetchCaseNotificationsSuccess,
  onSetLoaders,
  onSetCaseDetails,
} from './fetchCase';
import {
  processCase,
  processCaseSuccessful,
  processCaseFailure,
} from './processCase';
import {
  updateCaseName,
  updateCaseNameSuccess,
  updateCaseNameFailure,
} from './updateCaseName';
import { updateNotificationSuccess } from './updateNotification';
import { TDOId } from '@cbsa-modules/universal';
import { CaseReducer, createReducer } from '@reduxjs/toolkit';

export type Re<P = unknown> = CaseReducer<
  AddMediaStore,
  { payload: P; meta?: any; type: string } // TODO: type meta as Record<string, any> and clean up errors
>;

export const toggleTdoImage: Re<{ readonly tdoId: TDOId }> = (
  state,
  { payload }
) => ({
  ...state,
  selectedTdoImages:
    payload.tdoId in state.selectedTdoImages
      ? omit(state.selectedTdoImages, payload.tdoId)
      : { ...state.selectedTdoImages, [payload.tdoId]: true },
});

export const toggleTdoMedia: Re<{ readonly tdoId: TDOId }> = (
  state,
  { payload }
) => ({
  ...state,
  selectedTdoMedia:
    payload.tdoId in state.selectedTdoMedia
      ? omit(state.selectedTdoMedia, payload.tdoId)
      : { ...state.selectedTdoMedia, [payload.tdoId]: true },
});

export const removeTdo: Re<{ readonly tdoId: TDOId }> = (
  state,
  { payload }
) => ({
  ...state,
  tdos: omit(state.tdos, payload.tdoId),
  selectedTdoImages: omit(state.selectedTdoImages, payload.tdoId),
  selectedTdoMedia: omit(state.selectedTdoMedia, payload.tdoId),
});

const reducers = createReducer(defaultState, (builder) => {
  builder
    .addCase(GET_TDO_AUDIT_LOGS_SUCCESS, getTdoAuditLogSuccess)

    .addCase(DELETE_CASE, deleteCase)
    .addCase(DELETE_CASE_SUCCESS, deleteCaseSuccessful)
    .addCase(DELETE_CASE_FAILURE, deleteCaseFailure)

    .addCase(FETCH_TDOS_SUCCESS, fetchTdosSuccess)
    .addCase(FETCH_CASE_NOTIFICATIONS_SUCCESS, fetchCaseNotificationsSuccess)

    .addCase(PROCESS_CASE, processCase)
    .addCase(PROCESS_CASE_SUCCESS, processCaseSuccessful)
    .addCase(PROCESS_CASE_FAILURE, processCaseFailure)

    .addCase(UPDATE_CASE_NAME, updateCaseName)
    .addCase(UPDATE_CASE_NAME_SUCCESS, updateCaseNameSuccess)
    .addCase(UPDATE_CASE_NAME_FAILURE, updateCaseNameFailure)

    .addCase(CLEAR_NOTIFICATION_SUCCESS, updateNotificationSuccess)
    .addCase(DISMISS_NOTIFICATION_SUCCESS, updateNotificationSuccess)

    /* Local reducers */
    .addCase(TOGGLE_TDO_IMAGE, toggleTdoImage)
    .addCase(TOGGLE_TDO_MEDIA, toggleTdoMedia)
    .addCase(DELETE_TDO_SUCCESS, removeTdo)
    .addCase(SET_LOADERS, onSetLoaders)
    .addCase(SET_CASE_DETAILS, onSetCaseDetails);
});

export default reducers;

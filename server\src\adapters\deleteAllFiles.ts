import { TDOId } from '../model/brands';
import { callGQL } from '../api/callGraphql';
import { Messages } from '../errors/messages'
import { Logger } from '../logger';
import { RequestHeader } from '../model/requests';

export const deleteAllFilesAdapter = async (
  headers: RequestHeader,
  tdoIds: Array<TDOId>
) => {
    const query = `mutation deleteTDOs {
      ${tdoIds
        .map(
          (tdoId, index) => `
          i${index}: deleteTDO(id: "${tdoId}") {
            id
          }`
        )
        .join(',')}
    }`;
    const res = { isFailed: false }
    try {
      const deletedIds = await callGQL<Record<string, { id: string }>>(headers, query);
      Logger.log('deleted TDOIds: ' + JSON.stringify(Object.values(deletedIds)));
    } catch(err) {
      Logger.error(Messages.createRootFolder + JSON.stringify(err));
      res.isFailed = true;
    }
    return res;
};

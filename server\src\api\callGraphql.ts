import { request, Variables } from 'graphql-request';
import { Errors } from '../errors';
import Config from '../../apiConfig.json';
import { RequestHeader } from '../model/requests';
import { Logger } from '../logger';

export async function callGQL<T>(
  headers: RequestHeader,
  query: string,
  variables?: Variables,
) {
  const url = `${Config.apiRoot}/${Config.graphQLEndpoint}`;
  try {
    return await request<T>({
      url: url,
      document: query,
      variables: variables,
      requestHeaders: headers,
    });
  } catch (err: any) {
    Logger.log(err);
    throw Errors.InternalServerError(err);
  }
}

import { useRef, useState } from 'react';
import TdoGrid from './components/TdoGrid';
import DeleteButton from './components/DeleteButton';
import { SummaryTDO, TDOId } from '@cbsa-modules/universal/models';
import { DeleteTdoDialog } from '@cbsa-components/AddMedia/Dialogs';
import {
  Checkbox,
  CircularProgress,
  FormControlLabel,
  ThemeProvider,
  Typography,
} from '@mui/material';
// import * as styles from './index.scss';
import { useStyles } from './styles';
import { useIntl } from 'react-intl';
import { I18nTranslate } from '@common/i18n';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const MediaLibraryContent = ({
  type,
  tdos,
  deleteTdo,
  toggleTdo,
  selectedTdos,
  loadingTdos,
}: Props) => {
  const [isDeleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const scrollContainer = useRef<HTMLDivElement>(null);
  const allTdosSelected =
    tdos.length > 0 && Object.keys(selectedTdos).length === tdos.length;
  const classes = useStyles();
  const intl = useIntl();

  const renderTitle = () =>
    ({
      images: intl.formatMessage({ id: 'images' }),
      video: intl.formatMessage({ id: 'video' }),
    })[type];

  const toggleAllTdos = () =>
    tdos.forEach((tdo) => {
      if (allTdosSelected || !(tdo?.id in selectedTdos)) {
        toggleTdo(tdo?.id);
      }
    });

  return (
    <>
      <div className={classes.mediaLibrary}>
        <div className={'Header'}>
          <div className={'Title'}>{renderTitle()}</div>
          <FormControlLabel
            data-test="add-media-select-all"
            control={
              <Checkbox
                classes={{ root: 'Checkbox' }}
                onClick={toggleAllTdos}
                checked={allTdosSelected}
              />
            }
            label={
              <Typography classes={{ root: 'Label' }}>
                {I18nTranslate.TranslateMessage(
                  allTdosSelected ? 'deselectAll' : 'selectAll'
                )}
              </Typography>
            }
          />
        </div>
        <div className={'Content'} ref={scrollContainer}>
          <TdoGrid
            {...{
              tdos,
              toggleTdo,
              selectedTdos,
            }}
          />
          {loadingTdos && (
            <div className={'Loading'}>
              <CircularProgress thickness={2} size={60} />
            </div>
          )}
        </div>
        {Object.keys(selectedTdos).length > 0 && (
          <div className={'Footer'}>
            <DeleteButton onClick={() => setDeleteDialogOpen(true)}>
              Delete Selected
            </DeleteButton>
          </div>
        )}
      </div>
      <DeleteTdoDialog
        isOpen={isDeleteDialogOpen}
        onConfirm={() => {
          (
            Object.keys(selectedTdos) as Array<keyof typeof selectedTdos>
          ).forEach((tdoId) => deleteTdo(tdoId));
          setDeleteDialogOpen(false);
        }}
        onClose={() => setDeleteDialogOpen(false)}
      />
    </>
  );
};

interface Props {
  type: 'images' | 'video';
  tdos: SummaryTDO[];
  deleteTdo: (tdoId: TDOId) => void;
  toggleTdo: (tdoId: TDOId) => void;
  selectedTdos: { readonly [id: TDOId]: SummaryTDO | boolean };
  loadingTdos: boolean;
}

const MediaLibrary = ({
  type,
  tdos,
  deleteTdo,
  toggleTdo,
  selectedTdos,
  loadingTdos,
}: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <MediaLibraryContent
      type={type}
      tdos={tdos}
      deleteTdo={deleteTdo}
      toggleTdo={toggleTdo}
      selectedTdos={selectedTdos}
      loadingTdos={loadingTdos}
    />
  </ThemeProvider>
);

export default MediaLibrary;

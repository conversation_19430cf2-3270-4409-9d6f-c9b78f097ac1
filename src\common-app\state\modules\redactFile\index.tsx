import { createAction, createReducer } from '@reduxjs/toolkit';
import { TDOId } from '../universal/models/Brands';

export const REDACT_FILE_COMPLETED = createAction<{
  tdoId: TDOId;
}>('REDACT_FILE_COMPLETED');
export const REDACTING_FILE = createAction<{
  tdoId: TDOId;
}>('REDACTING_FILE');

const defaultState = {
  redactFiles: {} as Record<string, { status: string }>,
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(REDACTING_FILE, (state, action) => ({
      ...state,
      redactFiles: {
        ...state.redactFiles,
        [action.payload.tdoId]: {
          status: 'running',
        },
      },
    }))
    .addCase(REDACT_FILE_COMPLETED, (state, action) => ({
      ...state,
      redactFiles: {
        ...state.redactFiles,
        [action.payload.tdoId]: {
          status: 'complete',
        },
      },
    }));
});

export default reducer;
export const namespace = 'redactFile';
export const local = (state: { [namespace]: typeof defaultState }) =>
  state[namespace];

export const redactingFile = (tdoId: TDOId) => REDACTING_FILE({ tdoId });

export const redactFileCompleted = (tdoId: TDOId) =>
  REDACT_FILE_COMPLETED({ tdoId });

export const selectRedactFiles = (
  state: any
): Record<
  string,
  {
    status: string;
  }
> => local(state).redactFiles;

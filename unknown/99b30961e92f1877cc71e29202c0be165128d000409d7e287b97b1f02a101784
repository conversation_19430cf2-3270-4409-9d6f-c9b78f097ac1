import { Input, InputAdornment } from '@mui/material';
import { KeyboardEvent, useState, useRef } from 'react';
import { Clear, Check, Edit } from '@mui/icons-material';
import * as styles from './styles.scss';

export interface ClickToEditPropTypes {
  readonly wrapperClass: string;
  readonly inputClass: string;
  readonly textClass: string;
  readonly value: string;
  readonly endEditing: (value: string) => void;
}

const ClickToEdit = ({
  wrapperClass,
  inputClass,
  textClass,
  value,
  endEditing,
}: ClickToEditPropTypes) => {
  const inputRef = useRef<any>();
  const fragmentRef = useRef<HTMLElement>(null);
  const [onEditMode, setOnEditMode] = useState(false);

  const getIntoEditMode = () => {
    setOnEditMode(true);
    document.addEventListener('mousedown', handleClick);
  };

  const handleEnterKey = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleCheck();
    }
  };

  const handleClick = (e: MouseEvent) => {
    if (fragmentRef.current?.contains(e.target as Element) === false) {
      // outside click
      handleClear();
    }
  };

  const handleClear = () => {
    document.removeEventListener('mousedown', handleClick);
    setOnEditMode(false);
  };

  const handleCheck = () => {
    const newValue = inputRef.current.value;
    setOnEditMode(false);
    if (endEditing) {
      endEditing(newValue);
    }
  };

  return (
    <section
      className={`${styles.cTEwrapper} ${wrapperClass}`}
      ref={fragmentRef}
      data-testid="click-to-edit"
    >
      {onEditMode ? (
        <>
          <Input
            type="text"
            autoFocus
            fullWidth
            defaultValue={value}
            inputRef={inputRef}
            onKeyDown={handleEnterKey}
            // onKeyDown={handleEnterKey}
            className={`${styles.cTEinput} ${inputClass}`}
            data-testid="click-to-edit-input"
            endAdornment={
              <InputAdornment position="end">
                <Check
                  data-testid="click-to-edit-icon-check"
                  onClick={handleCheck}
                  className={styles.icon}
                />
                <Clear
                  data-testid="click-to-edit-icon-clear"
                  onClick={handleClear}
                  className={styles.icon}
                />
              </InputAdornment>
            }
          />
        </>
      ) : (
        <span
          className={`${styles.cTEtext} ${textClass}`}
          onClick={getIntoEditMode}
          data-testid="click-to-edit-span"
        >
          <p data-testid="click-to-edit-p" className={`${styles.editP}`}>
            {value}
          </p>
          <Edit
            data-testid="click-to-edit-icon-edit"
            className={styles.editIcon}
          />
        </span>
      )}
    </section>
  );
};

export default ClickToEdit;

import {
  put,
  select,
  take,
  takeLatest,
  race,
  delay,
} from 'typed-redux-saga/macro';

import { selectConfigEngines } from '../engines/selectors';
import { FETCH_ORGANIZATION_TDOS_SUCCESS } from '@redact-modules/mainPage/actions';
import {
  selectCurrentTdoId,
  selectDetectionRunning,
  selectGlobalSettings,
  selectMediaDuration,
  selectTdo,
  FETCH_TDO_SUCCESS,
  selectClusterList,
  selectUdrAsset,
  actionSetSelectedGroups,
  selectSelectedBoundingPolyGroup,
  selectTranscriptionView,
  selectDetectObjects,
  selectClusterMap,
} from '@common-modules/mediaDetails';
import {
  IN_LOAD_DETECTION_DATA,
  IN_SEND_TO_HEAD_DETECTION,
  OUT_DETECTION_CLUSTER_GROUPS,
  OUT_BOUNDING_POLY_COLLECTION,
  OUT_SELECTED_BOUNDINGPOLY_GROUP,
  ClusterMapItem,
} from '@worker';

import { ROUTE_HOME } from '../routing';
import { CREATE_JOB_FACES_ACTION, CREATE_JOB_FACES_SUCCESS } from './index';
import { selectFeatureFlags } from '@user-permissions';
import { ClusterItem, DetailTDO } from '@common-modules/mediaDetails/models';
import { deleteAllHeadDetectionResults } from '@common-modules/mediaDetails/services';
import { saveMediaData } from '@worker/services';

export function* watchCreateJobFacesData() {
  yield* takeLatest(CREATE_JOB_FACES_ACTION, watchCreateJobFacesDataHandle);
}

const purgeDetectionGroupsFromClusterMap = (clusterMap: {
  readonly [clusterId: string]: ClusterMapItem;
}) => {
  const filterClusterMap: {
    [clusterId: string]: ClusterMapItem;
  } = {};

  for (const [clusterId, clusterMapItem] of Object.entries(clusterMap)) {
    // only keep clusters that have udr groups
    if (clusterMapItem.udrGroupIds.length > 0) {
      filterClusterMap[clusterId] = {
        ...clusterMapItem,
        detectionGroupIds: [], // clear any detectionGroupIds
      };
    }
  }
  return filterClusterMap;
};

export function* watchCreateJobFacesDataHandle() {
  const tdo: DetailTDO | null = yield* select(selectTdo);

  if (tdo) {
    const {
      apiRoot,
      defaultClusterId,
      detectionEngineId,
      fastChunkerEngineId,
      outputWriterEngineId,
    } = yield* select(selectConfigEngines);
    const {
      faceDetectionThreshold,
      videoType,
      detectionRate,
      legacyClustering,
      clusterSimThreshold,
    } = yield* select(selectGlobalSettings);
    const featureFlags: Record<string, any> = yield* select(selectFeatureFlags);
    const detectObjects = yield* select(selectDetectObjects);
    const req = {
      apiRoot,
      tdoId: tdo.id,
      tdoName: tdo.name,
      clusterId: defaultClusterId,
      detectionEngineId,
      fastChunkerEngineId,
      outputWriterEngineId,
      confidenceThreshold: (faceDetectionThreshold || 60) / 100,
      videoType: videoType || 'Bodycam',
      detectionRate: detectionRate || 1,
      legacyClustering,
      clusterSimThreshold,
      featureFlags,
      detectObjects,
    };

    const clusterList = yield* select(selectClusterList);

    let clusterMap = yield* select(selectClusterMap);
    clusterMap = purgeDetectionGroupsFromClusterMap(clusterMap);

    const selectedBoundingPolys = yield* select(
      selectSelectedBoundingPolyGroup
    );
    const selectedPolyGroups = clearSelectedDetections(
      clusterList,
      selectedBoundingPolys
    );
    const { boundingPolys } = yield* select(selectUdrAsset);
    const { redactions: audioRedactions } = yield* select(
      selectTranscriptionView
    );

    /* Remove existing detections */
    yield* put(OUT_BOUNDING_POLY_COLLECTION({}));
    yield* put(OUT_DETECTION_CLUSTER_GROUPS({})); // clear the current detection cluster groups
    yield* put(
      OUT_SELECTED_BOUNDINGPOLY_GROUP({ selected: selectedPolyGroups })
    );
    yield* put(actionSetSelectedGroups({ selected: selectedPolyGroups }));
    yield* put(
      saveMediaData({
        tdoId: tdo.id,
        selectedPolyGroups,
        boundingPolys,
        clusterMap,
        audioRedactions,
      })
    );

    yield* put(deleteAllHeadDetectionResults());

    yield* put(IN_SEND_TO_HEAD_DETECTION(req));
  }
}

export function* watchCreateJobFacesSuccessHandle() {
  yield* take(FETCH_ORGANIZATION_TDOS_SUCCESS);
  const tdoId = yield* select(selectCurrentTdoId);
  if (!tdoId) {
    return;
  }
  const tdo = yield* select(selectTdo);
  const trimmedVideo = Boolean(
    tdo?.details.veritoneProgram?.sourceType === 'trimmed'
  );
  const { detectionEngineId } = yield* select(selectConfigEngines);
  const mediaDuration = yield* select(selectMediaDuration);

  let hadDetectionRunning = false;
  while (true) {
    const { ok } = yield* race({
      ok: delay(1000),
      __: take(ROUTE_HOME),
    });
    if (!ok) {
      return;
    }

    const tasks = yield* select(selectDetectionRunning);
    if (tasks.length === 0) {
      break;
    }
    hadDetectionRunning = true;
  }

  if (hadDetectionRunning || trimmedVideo) {
    const { detectionCategory } = yield* select(selectConfigEngines);
    yield* put(
      IN_LOAD_DETECTION_DATA({
        tdoId,
        engines: [detectionEngineId],
        mediaDuration,
        categoryId: detectionCategory,
      })
    );
  }
}

export function* watchCreateJobFacesSuccess() {
  yield* takeLatest(
    [CREATE_JOB_FACES_SUCCESS, FETCH_TDO_SUCCESS],
    watchCreateJobFacesSuccessHandle
  );
}

const clearSelectedDetections = (
  clusterList: ReadonlyArray<ClusterItem>,
  selectedPolyGroups: { readonly [id: string]: boolean | undefined }
) =>
  clusterList.reduce<Record<string, boolean | undefined>>(
    (prev, { groups }) => ({
      ...prev,
      ...groups.reduce<Record<string, boolean | undefined>>(
        (groupPrev, group) => ({
          ...groupPrev,
          ...group.segments.reduce<Record<string, boolean | undefined>>(
            (segmentPrev, segment) => ({
              ...segmentPrev,
              ...segment.subsegmentIds.reduce<
                Record<string, boolean | undefined>
              >(
                (subsegmentIds, subsegmentId) => ({
                  ...subsegmentIds,
                  ...(segment.type === 'udr' && {
                    [subsegmentId]: selectedPolyGroups[subsegmentId], // keep udrs
                  }),
                }),
                {}
              ),
              // ...(segment.type === 'udr' && { [segment.id]: selectedPolyGroups[id] }), // we dont need segment level ids anymore
            }),
            {}
          ),
        }),
        {}
      ),
      // ...(type === 'udr' && { [id]: selectedPolyGroups[id] }), // we dont need cluster level ids anymore
    }),
    {}
  );

// function* loadFacesStatus(payload) {
//   const { detectionEngineId } = yield select(selectConfigEngines);
//   yield put(fetchFacesStatus({ ...payload, detectionEngineId }));
// }

// export function* watchLoadFacesStatus() {
//   while (true) {
//     yield take(CREATE_JOB_FACES_SUCCESS);
//     const state = yield select(state => state);
//     const job = state[namespace].job;
//     yield race([
//       call(pollLoadFacesStatus, { jobId: job.id }),
//       take(FACES_JOB_STATUS_ACTION_STOP_POLLING),
//     ]);
//   }
// }

// export function* watchReLoadFacesStatus() {
//   yield takeLatest(FACES_JOB_STATUS_ACTION_START_POLLING, function*(action) {
//     yield race([
//       call(pollLoadFacesStatus, { jobId: action.payload.id }),
//       take(FACES_JOB_STATUS_ACTION_STOP_POLLING),
//     ]);
//   });
// }

// export function* pollLoadFacesStatus(payload) {
//   while (true) {
//     try {
//       yield call(loadFacesStatus, payload);
//       const { jobStatus } = yield select(state => state[namespace]);
//       if (jobStatus === 'complete' || jobStatus === 'failed') {
//         yield put({ type: FACES_JOB_STATUS_ACTION_STOP_POLLING });
//       }

//       yield call(delay, 15000);
//     } catch (err) {
//       console.error('Error polling');
//     }
//   }
// }

// // Utility function to delay effects
// function delay(millis) {
//   const promise = new Promise(resolve => {
//     setTimeout(() => resolve(true), millis);
//   });
//   return promise;
// }

import { SendToHeadDetectionResponse } from '@common/web-worker/services';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { createAction } from '@reduxjs/toolkit';
import { markWorkerAction } from '@utils';
import { TDOId } from '../universal/models/Brands';

interface CREATE_JOB_FACES_VARIABLES {
  id: TDOId;
  tdoName: string;
}
export const CREATE_JOB_FACES = 'request to create job for heads';
export const CREATE_JOB_FACES_SUCCESS =
  createGraphQLSuccessAction<SendToHeadDetectionResponse>(
    'create job for heads successfully'
  );
export const CREATE_JOB_FACES_FAILURE = createGraphQLFailureAction<
  any,
  CREATE_JOB_FACES_VARIABLES,
  true
>('create job for heads failed');
export const CREATE_JOB_FACES_ACTION = createAction<{
  targetId?: string;
  url?: string;
}>('heads/create-job');
export const actionSendToHeadDetection = () =>
  markWorkerAction(CREATE_JOB_FACES_ACTION({}));

export const ON_CHANGE_DETECT_OBJECTS = createAction<{
  name: string;
  value: boolean;
}>('on change detect objects');
export const actionOnChangeDetectObjects = (name: string, value: boolean) =>
  ON_CHANGE_DETECT_OBJECTS({ name, value });
export const SHOW_DETECT_OPTIONS_DIALOG = createAction<{
  value: boolean;
}>('show detect options dialog');
export const actionShowDetectOptionsDialog = (value: boolean) =>
  SHOW_DETECT_OPTIONS_DIALOG({ value });
export const DETECT_OPTIONS = {
  HEAD_OBJECTS: 'headObjects',
  PERSON_OBJECTS: 'personObjects',
};
// export const FACES_JOB_STATUS = 'request to get status of job';
// export const FACES_JOB_STATUS_SUCCESS = 'get status of job successfully';
// export const FACES_JOB_STATUS_FAILURE = 'get status of job failed';
// export const FACES_JOB_STATUS_ACTION_START_POLLING =
//   'faces/get-faces-status-start-polling';
// export const FACES_JOB_STATUS_ACTION_STOP_POLLING =
//   'faces/get-faces-status-stop-polling';

// export const FACES_JOB_STATUS_CLEAR_ACTION = 'faces/get-faces-clean-status';

// const defaultState: {
//   job: null | {};
//   creatingJobFaces: boolean;
//   failedCreatingJobFaces: boolean;
//   jobStatus: null | string;
//   gettingJobStatus: boolean;
//   failedGettingJobStatus: boolean;
// } = {
//   job: null,
//   creatingJobFaces: false,
//   failedCreatingJobFaces: false,

//   jobStatus: null,
//   gettingJobStatus: false,
//   failedGettingJobStatus: false,
// };

// const reducer = createReducer(defaultState, {
//   [CREATE_JOB_FACES]: state => ({
//     ...state,
//     creatingJobFaces: true,
//     failedCreatingJobFaces: false,
//   }),
//   [CREATE_JOB_FACES_SUCCESS]: (state, action) => ({
//     ...state,
//     creatingJobFaces: false,
//     job: action.payload.createJob,
//     failedCreatingJobFaces: true,
//   }),
//   [CREATE_JOB_FACES_FAILURE]: state => ({
//     ...state,
//     creatingJobFaces: false,
//     job: {},
//     failedCreatingJobFaces: true,
//   }),
// [FACES_JOB_STATUS]: state => ({
//   ...state,
//   gettingJobStatus: true,
//   failedGettingJobStatus: false,
// }),
// [FACES_JOB_STATUS_SUCCESS]: (state, action) => ({
//   ...state,
//   gettingJobStatus: false,
//   job: action.payload.job,
//   jobStatus: action.payload.job.status,
//   failedGettingJobStatus: false,
// }),
// [FACES_JOB_STATUS_FAILURE]: state => ({
//   ...state,
//   gettingJobStatus: false,
//   failedGettingJobStatus: true,
// }),
// [FACES_JOB_STATUS_ACTION_STOP_POLLING]: state => ({
//   ...state,
//   job: null,
//   creatingJobFaces: false,
//   failedCreatingJobFaces: false,

//   gettingJobStatus: false,
//   failedGettingJobStatus: false,
// }),
// [FACES_JOB_STATUS_CLEAR_ACTION]: state => ({
//   ...state,
//   jobStatus: null,
// }),
// });

// export default reducer;
// export const namespace = 'facesTab';
// export const local = (state: any) => state[namespace];

// export const fetchFacesStatus = ({
//   jobId,
//   detectionEngineId,
// }: {
//   jobId: string;
//   detectionEngineId: string;
// }) => async (dispatch: (action: UnknownAction) => void, getState: () => any) => {
//   const query = `query($jobId: ID!) {
//     job(id: $jobId) {
//       status
//       createdDateTime
//       tasks {
//         records {
//           startedDateTime
//           status
//           engineId
//         }
//       }
//     }
//   }`;

//   return await callGraphQLApi({
//     actionTypes: [
//       FACES_JOB_STATUS,
//       FACES_JOB_STATUS_SUCCESS,
//       FACES_JOB_STATUS_FAILURE,
//     ],
//     query,
//     variables: { jobId },
//     dispatch: action =>
//       dispatch(
//         action.type === FACES_JOB_STATUS_SUCCESS
//           ? {
//               ...action,
//               payload: {
//                 job: {
//                   ...action.payload.job,
//                   ...action.payload.job.tasks.records.find(
//                     ({ engineId }) => engineId === detectionEngineId
//                   ),
//                 },
//               },
//             }
//           : action
//       ),
//     getState,
//     bailout: state => gettingJobStatusAreLoading(state),
//   });
// };

// export const selectJob = (state: any) => local(state).job;

// export const createJobAreLoading = (state: any) =>
//   local(state).creatingJobFaces;

// export const selectJobStatus = (state: any) => local(state).jobStatus;

// export const gettingJobStatusAreLoading = (state: any) =>
//   local(state).gettingJobStatus;

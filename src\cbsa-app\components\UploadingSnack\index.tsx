import { Fragment } from 'react';
import { concat, some } from 'lodash';
import { WatchLater } from '@mui/icons-material';
import { CircularProgress } from '@mui/material';
import { CSSTransition } from 'react-transition-group';
import { FailureIcon, SuccessIcon } from './components';
import { UploadMedia } from '@cbsa-modules/universal';
import * as styles from './index.scss';

const UploadingSnack = ({
  uploadingFiles,
  removeSuccessfulFile,
  retryFailedFile,
}: Props) => {
  const { queue, processing, successful, failed } = uploadingFiles;
  const mediaList = concat(queue, processing, successful, failed).sort(
    (a, b) => a.key - b.key
  );

  const isQueued = (uploadFile: UploadMedia) => some(queue, uploadFile);
  const isProcessing = (uploadFile: UploadMedia) =>
    some(processing, uploadFile);
  const isSuccessful = (uploadFile: UploadMedia) =>
    some(successful, uploadFile);
  const isFailure = (uploadFile: UploadMedia) => some(failed, uploadFile);

  const mapIcon = (uploadFile: UploadMedia) => {
    if (isQueued(uploadFile)) {
      return (
        <WatchLater
          data-testid="watch-later-icon"
          style={{ color: '#D57200' }}
        />
      );
    } else if (isProcessing(uploadFile)) {
      return (
        <CircularProgress
          data-testid="circular-progress-icon"
          size={20}
          thickness={2}
          style={{
            color: '#005C7E',
          }}
        />
      );
    } else if (isSuccessful(uploadFile)) {
      return (
        <SuccessIcon
          data-testid="success-icon"
          onClick={() => removeSuccessfulFile(uploadFile)}
        />
      );
    } else if (isFailure(uploadFile)) {
      return (
        <FailureIcon
          data-testid="failure-icon"
          onClick={() => retryFailedFile(uploadFile)}
        />
      );
    }
  };

  return (
    <CSSTransition
      in={mediaList.length > 0}
      timeout={300}
      classNames={{ ...styles }}
      unmountOnExit
    >
      <div data-testid="uploading-snack" className={styles.uploadingSnack}>
        <div
          data-testid="uploading-snack-title"
          className={styles.uploadingSnackTitle}
        >
          Files Uploading
        </div>
        <div className={styles.uploadingSnackItems}>
          {mediaList.map((uploadFile) => (
            <Fragment key={uploadFile.key}>
              <hr />
              <div className={styles.uploadingSnackItem}>
                <div
                  data-testid="file-name"
                  className={styles.uploadingSnackItemText}
                >
                  {uploadFile.file.name}
                </div>
                <div className={styles.uploadingSnackItemProgress}>
                  {mapIcon(uploadFile)}
                </div>
              </div>
            </Fragment>
          ))}
        </div>
      </div>
    </CSSTransition>
  );
};

interface Props {
  uploadingFiles: {
    queue: UploadMedia[];
    processing: UploadMedia[];
    successful: UploadMedia[];
    failed: UploadMedia[];
  };
  removeSuccessfulFile: (uploadFile: UploadMedia) => void;
  retryFailedFile: (uploadFile: UploadMedia) => void;
}

export default UploadingSnack;

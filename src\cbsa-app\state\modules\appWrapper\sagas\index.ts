import { fetchC<PERSON><PERSON><PERSON><PERSON>, fetchCasesF<PERSON>ureHandler } from './fetchCases';
import { fetchMediaHandler, fetchMediaFailureHandler } from './fetchMedia';
import {
  addFile,
  checkQueue,
  retryFailedFile,
  uploadMedia,
  uploadMediaSuccess,
  uploadMediaFailure,
} from './uploadMedia';

export interface Action<P, M = unknown> {
  type: string;
  payload: P;
  meta: M;
}

export const appWrapperSagas = [
  fetchCasesHandler,
  fetchCasesFailureHandler,

  fetchMediaHandler,
  fetchMediaFailureHandler,

  addFile,
  checkQueue,
  retryFailedFile,
  uploadMedia,
  uploadMediaSuccess,
  uploadMediaFailure,
];

import {
  CLOSE_UPGRADE_ACCOUNT,
  HIDE_REQUEST_QUOTE_FORM,
  MAKE_PURCHASE,
  MakePurchasePayload,
  OPEN_UPGRADE_ACCOUNT,
  SHOW_REQUEST_QUOTE_FORM,
} from '../actions';
import { OnboardingState } from '../models';
import { defaultState } from '../store';
import { CaseReducer, createReducer } from '@reduxjs/toolkit';

type Re<P = unknown> = CaseReducer<
  OnboardingState, // UpgradeAccountState,
  { type: string; payload: P }
>;

const onActionMakePurchase: Re<MakePurchasePayload> = (state, { payload }) => ({
  ...state,
  unknown: payload,
});

const onActionOpenUpgradeAccount: Re<void> = (state) => ({
  ...state,
  isUpgradeAccountOpen: true,
  checkForSubscriptionUpdates: true,
});

const onActionCloseUpgradeAccount: Re<void> = (state) => ({
  ...state,
  isUpgradeAccountOpen: false,
});

const onActionShowRequestQuoteForm: Re<void> = (state) => ({
  ...state,
  showRequestQuoteForm: true,
});

const onActionHideRequestQuoteForm: Re<void> = (state) => ({
  ...state,
  showRequestQuoteForm: false,
});

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(MAKE_PURCHASE, onActionMakePurchase)
    .addCase(OPEN_UPGRADE_ACCOUNT, onActionOpenUpgradeAccount)
    .addCase(CLOSE_UPGRADE_ACCOUNT, onActionCloseUpgradeAccount)
    .addCase(SHOW_REQUEST_QUOTE_FORM, onActionShowRequestQuoteForm)
    .addCase(HIDE_REQUEST_QUOTE_FORM, onActionHideRequestQuoteForm);
});

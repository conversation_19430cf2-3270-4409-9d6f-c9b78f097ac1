import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { SendEmailInviteResponse } from '../services';
import { createAction } from '@reduxjs/toolkit';

export const SEND_EMAIL_INVITE = createAction<SendEmailInvitePayload>(
  'action/SEND_EMAIL_INVITE'
);
export const SEND_EMAIL_INVITE_SUCCESS =
  createGraphQLSuccessAction<SendEmailInviteResponse>(
    'action/SEND_EMAIL_INVITE_SUCCESS'
  );
export const SEND_EMAIL_INVITE_FAILURE = createGraphQLFailureAction(
  'action/SEND_EMAIL_INVITE_FAILURE'
);

export interface SendEmailInvitePayload {
  readonly emails: ReadonlyArray<string>;
}
export const actionSendEmailInvite = (payload: SendEmailInvitePayload) =>
  SEND_EMAIL_INVITE(payload);

export const OPEN_SEND_EMAIL_INVITE = createAction(
  'action/OPEN_SEND_EMAIL_INVITE'
);
export const CLOSE_SEND_EMAIL_INVITE = createAction(
  'action/CLOSE_SEND_EMAIL_INVITE'
);

export const actionOpenSendEmailInvite = () => OPEN_SEND_EMAIL_INVITE();

export const actionCloseSendEmailInvite = () => CLOSE_SEND_EMAIL_INVITE();

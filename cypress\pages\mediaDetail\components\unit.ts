import {
  DataTestSelector,
  DataTestSelectorMessages,
  DataTestSelectorRightPanelVideo,
  DataTestSelectorVideoTab,
} from '../../../support/helperFunction/mediaDetailHelper';
import { mediaDetailPage } from '../mediaDetail.po';

export function checkMenuOptionsVisibility(options: string[]) {
  options.forEach((optionText) => {
    cy.getByRoles('option').should('contain.text', optionText);
  });
}

export const verifyVidPanelHeader = () => {
  mediaDetailPage.detectFaceButton().should('be.disabled').and('exist');
  mediaDetailPage.redactionEffectsDropdown().should('be.visible');
  mediaDetailPage.rightPanelFilterContainer().should('be.visible');
};

export const verifyVideoTabObjectList = () => {
  mediaDetailPage.checkBoxContainer().should('be.visible');
  mediaDetailPage.sortByTimeButton().should('be.visible');
  mediaDetailPage
    .listOfObjects()
    .should('be.visible')
    .find(`[data-testid=${DataTestSelector.ClusterRow}]`)
    .should('have.length.at.least', 1);
};

export const verifyRedactionEffectsDefaults = () => {
  mediaDetailPage.redactionType().contains('Blur').should('be.visible');
  mediaDetailPage.blurLevelInput().should('have.value', '10');
  mediaDetailPage.restoreDefaultButton().should('be.visible');
  mediaDetailPage.cancelButton().should('be.visible');
  mediaDetailPage.saveButtonRedaction().should('be.visible');
};

export const verifyHeaderIcons = () => {
  mediaDetailPage.header.undoButton().should('be.visible');
  mediaDetailPage.header.redoButton().should('be.visible');
  mediaDetailPage.header.notificationButton().should('be.visible');
  mediaDetailPage.header.shortcutsButton().should('be.visible');
  mediaDetailPage.header.mediaSettingsBtn().should('be.visible');
  mediaDetailPage.header.helpCenterButton().should('be.visible');
  mediaDetailPage.mediaDetailCloseBtn().should('be.visible');
};

export const verifyDetectingObjects = () => {
  mediaDetailPage
    .engineProcessingTitle()
    .should('exist')
    .within(() => {
      mediaDetailPage.detectingObjects().should('be.visible');
      mediaDetailPage
        .engineProcessingMessage()
        .contains(DataTestSelectorMessages.DetectingObjectsMessage1)
        .should('be.visible');
      mediaDetailPage
        .engineProcessingMessage()
        .contains(DataTestSelectorMessages.DetectingObjectsMessage2)
        .should('be.visible');
      mediaDetailPage
        .engineProcessingMessage()
        .contains(DataTestSelectorMessages.DetectingObjectsMessage3)
        .should('be.visible');
      cy.get(mediaDetailPage.homePageButton())
        .contains('HOMEPAGE')
        .should('be.visible');
    });
};

export const verifyBlurLevelInputMaximumValue = () =>
  mediaDetailPage
    .blurLevelInput()
    .invoke('val')
    .then((value) => {
      const blurValue = Number(value);
      expect(blurValue).to.be.at.most(10);
      return blurValue;
    });

export const setFileName = (fileName: string) => {
  mediaDetailPage.fileName().click();
  mediaDetailPage.fileName().find('input').clear();
  mediaDetailPage.fileName().find('input').type(fileName);
};

export const verifyDetectionsChecked = () => {
  mediaDetailPage
    .filterDetectionsDialog()
    .should('be.visible')
    .within(() => {
      mediaDetailPage.UDRFilterCheckbox().should('be.checked');
      mediaDetailPage.headerFilterCheckbox().should('be.checked');
      mediaDetailPage.personFilterCheckbox().should('be.checked');
      mediaDetailPage.allFilterCheckbox().should('be.checked');
    });
};

export const verifyRemoveFromOutputColumn = () => {
  mediaDetailPage.UDRFilterRemoveCheckBox().should('not.be.checked');
  mediaDetailPage.headFilterRemoveCheckBox().should('not.be.checked');
  mediaDetailPage.personFilterRemoveCheckBox().should('not.be.checked');
};

export const isDetectionUnchecked = (detection: string) => {
  cy.get('body').then(($body) => {
    if (
      $body.find(
        `[data-testid=${DataTestSelectorRightPanelVideo.ObjectItemContainer}]`
      ).length > 0
    ) {
      cy.getDataIdCy({
        idAlias: String(DataTestSelectorRightPanelVideo.ObjectItemContainer),
      })
        .contains(detection)
        .should('not.exist');
    }

    return undefined;
  });
};

export const selectsPerson = () => {
  cy.get(
    '[data-testid="cluster-list-sort-container"] [data-testid="check-box-container"]'
  ).each(($container) => {
    if ($container.find('svg').length > 0) {
      cy.wrap($container).click();
    }
  });

  cy.get('[data-testid="clusterList-row"]').then(($rows) => {
    let selectedRowId: string | undefined;

    for (let i = 0; i < $rows.length; i++) {
      const $row = Cypress.$($rows[i]);
      const detectionName = $row
        .find(`[data-test=${DataTestSelectorRightPanelVideo.DetectionName}]`)
        .text()
        .trim();

      if (detectionName === 'PERSON') {
        selectedRowId = $row.attr('id');
        cy.wrap($row).find('[data-testid="check-box-container"]').click();
        break;
      }
    }

    cy.wrap(selectedRowId).as('selectedRowId');
    return undefined;
  });
};

export const resizesTheBounding = () => {
  mediaDetailPage.firstObjectRedaction().click();

  mediaDetailPage.firstObjectRedaction().within(() => {
    cy.get('[style*="cursor: se-resize"]').then(($handle) => {
      const handle = $handle[0];
      const coords = handle.getBoundingClientRect();

      cy.wrap($handle).trigger('mousedown', {
        button: 0,
        clientX: coords.x + 5,
        clientY: coords.y + 5,
        force: true,
      });

      cy.wrap($handle).trigger('mousemove', {
        clientX: coords.x + 100,
        clientY: coords.y + 100,
        force: true,
      });

      cy.wrap($handle).trigger('mouseup', { force: true });

      return null;
    });
  });
};

export const verifyObjectIsResized = () => {
  cy.getDataIdCy({ idAlias: DataTestSelectorVideoTab.ObjectRedaction })
    .first()
    .then(($box) => {
      const box = $box[0];
      const rect = box.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;

      expect(width).to.be.greaterThan(0);
      expect(height).to.be.greaterThan(0);
      return undefined;
    });
};

export const checkProgressIndicator = (maxRetries = 100, retryCount = 0) => {
  // todo: Wait using API call instead of DOM element
  cy.get('body').then(($body) => {
    if (
      $body.find('[data-testid="engine-processing-circular-progress"]').length >
      0
    ) {
      cy.log(
        `Progress indicator still visible (Retry ${retryCount + 1}/${maxRetries}), waiting...`
      );
      if (retryCount < maxRetries) {
        // eslint-disable-next-line cypress/no-unnecessary-waiting
        cy.wait(3000);
        checkProgressIndicator(maxRetries, retryCount + 1);
        return;
      } else {
        const errorMessage = `Progress indicator still visible after ${maxRetries} retries. Job likely failed or is taking too long.`;
        cy.log(errorMessage);
        throw new Error(errorMessage);
      }
    } else {
      return cy.log(
        `Progress indicator no longer visible after ${retryCount + 1} check(s), job completed`
      );
    }
  });
};

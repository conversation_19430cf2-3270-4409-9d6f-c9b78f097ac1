### Steps to create comments schema using Developer App

1. Create new schema with the following:
- Title: Veritone Redact Audit Log
- Description: An audit log entry for Veritone Redact

2. Enter schema:
```
{
	"$id": "http://example.com/example.json",
	"type": "object",
	"$schema": "http://json-schema.org/draft-06/schema#",
	"properties": {
		"user": {
			"$id": "/properties/user",
			"type": "string"
		},
		"action": {
			"$id": "/properties/action",
			"type": "string"
		},
		"dateTime": {
			"$id": "/properties/dateTime",
			"type": "string"
		}
	},
	"definitions": {}
}
```

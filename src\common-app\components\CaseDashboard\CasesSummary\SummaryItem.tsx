import makeStyles from '@mui/styles/makeStyles';

import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';
import { ThemeProvider, Theme } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const useStyles = makeStyles((theme: Theme) => ({
  mainNumberGrid: {
    marginRight: theme.spacing(0.5),
  },
  mainNumberText: {
    fontSize: theme.spacing(5),
    fontWeight: theme.typography.fontWeightLight,
  },
  subtextHeadline: {
    width: '70px',
    paddingTop: '20px',
    fontSize: `${theme.typography.fontSize}px`,
    color: theme.palette.text.secondary,
    fontWeight: theme.typography.fontWeightBold,
    textTransform: 'uppercase',
  },
  subtextData: {
    fontSize: `${theme.typography.fontSize}px`,
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightBold,
    marginRight: theme.spacing(0.25),
  },
  subtextUnits: {
    fontSize: `${theme.typography.fontSize}px`,
    color: theme.palette.text.secondary,
    fontWeight: theme.typography.fontWeightRegular,
    textTransform: 'uppercase',
  },
}));

interface SummaryItemPropTypes {
  mainNumber: string | number;
  subtextHeadline: string;
  subtextData?: string | number;
  subtextUnits?: string;
}

const SummaryItemContent = (props: SummaryItemPropTypes) => {
  const { mainNumber, subtextHeadline, subtextData, subtextUnits } = props;

  const classes = useStyles();

  return (
    <Grid>
      <Grid
        container
        direction="row"
        justifyContent="flex-start"
        alignItems="center"
      >
        <Grid className={classes.mainNumberGrid}>
          <Typography
            variant="h4"
            color="primary"
            data-testid="summaryCount"
            classes={{ root: classes.mainNumberText }}
          >
            {mainNumber}
          </Typography>
        </Grid>
        <Grid>
          <Grid
            container
            direction="column"
            justifyContent="center"
            alignItems="flex-start"
          >
            <Grid>
              <Typography
                data-testid="summaryType"
                classes={{ root: classes.subtextHeadline }}
              >
                {subtextHeadline}
              </Typography>
            </Grid>
            <Grid>
              <Typography>
                <span className={classes.subtextData}>{subtextData}</span>
                <span className={classes.subtextUnits}>{subtextUnits}</span>
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

const SummaryItem = (props: SummaryItemPropTypes) => (
  <ThemeProvider theme={defaultTheme}>
    <SummaryItemContent {...props} />
  </ThemeProvider>
);

export default SummaryItem;

import { sagaIntl } from '@i18n';
import { enqueueSnackbar } from '@common-modules/snackbar';
import * as Actions from '@cbsa-modules/appWrapper/actions';
import { put, select, takeEvery } from 'typed-redux-saga/macro';
import * as Services from '@cbsa-modules/appWrapper/services';
import { selectUploadingFiles } from '@cbsa-modules/appWrapper';

export function* addFile() {
  yield* takeEvery(Actions.ADD_FILE, function* ({ payload }) {
    const { file, caseId } = payload;
    if (caseId) {
      yield* put(Actions.queueFile({ file, caseId }));
    }
    yield* put(Actions.checkQueue());
  });
}

export function* checkQueue() {
  yield* takeEvery(Actions.CHECK_QUEUE, function* () {
    const { queue } = yield* select(selectUploadingFiles);
    if (queue.length > 0) {
      yield* put(Actions.uploadMedia());
    }
  });
}

export function* retryFailedFile() {
  yield* takeEvery(Actions.RETRY_FAILED_FILE, function* () {
    yield* put(Actions.checkQueue());
  });
}

export function* uploadMedia() {
  yield* takeEvery(Actions.UPLOAD_FILE, function* () {
    const { queue, processing } = yield* select(selectUploadingFiles);
    if (processing.length < 5) {
      /* Move file to processing state */
      const uploadFile = queue[0];
      if (uploadFile) {
        yield* put(Actions.processFile(uploadFile));

        /* Upload file */
        yield* put(
          Services.uploadMedia({
            uploadFile,
          })
        );
      }
    }
  });
}

export function* uploadMediaSuccess() {
  yield* takeEvery(Actions.UPLOAD_FILE_SUCCESS, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'fileUploadSuccess',
        }),
        variant: 'success',
      })
    );

    /* Check for queued files */
    yield* put(Actions.checkQueue());
  });
}

export function* uploadMediaFailure() {
  yield* takeEvery(Actions.UPLOAD_FILE_FAILURE, function* ({ payload }) {
    const intl = sagaIntl();
    const { error } = payload;

    console.log(error);
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({ id: 'fileUploadFail' }),
        variant: 'error',
      })
    );

    /* Check for queued files */
    yield* put(Actions.checkQueue());
  });
}

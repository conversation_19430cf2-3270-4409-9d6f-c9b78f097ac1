import {
  createGraph<PERSON>FailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraph<PERSON>Api';
import { AIWareNotification } from '../AddMediaStore';
import { UPDATE_NOTIFICATION_QUERY_RESPONSE } from '../services/queries/updateNotification';
import { createAction } from '@reduxjs/toolkit';

export const DISMISS_NOTIFICATION = createAction<{
  notification: AIWareNotification;
}>('CBSA/DISMISS_NOTIFICATION');
export const DISMISS_NOTIFICATION_SUCCESS =
  createGraphQLSuccessAction<UPDATE_NOTIFICATION_QUERY_RESPONSE>(
    'CBSA/DISMISS_NOTIFICATION_SUCCESS'
  );
export const DISMISS_NOTIFICATION_FAILURE = createGraphQLFailureAction(
  'CBSA/DISMISS_NOTIFICATION_FAILURE'
);

export const CLEAR_NOTIFICATION = createAction<{
  notification: AI<PERSON>areNotification;
}>('CBSA/CLEAR_NOTIFICATION');
export const CLEAR_NOTIFICATION_SUCCESS =
  createGraphQLSuccessAction<UPDATE_NOTIFICATION_QUERY_RESPONSE>(
    'CBSA/CLEAR_NOTIFICATION_SUCCESS'
  );
export const CLEAR_NOTIFICATION_FAILURE = createGraphQLFailureAction(
  'CBSA/CLEAR_NOTIFICATION_FAILURE'
);

export const dismissNotification = (notification: AIWareNotification) =>
  DISMISS_NOTIFICATION({ notification });

export const clearNotification = (notification: AIWareNotification) =>
  CLEAR_NOTIFICATION({ notification });

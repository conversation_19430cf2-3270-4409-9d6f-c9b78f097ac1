import callGrap<PERSON><PERSON><PERSON><PERSON> from '@helpers/callGraph<PERSON><PERSON><PERSON>';
import {
  Thunk,
  lookupLatestCaseNotificationSchemaId,
} from '@cbsa-modules/universal';
import {
  UPDATE_NOTIFICATION_QUERY,
  UPDATE_NOTIFICATION_QUERY_RESPONSE,
} from './queries/updateNotification';
import {
  DISMISS_NOTIFICATION_SUCCESS,
  DISMISS_NOTIFICATION_FAILURE,
  CLEAR_NOTIFICATION_SUCCESS,
  CLEAR_NOTIFICATION_FAILURE,
  AIWareNotification,
} from '@cbsa-modules/addMedia';
import { NOOP } from '@cbsa-modules/universal/actions';

export const updateNotification: Thunk<{
  readonly notification: AIWareNotification;
  readonly status: string;
}> =
  ({ notification, status }) =>
  async (dispatch, getState) => {
    const { id, data } = notification;
    const schemaId = await lookupLatestCaseNotificationSchemaId(
      dispatch,
      getState
    );
    if (!schemaId) {
      return;
    }
    const isDismissed = data.status === 'dismissed';
    return await callGraph<PERSON><PERSON>pi<UPDATE_NOTIFICATION_QUERY_RESPONSE>({
      actionTypes: [
        NOOP,
        isDismissed ? DISMISS_NOTIFICATION_SUCCESS : CLEAR_NOTIFICATION_SUCCESS,
        isDismissed ? DISMISS_NOTIFICATION_FAILURE : CLEAR_NOTIFICATION_FAILURE,
      ],
      query: UPDATE_NOTIFICATION_QUERY,
      variables: {
        id,
        schemaId,
        data: {
          ...data,
          status,
        },
      },
      dispatch,
      getState,
    });
  };

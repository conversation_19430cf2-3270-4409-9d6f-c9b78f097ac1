import type { CasesSummaryInfo, MainPageStore } from './MainPageStore';
import { ROUTE_MEDIA_DETAILS } from '@common-modules/routing';
import { Case, FolderId, TDOId, TreeObjectId } from '@cbsa-modules/universal';
import { createGraphQLFailureAction } from '@helpers/callGraphQLApi';
import { createAction } from '@reduxjs/toolkit';

const prefix = 'CBSA/MainPage';

export const SET_CASES_SUMMARY = createAction<MainPageStore['casesSummary']>(
  `${prefix}/SET_CASES_SUMMARY`
);
export const SET_SEARCH_TEXT = createAction<MainPageStore['searchText']>(
  `${prefix}/SET_SEARCH_TEXT`
);
export const SET_STATUS_FILTER = createAction<MainPageStore['statusFilter']>(
  `${prefix}/SET_STATUS_FILTER`
);
export const SET_SHOW_ARCHIVED = createAction<MainPageStore['showArchived']>(
  `${prefix}/SET_SHOW_ARCHIVED`
);
export const SET_CASES_LIST = createAction<MainPageStore['casesList']>(
  `${prefix}/SET_CASES_LIST`
);
export const SET_CASES_TOTAL = createAction<MainPageStore['casesTotal']>(
  `${prefix}/SET_CASES_TOTAL`
);
export const SET_CASES_SORT_COLUMN = createAction<
  MainPageStore['casesSortColumn']
>(`${prefix}/SET_CASES_SORT_COLUMN`);
export const SET_CASES_SORT_ORDER = createAction<
  MainPageStore['casesSortOrder']
>(`${prefix}/SET_CASES_SORT_ORDER`);
export const SET_PAGINATION_AMOUNT = createAction<
  MainPageStore['paginationAmount']
>(`${prefix}/SET_PAGINATION_AMOUNT`);
export const SET_PAGINATION_START = createAction<
  MainPageStore['paginationStart']
>(`${prefix}/SET_PAGINATION_START`);
export const FETCH_CASES = createAction(`${prefix}/FETCH_CASES`);
export const FETCH_CASES_SUCCESS = createAction<{
  results: Case[];
  totalResults: number;
}>(`${prefix}/FETCH_CASES_SUCCESS`);
export const FETCH_CASES_FAILURE = createAction<any>(
  `${prefix}/FETCH_CASES_FAILURE`
);
export const FETCH_SUMMARY = createAction(`${prefix}/FETCH_SUMMARY`);
export const FETCH_SUMMARY_SUCCESS = createAction<CasesSummaryInfo>(
  `${prefix}/FETCH_SUMMARY_SUCCESS`
);
export const FETCH_SUMMARY_FAILURE = createAction<any>(
  `${prefix}/FETCH_SUMMARY_FAILURE`
);
export const PUT_CASE = createAction<string>(`${prefix}/PUT_CASE`);
export const PUT_CASE_SUCCESS = createAction<{
  caseId: FolderId;
  caseData: Case;
}>(`${prefix}/PUT_CASE_SUCCESS`);
export const PUT_CASE_FAILURE = createGraphQLFailureAction<string>(
  `${prefix}/PUT_CASE_FAILURE`
);
export const DELETE_CASE = createAction<TreeObjectId>(`${prefix}/DELETE_CASE`);
export const DELETE_CASE_SUCCESS = createAction<FolderId>(
  `${prefix}/DELETE_CASE_SUCCESS`
);
export const DELETE_CASE_FAILURE = createAction<string>(
  `${prefix}/DELETE_CASE_FAILURE`
);
export const ARCHIVE_CASE = createAction<TreeObjectId>(
  `${prefix}/ARCHIVE_CASE`
);
export const ARCHIVE_CASE_SUCCESS = createAction<FolderId>(
  `${prefix}/ARCHIVE_CASE_SUCCESS`
);
export const ARCHIVE_CASE_FAILURE = createAction<string>(
  `${prefix}/ARCHIVE_CASE_FAILURE`
);
export const CBSA_MAINPAGE_NOOP = `${prefix}/NOOP`;

export const setCasesSummary = (payload: MainPageStore['casesSummary']) =>
  SET_CASES_SUMMARY(payload);

export const setSearchText = (payload: MainPageStore['searchText']) =>
  SET_SEARCH_TEXT(payload);

export const setStatusFilter = (payload: MainPageStore['statusFilter']) =>
  SET_STATUS_FILTER(payload);

export const setShowArchived = (payload: MainPageStore['showArchived']) =>
  SET_SHOW_ARCHIVED(payload);

export const setCasesList = (payload: MainPageStore['casesList']) =>
  SET_CASES_LIST(payload);

export const setCasesTotal = (payload: MainPageStore['casesTotal']) =>
  SET_CASES_TOTAL(payload);

export const setCasesSortColumn = (payload: MainPageStore['casesSortColumn']) =>
  SET_CASES_SORT_COLUMN(payload);

export const setCasesSortOrder = (payload: MainPageStore['casesSortOrder']) =>
  SET_CASES_SORT_ORDER(payload);

export const setPaginationAmount = (
  payload: MainPageStore['paginationAmount']
) => SET_PAGINATION_AMOUNT(payload);

export const setPaginationStart = (payload: MainPageStore['paginationStart']) =>
  SET_PAGINATION_START(payload);

export const putNewCase = (payload: Case['name']) => PUT_CASE(payload);

export const deleteCase = (payload: TreeObjectId) => DELETE_CASE(payload);

export const openMediaDetails = (tdoId: TDOId) =>
  ROUTE_MEDIA_DETAILS({ tdoId });

export const archiveCase = (caseId: TreeObjectId) => ARCHIVE_CASE(caseId);

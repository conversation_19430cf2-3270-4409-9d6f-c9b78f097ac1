import {
  CLUSTER_TYPE,
  DEFAULT_GLOBAL_SETTINGS,
  DETECTION_COLLECTION_KEYS,
  FILTER_PARAMETER_TYPE,
  FILTER_PARAMETER_TYPES,
  SEGMENT_SORT_BY_TYPE_ORDER,
  SEGMENT_TYPE,
} from '@helpers/constants';
import { getOverlayStyles } from '@redact/components/MediaDetailsPage/Editor/MediaPlayer/getOverlayStyles';
import { selectFeatureFlags } from '@user-permissions';
import {
  NonEmptyArray,
  findClusterGroupIds,
  getDominantSegmentType,
} from '@utils';
import { CollectionWrapper, GroupedBoundingPoly, search } from '@worker';
import { get, groupBy, isEmpty, omitBy, sortBy } from 'lodash';
import {
  createSelector,
  createSelectorCreator,
  lruMemoize as defaultMemoize,
} from 'reselect';
import {
  ClusterItem,
  ClusterItemGroup,
  ClusterSegment,
  TabState,
  ViewSettings,
} from '../models';
import { namespace } from '../store';
import { DetectionAssetState, Extra } from '../store.models';
import { selectCurrentPosition } from './general';
import {
  selectTranscriptionCompleted,
  selectTranscriptionFailed,
  selectTranscriptionRunning,
} from './tasks';
import { selectAudioRedactions, selectTranscription } from './transcription';

type MediaDetailsState = DetectionAssetState & ViewSettings & Extra;

const selectStore = (s: { [namespace]: MediaDetailsState }) => s[namespace];

export const selectMediaDuration = createSelector(selectStore, (s) =>
  ((tdo): number =>
    tdo
      ? get(tdo, 'primaryAsset.jsondata.mediaDuration') ||
        (new Date(tdo.stopDateTime).getTime() -
          new Date(tdo.startDateTime).getTime()) /
          1000
      : 0)(s.tdo)
);

export const selectGlobalSettings = createSelector(
  selectStore,
  (s) => s.tdo?.details?.settings || DEFAULT_GLOBAL_SETTINGS
);

export const selectShowGlobalSettingsModal = createSelector(
  selectStore,
  (s) => s.showGlobalSettingsModal
);

export const selectExpandedPolyGroups = createSelector(
  selectStore,
  (s) => s.expandedPolyGroups
);

export const selectPolyGroups = createSelector(
  selectStore,
  (s) => s.selectedPolyGroups
);

/**
 * Interfaces.
 */

export const selectSortBy = createSelector(
  selectStore,
  (s) => s.sortPolyGroupsBy
);

export const selectDisplayUnselectedOverlays = createSelector(
  selectStore,
  (s) => s.displayUnselectedOverlays
);

export const selectFilterParameters = createSelector(
  selectStore,
  (s) => s.filterParameters
);

const selectDataFetchedFromStore = (s: { [namespace]: MediaDetailsState }) =>
  selectStore(s).dataFetchedForDetectionType;

export const selectDataFetchedForDetectionType = createSelector(
  selectDataFetchedFromStore,
  selectFeatureFlags,
  (dataFetchedForDetectionType, featureFlags) => ({
    ...dataFetchedForDetectionType,
    notepad: Boolean(
      dataFetchedForDetectionType?.notepad && featureFlags?.detectNotepads
    ),
    card: Boolean(
      dataFetchedForDetectionType?.card && featureFlags?.detectCards
    ),
  })
);

export const selectFilterCount = createSelector(
  selectFilterParameters,
  selectDataFetchedForDetectionType,
  (filterParameters, dataFetchedForDetectionType) => {
    let filterCount = 0;
    FILTER_PARAMETER_TYPES.forEach((type) => {
      switch (type) {
        case 'udr':
          filterCount += filterParameters.show[type] ? 1 : 0;
          break;
        // case 'manual':
        //   filterCount +=
        //     (settings?.manualInterpolationMax ??
        //       DEFAULT_GLOBAL_SETTINGS.manualInterpolationMax) > 0 &&
        //     filterParameters.show[type]
        //       ? 1
        //       : 0;
        //   break;
        default: {
          filterCount +=
            filterParameters.show[type] && dataFetchedForDetectionType[type]
              ? 1
              : 0;
        }
      }
    });
    return filterCount;
  }
);

export const selectAllSelectedFilter = createSelector(
  selectFilterParameters,
  selectDataFetchedForDetectionType,
  (filterParameters, dataFetchedForDetectionType) =>
    FILTER_PARAMETER_TYPES.every((type) => {
      // ignore if remove from output
      if (filterParameters.remove[type]) {
        return true;
      }

      switch (type) {
        case 'udr':
          return filterParameters.show[type] && !filterParameters.remove[type];
        // case 'manual':
        //   return (
        //     filterParameters.show[type] ||
        //     !(
        //       (settings?.manualInterpolationMax ??
        //         DEFAULT_GLOBAL_SETTINGS.manualInterpolationMax) > 0
        //     )
        //   );
        default: {
          return (
            filterParameters.show[type] || !dataFetchedForDetectionType[type]
          );
        }
      }
    })
);

export const selectAllDisabledFilter = createSelector(
  selectFilterParameters,
  selectDataFetchedForDetectionType,
  (filterParameters, dataFetchedForDetectionType) =>
    FILTER_PARAMETER_TYPES.every((type) => {
      switch (type) {
        case 'udr':
          return filterParameters.remove[type];
        // case 'manual':
        //   return (
        //     filterParameters.remove[type] ||
        //     !(
        //       (settings?.manualInterpolationMax ??
        //         DEFAULT_GLOBAL_SETTINGS.manualInterpolationMax) > 0
        //     )
        //   );
        default: {
          return (
            filterParameters.remove[type] || !dataFetchedForDetectionType[type]
          );
        }
      }
    })
);

export const selectOverlayPreview = createSelector(
  selectStore,
  (s) => s.overlayPreview
);

export const selectOverlayStyles = createSelector(
  selectOverlayPreview,
  (overlayPreview) => getOverlayStyles(overlayPreview)
);

export const selectIsPlayerSeeked = createSelector(
  selectStore,
  (s) => s.isPlayerSeeked
);

export const selectPlayback = createSelector(
  selectStore,
  ({ playbackDirection, playbackSpeed }) => ({
    playbackDirection,
    playbackSpeed,
  })
);

export const selectLastSaveMediaDataDisplayTime = createSelector(
  selectStore,
  (s) => s.lastSaveMediaDataDisplayTime
);

export const selectIsFetchingClusterListData = (s: {
  [namespace]: MediaDetailsState;
}) => selectStore(s).isFetchingClusterListData;

export const selectDetectionClusterGroups = createSelector(
  selectStore,
  (s) => s.detectionClusterGroups
);

export const selectDetectionClusterGroupsCount = createSelector(
  selectDetectionClusterGroups,
  (groups) => (groups ? Object.keys(groups).length : undefined)
);

export const selectUdrClusterGroups = createSelector(
  selectStore,
  (s) => s.udrClusterGroups
);

export const selectClusterMap = createSelector(
  selectStore,
  (s) => s.clusterMap
);

/**
 * Select cluster list for right-side panel.
 */
export const selectClusterList = createSelector(
  selectDetectionClusterGroups,
  selectUdrClusterGroups,
  selectClusterMap,
  (detectionClusterGroups, udrClusterGroups, clusterMap) => {
    const groupsByClusterId: { [clusterId: string]: ClusterItemGroup[] } = {};
    // add detection groups
    if (detectionClusterGroups) {
      for (const group of Object.values(detectionClusterGroups)) {
        // filtering now done in clusters.ts
        // if (!featureFlags.detectCards && group.type === 'card') {
        //   continue;
        // }

        // if (!featureFlags.detectNotepads && group.type === 'notepad') {
        //   continue;
        // }

        const groups = groupsByClusterId[group.clusterId];
        if (groups) {
          groups.push(group);
        } else {
          groupsByClusterId[group.clusterId] = [group];
        }
      }
    }

    // add udr groups
    if (udrClusterGroups) {
      for (const group of Object.values(udrClusterGroups)) {
        const groups = groupsByClusterId[group.clusterId];
        if (groups) {
          groups.push(group);
        } else {
          groupsByClusterId[group.clusterId] = [group];
        }
      }
    }

    // final packaging of clusterItems
    const clusters: ClusterItem[] = [];
    for (const [clusterId, groupsUnsorted] of Object.entries(
      groupsByClusterId
    )) {
      // determine cluster type

      // sort groups in each cluster by start time of first segment
      // const groups = sortBy(groupsUnsorted, 'startTimeMs');
      const groups = groupsUnsorted.sort(
        (a, b) => a.segments[0].startTimeMs - b.segments[0].startTimeMs
      );

      let firstGroup = groups[0];
      if (!firstGroup) {
        continue;
      }

      // concatenate all segments
      let segments: ClusterSegment[] = [];
      for (const group of groups) {
        segments.push(...group.segments);
      }

      let type: CLUSTER_TYPE = firstGroup.type;
      let dominantType: SEGMENT_TYPE | undefined;
      if (groups.length > 1) {
        // check if there is more than more type
        const uniqueTypes = Array.from(
          new Set(groups.map((group) => group.type))
        );
        if (uniqueTypes.length > 1) {
          type = 'mixed';

          // determine most common segment type
          dominantType = getDominantSegmentType(segments.map((v) => v.type));

          // update default group for pic thumbnail to use first group with dominant type
          const firstGroupDominantType = groups.find(
            (group) => group.type === dominantType
          );
          if (firstGroupDominantType) {
            firstGroup = firstGroupDominantType;
          }
        }
      }

      let userLabel;

      // if single group cluster use group label
      if (groups.length === 1) {
        userLabel = firstGroup.userLabel;
      }

      let pinnedDate = undefined;
      if (clusterMap[clusterId]) {
        userLabel = clusterMap[clusterId].userLabel; // clusterMap userLabel will take priority over group userLabel
        pinnedDate = clusterMap[clusterId].pinnedDate;

        // sort segments
        if (clusterMap[clusterId].sortBy === 'type') {
          const segmentGroupedByType = groupBy(segments, 'type');

          const sortedSegments: ClusterSegment[] = [];
          SEGMENT_SORT_BY_TYPE_ORDER.forEach((segType) => {
            if (segmentGroupedByType[segType]) {
              sortedSegments.push(
                ...sortBy(segmentGroupedByType[segType], 'startTimeMs')
              );
            }
          });

          segments = sortedSegments;
        } else if (clusterMap[clusterId].sortBy === 'group') {
          // already sorted by group
        } else {
          // default to sortBy time
          segments = sortBy(segments, 'startTimeMs');
        }
      }

      const startTimeMs = Math.min.apply(
        null,
        segments.map((segment) => segment.startTimeMs)
      );

      const stopTimeMs = Math.max.apply(
        null,
        segments.map((segment) => segment.stopTimeMs)
      );

      const clusterItem: ClusterItem = {
        id: clusterId,
        userLabel,
        pinnedDate,
        picUri: firstGroup.picUri,
        groups: groups as NonEmptyArray<ClusterItemGroup>,
        segments: segments as NonEmptyArray<ClusterSegment>,
        type,
        ...(dominantType && { dominantType }),
        segmentSortType: clusterMap[clusterId]?.sortBy ?? 'time', // Default, sort by time in ascending order
        startTimeMs,
        stopTimeMs,
        isMergedGroup: groups.length > 1,
        // version: firstGroup.version, // may not need this
      };
      clusters.push(clusterItem);
    }

    return clusters;
  }
);

export const selectSelected = createSelector(
  selectStore,
  selectClusterList,
  selectFeatureFlags,
  (s, clusterList, { analyzeInterpolation }) =>
    analyzeInterpolation
      ? clusterList.reduce<Record<string, boolean>>(
          (listPrev, cluster) => ({
            ...listPrev,
            ...cluster.groups.reduce<Record<string, boolean>>(
              (groupPrev, group) => ({
                ...groupPrev,
                ...group.segments.reduce<Record<string, boolean>>(
                  (segmentPrev, segment) => ({
                    ...segmentPrev,
                    [segment.id]: segment.isManualInterpolation,
                  }),
                  {}
                ),
              }),
              {}
            ),
          }),
          {}
        )
      : s.selectedPolyGroups
);

export const selectClusterMergeGroupIds = createSelector(
  selectStore,
  (s) => s.clusterMergeGroupIds
);

export const selectClusterMergeSegments = createSelector(
  selectStore,
  (s) => s.clusterMergeSegments
);

export const selectFilterParametersFromStore = (s: {
  [namespace]: MediaDetailsState;
}) => selectStore(s).filterParameters;

export const selectUDRCollection = createSelector(
  selectStore,
  selectFilterParametersFromStore,
  (s, filterParameters) => {
    if (filterParameters.show.udr) {
      return s.udrCollection;
    }
    return undefined;
  }
);

const createVersionSelector = createSelectorCreator(
  defaultMemoize,
  (
    a: { [key: string]: CollectionWrapper | undefined },
    b: { [key: string]: CollectionWrapper | undefined }
  ) => {
    // list of union of keys - probably a more elegant way to do this
    let allKeys: string[] = [];
    if (a) {
      allKeys = allKeys.concat(Object.keys(a));
    }
    if (b) {
      allKeys = allKeys.concat(Object.keys(b));
    }
    allKeys = [...new Set(allKeys)];

    // check that all collection versions still match
    const allMatch =
      (isEmpty(a) && isEmpty(b)) ||
      allKeys.every(
        (key) =>
          a?.[key] &&
          b?.[key] &&
          a?.[key]?.collection.version === b?.[key]?.collection.version
      );
    return allMatch;
  }
);

export const selectDetectionCollections = createVersionSelector(
  createSelector(
    selectStore,
    selectFilterParameters,
    selectFeatureFlags,
    (s, filterParameters, featureFlags) => ({
      ...omitBy(
        s.detectionCollections,
        (_value, key) =>
          (!featureFlags.detectCards && key.includes('card')) ||
          (!featureFlags.detectNotepads && key.includes('notepad')) ||
          !filterParameters.show[key as FILTER_PARAMETER_TYPE] // FTODO
      ),
    })
  ),
  (detectionCollections) => detectionCollections
);

export const selectProgress = createSelector(selectStore, (s) => s.progress);

export const isFetchingFaceDetectionSelector = createSelector(
  selectStore,
  (s) => s.faceDetection.isFetching
);

export const isFaceDetectionProcessingSelector = createSelector(
  selectStore,
  (s) => s.faceDetection.isProcessing
);

export const faceDetectionInfoSelector = createSelector(
  selectStore,
  (s) => s.faceDetection
);

export const isFaceDetectionReadySelector = createSelector(
  selectStore,
  (s) => s.faceDetection.isReady
);

export const mediaHasAudioSelector = createSelector(
  selectStore,
  (s) => s && s.hasAudio
);

/**
 * Select highlighted Head
 */
export const selectHighlightedOverlay = createSelector(
  selectStore,
  (s) => s.highlightedOverlay
);

/**
 * Select highlighted Head id
 */
export const selectHighlightFaceId = createSelector(
  selectHighlightedOverlay,
  (f) => get(f, 'id')
);

export const selectUDRBoundingPolyBeingUpdated = createSelector(
  selectStore,
  (s) => s.udrsState.udrBoundingPolyBeingUpdated
);

export const selectPolysAtCurrentTime = createSelector(
  selectDetectionCollections,
  selectUDRCollection,
  selectCurrentPosition,
  (detectionCollections, udrCollection, time) => {
    let result = detectionCollections
      ? DETECTION_COLLECTION_KEYS.reduce<GroupedBoundingPoly[]>((acc, key) => {
          const detectedCollection = detectionCollections[key]?.collection;
          const coll = detectedCollection
            ? search(detectedCollection, time)
            : [];
          for (const poly of coll) {
            acc.push(poly);
          }
          return acc;
        }, [])
      : [];

    result = udrCollection
      ? [...result, ...search(udrCollection.collection, time)]
      : result;

    return result;
  }
);

export const selectHighlightedPoly = createSelector(
  selectHighlightedOverlay,
  selectPolysAtCurrentTime,
  (highlightedOverlay, polys) => {
    if (!highlightedOverlay) {
      return null;
    }
    // search by id first
    let match = polys.find((p) => p.id === highlightedOverlay.id);

    // if no match search for matching group
    if (!match) {
      match = polys.find(
        (p) => !!p.groupId && p.groupId === highlightedOverlay.groupId
      );
    }
    return match;
  }
);

export const selectHighlightedClusterItem = createSelector(
  selectClusterList,
  selectHighlightedOverlay,
  selectUdrClusterGroups,
  selectDetectionClusterGroups,
  (
    clusterList,
    highlightedOverlay,
    udrClusterGroups,
    detectionClusterGroups
  ) => {
    let clusterId: string | undefined;

    if (highlightedOverlay) {
      if (highlightedOverlay.type === 'udr') {
        clusterId = udrClusterGroups[highlightedOverlay.groupId]?.clusterId;
      } else {
        clusterId =
          detectionClusterGroups[highlightedOverlay.groupId]?.clusterId;
      }
    }

    if (clusterId) {
      return clusterList.find((cl) => cl.id === clusterId);
    }
  }
);

export const selectHighlightedClusterItemGroup = createSelector(
  selectHighlightedOverlay,
  selectUdrClusterGroups,
  selectDetectionClusterGroups,
  (highlightedOverlay, udrClusterGroups, detectionClusterGroups) => {
    if (highlightedOverlay) {
      if (highlightedOverlay.type === 'udr') {
        return udrClusterGroups[highlightedOverlay.groupId];
      } else {
        return detectionClusterGroups[highlightedOverlay.groupId];
      }
    }
  }
);

export const selectIsRedactionAllowed = createSelector(
  selectClusterList,
  selectSelected,
  selectAudioRedactions,
  (list, selected, audioRedactions) =>
    audioRedactions.length ||
    list.reduce<boolean>(
      (b, l) =>
        b ||
        l.groups.some((g) =>
          g.segments.some((s) =>
            s.subsegmentIds.some((ssid) => !!selected[ssid])
          )
        ), // CTODO this could be simplified is went back to including groups in the selected - not sure worth it
      false
    )
);

export const selectorVideoLoaded = createSelector(
  selectStore,
  (s) => s && s.videoLoaded
);

export const selectThumbnailTracks = createSelector(
  selectStore,
  (s) => s && s.thumbnailTracks
);

export const selectShakaPlayer = createSelector(
  selectStore,
  (s) => s && s.shakaPlayer
);

/**
 * Select the state of the AUDIO tab.
 * ENGINE_RUNNING, NOT_RUN, LOADING, EMPTY, COMPLETE, FAILED
 */
export const selectAudioTabState = createSelector(
  selectTranscription,
  selectProgress,
  selectTranscriptionRunning,
  mediaHasAudioSelector,
  selectTranscriptionCompleted,
  selectTranscriptionFailed,
  (t, p, r, hasAudio, completedTasks, failedTasks) =>
    // When (t.transcription.length === 0), t.startOffsetMs === t.stopOffsetMs is also true which was why transcription state gets set to NOT_RUN
    // Making an assumption that once transcription is complete cannot re-run transcription and the latest would be failed.
    r.length > 0
      ? TabState.ENGINE_RUNNING
      : !hasAudio || (t?.transcription.length === 0 && completedTasks.length)
        ? TabState.EMPTY
        : failedTasks.length && !completedTasks.length
          ? TabState.FAILED
          : t === undefined && p.transcription !== 1
            ? TabState.LOADING
            : (t === undefined && p.transcription === 1) ||
                (t && t.startOffsetMs === t.stopOffsetMs)
              ? TabState.NOT_RUN
              : TabState.COMPLETE
);

export const selectRegularModePlaybackSpeed = createSelector(
  selectStore,
  (s) => s.udrsState.regularModePlaybackSpeed
);

export const selectAudiowaves = createSelector(
  selectStore,
  (s) => s.audiowaves
);

export const selectCaseMedia = createSelector(selectStore, (s) => s.caseMedia);

export const selectIsCaseMediaDrawerOpen = createSelector(
  selectStore,
  (s) => s.isCaseMediaDrawerOpen
);

export const selectMediaComments = createSelector(
  selectStore,
  (s) => s.mediaComments
);

export const selectIsFetchingComments = createSelector(
  selectStore,
  (s) => s.isFetchingComments
);

export const selectIsHideComment = createSelector(
  selectStore,
  (s) => s.isHideComment
);

export const selectCommentUsers = createSelector(
  selectStore,
  (s) => s.mediaCommentsUsers
);

export const selectUser = createSelector(selectStore, (s) => s.user);

export const selectSelectedComments = createSelector(
  selectStore,
  (s) => s.selectedMediaComments
);

export const selectIsBasicUserQuerySupported = createSelector(
  selectStore,
  (s) => s.isBasicUserQuerySupported
);

export const selectTrimInterval = createSelector(
  selectStore,
  (s) => s.tdo?.details?.redact?.trimInterval
);

export const selectTdoReadonly = createSelector(
  selectStore,
  (s) => s.tdoReadonly
);

export const selectPrevMarkerTime = createSelector(
  selectStore,
  (s) => s.prevMarkerTime
);

export const selectDraggedOverlayId = createSelector(
  selectStore,
  (s) => s.draggedOverlayId
);

export const selectIsSaveFailed = createSelector(
  selectStore,
  (s) => s.isSaveFailed
);

export const selectClusterGroupIds = createSelector(
  selectClusterMap,
  selectUdrClusterGroups,
  selectDetectionClusterGroups,
  selectHighlightedOverlay,
  (clusterMap, udrClusterGroups, detectionClusterGroups, highlightedOverlay) =>
    findClusterGroupIds(
      clusterMap,
      udrClusterGroups,
      detectionClusterGroups,
      highlightedOverlay?.groupId
    )
);

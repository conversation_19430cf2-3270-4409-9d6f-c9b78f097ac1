import { namespace } from './index';
import type { AppWrapperStore } from '@cbsa-modules/appWrapper';
import { createSelector } from 'reselect';

const selectStore = (store: {
  [namespace]: AppWrapperStore;
}): AppWrapperStore => store[namespace];

export const selectCases = createSelector(selectStore, (s) => s.cases);

export const selectMedia = createSelector(selectStore, (s) => s.media);

export const selectUploadingFiles = createSelector(
  selectStore,
  (s) => s.uploadingFiles
);

export const selectLoaders = createSelector(selectStore, (s) => s.loaders);

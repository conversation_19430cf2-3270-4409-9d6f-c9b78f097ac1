import { createSelector } from 'reselect';
import {
  selectUploadingFiles,
  removeSuccessfulFile,
  retryFailedFile,
} from '@cbsa-modules/appWrapper';
import { UploadMedia } from '@cbsa-modules/universal';
import { selectSDOs as selectNotification } from '@common-modules/notification';
import { connect, ConnectedProps } from 'react-redux';
import { ArrayOrSingle } from 'ts-essentials';

const mapStateToProps = createSelector(
  selectNotification,
  selectUploadingFiles,
  (listNotification, uploadingFiles) => ({
    listNotification,
    uploadingFiles,
  })
);

const mapDispatchToProps = {
  removeSuccessfulFile: (uploadFile: UploadMedia) =>
    removeSuccessfulFile(uploadFile),
  retryFailedFile: (uploadFile: UploadMedia) => retryFailedFile(uploadFile),
};

export const connector = connect(mapStateToProps, mapDispatchToProps);
export type Props = ConnectedProps<typeof connector> & {
  title: string;
  children: ArrayOrSingle<
    string | React.ReactElement<any, string | React.JSXElementConstructor<any>>
  >;
};

.mediaPlayer {
  font-size: 12px !important;
  font-family: Roboto, sans-serif;
  outline: none !important;
  padding: 0 !important;
}

.mediaPlayerHeight {
  height: 100% !important;
}

.mediaPlayerHidden {
  display: none;
}

.canvas {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.canvasMat {
  background: black;
  height: inherit;
}

.mediaPlayButton {
  border-radius: 1em !important;
  border: none !important;
  display: none !important;
}

.tdoButtonPlayVideoWrapper {
  cursor: pointer;
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: 1;

  .tdoButtonPlayBackground {
    background: #e0e0e0;
    opacity: 0.2;
    width: 48px;
    height: 48px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
  }
}

.btnHide {
  display: none;
}

.hiddenDummyControls {
  display: none !important;
}

:global {
  .video-react-control-bar {
    top: unset !important;
    bottom: unset !important;
    position: static !important;
  }
  .video-react-control {
    width: 3em;
    // VTN-14463 Needed because the overlay has a z-index of 100
    z-index: 101;
  }
  .video-react-time-control {
    padding-left: 0.5em;
    padding-right: 0.5em;
  }

  .video-react-control-text,
  .video-react-mouse-display,
  .video-react-play-progress::after {
    font-family: Roboto, sans-serif !important;
  }

  .video-react-slider:focus {
    text-shadow: none;
    box-shadow: none;
  }

  .video-react-playback-rate .video-react-menu-content {
    max-height: 20em !important;
    overflow-x: hidden;
  }
}

import { buffers } from 'redux-saga';
import {
  all,
  cancel,
  fork,
  put,
  select,
  take,
  takeLatest,
  takeEvery,
  actionChannel,
  delay,
  race,
} from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';
import { v4 as uuid } from 'uuid';

import { sagaIntl } from '@i18n';

import { enqueueSnackbar } from '../snackbar';
import * as Actions from './actions';
import {
  selectSDOs,
  listPreparing,
  listProcessing,
  listFailed,
  listComplete,
} from './selectors';
import * as Services from './services';

import {
  OUT_CREATE_JOB_SUCCESS,
  OUT_CREATE_JOB_FAIL,
  FETCH_DOWNLOAD_ASSET_FAILURE,
  FETCH_DOWNLOAD_ASSET_SUCCESS,
  FETCH_REDACT_TDO_SUCCESS,
  FETCH_REDACT_TDO_FAILURE,
  CreateJobFailPayload,
} from '@worker';
import {
  CREATE_JOB_FACES_SUCCESS,
  CREATE_JOB_FACES_FAILURE,
} from '../facesTabModule';
import {
  CREATE_ENGINE_JOB_SUCCESS as CREATE_OPTICAL_TRACKING_JOB_SUCCESS,
  CREATE_ENGINE_JOB_FAILURE as CREATE_OPTICAL_TRACKING_JOB_FAILURE,
} from '../engines/optical-tracking';
import {
  CREATE_ENGINE_JOB_SUCCESS as CREATE_TRANSCRIPTION_JOB_SUCCESS,
  CREATE_ENGINE_JOB_FAILURE as CREATE_TRANSCRIPTION_JOB_FAILURE,
} from '../engines/transcription';
import {
  CREATE_ENGINE_JOB_FAILURE as CREATE_INGESTION_JOB_FAILURE,
  CREATE_ENGINE_JOB_SUCCESS as CREATE_INGESTION_JOB_SUCCESS,
} from '../engines/ingestion';
import { Mailbox, NotificationInterface, NotificationSDOId } from './models';
import { TDOId } from '@common-modules/universal/models/Brands';
import { SCHEMA_ID_NOT_FOUND } from './actions';
import { ArrayOrSingle } from 'ts-essentials';
const {
  user: { LOGOUT, selectUser, FETCH_USER_SUCCESS, userIsAuthenticated },
} = modules;

interface Action<P, M = any> {
  type: string;
  payload: P;
  meta: M;
}

const DOWNLOAD = 'Download',
  REDACTION = 'Redaction',
  OBJECT_DETECTION = 'Object Detection',
  TRANSCRIPTION = 'Transcription',
  OPTICAL_TRACKING = 'Optical Tracking',
  INGESTION = 'Ingestion process';

/**
 * Kickoff watching actions and handling effects.
 */
export function* initNotification() {
  const tasks = yield* all([
    fork(watchDeleteJobSDO),
    fork(watchDeleteJobSDOFailure),

    fork(onActionFetchJobSDOs),
    fork(watchFetchJobSdoSuccess),
    // fork(onActionFetchSDOsFailure),

    fork(fetchSDOS),
    fork(startPollingJobs),
    fork(watchCreateJobSuccess),
    fork(watchCreateJobFail),
    fork(watchRetryJob),
    fork(watchRetryJobFailed),
    fork(watchFetchMailBoxsSuccess),
    fork(watchCreateMailBoxsSuccess),
    fork(watchInitAiware),
    fork(watchSchemaIdNotFound),
  ]);

  yield* take(LOGOUT);

  yield* cancel(tasks);
}

// delete sdo when click remove in Notification List
function* watchDeleteJobSDO() {
  yield* takeEvery(Actions.DELETE_JOB_SDO, watchDeleteJobSDOHandle);
}

export function* watchDeleteJobSDOHandle({
  payload,
}: {
  payload: { readonly id: string; readonly data: NotificationInterface };
}) {
  const { id, data } = payload;
  // remove notification was created when createJob Failed
  if (!data.jobId) {
    yield* put(
      Actions.DELETE_JOB_SDO_SUCCESS(
        {
          deleteStructuredData: {
            id,
          },
        },
        undefined
      )
    );
    yield* cancel();
  }

  if (
    listPreparing.includes(data.status) ||
    listProcessing.includes(data.status)
  ) {
    yield* put(
      Services.upsertJobSDO({
        id,
        data: {
          ...data,
          deleted: true,
        },
      })
    );
  } else {
    yield* put(
      Services.deleteJobSDO({
        id,
      })
    );
  }
}

// Show error when delete sdo fail
function* watchDeleteJobSDOFailure() {
  yield* takeEvery(
    Actions.DELETE_JOB_SDO_FAILURE,
    watchDeleteJobSDOFailureHandle
  );
}

export function* watchDeleteJobSDOFailureHandle({
  payload,
  meta,
}: Action<any>) {
  if (payload[0]?.name === 'not_found') {
    // TODO: Fix type of variables
    const id = meta.variables?.id as string | undefined;
    if (id) {
      yield* put(
        Actions.DELETE_JOB_SDO_SUCCESS(
          { deleteStructuredData: { id } },
          undefined
        )
      );
      yield* cancel();
    }
  }

  const intl = sagaIntl();
  yield* put(
    enqueueSnackbar({
      message: intl.formatMessage({
        id: 'notiDismissFail',
        defaultMessage: 'Notification could not be dismissed',
      }),
    })
  );
}

// create sdo when create job success
function* watchCreateJobSuccess() {
  yield* takeEvery<
    Action<{
      type: string;
      payload: {
        createJob: {
          id: string;
          targetId: TDOId;
          status: string;
          tasks: {
            records: ReadonlyArray<{
              engineId: string;
            }>;
          };
        };
      };
      meta: {
        variables: {
          id: TDOId;
          tdoName: string;
        };
      };
    }>
  >(
    [OUT_CREATE_JOB_SUCCESS.type, Actions.PRE_PROCESS_JOB],
    watchCreateJobSuccessHandle
  );
}

export function* watchCreateJobSuccessHandle({
  payload,
}: {
  payload: {
    type: string;
    payload: {
      createJob: {
        id: string;
        targetId: TDOId;
        status: string;
        tasks: {
          records: ReadonlyArray<{
            engineId: string;
          }>;
        };
      };
    };
    meta: {
      variables: {
        id: TDOId;
        tdoName: string;
      };
    };
  };
}) {
  try {
    const { createJob } = payload.payload;
    const { userId } = yield* select(selectUser);
    let engineName = '';
    switch (payload.type) {
      case FETCH_DOWNLOAD_ASSET_SUCCESS.type:
        engineName = DOWNLOAD;
        break;
      case FETCH_REDACT_TDO_SUCCESS.type:
        engineName = REDACTION;
        break;
      case CREATE_JOB_FACES_SUCCESS.type:
        engineName = OBJECT_DETECTION;
        break;
      case CREATE_TRANSCRIPTION_JOB_SUCCESS.type:
        engineName = TRANSCRIPTION;
        break;
      case CREATE_OPTICAL_TRACKING_JOB_SUCCESS.type:
        engineName = OPTICAL_TRACKING;
        break;
      case CREATE_INGESTION_JOB_SUCCESS.type:
        engineName = INGESTION;
        break;
      default:
    }

    const jobId = createJob.id;
    const { status } = createJob;
    if (userId) {
      yield* put(
        Services.upsertJobSDO({
          data: {
            jobId,
            tdoId: payload.meta.variables.id,
            userId,
            tdoName: payload.meta.variables.tdoName,
            engineName,
            status,
            deleted: false,
          },
        })
      );
    }
  } catch (e) {
    console.log('WatchCreateJobSuccessHandle error', e);
  }
}

// create sdo when create job fail
function isMessageString(
  payload: ArrayOrSingle<{ message: string }>
): payload is { message: string } {
  return (payload as { message: string }).message !== undefined;
}

function* watchCreateJobFail() {
  yield* takeEvery([OUT_CREATE_JOB_FAIL], watchCreateJobFailHandle);
}

export function* watchCreateJobFailHandle({
  payload,
}: {
  payload: CreateJobFailPayload;
}) {
  try {
    const { userId } = yield* select(selectUser);
    let engineName = '';
    switch (payload.type) {
      case FETCH_DOWNLOAD_ASSET_FAILURE.type:
        engineName = DOWNLOAD;
        break;
      case FETCH_REDACT_TDO_FAILURE.type:
        engineName = REDACTION;
        break;
      case CREATE_JOB_FACES_FAILURE.type:
        engineName = OBJECT_DETECTION;
        break;
      case CREATE_TRANSCRIPTION_JOB_FAILURE.type:
        engineName = TRANSCRIPTION;
        break;
      case CREATE_OPTICAL_TRACKING_JOB_FAILURE.type:
        engineName = OPTICAL_TRACKING;
        break;
      case CREATE_INGESTION_JOB_FAILURE.type:
        engineName = INGESTION;
        break;
      default:
    }
    let message;
    if (isMessageString(payload.payload)) {
      message = payload.payload.message;
    } else {
      if (payload.payload[0]) {
        message = payload.payload[0].message;
      }
    }
    if (message) {
      engineName += `: ${message}`;
    }

    // when createJob failed, has no jobId and add Notification in redux
    // to show in Appbar, without create SDO
    if (userId) {
      yield* put(
        Actions.actionInsertNotificationReduxWhenNetWorkError({
          jobId: '',
          tdoId: payload.meta.variables.id,
          userId,
          tdoName: payload.meta.variables.tdoName,
          engineName,
          status: 'failed',
          deleted: false,
          sdoId: uuid() as NotificationSDOId,
        })
      );
    }
  } catch (error) {
    console.log('error', error);
  }
}

// fetch list sdos when first load
function* onActionFetchJobSDOs() {
  const chan = yield* actionChannel(
    Actions.FETCH_JOB_SDOS,
    buffers.expanding(10)
  );
  yield* takeEvery(chan, function* () {
    const { userId } = yield* select(selectUser);
    if (!userId) {
      return;
    }

    yield* put(
      Services.fetchJobSDOs({
        limit: 10,
        offset: 0,
        userId,
      })
    );
  });
}

// Check fetUser status before fetch sdos notification
function* fetchSDOS() {
  const userAuthed = yield* select(userIsAuthenticated);
  if (!userAuthed) {
    yield* take(FETCH_USER_SUCCESS);
  }
  yield* all([
    // starts polling for legacy notifications
    put(Actions.actionFetchJobSDOs()),
    /*
     ** Starts a chain of events that
     ** checks for a `Mailbox Redact`
     ** mailbox for the current user,
     ** and creates one if needed.
     ** aiware.js receives push
     ** notifications for the users
     ** mailboxes and displays
     ** their contents
     */
    put(Services.getMailboxes()),
  ]);
}

function* startPollingJobs() {
  yield* takeEvery(Actions.FETCH_JOB_SDOS_SUCCESS, startPollingJobsHandle);
}

export function* startPollingJobsHandle() {
  const sdos = yield* select(selectSDOs);
  // use to remove sdo has status complete before loadPage
  const listCompletedJobId: string[] = [];
  sdos.forEach((sdo) => {
    if (sdo.sdoId && sdo.status === 'complete') {
      listCompletedJobId.push(sdo.sdoId);
    }
  });
  if (listCompletedJobId.length > 0) {
    // remove in redux
    yield* put(
      Actions.actionDeleteJobSdoRedux({
        ids: listCompletedJobId,
      })
    );

    // remove in server
    yield* put(
      Services.deleteMultiJobSDO({
        ids: listCompletedJobId,
      })
    );
  }

  // poll to update status of notifications
  while (true) {
    const sdosUpdateStatus = yield* select(selectSDOs);
    const sdosQuery = sdosUpdateStatus.filter(
      (val: NotificationInterface) => val.status !== 'complete' && val.jobId
    );
    if (sdosQuery.length > 0) {
      yield* put(Services.fetchEachJob({ sdos: sdosQuery }));
      yield* race({
        _: take(Actions.FETCH_JOB_STATUS_SUCCESS),
        __: take(Actions.FETCH_JOB_STATUS_FAILURE),
      });
    }

    yield* delay(60000);
  }
}

// retry job when user click retry in list Notification
function* watchRetryJob() {
  yield* takeEvery(Actions.RETRY_JOB, function* ({ payload }) {
    const { id } = payload;
    yield* put(
      Services.retryJob({
        id,
      })
    );
  });
}

// Show error when click retry failed
function* watchRetryJobFailed() {
  yield* takeEvery(Actions.RETRY_JOB_FAILED, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'engineRetryError',
          defaultMessage: 'An error occurred while retrying engine.',
        }),
      })
    );
  });
}

function* watchFetchJobSdoSuccess() {
  yield* takeEvery(Actions.FETCH_JOB_STATUS_SUCCESS, function* ({ payload }) {
    const sdos = yield* select(selectSDOs);

    // update sdo status when status change
    const listSdoUpdate: any[] = [];
    sdos.forEach((sdo) => {
      const { jobId, sdoId } = sdo;
      const jobResponse = payload[`jobId${jobId}`];
      if (jobResponse) {
        const newStatus = jobResponse.status;
        const oldStatus = sdo.status;
        if (newStatus !== oldStatus) {
          listSdoUpdate.push({
            sdoId,
            data: {
              ...sdo,
              status: newStatus,
              deleted:
                listFailed.includes(newStatus) ||
                listComplete.includes(newStatus)
                  ? false
                  : sdo.deleted,
            },
          });
        }
      }
    });
    // update status sdo in server
    if (listSdoUpdate.length > 0) {
      yield* put(Services.updateMultyJobSDO({ sdos: listSdoUpdate }));
    }

    // update status sdo in store redux
    yield* put(Actions.FETCH_JOB_STATUS_SUCCESS_REDUX(payload));
  });
}

// aiware.js hookups
function* watchFetchMailBoxsSuccess() {
  yield* takeEvery(Actions.FETCH_MAILBOX_SUCCESS, function* ({ payload }) {
    const { notificationMailboxes } = payload;
    const applicationId: string =
      window.config.veritoneAppId || '766e9916-9536-47e9-8dcb-dc225654bab3';
    const mailboxApp = notificationMailboxes?.find(
      (item: Mailbox) =>
        item.eventFilter?.applicationId === applicationId &&
        item.name === 'Mailbox Redact'
    );

    if (!mailboxApp) {
      // create mailbox App
      yield* put(Services.createMailboxApp({ applicationId }));
    }
  });
}

function* watchCreateMailBoxsSuccess() {
  yield* takeLatest(
    [Actions.CREATE_MAILBOX_SYSTEM_SUCCESS, Actions.CREATE_MAILBOX_APP_SUCCESS],
    function* () {
      // this tells aiware.js to fetch the new mailboxes for the current user
      // and update its internal state.
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      yield window.aiware?.store.dispatch({ type: 'UPDATE_MAILBOX_SETTING' });
    }
  );
}

// attach mailbox listener the aiware.js saga
function* watchInitAiware() {
  const action = yield* take(Actions.INIT_AIWARE);
  // TODO: fully type the INIT_AIWARE payload
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const { store } = action.payload;
  const dataListener = {
    id: 'panelId',
    sagas: [watchNotificationPushToSystem],
  };

  // adds the listener for mailbox/PUSH_NOTIFICATION to the aiware.js store
  // eslint-disable-next-line @typescript-eslint/no-unsafe-call
  store?.addModule(dataListener);
}

// aiware.js emits a redux event when it receives a mailbox notification
// No action is necessary to display a notification, however this is a
// place to trigger side effects (ex: translation/internationalization)
function* watchNotificationPushToSystem() {
  yield* takeEvery<Action<{ body: string; mailboxId: string }>>(
    'mailbox/PUSH_NOTIFICATION',
    function* ({ payload: _payload }) {}
  );
}

function* watchSchemaIdNotFound() {
  yield* takeEvery(SCHEMA_ID_NOT_FOUND, function* ({ payload }) {
    const registryName = payload?.registryName;
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message:
          `"${registryName}" - ` +
          intl.formatMessage({
            id: 'schemaIdNotFound',
            defaultMessage:
              'SchemaId not found. Please verify the system configuration file.',
          }),
        variant: 'error',
      })
    );
  });
}

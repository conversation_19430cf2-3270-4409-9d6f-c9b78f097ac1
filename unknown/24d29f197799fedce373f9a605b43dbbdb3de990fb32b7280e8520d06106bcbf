import { useState } from 'react';
import cn from 'classnames';
import { upperCase } from 'lodash';
import { isImage } from '@helpers/tdoHelper';
import { Videocam, Photo } from '@mui/icons-material';
import { TreeObjectId } from '@cbsa-modules/universal';
import { ViewImageDialog, ViewVideoDialog } from '@cbsa-components/Dialogs';
import { FETCH_MEDIA_RESPONSE } from '@cbsa/state/modules/appWrapper/services/queries/fetchMedia';
import * as styles from './index.scss';
import { ElementOf } from 'ts-essentials';

const Media = ({ caseId, media, isRedacted }: Props) => {
  const [isImageModalOpen, setImageModalOpen] = useState(false);
  const [isVideoModalOpen, setVideoModalOpen] = useState(false);

  const { assets, id: fileId, name, primaryAsset } = media;

  const isPhoto = isImage(media);
  const redactedAsset = (assets?.records || []).find(
    (asset) => asset?.assetType === 'redacted-media'
  );

  const renderIcon = () =>
    isPhoto ? (
      <Photo
        className={styles.mediaIcon}
        data-testid="app-bar-file-menu-photo"
      />
    ) : (
      <Videocam
        className={cn(styles.mediaIcon)}
        data-testid="app-bar-file-menu-video-cam"
      />
    );

  const renderStatus = (): string => {
    const status = media?.jobs?.records?.[0]?.status;
    return {
      pending: 'processing',
      running: 'processing',
      complete: 'complete',
      cancelled: 'error',
      queued: 'processing',
      failed: 'error',
      default: isPhoto ? 'new' : 'error',
    }[status || 'default'];
  };

  return (
    <>
      <div
        className={styles.media}
        onClick={() =>
          isPhoto
            ? setImageModalOpen(true)
            : isRedacted
              ? setVideoModalOpen(true)
              : (window.location.href = `/case/${caseId}/files/${fileId}`)
        }
        data-testid="app-bar-file-menu-media-container"
      >
        <div className={styles.mediaGroup}>
          {renderIcon()}
          <div
            className={styles.mediaName}
            data-testid="app-bar-file-menu-media-name"
          >
            {name}
          </div>
        </div>
        <div className={cn(styles.mediaStatus, styles[renderStatus()])}>
          {upperCase(renderStatus())}
        </div>
      </div>
      <ViewImageDialog
        title={name}
        signedUri={primaryAsset?.signedUri ?? ''}
        isOpen={isImageModalOpen}
        onClose={() => setImageModalOpen(false)}
      />
      <ViewVideoDialog
        title={name}
        signedUri={redactedAsset?.signedUri ?? ''}
        isOpen={isVideoModalOpen}
        onClose={() => setVideoModalOpen(false)}
      />
    </>
  );
};

interface Props {
  readonly caseId: TreeObjectId;
  readonly media: ElementOf<
    FETCH_MEDIA_RESPONSE['folder']['childTDOs']['records']
  >;
  readonly isRedacted: boolean;
}

export default Media;

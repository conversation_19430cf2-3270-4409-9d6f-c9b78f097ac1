import {
  DetailedHTMLProps,
  ImgHTMLAttributes,
  memo,
  RefObject,
  useEffect,
  useRef,
  useState,
} from 'react';
// import { debounce } from 'lodash';
import QueueManager from './queue';

const emptyImg =
  'data:image/gif;base64,R0lGODlhAQABAIAAAAUEBAAAACwAAAAAAQABAAACAkQBADs=';
import loadingBalls from '@resources/images/loading-balls.svg';

const MAX_RUNNING = 6; // max number of active requests - no point in having more than 6 since browser can only open 6 connections at a time

class ImageManager extends QueueManager<HTMLImageElement> {
  private lookup: WeakMap<RefObject<HTMLImageElement>, HTMLImageElement> =
    new WeakMap();

  onInit(
    ref: RefObject<HTMLImageElement>,
    setSrc: (src: string) => void,
    priority = 10,
    src = ''
  ) {
    const image = new Image();
    image.onload = () => {
      if (ref.current) {
        setSrc(image.src || emptyImg);
      }

      this.onComplete(ref, image.src);
    };

    image.onerror = () => {
      if (ref.current) {
        setSrc(emptyImg);
      }
      this.onComplete(ref, src);
    };

    this.lookup.set(ref, image);
    this.add({ priority, key: src, payload: image });

    this.callNextItem();
  }

  private callNextItem() {
    if (this.runningCount() < MAX_RUNNING) {
      // get next (most recently added) item from queue backlog
      // could in theory keep add items until hit MAX_RUNNING but probably not necessary
      const nextItem = this.nextItem();
      if (!nextItem) {
        return;
      }
      // set the src so will begin to download
      nextItem.payload.src = nextItem.key;
    }
  }

  private onComplete(ref: RefObject<HTMLImageElement>, key?: string) {
    const image = this.lookup.get(ref);
    if (image) {
      image.onload = null;
      image.onerror = null;
      this.completeItem(key || image.src, true); // remove from runningQueue and save to resultCache
    } else {
      // possible by the time it has loaded image has already gone off screen
      if (key) {
        this.completeItem(key, true);
      }
    }

    this.callNextItem();
  }

  getLookup(ref: RefObject<HTMLImageElement>) {
    return this.lookup.get(ref) || null;
  }

  removeLookup(ref: RefObject<HTMLImageElement>) {
    this.lookup.delete(ref);
  }
}

const imageManager = new ImageManager();

const Qimg = ({ src, priority, ...imgProps }: QimgPropTypes) => {
  const ref = useRef<HTMLImageElement>(null);
  const [imgSrc, setSrc] = useState(loadingBalls);

  useEffect(() => {
    if (!src) {
      setSrc(emptyImg);
      return;
    }

    const cacheImage = imageManager.getCachedItem(src); // check if we have the cached image
    if (cacheImage && ref.current) {
      ref.current.src = cacheImage.payload.src;
      return;
    }
  }, [src]);

  useEffect(() => {
    if (!src) {
      return;
    }

    // useEffect is being triggered twice for items the get run during the onInit ( due to strict mode )
    // use checkQueue to prevent it from adding onInit twice and creating a duplicate item to the queue
    if (imageManager.checkForDuplicate(src)) {
      return;
    }

    imageManager.onInit(ref, setSrc, priority, src);

    return () => {
      // when element goes offscreen it will be cleaned up
      const image = imageManager.getLookup(ref);
      if (image) {
        imageManager.removeLookup(ref);
      }

      if (src) {
        imageManager.remove(src); // remove item if still in waitingQueue
      }
    };
  }, [priority, src]);

  return <img ref={ref} src={imgSrc} {...imgProps} />;
};

export default memo(Qimg);

export interface QimgPropTypes
  extends DetailedHTMLProps<
    ImgHTMLAttributes<HTMLImageElement>,
    HTMLImageElement
  > {
  readonly priority?: number;
}

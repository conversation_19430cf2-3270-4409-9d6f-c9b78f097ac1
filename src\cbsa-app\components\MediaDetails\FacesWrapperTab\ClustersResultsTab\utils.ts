import { selectSelected } from '@common-modules/mediaDetails';
import { ClusterSegment } from '@common-modules/mediaDetails/models';
import { NonEmptyArray } from 'ts-essentials';

export const getActiveState = (
  segments: Readonly<NonEmptyArray<ClusterSegment>>,
  selected: ReturnType<typeof selectSelected>
): 1 | 0 | -1 => {
  const selectedItems = segments.filter((segment) => selected[segment.id]);
  return selectedItems.length === 0
    ? -1
    : selectedItems.length === segments.length
      ? 1
      : 0;
};

import { BoundingPolyRect } from './BoundingPoly';
import { TDOId } from '@common-modules/universal/models/Brands';

export interface Corsight {
  readonly tdoId: TDOId;
  readonly engineId: string;
  readonly startOffsetMs: number;
  readonly stopOffsetMs: number;
  readonly jsondata: {
    readonly sourceEngineId: string;
    readonly taskId: string;
    readonly modifiedDateTime: number;
    readonly internalTaskId: string;
    readonly series: ReadonlyArray<CorsightSeriesObject>;
  };
}
export interface CorsightSeriesObject {
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
  object: {
    readonly label: string;
    readonly confidence: number;
    readonly type: string;
    boundingPoly: BoundingPolyRect;
    readonly uri: string;
  };
}

import {
  CaseId,
  Thunk,
  lookupLatestCaseSchemaId,
  lookupLatestCaseNotificationSchemaId,
} from '@cbsa-modules/universal';
import callGraph<PERSON><PERSON>pi from '@helpers/callGraph<PERSON>A<PERSON>';
import { NOOP } from '@cbsa-modules/universal/actions';
import {
  FETCH_TDOS_QUERY,
  FETCH_CASE_DETAILS_QUERY,
  FETCH_CASE_NOTIFICATIONS_QUERY,
  FETCH_CASE_DETAILS_RESPONSE,
  FETCH_TDOS_QUERY_RESPONSE,
  FETCH_CASE_NOTIFICATIONS_QUERY_RESPONSE,
} from './queries/fetchCase';
import {
  FETCH_TDOS_SUCCESS,
  <PERSON>ETCH_TDOS_FAILURE,
  FETCH_CASE_DETAILS_SUCCESS,
  FETCH_CASE_DETAILS_FAILURE,
  FETCH_CASE_NOTIFICATIONS_SUCCESS,
  FETCH_CASE_NOTIFICATIONS_FAILURE,
} from '../actions';

export const fetchTdos: Thunk<{
  readonly caseId: CaseId;
}> =
  ({ caseId }) =>
  async (dispatch, getState) =>
    await callGraph<PERSON>Api<FETCH_TDOS_QUERY_RESPONSE>({
      actionTypes: [NOOP, FETCH_TDOS_SUCCESS, FETCH_TDOS_FAILURE],
      query: FETCH_TDOS_QUERY,
      variables: { caseId },
      dispatch,
      getState,
    });

export const fetchCaseDetails: Thunk<
  {
    readonly caseId: CaseId;
  },
  FETCH_CASE_DETAILS_RESPONSE
> =
  ({ caseId }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    return await callGraphQLApi<FETCH_CASE_DETAILS_RESPONSE>({
      actionTypes: [
        NOOP,
        FETCH_CASE_DETAILS_SUCCESS,
        FETCH_CASE_DETAILS_FAILURE,
      ],
      query: FETCH_CASE_DETAILS_QUERY,
      variables: { caseId, schemaId },
      dispatch,
      getState,
    });
  };

export const fetchCaseNotifications: Thunk<{
  readonly caseId: CaseId;
}> =
  ({ caseId }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseNotificationSchemaId(
      dispatch,
      getState
    );
    if (!schemaId) {
      return;
    }

    return await callGraphQLApi<FETCH_CASE_NOTIFICATIONS_QUERY_RESPONSE>({
      actionTypes: [
        NOOP,
        FETCH_CASE_NOTIFICATIONS_SUCCESS,
        FETCH_CASE_NOTIFICATIONS_FAILURE,
      ],
      query: FETCH_CASE_NOTIFICATIONS_QUERY,
      variables: { caseId, schemaId },
      dispatch,
      getState,
    });
  };

import express from 'express';
import { Messages } from '../errors/messages';
import { Logger } from '../logger';
import { redactedMediaQuery } from '../api/queries';
import { pick } from 'lodash';
import { callGQL } from '../api/callGraphql';
import { RedactedMediaResponse } from '../model/responses';
import { StatusCodes } from '../errors/statusCodes';
import { isTdoId } from '../validations/helpers';

export const redactedMedia = async (
    req: express.Request,
    res: express.Response
  ) => {
    const tdoId = req.params.tdoId;
    if (!isTdoId(tdoId)) {
        return res.status(StatusCodes.BadRequest).json({
          error: Messages.caseIdRequired
        });
      }
    const headers = pick(req.headers, ['authorization']);
    const query = redactedMediaQuery(tdoId);
    try {
      const response = await callGQL<RedactedMediaResponse>(headers, query);
      return res.status(StatusCodes.Success).json({
        ...response?.temporalDataObject
      });
    } catch(err) {
      Logger.error(err);
      return res.status(StatusCodes.BadRequest).send(err);
    }
  };
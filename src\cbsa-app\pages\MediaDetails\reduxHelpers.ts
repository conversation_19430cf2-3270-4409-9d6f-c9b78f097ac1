import { createSelector } from 'reselect';

import { selectUploadingFiles } from '@cbsa-modules/appWrapper';

import { actionTryPermission } from '@user-permissions';

import {
  fetchCaseMedia,
  selectCaseMedia,
  selectIsLoading,
  selectTdoIsChanged,
  selectMediaDetailsPage,
  faceDetectionInfoSelector,
  selectIsRedactionAllowed,
  isFetchingFaceDetectionSelector,
  selectGettingRedactionJobStatus,
  FETCH_UPDATE_ASSET_ACTION,
  REDACT_TDO,
} from '@common-modules/mediaDetails';

import { CaseId } from '@cbsa-modules/universal';
import { selectCurrentRoutePayload } from '@common-modules/routing';

export const componentSelectors = createSelector(
  selectMediaDetailsPage,
  selectIsLoading,
  selectTdoIsChanged,
  faceDetectionInfoSelector,
  selectCurrentRoutePayload,
  selectCaseMedia,
  selectUploadingFiles,
  selectIsRedactionAllowed,
  isFetchingFaceDetectionSelector,
  selectGettingRedactionJobStatus,
  (
    page,
    isLoading,
    hasUnsavedChanges,
    faceDetectionInfo,
    payload,
    caseMedia,
    uploadingFiles,
    isRedactionAllowed,
    isFetchingFaceDetection,
    gettingRedactionJobStatus
  ) => ({
    page,
    isLoading,
    hasUnsavedChanges,
    faceDetectionInfo,
    caseId: payload.case_id,
    tdoId: payload.tdoId,
    caseMedia,
    uploadingFiles,
    isRedactionAllowed,
    isSaveEnabled: !!hasUnsavedChanges && !isFetchingFaceDetection,
    gettingRedactionJobStatus,
  })
);

export const componentActions = {
  fetchCaseMedia: (caseId: CaseId) => fetchCaseMedia(caseId),
  onUpdateAsset: () => FETCH_UPDATE_ASSET_ACTION(),
  onTryRedact: actionTryPermission,
  onRedact: () => REDACT_TDO(),
};

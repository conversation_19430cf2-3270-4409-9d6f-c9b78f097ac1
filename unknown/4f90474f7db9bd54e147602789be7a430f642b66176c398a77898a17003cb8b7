import { createReducer } from '@reduxjs/toolkit';
import { ENQUEUE_SNACKBAR, CLOSE_SNACKBAR, REMOVE_SNACKBAR } from './action';
import { SnackbarInterface } from './models';

const defaultState = {
  notifications: [] as SnackbarInterface[],
};

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(ENQUEUE_SNACKBAR, (state, action) => ({
      ...state,
      notifications: [...state.notifications, action.payload.notification],
    }))
    .addCase(CLOSE_SNACKBAR, (state, action) => ({
      ...state,
      notifications: state.notifications.map(
        (notification: SnackbarInterface) =>
          action.payload.dismissAll || notification.key === action.payload.key
            ? { ...notification, dismissed: true }
            : { ...notification }
      ),
    }))
    .addCase(REMOVE_SNACKBAR, (state, action) => ({
      ...state,
      notifications: state.notifications.filter(
        (notification: SnackbarInterface) =>
          notification.key !== action.payload.key
      ),
    }));
});

export default reducer;

import * as React from 'react';
import { connect, ConnectedProps } from 'react-redux';
import * as styles from './styles.scss';
import Draggable, { DraggableEventHandler } from 'react-draggable';
import Tooltip from '@mui/material/Tooltip';
import moment from 'moment';
import { Player } from 'video-react';

interface Props extends PropsFromRedux {
  duration: number;
  onChange: (change: { startTime?: number; endTime?: number }) => void;
  playerRef: React.MutableRefObject<Player | null>;
  // ???
  order?: number;
}

const DEFAULT_TRIMBAR_OFFSET = 216;

class TrimSlider extends React.PureComponent<Props> {
  static defaultProps = {};
  state = {
    showStick: false,
    maxOfX: 0,

    startPosition: { x: 0, y: 0 },
    endPosition: { x: 0, y: 0 },
    rangePosition: { x: 0, y: 0 },
    rangeWidth: undefined,
  };

  componentDidMount() {
    const { endPosition } = this.state;
    const maxOfX = (this.trimRef.current?.clientWidth || 0) - 9; // 9 = width of end icon
    if (!endPosition.x) {
      const { onChange, duration } = this.props;
      onChange({ startTime: 0, endTime: duration });

      this.setState({
        endPosition: { x: maxOfX, y: 0 },
        widthOfWrapper: maxOfX,
        maxOfX,
      });
    }
  }

  trimRef = React.createRef<HTMLDivElement>();

  getTrimbarLeftOffset = () =>
    this.trimRef?.current?.getBoundingClientRect().left ??
    DEFAULT_TRIMBAR_OFFSET;

  handleOnclickIcons = () => {
    const { showStick } = this.state;
    this.setState({ showStick: !showStick });
  };
  // START TIME AREA
  startTimeDragOver: DraggableEventHandler = (dragEvt) => {
    // Calculate x from event taking into account the offset of the trim bar
    const x =
      (dragEvt as MouseEvent).clientX -
      this.trimRef.current!.getBoundingClientRect().left;
    const endTimeX = this.state.endPosition.x;

    if (x < endTimeX) {
      const width = endTimeX - x;
      // set start time to store
      const { maxOfX } = this.state;
      const { onChange, duration } = this.props;
      this.props.playerRef.current?.video.seek((x / maxOfX) * duration);
      onChange({ startTime: maxOfX ? (x / maxOfX) * duration : 0 });

      this.setState({
        rangePosition: { x: x, y: 0 },
        rangeWidth: width,
        startPosition: { x, y: 0 },
      });
    }
  };
  // END - START TIME AREA

  // END TIME AREA
  endTimeDragOver: DraggableEventHandler = (dragEvt) => {
    // Calculate x from event taking into account the offset of the trim bar
    const x =
      (dragEvt as MouseEvent).clientX -
      this.trimRef.current!.getBoundingClientRect().left;

    const startTimeX = this.state.startPosition.x;
    if (x > startTimeX) {
      const width = x - startTimeX;
      // set end time to store
      const { maxOfX } = this.state;
      const { onChange, duration } = this.props;
      onChange({ endTime: maxOfX ? (x / maxOfX) * duration : 0 });

      this.setState({
        rangeWidth: width,
        endPosition: { x, y: 0 },
      });
    }
  };
  // END - END TIME AREA

  getTimeByPosition = (x: number) => {
    const { duration } = this.props;
    const { maxOfX } = this.state;
    const numberOfSeconds = maxOfX ? (x / maxOfX) * duration : 0;
    return moment().startOf('day').seconds(numberOfSeconds).format('H:mm:ss');
  };

  render() {
    const { rangeWidth, rangePosition, startPosition, endPosition } =
      this.state;
    const startXLabel = this.getTimeByPosition(startPosition.x);
    const endXLabel = this.getTimeByPosition(endPosition.x);
    return (
      <div
        className={`${styles.trimWrapper} trimbar-tool-wrapper`}
        data-tag="trimbar-tool"
        ref={this.trimRef}
      >
        <div className={styles.fakeProgressBar} />
        <Draggable
          axis="x"
          position={startPosition}
          bounds="parent"
          onDrag={this.startTimeDragOver}
        >
          <Tooltip title={startXLabel} placement="top">
            <div className={styles.startTime} />
          </Tooltip>
        </Draggable>
        <Draggable
          axis="x"
          position={rangePosition}
          bounds="parent"
          cancel="div"
        >
          <div
            className={styles.trimRange}
            style={{
              width: rangeWidth || `calc(100%)`,
            }}
          />
        </Draggable>
        <Draggable
          axis="x"
          defaultPosition={endPosition}
          position={endPosition}
          bounds="parent"
          onDrag={this.endTimeDragOver}
        >
          <Tooltip title={endXLabel} placement="top">
            <div className={styles.endTime} />
          </Tooltip>
        </Draggable>
      </div>
    );
  }
}

const mapState = (state: any) => ({
  // TODO: Player type needs to be defined properly
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  duration: state.player.duration,
});

const connector = connect(mapState, {});

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(React.memo(TrimSlider));

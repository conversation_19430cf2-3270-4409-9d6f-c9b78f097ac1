import moment from 'moment';
import { Logger } from '../logger';
import Config from '../../apiConfig.json';
import { callGQL } from '../api/callGraphql';
import { Messages } from '../errors/messages';
import { LOCK_REFRESH } from '../api/constants';
import { getSchemaIdAdapter} from './getSchema';
import { RequestHeader } from '../model/requests';
import { findTdoLockQuery } from '../api/queries';
import { TDOId } from '../model/brands';

export const checkTdoIsLockedAdapter = async (
  headers: RequestHeader,
  tdoId: TDOId
) => {
  const schemaId = await getSchemaIdAdapter(headers, Config.tdoLockRegistryId);

  try {
    const currentTime = moment();
    const data = await callGQL<{
      structuredDataObjects: {
        records: [{ id: string; data: UpdateTDOLockResponse }];
      };
    }>(headers, findTdoLockQuery, { schemaId, tdoId });

    const records = data?.structuredDataObjects?.records;
    const record = records?.find(({ data: { lastAccessed } }) => {
      const lockedTime = moment(lastAccessed);
      const currentTime = moment();
      const timeDifference = moment
        .duration(currentTime.diff(lockedTime))
        .asMilliseconds();

      const isExpired = timeDifference > LOCK_REFRESH * 5;
      return !isExpired;
    });

    if (record?.data && record?.id) {
      const timePassed = currentTime.diff(moment(record.data.lastAccessed));
      return timePassed < LOCK_REFRESH * 5;
    }
  } catch(err) {
    Logger.error(Messages.checkTdoIsLockedFail + JSON.stringify(err));
    return true;
  }
  return false;
};

interface UpdateTDOLockResponse {
  id: string;
  name: string;
  tdoId: TDOId;
  userId: string;
  lastAccessed: string;
}

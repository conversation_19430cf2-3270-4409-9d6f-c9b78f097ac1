import { TreeObjectId, UploadMedia } from '@cbsa-modules/universal';
import { createAction } from '@reduxjs/toolkit';

export const ADD_FILE = createAction<{
  file: File;
  caseId: TreeObjectId;
}>('CBSA/ADD_FILE');
export const QUEUE_FILE = createAction<{
  file: File;
  caseId: TreeObjectId;
}>('CBSA/QUEUE_FILE');
export const UPLOAD_FILE = createAction('CBSA/UPLOAD_FILE');
export const CHECK_QUEUE = createAction('CBSA/CHECK_QUEUE');
export const PROCESS_FILE = createAction<{ uploadFile: UploadMedia }>(
  'CBSA/PROCESS_FILE'
);
export const UPLOAD_FILE_SUCCESS = createAction<{ uploadFile: UploadMedia }>(
  'CBSA/UPLOAD_FILE_SUCCESS'
);
export const UPLOAD_FILE_FAILURE = createAction<{
  uploadFile: UploadMedia;
  error?: any;
}>('CBSA/UPLOAD_FILE_FAILURE');
export const REMOVE_SUCCESSFUL_FILE = createAction<{
  uploadFile: UploadMedia;
}>('CBSA/REMOVE_SUCCESSFUL_FILE');
export const RETRY_FAILED_FILE = createAction<{
  uploadFile: UploadMedia;
}>('CBSA/RETRY_FAILED_FILE');

export const addFile = ({
  file,
  caseId,
}: {
  file: File;
  caseId: TreeObjectId;
}) => ADD_FILE({ file, caseId });

export const queueFile = ({
  file,
  caseId,
}: {
  file: File;
  caseId: TreeObjectId;
}) => QUEUE_FILE({ file, caseId });

export const uploadMedia = () => UPLOAD_FILE();

export const checkQueue = () => CHECK_QUEUE();

export const processFile = (uploadFile: UploadMedia) =>
  PROCESS_FILE({ uploadFile });

export const removeSuccessfulFile = (uploadFile: UploadMedia) =>
  REMOVE_SUCCESSFUL_FILE({ uploadFile });

export const retryFailedFile = (uploadFile: UploadMedia) =>
  RETRY_FAILED_FILE({ uploadFile });

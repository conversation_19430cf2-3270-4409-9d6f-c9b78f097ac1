import { memo } from 'react';
import { useIntl } from 'react-intl';
import Dialog from '@common-components/ConfirmDialog';
import { Player, ControlBar, BigPlayButton } from 'video-react';
import * as styles from './index.scss';

const ViewVideoDialog = ({ title, signedUri, isOpen, onClose }: Props) => {
  const intl = useIntl();
  return (
    <Dialog
      isOpen={isOpen}
      title={title}
      content={
        <div className={styles.video} data-testid="view-video-dialog-container">
          <Player autoPlay src={signedUri}>
            <ControlBar autoHide={false} className={styles.controlBar} />
            <BigPlayButton
              position="center"
              className={styles.mediaPlayButton}
            />
          </Player>
        </div>
      }
      confirmText={intl.formatMessage({ id: 'download' })}
      onConfirm={() => {
        window.location.href = signedUri;
        onClose();
      }}
      cancelText={intl.formatMessage({ id: 'close' })}
      onCancel={onClose}
    />
  );
};

interface Props {
  readonly title: string;
  readonly signedUri: string;
  readonly isOpen: boolean;
  readonly onClose: () => void;
}

export default memo(ViewVideoDialog);

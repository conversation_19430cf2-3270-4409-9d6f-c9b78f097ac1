import { lazy, Suspense } from 'react';
import { useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import { modules } from '@veritone/glc-redux';
import { isString } from 'lodash';

import NotFound from '@common-pages/NotFound';
import RouteErrorScreen from '@common-components/RouteErrorScreen';
import RouteLoadingScreen from '@common-components/RouteLoadingScreen';
import {
  ROUTE_HOME,
  ROUTE_INGEST,
  ROUTE_INGEST_GOVQA,
  selectRoutesMap,
  selectRouteType,
} from '@common-modules/routing';
import { bootDidFinish } from '@common-modules/app';
import { selectEnableFolderView } from '@common/user-onboarding/selectors/organization';
import { isRedact } from '@common-pages/App/helper';

const {
  user: { userIsAuthenticated },
} = modules;

const combinedSelectors = createSelector(
  selectRoutesMap,
  selectRouteType,
  userIsAuthenticated,
  bootDidFinish,
  (
    routesMap,
    routeType,
    isAuthed,
    isBooted,
    currentRoute:
      | ReturnType<typeof selectRoutesMap>['']
      | undefined = routesMap[routeType]
  ) => ({
    isPermittedToRenderCurrentPage:
      (!isString(currentRoute) && !currentRoute?.requiresAuth) || isAuthed,
    PageComponent:
      isString(currentRoute) || !currentRoute?.component
        ? NotFound
        : lazy(currentRoute?.component),
    isLoading: !isBooted,
    routeType,
  })
);

const RootRouter = ({ appName }: { appName?: string }) => {
  const {
    routeType,
    PageComponent,
    isLoading,
    isPermittedToRenderCurrentPage,
  } = useSelector(combinedSelectors);

  const isEnableFolderView = useSelector(selectEnableFolderView);
  let RenderPageComponent = PageComponent;
  const allowedRoutes: string[] = [
    ROUTE_HOME.type,
    ROUTE_INGEST.type,
    ROUTE_INGEST_GOVQA.type,
  ];
  if (
    isRedact(appName) &&
    isEnableFolderView &&
    allowedRoutes.includes(routeType)
  ) {
    RenderPageComponent = lazy(() => import(`@redact-pages/CaseMediaPage`));
  }

  if (isLoading) {
    return <RouteLoadingScreen />;
  } else if (isPermittedToRenderCurrentPage) {
    return (
      <RouteErrorScreen>
        <Suspense key={routeType} fallback={<RouteLoadingScreen />}>
          <RenderPageComponent />
        </Suspense>
      </RouteErrorScreen>
    );
  } else {
    return <></>;
  }
};

export default RootRouter;

import { I18nTranslate } from '@common/i18n';
import * as styles from './styles.scss';
import { RotateLeft, RotateRight } from '@mui/icons-material';
import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  FormGroup,
  IconButton,
} from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import { Player } from 'video-react';
import { actionOnChangeDetectObjects } from '@common/state/modules/facesTabModule';
import {
  rotateVideo,
  selectDetectObjects,
} from '@common/state/modules/mediaDetails';
import { useDispatch, useSelector } from 'react-redux';
import useClosePopup from '@common/state/hooks/useClosePopup';

const style = {
  dialogTitle: {
    '&.MuiTypography-root': {
      '&.MuiDialogTitle-root': {
        color: '#005C7E',
        backgroundColor: '#005C7E1A',
        fontSize: '1rem',
        textTransform: 'uppercase',
      },
    },
  },
  dialogContent: {
    '&.MuiDialogContent-root': {
      whiteSpace: 'pre-line',
      padding: '20px 24px 0 24px',
      display: 'flex',
      flexDirection: 'column',
      gap: '8px',

      '.rotate-warning': {
        color: '#AE2E24',
        fontWeight: 500,
      },
    },
  },
  controlButton: {
    '&.MuiSvgIcon-root': {
      '&:hover, &:focus': {
        filter: 'drop-shadow(0em 0em 1em #fff) drop-shadow(0em 0em 0.5em #fff)',
      },
    },
  },
  checkbox: {
    height: '32px',
  },
};

// Aspect ratio of the background is 16/9
const CANVAS_BACKGROUND_WIDTH = 480;
const CANVAS_BACKGROUND_HEIGHT = 270;

const RotateIcon = (props: RotateIconProps) => {
  const dispatch = useDispatch();

  const { playerRef } = props;
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const [step, setStep] = useState(0);
  const [rotationDegrees, setRotationDegrees] = useState(0);

  const detectObjects = useSelector(selectDetectObjects);

  const isFlipAxes = Math.abs(rotationDegrees) % 180 === 90;

  let videoWidth = playerRef.current?.video.videoWidth;
  let videoHeight = playerRef.current?.video.videoHeight;

  // Swap width and height when flipping axes
  if (isFlipAxes) {
    [videoWidth, videoHeight] = [videoHeight, videoWidth];
  }

  // Calculate the image size to fit inside the background canvas
  const imageAspectRatio =
    // TODO: Not sure if this makes sense, should we just return empty element if playerRef is null?
    videoWidth !== undefined && videoHeight !== undefined
      ? videoWidth / videoHeight
      : 1;

  // case 1 background matches or black pads left and right of image
  let canvasWidth = CANVAS_BACKGROUND_HEIGHT * imageAspectRatio;
  let canvasHeight = CANVAS_BACKGROUND_HEIGHT;

  // case 2 aspect ratio will black pad top and bottom (uncommon - would only happen on very high AR video)
  if (imageAspectRatio > CANVAS_BACKGROUND_WIDTH / CANVAS_BACKGROUND_HEIGHT) {
    canvasWidth = CANVAS_BACKGROUND_WIDTH;
    canvasHeight = CANVAS_BACKGROUND_WIDTH / imageAspectRatio;
  }

  useEffect(() => {
    const drawPreview = () => {
      const canvas = canvasRef.current;
      const context = canvas?.getContext('2d');

      const video = playerRef.current?.video.video;

      if (canvas && context && video) {
        // apply rotation
        if (rotationDegrees) {
          // 1.) Shift so center is at the origin the canvas
          // 2.) Rotate (about the origin)
          // 3.) Shift top left corner back to the origin
          context.translate(canvasWidth / 2, canvasHeight / 2);
          context.rotate((-1 * rotationDegrees * Math.PI) / 180); // -1 converts angle to counter clockwise
          if (isFlipAxes) {
            context.translate(-canvasHeight / 2, -canvasWidth / 2);
          } else {
            context.translate(-canvasWidth / 2, -canvasHeight / 2);
          }
        }

        // draw image
        if (isFlipAxes) {
          context.drawImage(video, 0, 0, canvasHeight, canvasWidth);
        } else {
          context.drawImage(video, 0, 0, canvasWidth, canvasHeight);
        }
      }
    };

    if (step === 1) {
      drawPreview();
    }
  }, [canvasHeight, canvasWidth, isFlipAxes, playerRef, rotationDegrees, step]);

  const onRotateLeft = () => setRotationDegrees((rotationDegrees + 90) % 360);
  const onRotateRight = () => setRotationDegrees((rotationDegrees + 270) % 360);

  const onConfirm = () => {
    setStep(0);
    setRotationDegrees(0);

    dispatch(rotateVideo(rotationDegrees));
  };

  const onCancel = () => {
    setStep(0);
    setRotationDegrees(0);
  };

  useClosePopup({ handleCloses: [onCancel] });

  const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = event.target;
    dispatch(actionOnChangeDetectObjects(name, checked));
  };

  return (
    <>
      <button
        // These classes are from the Video-React library
        className="video-react-control video-react-button video-react-icon"
        onClick={() => {
          setStep(1);
          setRotationDegrees(90);
        }}
      >
        <RotateLeft fontSize="small" sx={style.controlButton} />
      </button>

      <Dialog open={step === 1} onClose={onCancel} disablePortal>
        <DialogTitle sx={style.dialogTitle}>
          {I18nTranslate.TranslateMessage('rotationPreview')}
        </DialogTitle>
        <DialogContent sx={style.dialogContent}>
          <div
            className={styles.canvasBackground}
            style={{
              width: CANVAS_BACKGROUND_WIDTH,
              height: CANVAS_BACKGROUND_HEIGHT,
            }}
          >
            <canvas ref={canvasRef} width={canvasWidth} height={canvasHeight} />
          </div>
          <div className={styles.rotateButtonsContainer}>
            <IconButton onClick={onRotateLeft}>
              <RotateLeft />
            </IconButton>
            <IconButton onClick={onRotateRight}>
              <RotateRight />
            </IconButton>
          </div>
        </DialogContent>
        <DialogActions>
          <Button onClick={onCancel} color="primary">
            {I18nTranslate.TranslateMessage('cancel')}
          </Button>
          <Button
            type="submit"
            color="primary"
            onClick={() => setStep(2)}
            disabled={rotationDegrees === 0}
          >
            {I18nTranslate.TranslateMessage('continue')}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={step === 2}
        onClose={onCancel}
        container={document.fullscreenElement}
      >
        <DialogTitle sx={style.dialogTitle}>
          {I18nTranslate.TranslateMessage('rotateConfirmTitle')}
        </DialogTitle>
        <DialogContent sx={style.dialogContent}>
          <div className="rotate-warning">
            {I18nTranslate.TranslateMessage('rotateConfirmDes')}
          </div>
          <div>{I18nTranslate.TranslateMessage('selectDetectOption')}</div>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  name={'headObjects'}
                  value={detectObjects.headObjects}
                  onChange={onChange}
                  sx={style.checkbox}
                />
              }
              label={I18nTranslate.TranslateMessage('detectHeadObjects')}
            />
            <FormControlLabel
              control={
                <Checkbox
                  name={'personObjects'}
                  value={detectObjects.personObjects}
                  onChange={onChange}
                  sx={style.checkbox}
                />
              }
              label={I18nTranslate.TranslateMessage('detectPersonObjects')}
            />
          </FormGroup>
        </DialogContent>
        <DialogActions>
          <Button onClick={onCancel} color="primary">
            {I18nTranslate.TranslateMessage('cancel')}
          </Button>
          <Button type="submit" color="primary" onClick={onConfirm}>
            {I18nTranslate.TranslateMessage('continue')}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

interface RotateIconProps {
  order?: number; // This prop is unused here but controls the button order in the parent component.
  playerRef: React.MutableRefObject<Player | null>;
}

export default RotateIcon;

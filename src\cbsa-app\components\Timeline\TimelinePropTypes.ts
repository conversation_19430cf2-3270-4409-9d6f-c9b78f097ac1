import { createSelector } from 'reselect';

import {
  actionRedactSlice,
  actionSelectSlice,
  actionDeselectAll,
  actionSeekVideo,
  actionUnredactSlice,
  selectAudiowaves,
  selectDetectionCollections,
  selectCurrentPosition,
  selectMediaDuration,
  selectProgress,
  selectAudioRedactions,
  selectSelected,
  selectSelectedTimeSlices,
  selectUdrAsset,
  selectTranscriptionList,
  setSelectedUdrGroup,
  actionChangeUDRsPolyAssetGroupSeriesItem,
  actionChangeUDRsPolyAssetGroupSeriesItemSubmit,
  actionChangeUDRsPolyAssetGroupLabel,
  actionUDRSelectedFromTimeline,
  selectHighlightedOverlay,
  selectGlobalSettings,
  setFaceHighlight,
  selectUDRCollection,
  selectSelectedUDRGroupId,
} from '@common-modules/mediaDetails';
import { UDRsPolyAssetGroupSeriesItem } from '@common-modules/mediaDetails/models';
import { OBJECT_TYPE } from '@helpers/constants';

export const mapStateToProps = createSelector(
  selectMediaDuration,
  selectCurrentPosition,
  selectProgress,
  selectDetectionCollections,
  selectUDRCollection,
  selectSelected,
  selectAudioRedactions,
  selectSelectedTimeSlices,
  selectUdrAsset,
  selectTranscriptionList,
  selectGlobalSettings,
  selectAudiowaves,
  selectHighlightedOverlay,
  selectSelectedUDRGroupId,
  (
    mediaDuration,
    currentTime,
    progress,
    detectionCollections,
    udrCollection,
    selectedPolys,
    transcriptRedactions,
    selectedTimeSlices,
    udrAsset,
    transcriptionList,
    globalSettings,
    audiowaves,
    highlightedOverlay,
    selectedUDRGroupId
  ) => ({
    audiowaves,
    mediaDuration,
    currentTime,
    progress,
    detectionCollections,
    udrCollection,
    selectedPolys,
    transcriptRedactions,
    selectedTimeSlices,
    udrAsset,
    transcriptionList,
    highlightedOverlay,
    globalSettings,
    selectedUDRGroupId,
  })
);

export interface TimelineMapDispatchToProps {
  onSelectSlice: typeof actionSelectSlice;
  onDeselectAll: typeof actionDeselectAll;
  onRedactSlice: typeof actionRedactSlice;
  onUnredactSlice: typeof actionUnredactSlice;
  onSeekMedia: (startTimeMs: number) => ReturnType<typeof actionSeekVideo>;
  onSetSelectedUDRGroup: (groupId: string | undefined) => {
    type: string;
    payload?: {
      groupId: string | undefined;
    };
  };
  onChangeUDRGroupLabel: (
    groupId: string,
    label: string
  ) => { type: string; payload?: { groupId: string; label: string } };
  onUDRSelect: (
    groupId: string,
    id: string,
    seriesItem: UDRsPolyAssetGroupSeriesItem
  ) => {
    type: string;
    payload?: {
      groupId: string;
      id: string;
      seriesItem: UDRsPolyAssetGroupSeriesItem;
    };
  };
  onChangeUDR: (
    groupId: string,
    id: string,
    changeType: 'start' | 'end',
    seriesItem: UDRsPolyAssetGroupSeriesItem
  ) => {
    type: string;
    payload?: {
      groupId: string;
      id: string;
      seriesItem: UDRsPolyAssetGroupSeriesItem;
    };
  };
  onChangeUDRSubmit: (
    groupId: string,
    id: string,
    changeType: 'start' | 'end',
    seriesItem: UDRsPolyAssetGroupSeriesItem
  ) => {
    type: string;
    payload?: {
      groupId: string;
      id: string;
      seriesItem: UDRsPolyAssetGroupSeriesItem;
    };
  };
  onSetFaceHighlight: (
    payload: {
      id: string;
      timeMs: number;
      type: OBJECT_TYPE;
      groupId: string;
    } | null
  ) => {
    type: string;
    payload: {
      id: string;
      timeMs: number;
      type: OBJECT_TYPE;
      groupId: string;
    } | null;
  };
}

export const mapDispatchToProps = {
  onSelectSlice: actionSelectSlice,
  onDeselectAll: actionDeselectAll,
  onRedactSlice: actionRedactSlice,
  onUnredactSlice: actionUnredactSlice,
  onSeekMedia: (startTimeMs: number) => actionSeekVideo({ startTimeMs }),
  onSetSelectedUDRGroup: (groupId: string | undefined) =>
    setSelectedUdrGroup(groupId),
  onChangeUDRGroupLabel: (groupId: string, label: string) =>
    actionChangeUDRsPolyAssetGroupLabel({ groupId, label }),
  onUDRSelect: (
    groupId: string,
    id: string,
    seriesItem: UDRsPolyAssetGroupSeriesItem
  ) => actionUDRSelectedFromTimeline({ id, groupId, seriesItem }),
  onChangeUDR: (
    groupId: string,
    id: string,
    changeType: 'start' | 'end',
    seriesItem: UDRsPolyAssetGroupSeriesItem
  ) =>
    actionChangeUDRsPolyAssetGroupSeriesItem({
      groupId,
      id,
      seriesItem,
      changeType,
    }),
  onChangeUDRSubmit: (
    groupId: string,
    id: string,
    changeType: 'start' | 'end',
    seriesItem: UDRsPolyAssetGroupSeriesItem
  ) =>
    actionChangeUDRsPolyAssetGroupSeriesItemSubmit({
      groupId,
      id,
      seriesItem,
      changeType,
    }),
  onSetFaceHighlight: (
    payload: {
      id: string;
      timeMs: number;
      type: OBJECT_TYPE;
      groupId: string;
    } | null
  ) => setFaceHighlight(payload),
};

export type TimelinePropTypes = ReturnType<typeof mapStateToProps> &
  TimelineMapDispatchToProps;

import Konva from 'konva';
import { KonvaEventObject } from 'konva/lib/Node';
import { clamp } from 'lodash';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';

import { MIN_SLIDERS_WIDTH_PX, OFFSET_LEFT_X } from './constants';
import { Render } from './interfaces';
import { aToB } from './utils';

const onHover = () => (document.body.style.cursor = 'ew-resize');
const onEndHover = () => (document.body.style.cursor = 'default');

export const interactRenderer = (
  stage: Konva.Stage,
  minWindowMs: number,
  maxWindowMs: number,
  layer: Konva.Layer,
  onSetStartMs: (ms: number) => void,
  onSetStopMs: (ms: number) => void,
  onResize$: Observable<{ widthPx: number }>
): Render => {
  const container = stage.container();

  // SETUP CANVAS

  const leftHandle = new Konva.Rect({
    x: OFFSET_LEFT_X,
    y: 33,
    width: 15,
    height: 15,
    fill: '#900',
    draggable: true,
  });
  leftHandle.on('mouseover', onHover);
  leftHandle.on('mouseout', onEndHover);

  const rightHandle = new Konva.Rect({
    x: container.clientWidth,
    y: 33,
    width: 15,
    height: 15,
    offsetX: 15,
    fill: '#900',
    draggable: true,
  });
  rightHandle.on('mouseover', onHover);
  rightHandle.on('mouseout', onEndHover);

  const span = new Konva.Rect({
    x: leftHandle.x(),
    y: 38,
    width: rightHandle.x() - leftHandle.x(),
    height: 5,
    fill: '#900',
    draggable: true,
    dragBoundFunc: () => ({ x: leftHandle.x(), y: 38 }),
  });

  const group = new Konva.Group();
  group.on('mouseover', onHover);
  group.on('mouseout', onEndHover);
  group.add(span);
  group.add(leftHandle);
  group.add(rightHandle);
  layer.add(group);

  // UTIL FUNCTIONS

  const setLeftHandleDragBoundFunc = (p2m: (n: number) => number) => {
    const onStart = (x: number) => onSetStartMs(p2m(x - OFFSET_LEFT_X));

    leftHandle.dragBoundFunc(function (pos: { x: number }) {
      const x = clamp(
        pos.x,
        OFFSET_LEFT_X,
        rightHandle.x() - MIN_SLIDERS_WIDTH_PX
      );
      onStart(x);
      return {
        x,
        y: 33,
      };
    });
  };

  const setRightHandleDragBoundFunc = (
    rightX: number,
    p2m: (n: number) => number
  ) => {
    const onStop = (x: number) => onSetStopMs(p2m(x - OFFSET_LEFT_X));

    rightHandle.dragBoundFunc(function (pos: { x: number }) {
      const x = clamp(pos.x, leftHandle.x() + MIN_SLIDERS_WIDTH_PX, rightX);
      onStop(x);
      return {
        x,
        y: 33,
      };
    });
  };

  const setFullHandleDragHandler = (
    widthPx: number,
    p2m: (n: number) => number
  ) => {
    const onStart = (x: number) => onSetStartMs(p2m(x - OFFSET_LEFT_X));
    const onStop = (x: number) => onSetStopMs(p2m(x - OFFSET_LEFT_X));
    span.off('dragmove');
    span.on('dragmove', function ({ evt }: KonvaEventObject<DragEvent>) {
      const start = Math.max(
        leftHandle.x() + evt.movementX * 1.5,
        OFFSET_LEFT_X
      );
      const stop = Math.min(rightHandle.x() + evt.movementX * 1.5, widthPx);
      if (stop - start >= MIN_SLIDERS_WIDTH_PX) {
        onStart(start);
        onStop(stop);
      }
    });
  };

  // ANIMATION OBSERVABLES

  const onTick$ = new BehaviorSubject({
    startMs: minWindowMs,
    stopMs: maxWindowMs,
  });

  combineLatest(
    [
      onTick$,
      onResize$.pipe(
        map(({ widthPx }) => ({
          m2p: aToB(minWindowMs, maxWindowMs, OFFSET_LEFT_X, widthPx),
          p2m: aToB(OFFSET_LEFT_X, widthPx, minWindowMs, maxWindowMs),
          widthPx,
        })),
        tap(({ p2m, widthPx }) => {
          setLeftHandleDragBoundFunc(p2m);
          setRightHandleDragBoundFunc(widthPx, p2m);
          setFullHandleDragHandler(widthPx, p2m);
        })
      ),
    ],
    (a, b) => ({ ...a, ...b })
  ).subscribe(({ startMs, stopMs, m2p }) => {
    const leftX = m2p(startMs);
    const rightX = m2p(stopMs);
    leftHandle.x(leftX);
    rightHandle.x(rightX);
    span.setAbsolutePosition({ x: leftX, y: 38 });
    span.setSize({ width: rightX - leftX, height: 5 });
  });

  return (startMs, stopMs) => {
    onTick$.next({ startMs, stopMs });
    return layer;
  };
};

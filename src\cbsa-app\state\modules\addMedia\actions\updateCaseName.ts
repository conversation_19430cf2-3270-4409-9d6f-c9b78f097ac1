import { Case } from '@cbsa-modules/universal';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { UpdateCaseNameSDOQueryResponse } from '../services/queries/updateCaseName';
import { createAction } from '@reduxjs/toolkit';

export const UPDATE_CASE_NAME = createAction<{
  caseDetails: Case;
  name: string;
}>('CBSA/UPDATE_CASE_NAME');
export const UPDATE_CASE_NAME_SUCCESS =
  createGraphQLSuccessAction<UpdateCaseNameSDOQueryResponse>(
    'CBSA/UPDATE_CASE_NAME_SUCCESS'
  );
export const UPDATE_CASE_NAME_FAILURE = createGraphQLFailureAction<{
  error: any;
}>('CBSA/UPDATE_CASE_NAME_FAILURE');

export const updateCaseName = ({
  caseDetails,
  name,
}: {
  caseDetails: Case;
  name: string;
}) => UPDATE_CASE_NAME({ caseDetails, name });

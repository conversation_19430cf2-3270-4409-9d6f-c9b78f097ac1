import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import * as getSchema from '../../src/adapters/getSchema';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import {
  checkFolderExistsQuery,
  checkNameExistsQuery,
  createContentTemplateQuery,
  createFolderQuery,
  createIngestJobQuery,
  createRequestSDOQuery,
  createTdoQuery,
  getAppConfigsQuery,
} from '../../src/api/queries';
import { ingestMedia } from '../../src/controllers/ingestMedia';

const schemaId = 'schemaId';
const limit = 100;
const offset = 0;

describe('ingestMedia', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  const time = new Date();
  jest.spyOn(global, 'Date').mockReturnValue(time);

  const getSchemaId = jest.spyOn(getSchema, 'getSchemaIdAdapter');
  getSchemaId.mockImplementation(() => Promise.resolve(schemaId));

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
    callGQL.mockReset();
  });

  it('ingestMedia with empty urls', async () => {
    const req = getMockReq({
      body: {
        urls: [],
        runDetection: true,
        runTranscription: true,
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(0);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({
      error: Messages.urlRequired,
    });
  });

  it('ingest one or more media files with invalid urls', async () => {
    const req = getMockReq({
      body: {
        urls: [
          'invalid url test',
          '  ', //empty space
        ],
        runDetection: true,
        runTranscription: true,
        createNewCase: {
          name: 'SomeNewCase123',
          description: 'test case',
          parentFolderId: '07dfaa4d-fde9-477c-9b6e-19004289ef80',
        },
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(0);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({
      error: Messages.inValidUrls,
    });
  });

  it('ingest one or more media files to case w/ createNewCase', async () => {
    const req = getMockReq({
      body: {
        urls: [
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4',
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3',
        ],
        runDetection: true,
        runTranscription: true,
        createNewCase: {
          name: 'SomeNewCase123',
          description: 'test case',
          parentFolderId: '07dfaa4d-fde9-477c-9b6e-19004289ef80',
        },
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        folder: {
          id: '07dfaa4d-fde9-477c-9b6e-19004289ef80',
          contentTemplates: [],
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        folder: {
          childFolders: {
            records: ['folder1'],
          },
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createFolder: {
          id: '07dfaa4d-fde9-477c-9b6e-19004289ef81',
          name: req.body.createNewCase.name,
          treeObjectId: '07dfaa4d-fde9-477c-9b6e-19004289ef80',
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createStructuredData: { id: '123456789' } })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createFolderContentTemplate: { id: '123456789' } })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createTDO: { id: 'tdoId1' } })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        applicationConfig: {
          records: [{
            configKey: 'transcriptionEngineId',
            value: 'transcriptionEngineId',
            valueJSON: null
          }]
        }
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createJob: {
          id: 'jobId1',
          targetId: 'tdoId1',
          status: 'status',
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createTDO: { id: 'tdoId2' } })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        applicationConfig: {
          records: [{
            configKey: 'transcriptionEngineId',
            value: 'transcriptionEngineId',
            valueJSON: null,
          }]
        }
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createJob: {
          id: 'jobId2',
          targetId: 'tdoId2',
          status: 'status',
        },
      })
    );

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      checkFolderExistsQuery(req.body.createNewCase.parentFolderId)
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      checkNameExistsQuery(
        req.body.createNewCase.parentFolderId,
        req.body.createNewCase.name,
        limit,
        offset
      )
    );
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createFolderQuery, {
      description: req.body.createNewCase.description,
      name: req.body.createNewCase.name,
      parentFolderId: req.body.createNewCase.parentFolderId,
      userId: undefined,
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createRequestSDOQuery,
      {
        name: req.body.createNewCase.name,
        folderTreeObjectId: req.body.createNewCase.parentFolderId,
        currentTime: time.toISOString(),
        schemaId,
      }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createContentTemplateQuery,
      {
        schemaId,
        folderId: req.body.createNewCase.parentFolderId,
        sdoId: '123456789',
      }
    );
    const timestamp = time.toISOString();
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createTdoQuery, {
      input: {
        startDateTime: timestamp,
        stopDateTime: timestamp,
        addToIndex: true,
        parentFolderId: '07dfaa4d-fde9-477c-9b6e-19004289ef81',
        details: {
          govQARequestId: undefined,
          foiaXpressRequestId: undefined,
          casepointRequestId: undefined,
          nuixRequestId: undefined,
          exterroRequestId: undefined,
          tags: [{ value: 'in redaction', redactionStatus: 'Draft' }],
          veritoneProgram: { programLiveImage: '' },
        },
      },
      url: req.body.urls[0],
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      getAppConfigsQuery,
      { applicationId: "766e9916-9536-47e9-8dcb-dc225654bab3" }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createIngestJobQuery(
        { head: req.body.runDetection, person: false },
        req.body.runTranscription,
        "transcriptionEngineId",
        undefined,
        { detectCards: true, detectNotepads: true }
      ),
      {
        id: 'tdoId1',
        tdoName: 'tdoId1',
        payload: {
          targetId: 'tdoId1',
          url: req.body.urls[0],
          user: undefined,
          chunkSize: 10000000,
          zeropadOffsetAudio: true,
        },
      }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createTdoQuery, {
      input: {
        startDateTime: timestamp,
        stopDateTime: timestamp,
        addToIndex: true,
        parentFolderId: '07dfaa4d-fde9-477c-9b6e-19004289ef81',
        details: {
          govQARequestId: undefined,
          foiaXpressRequestId: undefined,
          casepointRequestId: undefined,
          nuixRequestId: undefined,
          exterroRequestId: undefined,
          tags: [{ value: 'in redaction', redactionStatus: 'Draft' }],
          veritoneProgram: { programLiveImage: '' },
        },
      },
      url: req.body.urls[0],
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      getAppConfigsQuery,
      { applicationId: "766e9916-9536-47e9-8dcb-dc225654bab3" }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createIngestJobQuery(
        { head: req.body.runDetection, person: false },
        req.body.runTranscription,
        "transcriptionEngineId",
        undefined,
        { detectCards: true, detectNotepads: true }
      ),
      {
        id: 'tdoId2',
        tdoName: 'tdoId2',
        payload: {
          targetId: 'tdoId2',
          url: req.body.urls[1],
          user: undefined,
          chunkSize: 10000000,
          zeropadOffsetAudio: true,
        },
      }
    );
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
    expect(res.json).toHaveBeenCalledWith({
      caseId: '07dfaa4d-fde9-477c-9b6e-19004289ef81',
      ingestedJobs: [
        { id: 'jobId1', status: 'status', targetId: 'tdoId1' },
        { id: 'jobId2', status: 'status', targetId: 'tdoId2' },
      ],
    });
  });

  it('ingest one or more media files to case w/ existingCaseId', async () => {
    const req = getMockReq({
      body: {
        urls: [
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4',
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3',
        ],
        runDetection: true,
        runTranscription: true,
        existingCaseId: 'f4e5d88c-b0ec-4d0f-a51d-42b529760c92',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        folder: {
          contentTemplates: [{ sdoId: '123456789' }],
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createTDO: { id: '123456789' } })
    );

        callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        applicationConfig: {
          records: []
        }
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createJob: {
          id: '123456789',
          targetId: 'targetId',
          status: 'status',
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createTDO: { id: '123456789' } })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        applicationConfig: {
          records: []
        }
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createJob: {
          id: '123456789',
          targetId: 'targetId',
          status: 'status',
        },
      })
    );

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      checkFolderExistsQuery(req.body.existingCaseId)
    );
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createTdoQuery, {
      input: {
        startDateTime: time.toISOString(),
        stopDateTime: time.toISOString(),
        addToIndex: true,
        parentFolderId: req.body.existingCaseId,
        details: {
          govQARequestId: undefined,
          foiaXpressRequestId: undefined,
          casepointRequestId: undefined,
          nuixRequestId: undefined,
          exterroRequestId: undefined,
          tags: [{ value: 'in redaction', redactionStatus: 'Draft' }],
          veritoneProgram: { programLiveImage: '' },
        },
      },
      url: req.body.urls[0],
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      getAppConfigsQuery,
      { applicationId: "766e9916-9536-47e9-8dcb-dc225654bab3" }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createIngestJobQuery(
        { head: req.body.runDetection, person: false },
        req.body.runTranscription,
        "e97d1564-39ff-4016-a034-e1f32aa9eb7d",
        undefined,
        { detectCards: true, detectNotepads: true }
      ),
      {
        id: '123456789',
        tdoName: '123456789',
        payload: {
          targetId: '123456789',
          url: req.body.urls[0],
          user: undefined,
          chunkSize: 10000000,
          zeropadOffsetAudio: true,
        },
      }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createTdoQuery, {
      input: {
        startDateTime: time.toISOString(),
        stopDateTime: time.toISOString(),
        addToIndex: true,
        parentFolderId: req.body.existingCaseId,
        details: {
          govQARequestId: undefined,
          foiaXpressRequestId: undefined,
          casepointRequestId: undefined,
          nuixRequestId: undefined,
          exterroRequestId: undefined,
          tags: [{ value: 'in redaction', redactionStatus: 'Draft' }],
          veritoneProgram: { programLiveImage: '' },
        },
      },
      url: req.body.urls[1],
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      getAppConfigsQuery,
      { applicationId: "766e9916-9536-47e9-8dcb-dc225654bab3" }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createIngestJobQuery(
        { head: req.body.runDetection, person: false },
        req.body.runTranscription,
        "e97d1564-39ff-4016-a034-e1f32aa9eb7d",
        undefined,
        { detectCards: true, detectNotepads: true }
      ),
      {
        id: '123456789',
        tdoName: '123456789',
        payload: {
          targetId: '123456789',
          url: req.body.urls[1],
          user: undefined,
          chunkSize: 10000000,
          zeropadOffsetAudio: true,
        },
      }
    );
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
    expect(res.json).toHaveBeenCalledWith({
      caseId: req.body.existingCaseId,
      ingestedJobs: [
        { id: '123456789', status: 'status', targetId: 'targetId' },
        { id: '123456789', status: 'status', targetId: 'targetId' },
      ],
    });
  });

  it('prefers runHeadDetection over runDetection', async () => {
    const req = getMockReq({
      body: {
        urls: [
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4',
          // 'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3'
        ],
        runDetection: false,
        runHeadDetection: true,
        runTranscription: true,
        existingCaseId: 'f4e5d88c-b0ec-4d0f-a51d-42b529760c92',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        folder: {
          contentTemplates: [{ sdoId: '123456789' }],
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createTDO: { id: '123456789' } })
    );
    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        applicationConfig: {
          records: []
        }
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createJob: {
          id: '123456789',
          targetId: 'targetId',
          status: 'status',
        },
      })
    );

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      checkFolderExistsQuery(req.body.existingCaseId)
    );
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createTdoQuery, {
      input: {
        startDateTime: time.toISOString(),
        stopDateTime: time.toISOString(),
        addToIndex: true,
        parentFolderId: req.body.existingCaseId,
        details: {
          govQARequestId: undefined,
          foiaXpressRequestId: undefined,
          casepointRequestId: undefined,
          nuixRequestId: undefined,
          exterroRequestId: undefined,
          tags: [{ value: 'in redaction', redactionStatus: 'Draft' }],
          veritoneProgram: { programLiveImage: '' },
        },
      },
      url: req.body.urls[0],
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      getAppConfigsQuery,
      { applicationId: "766e9916-9536-47e9-8dcb-dc225654bab3" }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createIngestJobQuery(
        { head: true, person: false },
        req.body.runTranscription,
        "e97d1564-39ff-4016-a034-e1f32aa9eb7d",
        undefined,
        { detectCards: true, detectNotepads: true }
      ),
      {
        id: '123456789',
        tdoName: '123456789',
        payload: {
          targetId: '123456789',
          url: req.body.urls[0],
          user: undefined,
          chunkSize: 10000000,
          zeropadOffsetAudio: true,
        },
      }
    );
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('runs person detection', async () => {
    const req = getMockReq({
      body: {
        urls: [
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4',
          // 'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3'
        ],
        runHeadDetection: false,
        runPersonDetection: true,
        runTranscription: true,
        existingCaseId: 'f4e5d88c-b0ec-4d0f-a51d-42b529760c92',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        folder: {
          contentTemplates: [{ sdoId: '123456789' }],
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createTDO: { id: '123456789' } })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        applicationConfig: {
          records: [{
            configKey: 'transcriptionEngineId',
            value: 'anotherTranscriptionEngineId',
            valueJSON: null
          }]
        }
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createJob: {
          id: '123456789',
          targetId: 'targetId',
          status: 'status',
        },
      })
    );

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      checkFolderExistsQuery(req.body.existingCaseId)
    );
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createTdoQuery, {
      input: {
        startDateTime: time.toISOString(),
        stopDateTime: time.toISOString(),
        addToIndex: true,
        parentFolderId: req.body.existingCaseId,
        details: {
          govQARequestId: undefined,
          foiaXpressRequestId: undefined,
          casepointRequestId: undefined,
          nuixRequestId: undefined,
          exterroRequestId: undefined,
          tags: [{ value: 'in redaction', redactionStatus: 'Draft' }],
          veritoneProgram: { programLiveImage: '' },
        },
      },
      url: req.body.urls[0],
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      getAppConfigsQuery,
      { applicationId: "766e9916-9536-47e9-8dcb-dc225654bab3" }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createIngestJobQuery(
        { head: false, person: true },
        req.body.runTranscription,
        "anotherTranscriptionEngineId",
        undefined,
        { detectCards: true, detectNotepads: true }
      ),
      {
        id: '123456789',
        tdoName: '123456789',
        payload: {
          targetId: '123456789',
          url: req.body.urls[0],
          user: undefined,
          chunkSize: 10000000,
          zeropadOffsetAudio: true,
        },
      }
    );
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('supports app config featureFlags override', async () => {
    const req = getMockReq({
      body: {
        urls: [
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4',
          // 'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3'
        ],
        runHeadDetection: false,
        runPersonDetection: true,
        runTranscription: true,
        existingCaseId: 'f4e5d88c-b0ec-4d0f-a51d-42b529760c92',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        folder: {
          contentTemplates: [{ sdoId: '123456789' }],
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createTDO: { id: '123456789' } })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        applicationConfig: {
          records: [{
            configType: 'String',
            configKey: 'transcriptionEngineId',
            value: 'anotherTranscriptionEngineId',
            valueJSON: null
          }, {
            configType: 'JSON',
            configKey: 'featureFlags',
            value: null,
            valueJSON: { detectCards: false }
          }]
        }
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createJob: {
          id: '123456789',
          targetId: 'targetId',
          status: 'status',
        },
      })
    );

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      checkFolderExistsQuery(req.body.existingCaseId)
    );
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createTdoQuery, {
      input: {
        startDateTime: time.toISOString(),
        stopDateTime: time.toISOString(),
        addToIndex: true,
        parentFolderId: req.body.existingCaseId,
        details: {
          govQARequestId: undefined,
          foiaXpressRequestId: undefined,
          casepointRequestId: undefined,
          nuixRequestId: undefined,
          exterroRequestId: undefined,
          tags: [{ value: 'in redaction', redactionStatus: 'Draft' }],
          veritoneProgram: { programLiveImage: '' },
        },
      },
      url: req.body.urls[0],
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      getAppConfigsQuery,
      { applicationId: "766e9916-9536-47e9-8dcb-dc225654bab3" }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createIngestJobQuery(
        { head: false, person: true },
        req.body.runTranscription,
        "anotherTranscriptionEngineId",
        undefined,
        { detectCards: false, detectNotepads: true }
      ),
      {
        id: '123456789',
        tdoName: '123456789',
        payload: {
          targetId: '123456789',
          url: req.body.urls[0],
          user: undefined,
          chunkSize: 10000000,
          zeropadOffsetAudio: true,
        },
      }
    );
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('supports app config featureFlags override with broken JSON app config', async () => {
    const req = getMockReq({
      body: {
        urls: [
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4',
          // 'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3'
        ],
        runHeadDetection: false,
        runPersonDetection: true,
        runTranscription: true,
        existingCaseId: 'f4e5d88c-b0ec-4d0f-a51d-42b529760c92',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        folder: {
          contentTemplates: [{ sdoId: '123456789' }],
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createTDO: { id: '123456789' } })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        applicationConfig: {
          records: [{
            configType: 'String',
            configKey: 'transcriptionEngineId',
            value: 'anotherTranscriptionEngineId',
            valueJSON: null
          }, {
            configType: 'JSON',
            configKey: 'featureFlags',
            value: '{ "detectCards": false }', // This should be an object in valueJSON but SDK implemented incorrectly
            valueJSON: null
          }]
        }
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createJob: {
          id: '123456789',
          targetId: 'targetId',
          status: 'status',
        },
      })
    );

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      checkFolderExistsQuery(req.body.existingCaseId)
    );
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createTdoQuery, {
      input: {
        startDateTime: time.toISOString(),
        stopDateTime: time.toISOString(),
        addToIndex: true,
        parentFolderId: req.body.existingCaseId,
        details: {
          govQARequestId: undefined,
          foiaXpressRequestId: undefined,
          casepointRequestId: undefined,
          nuixRequestId: undefined,
          exterroRequestId: undefined,
          tags: [{ value: 'in redaction', redactionStatus: 'Draft' }],
          veritoneProgram: { programLiveImage: '' },
        },
      },
      url: req.body.urls[0],
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      getAppConfigsQuery,
      { applicationId: "766e9916-9536-47e9-8dcb-dc225654bab3" }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createIngestJobQuery(
        { head: false, person: true },
        req.body.runTranscription,
        "anotherTranscriptionEngineId",
        undefined,
        { detectCards: false, detectNotepads: true }
      ),
      {
        id: '123456789',
        tdoName: '123456789',
        payload: {
          targetId: '123456789',
          url: req.body.urls[0],
          user: undefined,
          chunkSize: 10000000,
          zeropadOffsetAudio: true,
        },
      }
    );
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('can detect person and head', async () => {
    const req = getMockReq({
      body: {
        urls: [
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4',
          // 'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3'
        ],
        runHeadDetection: true,
        runPersonDetection: true,
        runTranscription: true,
        existingCaseId: 'f4e5d88c-b0ec-4d0f-a51d-42b529760c92',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        folder: {
          contentTemplates: [{ sdoId: '123456789' }],
        },
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({ createTDO: { id: '123456789' } })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        applicationConfig: {
          records: []
        }
      })
    );

    callGQL.mockImplementationOnce(() =>
      Promise.resolve({
        createJob: {
          id: '123456789',
          targetId: 'targetId',
          status: 'status',
        },
      })
    );

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      checkFolderExistsQuery(req.body.existingCaseId)
    );
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, createTdoQuery, {
      input: {
        startDateTime: time.toISOString(),
        stopDateTime: time.toISOString(),
        addToIndex: true,
        parentFolderId: req.body.existingCaseId,
        details: {
          govQARequestId: undefined,
          foiaXpressRequestId: undefined,
          casepointRequestId: undefined,
          nuixRequestId: undefined,
          exterroRequestId: undefined,
          tags: [{ value: 'in redaction', redactionStatus: 'Draft' }],
          veritoneProgram: { programLiveImage: '' },
        },
      },
      url: req.body.urls[0],
    });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      getAppConfigsQuery,
      { applicationId: "766e9916-9536-47e9-8dcb-dc225654bab3" }
    );
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createIngestJobQuery(
        { head: true, person: true },
        req.body.runTranscription,
        "e97d1564-39ff-4016-a034-e1f32aa9eb7d",
        undefined,
        { detectCards: true, detectNotepads: true }
      ),
      {
        id: '123456789',
        tdoName: '123456789',
        payload: {
          targetId: '123456789',
          url: req.body.urls[0],
          user: undefined,
          chunkSize: 10000000,
          zeropadOffsetAudio: true,
        },
      }
    );
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('fails to ingest one or more media files to case w/o name', async () => {
    const req = getMockReq({
      body: {
        urls: [
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/sample.mp4',
          'https://veritone-ds-datasets.s3-us-west-2.amazonaws.com/redact/bloomberg.mp3',
        ],
        runDetection: true,
        runTranscription: true,
        // existingCaseId: 'f4e5d88c-b0ec-4d0f-a51d-42b529760c92',
        createNewCase: {
          name: '',
          description: 'test case',
          parentFolderId: '07dfaa4d-fde9-477c-9b6e-19004289ef80',
        },
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await ingestMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({
      error: Messages.requestIdOrRequestDetailRequired,
    });
  });
});

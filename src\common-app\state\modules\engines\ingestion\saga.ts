import {
  all,
  call,
  cancel,
  fork,
  put,
  race,
  select,
  takeEvery,
  take,
  delay,
} from 'typed-redux-saga/macro';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { selectEnableFolderView } from '@common/user-onboarding/selectors/organization';
import { actionLogAuditEvent } from '@common-modules/mediaDetails/actions';
import { OUT_CREATE_JOB_FAIL, OUT_CREATE_JOB_SUCCESS } from '@worker';

import { STOP_ALL_ENGINE_POLLING } from '../actions';
import { actionSetCaseMediaStatus } from '@redact/state/modules/casemedia/actions';
import * as Actions from './actions';
import { selectEngineStatus, selectIngestionOptions } from './selectors';
import {
  cancelJobService,
  checkJobStatusService,
  createEngineJobService,
  queryEngineResultsService,
} from './services';
import { sagaIntl } from '@common/i18n';
import { TDOId } from '../../universal/models/Brands';
import { ActionCreatorWithPayload } from '@reduxjs/toolkit';
import { ProcessEngineRequest } from './models/services';
import { JobStatus } from '@veritone/glc-redux';

export function* saga() {
  yield* all([
    fork(onProcessEngineRequestAction),
    fork(onProcessEngineRequestSuccessAction),
    fork(onProcessEngineRequestFailureAction),
    fork(onCreateEngineJobSuccess),
    fork(onCreateEngineJobAction),
    fork(onCheckJobStatusSuccessAction),
    fork(onCheckJobStatusFailureAction),
    fork(onStartPollingEngineResultsAction),
    fork(onCheckJobStatusAction),
    fork(onQueryEngineResultsRecordAction),
    fork(onCancelJobAction),
    fork(onCancelJobSuccessAction),
    fork(onCancelJobFailureAction),
  ]);
}

/**
 * Action watchers.
 */

function* onProcessEngineRequestAction() {
  yield* takeEvery(Actions.PROCESS_ENGINE_REQUEST, function* (action) {
    yield* startEngineJob(action.payload);
  });
}

function* onProcessEngineRequestSuccessAction() {
  yield* takeEvery(Actions.PROCESS_ENGINE_REQUEST_SUCCESS, function* (action) {
    const isEnableFolderView = yield* select(selectEnableFolderView);
    yield* put(
      enqueueSnackbar({
        message: 'Media upload and process has completed.',
      })
    );
    if (isEnableFolderView) {
      yield* put(
        actionSetCaseMediaStatus({
          ...action.payload,
          status: JobStatus.Complete,
        })
      );
    }
  });
}

function* onProcessEngineRequestFailureAction() {
  yield* takeEvery(Actions.PROCESS_ENGINE_REQUEST_FAILURE, function* (action) {
    const isEnableFolderView = yield* select(selectEnableFolderView);
    yield* put(
      enqueueSnackbar({
        message: action.payload.errorMsg,
      })
    );
    if (isEnableFolderView) {
      yield* put(
        actionSetCaseMediaStatus({
          ...action.payload,
          status: JobStatus.Failed,
        })
      );
    }
  });
}

function* onCheckJobStatusSuccessAction() {
  yield* takeEvery(Actions.CHECK_JOB_STATUS_SUCCESS, function* (action) {
    const { job } = action.payload;
    const isEnableFolderView = yield* select(selectEnableFolderView);
    if (isEnableFolderView && job.status !== JobStatus.Complete) {
      yield* put(
        actionSetCaseMediaStatus({
          jobId: job.id,
          tdoId: job.targetId,
          status: job.status,
        })
      );
    }
  });
}

export function* onCheckJobStatusFailureAction() {
  yield* takeEvery(Actions.CREATE_ENGINE_JOB_FAILURE, function* (action) {
    const intl = sagaIntl();
    // Loop through all errors returned by the GraphQL API and display them one by one.
    // However, if the error comes from a fetch API call, it may not be in the expected format,
    // so we need to handle both cases appropriately.
    if (Array.isArray(action.payload)) {
      for (const err of action.payload) {
        yield* put(
          enqueueSnackbar({
            message: intl.formatMessage(
              {
                id: 'engineJobFailureMsg',
                defaultMessage:
                  'Engine failed to run with the following engine id: {engineId}',
              },
              { engineId: err?.data?.engineId ?? '' }
            ),
            variant: 'error',
          })
        );
      }
    } else if (action.payload instanceof Error) {
      yield* put(
        enqueueSnackbar({
          message: action.payload.message,
          variant: 'error',
        })
      );
    } else {
      console.error(action.payload);
    }
  });
}

function* onCreateEngineJobAction() {
  yield* takeEvery(Actions.CREATE_ENGINE_JOB, function* (action) {
    yield* put(createEngineJobService(action.payload));
  });
}

function* onCreateEngineJobSuccess() {
  yield* takeEvery(Actions.CREATE_ENGINE_JOB_SUCCESS, function* (action) {
    const { createJob } = action.payload;
    if (createJob) {
      yield* put(
        Actions.startPollingEngineResultsAction({
          tdoId: createJob.targetId,
          jobId: createJob.id,
        })
      );
      yield* put(OUT_CREATE_JOB_SUCCESS(action));
    }
  });
}

function* onStartPollingEngineResultsAction() {
  yield* takeEvery(Actions.START_POLLING_ENGINE_RESULTS, function* (action) {
    yield* call(startPollingJobStatus, action.payload);
  });
}

function* onCheckJobStatusAction() {
  yield* takeEvery(Actions.CHECK_JOB_STATUS, function* (action) {
    yield* put(checkJobStatusService(action.payload));
  });
}

function* onQueryEngineResultsRecordAction() {
  yield* takeEvery(Actions.QUERY_ENGINE_RESULTS, function* (action) {
    yield* put(queryEngineResultsService(action.payload));
  });
}

// function* onGetEngineResultsFromURIAction() {
//   yield takeEvery(Actions.GET_ENGINE_RESULTS_FROM_URI, function*(action) {
//     yield put(getEngineResultsFromURIService(action.payload));
//   });
// }

function* onCancelJobAction() {
  yield* takeEvery(Actions.CANCEL_JOB, function* (action) {
    yield* put(cancelJobService(action.payload));
  });
}

function* onCancelJobSuccessAction() {
  yield* takeEvery(Actions.CANCEL_JOB_SUCCESS, function* () {
    yield* put(
      enqueueSnackbar({
        message: 'Upload and process has been canceled.',
      })
    );
  });
}

function* onCancelJobFailureAction() {
  yield* takeEvery(Actions.CANCEL_JOB_FAILURE, function* () {
    yield* put(
      enqueueSnackbar({
        message: "I'm sorry. The cancel request failed.",
      })
    );
  });
}

/**
 * Start engine job process.
 */

export const startEngineJob = function* ({
  tdoId,
  tdoName,
  payload,
  retryCount = 3,
}: {
  tdoId: TDOId;
  tdoName: string;
  payload: ProcessEngineRequest['payload'];
  retryCount?: number;
}): any {
  yield* put(Actions.createEngineJobAction({ tdoId, tdoName, payload }));

  const { runHead, runPerson, runTranscription } = yield* select(
    selectIngestionOptions
  );
  if (runHead) {
    yield* put(actionLogAuditEvent('Head Detection Run'));
  }
  if (runPerson) {
    yield* put(actionLogAuditEvent('Person Detection Run'));
  }
  if (runTranscription) {
    yield* put(actionLogAuditEvent('Transcribe Audio'));
  }
  const { err } = yield* race({
    ok: take(Actions.CREATE_ENGINE_JOB_SUCCESS),
    err: take(Actions.CREATE_ENGINE_JOB_FAILURE),
  });

  if (err && retryCount <= 0) {
    yield* put(
      Actions.processEngineRequestFailureAction({
        tdoId,
        errorMsg:
          'Media upload and processing failed to start. Maximum retry limit exceeded',
      })
    );
    // create notification
    yield* put(OUT_CREATE_JOB_FAIL(err));

    // update UI become Upload fail
    yield* put(Actions.actionMarkTdoUploadingEnd({ tdoId }));
    yield* cancel();
  }

  if (err && retryCount > 0) {
    yield* delay(10000);
    console.log('The ingestion process failed to start...retrying');
    yield* startEngineJob({
      tdoId,
      tdoName,
      payload,
      retryCount: retryCount - 1,
    });
  }

  // if (ok) {
  //   yield put(
  //     Actions.startPollingEngineResultsAction({
  //       tdoId,
  //       jobId: ok.payload.createJob.id,
  //     })
  //   );
  //   yield put(actionOutCreateJobSuccess(ok));
  // }
};

export function* startPollingJobStatus({
  tdoId,
  jobId,
}: {
  tdoId: TDOId;
  jobId: string;
}) {
  while (true) {
    const { isRunning, status, error } = yield* select(
      selectEngineStatus(tdoId, jobId)
    );

    if (status === 'complete') {
      break;
    }
    if (!isRunning || error) {
      yield* put(Actions.stopPollingEngineResultsAction({ tdoId, jobId }));
      yield* cancel();
      return;
    }
    yield* put(Actions.checkJobStatusAction({ tdoId, jobId }));
    const { stop } = yield* race({
      ok: delay(15_000),
      stop: take([
        isActionOfTDO(Actions.STOP_POLLING_ENGINE_RESULTS, jobId),
        STOP_ALL_ENGINE_POLLING,
      ]),
    });
    if (stop) {
      yield* put(Actions.stopPollingEngineResultsAction({ tdoId, jobId }));
      yield* cancel();
      return;
    }
  }

  yield* put(Actions.processEngineRequestSuccessAction({ tdoId, jobId }));
}

export const isActionOfTDO =
  <P>(
    action: ActionCreatorWithPayload<P extends { jobId: string } ? P : never>,
    jobId: string
  ) =>
  ({ type, payload }: { type: string; payload?: { jobId?: string } }) =>
    type === action.type && payload?.jobId === jobId;

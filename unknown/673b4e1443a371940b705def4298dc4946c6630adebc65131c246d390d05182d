import { useEffect } from 'react';
import { add } from 'lodash';
import AppBar from '@cbsa-components/AppBar';
import { Container } from '@mui/material';
import useExitPrompt from '@common/shared/useExitPrompt';
import AppContainer from '@common-components/AppContainer';
import UploadingSnack from '@cbsa-components/UploadingSnack';
import { Props, connector } from './props';
import * as styles from './index.scss';

const AppWrapper = ({
  title,
  children,
  uploadingFiles,
  removeSuccessfulFile,
  retryFailedFile,
}: Props) => {
  const [, setShowExitPrompt] = useExitPrompt(false);
  const isUploading =
    add(uploadingFiles.queue.length, uploadingFiles.processing.length) > 0;

  useEffect(
    () => () => {
      setShowExitPrompt(false);
    },
    [setShowExitPrompt]
  );

  useEffect(() => {
    setShowExitPrompt(isUploading);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uploadingFiles]);

  return (
    <div
      data-testid="app-wrapper"
      className={[styles.wrapper, 'AppWrapper-wrapper'].join(' ')}
    >
      <AppBar title={title} />
      <Container className={styles.wrapperContainer} maxWidth={false}>
        <AppContainer>{children}</AppContainer>
        <UploadingSnack
          uploadingFiles={uploadingFiles}
          removeSuccessfulFile={removeSuccessfulFile}
          retryFailedFile={retryFailedFile}
        />
      </Container>
    </div>
  );
};

export default connector(AppWrapper);

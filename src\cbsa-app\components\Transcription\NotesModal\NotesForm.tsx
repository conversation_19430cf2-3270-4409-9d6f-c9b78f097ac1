import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import { ChangeEvent } from 'react';
import { I18nTranslate } from '@i18n';

import { AudioRedactionDetails } from '@common-modules/mediaDetails/models';

const NotesForm = ({ notes, onSetNotes }: NotesFormPropTypes) => {
  const onChangeLabel = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    onSetNotes({
      ...notes,
      labels: e.target.value.split(/[\s,]/).filter((v) => !!v),
    });
  };
  const onChangeNote = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    onSetNotes({ ...notes, notes: e.target.value });
  };

  return (
    <Grid
      data-testid="notes-form-grid"
      container
      direction="column"
      alignItems="stretch"
      spacing={3}
    >
      <Grid>
        <TextField
          fullWidth
          label={I18nTranslate.TranslateMessage('notesFormLabels')}
          helperText={I18nTranslate.TranslateMessage('notesFormHelpTextLabels')}
          type="string"
          defaultValue={notes.labels.join(' ')}
          onChange={onChangeLabel}
          data-testid="notes-form-textfield"
        />
      </Grid>
      <Grid>
        <TextField
          fullWidth
          multiline
          maxRows={5}
          label={I18nTranslate.TranslateMessage('notesFormNotes')}
          type="string"
          defaultValue={notes.notes}
          onChange={onChangeNote}
          data-testid="notes-form-textfield"
        />
      </Grid>
    </Grid>
  );
};

export default NotesForm;

export interface NotesFormPropTypes {
  readonly notes: Pick<AudioRedactionDetails, 'labels' | 'notes'>;
  readonly onSetNotes: (
    data: Pick<AudioRedactionDetails, 'labels' | 'notes'>
  ) => void;
}

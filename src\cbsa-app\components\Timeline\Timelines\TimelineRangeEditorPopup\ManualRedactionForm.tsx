import {
  Button,
  Grid2 as Grid,
  ThemeProvider,
  StyledEngineProvider,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { clamp } from 'lodash';
import { useMemo, useState, useEffect, useCallback } from 'react';

import { buttonTheme } from '@redact/materialUITheme';

import { AudioRedactionSlice } from '@common-modules/mediaDetails/models';
import * as styles from './styles.scss';
import TimeInput from './TimeInput';
import { I18nTranslate } from '@i18n';

const ManualRedactionForm = ({
  minMs,
  maxMs,
  onCancel,
  onSetRangeTime,
  onChange,
  onUnredactSlice,
  timeRange,
  hideGroupButton,
}: ManualRedactionFormPropTypes) => {
  // timeRage local state.
  const [[start, end, notes], setLocalTimeRange] = useState(timeRange);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const initTimeRange = useMemo(() => timeRange, []);

  // Inputs onChange handler. Clamp to proper
  const onTimeRangeChange = useCallback(
    ([s, e]: [number, number]) => {
      const range: AudioRedactionSlice = [
        clamp(s, minMs, maxMs),
        clamp(e, minMs, maxMs),
        notes,
      ];
      setLocalTimeRange(range);
      onChange([
        Math.min(range[0], range[1]),
        Math.max(range[0], range[1]),
        notes,
      ]);
    },
    [maxMs, minMs, notes, onChange]
  );

  useEffect(() => {
    onTimeRangeChange([timeRange[0], timeRange[1]]);
  }, [timeRange, onTimeRangeChange]);

  const onSubmit = () => {
    if (
      start === end ||
      (start < minMs && end < minMs) ||
      (start > maxMs && end > maxMs)
    ) {
      onSetRangeTime(initTimeRange);
      return;
    }
    const range: AudioRedactionSlice = [
      Math.min(start, end),
      Math.max(start, end),
      notes,
    ];
    onSetRangeTime(range);
  };

  const onChangTimeInputStart = (startTimeMs: number) => {
    onTimeRangeChange([startTimeMs, end]);
  };

  const onChangTimeInputEnd = (endTimeMs: number) => {
    onTimeRangeChange([start, endTimeMs]);
  };

  const onUnredact = () => {
    const range: AudioRedactionSlice = [start, end, notes];
    onUnredactSlice(range);
  };

  return (
    <Grid
      container
      direction="column"
      className={styles.formContent}
      data-testid="manual-redaction-form-grid-container"
    >
      <Grid size="grow">
        <Grid
          container
          spacing={3}
          alignItems="center"
          style={{ height: '100%' }}
        >
          <Grid size={{ xs: 6 }}>
            <TimeInput
              label="Start"
              value={start}
              onChange={onChangTimeInputStart}
              textHelper="(hh:mm:ss.SSS)"
            />
          </Grid>
          <Grid size={{ xs: 6 }}>
            <TimeInput
              label="End"
              value={end}
              onChange={onChangTimeInputEnd}
              textHelper="(hh:mm:ss.SSS)"
            />
          </Grid>
        </Grid>
      </Grid>
      {!hideGroupButton && (
        <Grid container spacing={4} justifyContent="space-between">
          <Grid>
            <StyledEngineProvider injectFirst>
              <ThemeProvider theme={buttonTheme}>
                <Button
                  className={styles.unredactButton}
                  onClick={onUnredact}
                  data-testid="timeline-range-editor-button-unredact"
                >
                  <DeleteIcon />
                </Button>
              </ThemeProvider>
            </StyledEngineProvider>
          </Grid>
          <Grid>
            <Grid
              container
              spacing={2}
              alignItems="center"
              justifyContent="flex-end"
            >
              <Grid>
                <StyledEngineProvider injectFirst>
                  <ThemeProvider theme={buttonTheme}>
                    <Button
                      color="primary"
                      onClick={onCancel}
                      data-testid="timeline-range-editor-button-cancel"
                    >
                      {I18nTranslate.TranslateMessage('cancel')}
                    </Button>
                  </ThemeProvider>
                </StyledEngineProvider>
              </Grid>
              <Grid>
                <StyledEngineProvider injectFirst>
                  <ThemeProvider theme={buttonTheme}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={onSubmit}
                      data-testid="timeline-range-editor-button-submit"
                    >
                      {I18nTranslate.TranslateMessage('set')}
                    </Button>
                  </ThemeProvider>
                </StyledEngineProvider>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      )}
    </Grid>
  );
};

export interface ManualRedactionFormPropTypes {
  readonly minMs: number;
  readonly maxMs: number;
  readonly timeRange: AudioRedactionSlice;
  readonly hideGroupButton: boolean;
  readonly onCancel: () => void;
  readonly onChange: (tr: AudioRedactionSlice) => void;
  readonly onSetRangeTime: (tr: AudioRedactionSlice) => void;
  readonly onUnredactSlice: (tr: AudioRedactionSlice) => void;
}

export default ManualRedactionForm;

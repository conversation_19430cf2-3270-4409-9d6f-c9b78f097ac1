import express from 'express';
import { Logger } from '../logger';
import { isEmpty, pick } from 'lodash';
import { Messages } from '../errors/messages';
import { StatusCodes } from '../errors/statusCodes';
import { getSdoIdAdapter } from '../adapters/getSdoId';
import { deleteSdoAdapter } from '../adapters/deleteSdo';
import { deleteFolderAdapter } from '../adapters/deleteFolder';
import { getFilesByTreeObjectIdAdapter } from '../adapters/getFilesByTreeObjectIdAdapter';
import { deleteAllFilesAdapter } from '../adapters/deleteAllFiles';
import { checkCaseLockAdapter } from '../adapters/checkCaseLock';
import { isCaseId } from '../validations/helpers';

export const deleteCase = async (
    req: express.Request,
    res: express.Response
  ) => {
    const caseId = req.params?.caseId;
    if (!isCaseId(caseId)) {
       return res.status(StatusCodes.BadRequest).json({ error: Messages.caseIdRequired });
    }
    const headers = pick(req.headers, ['authorization']);
    const folder = await getSdoIdAdapter(headers,  caseId);
    const isCase = folder && folder.contentTemplates?.length > 0;
    if (folder && isCase) {
      const sdoId = folder.contentTemplates[0]!.sdoId; // Safe due to previous length check
      if (isEmpty(sdoId)) {
        return res.status(StatusCodes.BadRequest).json({ error: Messages.getSdoIdFail });
      }
      const caseLockResponse = await checkCaseLockAdapter(headers, caseId);
      if (caseLockResponse.isFailed) {
        return res.status(StatusCodes.BadRequest).json({ error: Messages.checkCaseLockFail});
      }
      else if (caseLockResponse.isLocked ) {
        return res.status(StatusCodes.BadRequest).json({ error: Messages.caseIsLocked });
      }
      else if (caseLockResponse.isJobRunning ) {
        return res.status(StatusCodes.BadRequest).json({ error: Messages.CaseFilesInProcessingState });
      }
      const getFilesResponse = await getFilesByTreeObjectIdAdapter(headers, folder.treeObjectId);
      if(getFilesResponse.isFailed) {
        return res.status(StatusCodes.Success).json({ caseId: caseId, message: Messages.getFilesByTreeObjectIdFail });
      }
      if (getFilesResponse.tdoIds?.length > 0) {
        const deleteFilesResponse = await deleteAllFilesAdapter(headers, getFilesResponse.tdoIds);
          if (deleteFilesResponse.isFailed) {
            return res.status(StatusCodes.Success).json({ caseId: caseId, message: Messages.deleteAllFilesFail });
          }
      }

      const deletedSdoId = await deleteSdoAdapter(headers, sdoId);
        if (isEmpty(deletedSdoId)) {
          return res.status(StatusCodes.BadRequest).json({ error: Messages.DeleteSdoFail });
        }

        const deletedId = await deleteFolderAdapter(headers, folder.treeObjectId)
        if (deletedId === folder.treeObjectId) {
          Logger.log('deleted caseId: ' + caseId);
          return res.status(StatusCodes.Success).json({
            caseId: caseId,
            message: Messages.deleteCaseSuccess
          });
        }
        else {
            if (deletedId === 'not-found') {
              return res.status(StatusCodes.BadRequest).json({ error: Messages.InvalidFolderId});
            } else {
              return res.status(StatusCodes.BadRequest).json({ error: Messages.DeleteCaseFail });
            }
        }
    } else {
      return res.status(StatusCodes.BadRequest).json({ error: Messages.InvalidCaseId });
    }
}
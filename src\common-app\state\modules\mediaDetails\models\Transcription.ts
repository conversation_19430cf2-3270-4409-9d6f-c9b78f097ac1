import { TDOId } from '@common-modules/universal/models/Brands';
import { IndividualRedactionCode } from '@redact-modules/redactionCodes/models';

export interface TranscriptionViewable {
  readonly id: string; // create
  readonly startOffsetMs: number;
  readonly stopOffsetMs: number;
  readonly transcription: ReadonlyArray<TranscriptionViewableWords>;
}

export interface TranscriptionViewableWords {
  readonly id: string; // create
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
  readonly words: string;
  readonly confidence: number; // 0 - 1
  readonly redactionNotes?: AudioRedactionSlice[2];
}

/**
 * A redacted Words may only have partial redaction.
 * These properties define redaction start and stop MS.
 */
export interface RedactedWords extends TranscriptionViewableWords {
  readonly startRedactionMs: number;
  readonly stopRedactionMs: number;
  readonly redactionNotes: AudioRedactionSlice[2];
}

export interface TranscriptionViewState {
  // One or more selected word groups.
  readonly selected: TranscriptionViewable['transcription'];
  // State of each word group.
  redactions: Array<AudioRedactionSlice>;
  editNotes?: AudioRedactionSlice; // what is this?
  currentEditWords?: string;
}

/**
 * [startMs, endMs, AudioRedactionDetails | undefined]
 */
export type AudioRedactionSlice = [
  number,
  number,
  AudioRedactionDetails | null | undefined,
];

export enum OptionType {
  MASK_VOICE,
  REDACT,
}
export interface AudioRedactionDetails {
  readonly user: string;
  readonly modifiedDateTime: string;
  readonly labels: Array<string>;
  readonly notes: string;
  readonly redactionCode?: IndividualRedactionCode;
  readonly hasAuditEvent?: boolean; // 3/22/2023 transitioning how audio redaction are added to audit log
  readonly voiceMask?: boolean; // ideally this would be at the slice level - but require lots of code refactor
  readonly startBufferMs?: number; // ideally this would be at the slice level - but would require lots of code refactor
  readonly stopBufferMs?: number; // ideally this would be at the slice level - but would require lots of code refactor
}

export interface Transcription {
  readonly tdoId: TDOId;
  readonly engineId: string;
  readonly startOffsetMs: number;
  readonly stopOffsetMs: number;
  readonly jsondata: {
    readonly sourceEngineId: string;
    readonly modifiedDateTime: number;
    readonly series?: ReadonlyArray<TranscriptionSeries>; // Undefined due to https://veritone.atlassian.net/browse/VTN-50009
  };
}

export interface TranscriptionSeries {
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
  readonly words: ReadonlyArray<{
    readonly word: string;
    readonly confidence: number;
    readonly bestPath: boolean;
  }>;
}

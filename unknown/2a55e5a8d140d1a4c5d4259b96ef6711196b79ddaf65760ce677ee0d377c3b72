import { fork, put, select, take } from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';

import {
  actionStartPollingUserOrg,
  REQUEST_USER_ORGANIZATION_FAILURE,
  REQUEST_USER_ORGANIZATION_SUCCESS,
} from '../actions';
import { actionInitSuccess } from '../actions/init';
import { selectHasEnterprise } from '../selectors';
import { serviceFetchUserOrg } from '../services';

const {
  config: { getConfig },
  user: { userIsAuthenticated, FETCH_USER_SUCCESS },
} = modules;

export function* initSagas() {
  yield* take(FETCH_USER_SUCCESS);
  const isAuthed = yield* select(userIsAuthenticated);
  if (isAuthed) {
    yield* put(serviceFetchUserOrg());
    yield* take([
      REQUEST_USER_ORGANIZATION_SUCCESS,
      REQUEST_USER_ORGANIZATION_FAILURE,
    ]);
    const isEnterprise: boolean = yield* select(selectHasEnterprise);
    if (!isEnterprise) {
      yield* fork(initOnboarding);
      return true;
    }
  }
  return false;
}

function* initOnboarding() {
  const { fastSpringStorefrontUrl } = yield* select(
    getConfig<Window['config']>
  );
  if (fastSpringStorefrontUrl) {
    injectFastSpring(fastSpringStorefrontUrl);
    yield* put(actionStartPollingUserOrg({}));
    yield* put(actionInitSuccess());
  }
}

function injectFastSpring(storeFrontUrl: string) {
  const scriptEl = document.createElement('script');
  scriptEl.id = 'fsc-api';
  scriptEl.type = 'text/javascript';
  scriptEl.src =
    'https://d1f8f9xcsvx3ha.cloudfront.net/sbl/0.7.9/fastspring-builder.min.js';
  scriptEl.setAttribute('data-storefront', storeFrontUrl);
  document.head.appendChild(scriptEl);
}

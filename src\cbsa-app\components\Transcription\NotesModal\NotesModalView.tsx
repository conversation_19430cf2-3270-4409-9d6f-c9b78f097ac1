import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useEffect, useState } from 'react';

import { AudioRedactionDetails } from '@common-modules/mediaDetails/models';
import NotesForm from './NotesForm';
import { NotesModalPropTypes } from './NotesModalPropTypes';
import { I18nTranslate } from '@i18n';

import * as styles from './styles.scss';

const getDefaultNote = (): AudioRedactionDetails => ({
  user: '',
  modifiedDateTime: new Date().toISOString(),
  labels: [],
  notes: '',
});

const NotesModalView = ({
  isOpen,
  notes: [s, e, n] = [0, 0, null],
  onUpdateNotes,
  onCloseNotes,
}: NotesModalPropTypes) => {
  const [notes, setNotes] = useState<AudioRedactionDetails>(
    n || getDefaultNote()
  );
  useEffect(() => {
    if (isOpen) {
      setNotes(n || getDefaultNote());
    }
  }, [isOpen, n]);

  const onUpdate = () => {
    onUpdateNotes([s, e, notes]);
  };

  const onSetNotes = (data: Pick<AudioRedactionDetails, 'labels' | 'notes'>) =>
    setNotes({
      ...notes,
      ...data,
    });

  return (
    <Dialog
      data-testid="notes-modal-view"
      open={isOpen}
      onClose={onCloseNotes}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        {I18nTranslate.TranslateMessage('editNote')}
        <br />
        <small className={styles.dialogTitleSmall}>
          {msToTime(s)} - {msToTime(e)}
        </small>
        <hr className={styles.dialogTitleHr} />
      </DialogTitle>
      <DialogContent>
        <NotesForm
          {...{
            notes,
            onSetNotes,
          }}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onCloseNotes} color="secondary">
          {I18nTranslate.TranslateMessage('cancel')}
        </Button>
        <Button onClick={onUpdate}>
          {I18nTranslate.TranslateMessage('save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NotesModalView;

function msToTime(duration: number) {
  const milliseconds = duration % 1000 | 0;
  const seconds = (duration / 1000) % 60 | 0;
  const minutes = (duration / (1000 * 60)) % 60 | 0;
  const hours = (duration / (1000 * 60 * 60)) % 24 | 0;

  const hrs = hours < 10 ? '0' + hours : hours;
  const min = minutes < 10 ? '0' + minutes : minutes;
  const sec = seconds < 10 ? '0' + seconds : seconds;

  return hrs + ':' + min + ':' + sec + '.' + milliseconds;
}

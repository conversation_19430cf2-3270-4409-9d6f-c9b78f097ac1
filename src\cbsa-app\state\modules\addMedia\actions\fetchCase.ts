import {
  createGraph<PERSON>FailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import {
  FETCH_CASE_DETAILS_RESPONSE,
  FETCH_CASE_NOTIFICATIONS_QUERY_RESPONSE,
  <PERSON>ETCH_TDOS_QUERY_RESPONSE,
} from '../services/queries/fetchCase';

export const FETCH_TDOS_SUCCESS =
  createGraphQLSuccessAction<FETCH_TDOS_QUERY_RESPONSE>(
    'CBSA/FETCH_TDOS_SUCCESS'
  );
export const FETCH_TDOS_FAILURE = createGraphQLFailureAction(
  'CBSA/FETCH_TDOS_FAILURE'
);

export const FETCH_CASE_DETAILS_SUCCESS =
  createGraphQLSuccessAction<FETCH_CASE_DETAILS_RESPONSE>(
    'CBSA/FETCH_CASE_DETAILS_SUCCESS'
  );
export const FETCH_CASE_DETAILS_FAILURE = createGraphQLFailureAction(
  'CBSA/FETCH_CASE_DETAILS_FAILURE'
);

export const FETCH_CASE_NOTIFICATIONS_SUCCESS =
  createGraphQLSuccessAction<FETCH_CASE_NOTIFICATIONS_QUERY_RESPONSE>(
    'CBSA/FETCH_CASE_NOTIFICATIONS_SUCCESS'
  );
export const FETCH_CASE_NOTIFICATIONS_FAILURE = createGraphQLFailureAction(
  'CBSA/FETCH_CASE_NOTIFICATIONS_FAILURE'
);

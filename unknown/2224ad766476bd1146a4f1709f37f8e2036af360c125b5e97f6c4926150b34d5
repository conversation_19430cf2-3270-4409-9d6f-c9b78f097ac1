import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import { selectHistory } from '../selectors';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { isWorkerAction, markWorkerAction } from '@utils';
import {
  all,
  fork,
  put,
  select,
  takeEvery,
  takeLatest,
} from 'typed-redux-saga/macro';
import {
  CHANGE_CODE,
  CHANGE_SHAPE,
  IN_LOAD_TRACKING_ENGINE_RESULTS,
  SET_DETECTION_TYPE_REDACTION,
  SET_INDIVIDUAL_REDACTION,
} from '@worker';

export function* undoSaga() {
  yield* all([
    fork(watchUndoAction),
    fork(watchRedoAction),
    fork(watchPatchEmptyUndoAction),
  ]);
}

export function* watchUndoAction() {
  yield* takeEvery(Actions.UNDO_ACTION, function* (action) {
    if (isWorkerAction(action)) {
      return;
    }

    const { past } = yield* select(selectHistory);
    const undoAction = [...past].pop();
    if (undoAction !== undefined) {
      yield* put(Actions.PATCH_UNDO_ACTION());
      if (undoAction !== null && 'inverseAction' in undoAction) {
        const {
          inverseAction: { creator, payload },
        } = undoAction;
        yield* put(markWorkerAction(creator(payload as any)));
        yield* put(
          Actions.actionLogAuditEvent(
            `Undo Action: { creator: ${creator.name}, payload: ${JSON.stringify(payload)} }`
          )
        );
      } else if (undoAction === null || 'inversePatches' in undoAction) {
        yield* put(Actions.workerUndoAction());
      }

      if (undoAction !== null) {
        yield* put(
          Actions.actionLogAuditEvent(`Undo: ${JSON.stringify(undoAction)}`)
        );
      }
    } else {
      const intl = sagaIntl();
      yield* put(
        enqueueSnackbar({
          variant: 'error',
          message: intl.formatMessage({
            id: 'cannotUndo',
            defaultMessage: 'Cannot undo last action',
          }),
        })
      );
    }
  });
}

export function* watchRedoAction() {
  yield* takeEvery(Actions.REDO_ACTION, function* (action) {
    if (isWorkerAction(action)) {
      return;
    }

    const { future } = yield* select(selectHistory);
    const redoAction = [...future].pop();
    if (redoAction !== undefined) {
      yield* put(Actions.PATCH_REDO_ACTION());
      if (redoAction !== null && 'action' in redoAction) {
        const {
          action: { creator, payload },
        } = redoAction;
        yield* put(markWorkerAction(creator(payload as any)));
        yield* put(
          Actions.actionLogAuditEvent(
            `Redo Action: { creator: ${creator.name}, payload: ${JSON.stringify(payload)} }`
          )
        );
      } else if (redoAction === null || 'patches' in redoAction) {
        yield* put(Actions.workerRedoAction());
      }

      if (redoAction !== null) {
        yield* put(
          Actions.actionLogAuditEvent(`Redo: ${JSON.stringify(redoAction)}`)
        );
      }
    } else {
      const intl = sagaIntl();
      yield* put(
        enqueueSnackbar({
          variant: 'error',
          message: intl.formatMessage({
            id: 'cannotRedo',
            defaultMessage: 'Cannot redo previous action',
          }),
        })
      );
    }
  });
}

export function* watchPatchEmptyUndoAction() {
  yield* takeLatest(
    [
      CHANGE_CODE,
      CHANGE_SHAPE,
      Actions.NEW_OVERLAY,
      Actions.CHANGE_OVERLAY,
      SET_INDIVIDUAL_REDACTION,
      Actions.ON_SPRAY_PAINT_SAVE,
      Actions.RESIZE_OVERLAY_SEGMENT,
      SET_DETECTION_TYPE_REDACTION,
      Actions.SAVE_LOCAL_UDR_ASSETS,
      IN_LOAD_TRACKING_ENGINE_RESULTS,
    ],
    function* (action) {
      yield* put(
        Actions.PATCH_EMPTY_UNDO_ACTION({
          clearFuture: action.type !== IN_LOAD_TRACKING_ENGINE_RESULTS.type,
        })
      );
    }
  );
}

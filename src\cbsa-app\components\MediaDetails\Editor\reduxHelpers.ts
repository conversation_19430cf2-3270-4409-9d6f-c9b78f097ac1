import { createSelector } from 'reselect';

import {
  selectContentTypeHasAudio,
  selectContentTypeHasVideo,
  selectTdo,
  selectTdoIsChanged,
  isFetchingFaceDetectionSelector,
} from '@common-modules/mediaDetails';

import { selectCurrentRoutePayload } from '@common-modules/routing';

export const componentSelectors = createSelector(
  selectTdo,
  selectTdoIsChanged,
  selectContentTypeHasAudio,
  selectContentTypeHasVideo,
  isFetchingFaceDetectionSelector,
  selectCurrentRoutePayload,
  (
    tdo,
    tdoIsChanged,
    hasAudio,
    hasVideo,
    isFetchingFaceDetection,
    currentRoutePayload
  ) => ({
    tdo,
    hasAudio,
    hasVideo,
    isSaveEnabled: !!tdoIsChanged && !isFetchingFaceDetection,
    currentCaseId: currentRoutePayload.case_id,
  })
);

export const mapDispatchToProps = {};

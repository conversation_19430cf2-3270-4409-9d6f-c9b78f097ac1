import { v4 as uuid } from 'uuid';
import { find, findIndex, findLast, includes } from 'lodash';
import { all, fork, put, select, takeEvery } from 'typed-redux-saga/macro';

import { enqueueSnackbar } from '@common-modules/snackbar';
import { getActiveState } from '@redact-components/MediaDetailsPage/FacesWrapperTab/ClustersResultsTab/utils';
import { processEngineRequestAction } from '../../engines/optical-tracking/actions';
import { eq, scaleBoundingPolyObject } from '@utils';

import { KEYBOARD_EVENT, KeyboardEventPayload } from '@redact-modules/keyboard';
import { selectRouteType } from '@common-modules/routing';
import {
  DETECTION_TYPES,
  OVERLAY_PREVIEW_OPTIONS,
  PLAYBACK_RATES,
} from '@helpers/constants';
import { componentActions } from '@redact-components/MediaDetailsPage/Editor/MediaPlayer/reduxHelpers/componentActions';

import {
  actionChangeOverlay,
  actionJumpVideo,
  actionOverlayPreview,
  actionPauseVideo,
  actionPlaybackRateVideo,
  actionPlayVideo,
  actionPlayVideoBackwards,
  actionSeekVideo,
  actionSetSelectedGroups,
  actionTogglePlayPauseVideo,
  FACE_HIGHLIGHT,
  FETCH_UPDATE_ASSET_ACTION,
  REDACT_TDO,
  deleteDetectionOverlayAction,
  deleteDetectionGroupAction,
  deleteDetectionInFrameAction,
  deleteUDROverlayAction,
  deleteUDRInFrameAction,
  deleteUDRGroupAction,
  SET_PREV_MARKER_TIME,
  UNDO_ACTION,
  REDO_ACTION,
} from '../actions';
import {
  selectCurrentPosition,
  selectHighlightedOverlay,
  // selectHighlightedClusterItemGroup,
  selectHighlightedClusterItem,
  selectHighlightedPoly,
  selectMediaDuration,
  selectOverlayPreview,
  selectPolysAtCurrentTime,
  selectPrevMarkerTime,
  selectSelected,
  selectDisplayUnselectedOverlays,
} from '../selectors';
import { ProcessEngineRequest } from '../../engines/optical-tracking/models/services';
import { arrayHasContents } from '@common/shared/util';

interface Action {
  type: string;
  payload: KeyboardEventPayload;
}

const isControlPressed = (e: KeyboardEventPayload) => e.ctrlKey || e.metaKey;
const isAltPressed = (e: KeyboardEventPayload) => e.altKey;
const isModifierPressed = (e: KeyboardEventPayload) =>
  e.ctrlKey || e.metaKey || e.altKey || e.shiftKey;
const isShiftPressed = (e: KeyboardEventPayload) => e.shiftKey;

export function* keyboardSaga() {
  yield* all([fork(actionKeyboardEvent)]);
}

/**
 * This saga takes the keyboard actions and sends them to the
 * appropriate handlers.
 */
export function* actionKeyboardEvent() {
  const controlPlayheadHandler = controlPlayhead();
  const increasePolyFn = scaleBoundingPolyObject(5);
  const decreasePolyFn = scaleBoundingPolyObject(-4.76);

  yield* takeEvery(
    KEYBOARD_EVENT,
    actionKeyboardEventHandle({
      controlPlayheadHandler,
      increasePolyFn,
      decreasePolyFn,
    })
  );
}

export let keyDown = false;

export const actionKeyboardEventHandle = ({
  controlPlayheadHandler,
  increasePolyFn,
  decreasePolyFn,
}: {
  controlPlayheadHandler: ReturnType<typeof controlPlayhead>;
  increasePolyFn: Parameters<typeof changePolySize>[0];
  decreasePolyFn: Parameters<typeof changePolySize>[0];
}) =>
  function* ({ payload }: { payload: KeyboardEventPayload }) {
    const routeType = yield* select(selectRouteType);
    if (
      routeType === 'route/ROUTE_MEDIA_DETAILS' ||
      routeType === 'route/ROUTE_MEDIA_EDITOR'
    ) {
      if (payload.type === 'keyup') {
        switch (payload.key) {
          case 'q':
          case 'p':
            yield* toggleOverlayPreview();
            break;
          case 'w':
            if (!isModifierPressed(payload)) {
              yield* changePlaybackRate(1);
            }
            if (isControlPressed(payload)) {
              yield* changePlaybackRate(1, true);
            }
            break;
          case 'W':
            yield* jumpToNextSegment();
            break;
          case 'r':
            keyDown = false;
            break;
          case 's':
            if (!isModifierPressed(payload)) {
              yield* changePlaybackRate(-1);
            }
            if (isControlPressed(payload)) {
              yield* changePlaybackRate(-1, true);
            }
            keyDown = false;
            break;
          case 'e':
            yield* put(actionPlaybackRateVideo({ playbackRate: 1 }));
            break;
          case 'S':
            yield* selectNextPoly();
            break;
          case 'T':
            yield* trackObject();
            break;
          case 'x':
            yield* toggleHighlightedClusterItem();
            break;
          case ' ':
            if (isShiftPressed(payload)) {
              yield* setPrevMarkerTime();
            } else if (isControlPressed(payload)) {
              yield* gotoPrevMarkerTime();
            } else {
              yield* togglePlayPause();
            }
            keyDown = false;
            break;
          case 'Backspace':
          case 'Delete':
            if (isControlPressed(payload)) {
              yield* deletePolyCluster();
            } else if (isShiftPressed(payload)) {
              yield* deletePolyInFrame();
            } else {
              yield* deletePoly();
            }
            break;
          case '+':
            yield* changePolySize(increasePolyFn); // UDRs only
            break;
          case '_':
            yield* changePolySize(decreasePolyFn); // UDRs only
            break;
        }
      } else if (payload.type === 'keydown') {
        switch (payload.key) {
          case 'z':
            if (isControlPressed(payload)) {
              if (isShiftPressed(payload)) {
                yield* put(REDO_ACTION());
              } else {
                yield* put(UNDO_ACTION());
              }
            }
            break;
          case 'Z':
            if (isControlPressed(payload)) {
              yield* put(REDO_ACTION());
            }
            break;
          case 's':
          case 'ß' /* macOS */:
            if (isAltPressed(payload) && !keyDown) {
              yield saveData();
              keyDown = true;
            }
            break;
          case 'r':
          case '®' /* macOS */:
            if (isAltPressed(payload) && !keyDown) {
              yield* redactFile();
              keyDown = true;
            }
            break;
        }
      }
      yield* controlPlayheadHandler(payload);
    }
  };

export const gotoPrevMarkerTime = function* () {
  const prevMarkerTime = yield* select(selectPrevMarkerTime);
  if (prevMarkerTime) {
    yield* put(actionSeekVideo({ startTimeMs: prevMarkerTime }));
  }
};

export const setPrevMarkerTime = function* () {
  const currentTime = yield* select(selectCurrentPosition);
  yield* put(SET_PREV_MARKER_TIME(currentTime));
};

export const jumpToNextSegment = function* () {
  const highlightedOverlay = yield* select(selectHighlightedOverlay);

  if (highlightedOverlay) {
    const timeMs = highlightedOverlay.timeMs;

    // since only support merge cluster view right now use all cluster segments
    const cluster = yield* select(selectHighlightedClusterItem);
    const segments = cluster?.segments;

    // if we support split view would switch to this
    // const group = yield* select(selectHighlightedClusterItemGroup);
    // const segments = group?.segments;

    if (segments) {
      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        if (
          segment &&
          segment.startTimeMs <= timeMs &&
          segment.stopTimeMs > timeMs &&
          segment.groupId === highlightedOverlay.groupId
        ) {
          i = (i + 1) % segments.length;
          const nextSegment = segments[i]!; // Safe based on limits
          const startTimeMs = nextSegment.startTimeMs;
          const firstObjectId = nextSegment.objectIds[0];
          if (firstObjectId) {
            yield* put(
              FACE_HIGHLIGHT({
                id: firstObjectId,
                type: nextSegment.type,
                groupId: nextSegment.groupId,
                timeMs: startTimeMs,
              })
            );
          }
          yield* put(actionSeekVideo({ startTimeMs }));
          return;
        }
      }
    }
  }
};

export const selectNextPoly = function* () {
  let polys = yield* select(selectPolysAtCurrentTime);
  const selectedPolys = yield* select(selectSelected);
  const isShowUnselected = yield* select(selectDisplayUnselectedOverlays);
  if (!isShowUnselected) {
    polys = polys.filter((p) => !!selectedPolys[p.subsegmentId]);
  }
  const hiPoly = yield* select(selectHighlightedPoly);
  if (arrayHasContents(polys)) {
    let poly = polys[0];

    if (hiPoly) {
      const index = polys.findIndex(eq(hiPoly));
      poly = polys[index + 1] || poly;
    }
    yield* put(
      FACE_HIGHLIGHT({
        id: poly.id,
        type: poly.type,
        groupId: poly.groupId,
        timeMs: poly.startTimeMs,
      })
    );
  }
};

export const togglePlayPause = function* () {
  yield* put(actionTogglePlayPauseVideo());
};

export const deletePoly = function* () {
  const hiPoly = yield* select(selectHighlightedPoly);

  if (!hiPoly) {
    return;
  }

  if (includes(DETECTION_TYPES, hiPoly.type)) {
    yield* put(deleteDetectionOverlayAction(hiPoly));
  } else {
    yield* put(deleteUDROverlayAction(hiPoly));
  }
};

export const deletePolyCluster = function* () {
  const hiPoly = yield* select(selectHighlightedPoly);

  if (!hiPoly) {
    return;
  }

  if (includes(DETECTION_TYPES, hiPoly.type)) {
    yield* put(deleteDetectionGroupAction(hiPoly));
  } else {
    yield* put(deleteUDRGroupAction(hiPoly));
  }
};

export const deletePolyInFrame = function* () {
  const hiPoly = yield* select(selectHighlightedPoly);
  const currentTime = yield* select(selectCurrentPosition);

  if (!hiPoly) {
    return;
  }

  if (includes(DETECTION_TYPES, hiPoly.type)) {
    yield* put(deleteDetectionInFrameAction(hiPoly, currentTime));
  } else {
    yield* put(deleteUDRInFrameAction(hiPoly, currentTime));
  }
};

export const changePolySize = function* (
  fn: ReturnType<typeof scaleBoundingPolyObject>
) {
  const hiPoly = yield* select(selectHighlightedPoly);
  if (hiPoly?.type === 'udr') {
    const {
      object: { boundingPoly },
    } = fn({
      object: {
        boundingPoly: [...hiPoly.boundingPoly],
      },
    });
    yield* put(
      actionChangeOverlay({
        id: hiPoly.id,
        type: hiPoly.type,
        groupId: hiPoly.groupId,
        boundingPoly,
      })
    );
  }
};

export const trackObject = function* () {
  const hiPoly = yield* select(selectHighlightedPoly);
  if (hiPoly?.type === 'udr') {
    yield* put(
      enqueueSnackbar({
        message: 'Starting the optical tracking process.',
      })
    );
    const payload: ProcessEngineRequest = {
      id: uuid().toString(),
      overlay: hiPoly.id,
      boundingPoly: hiPoly.boundingPoly,
      timeMs: hiPoly.startTimeMs,
      parentUdrId: hiPoly.groupId,
      trackBack: true,
      trackForward: true,
    };
    yield* put(processEngineRequestAction(payload));
  }
};

export const redactFile = function* () {
  yield* put(REDACT_TDO());
};

export const saveData = function* () {
  yield* put(
    enqueueSnackbar({
      message: 'Saving your changes.',
    })
  );
  yield* put(FETCH_UPDATE_ASSET_ACTION());
};

export const toggleOverlayPreview = function* () {
  const overlayPreview = yield* select(selectOverlayPreview);
  const i = findIndex(OVERLAY_PREVIEW_OPTIONS, { value: overlayPreview });
  yield* put(
    actionOverlayPreview(
      // Index action is safe due to module with length
      OVERLAY_PREVIEW_OPTIONS[(i + 1) % OVERLAY_PREVIEW_OPTIONS.length]!.value
    )
  );
};

export const changePlaybackRate = function* (dir: 1 | -1, integral = false) {
  const playbackSpeed = yield* select(
    (s: { player: { playbackRate: number } }) => s.player.playbackRate
  );
  const playbackRate =
    dir === 1
      ? find(
          PLAYBACK_RATES,
          (v) => v > playbackSpeed && (!integral || Number.isInteger(v))
        ) || PLAYBACK_RATES.at(-1)
      : findLast(
          PLAYBACK_RATES,
          (v) => v < playbackSpeed && (!integral || Number.isInteger(v))
        ) || PLAYBACK_RATES[0];
  if (playbackRate) {
    yield* put(actionPlaybackRateVideo({ playbackRate: playbackRate }));
  }
};

/**
 * Selects/unselects the highlighted cluster.
 */
// export const toggleHighlightedClusterItem = function* () {
//   const selected = yield* select(selectSelected);
//   const highlightedOverlayCluster = yield* select(selectHighlightedOverlayCluster);

//   if (highlightedOverlayCluster) {
//     yield* put(
//       actionSetSelectedGroups({
//         selected: highlightedOverlayCluster.segments.reduce((a, ss) => {
//           const newSelected = ss.subsegmentIds.reduce<{
//             [key: string]: boolean;
//           }>((total, current) => {
//             total[current] =
//               getActiveState(highlightedOverlayCluster, selected) === -1;
//             return total;
//           }, {});

//           Object.assign(a, newSelected);
//           return a;
//         }, {}),
//       })
//     );
//   }
// };

/**
 * Selects/unselects the highlighted cluster
 */
export const toggleHighlightedClusterItem = function* () {
  const selected = yield* select(selectSelected);
  const highlightedClusterItem = yield* select(selectHighlightedClusterItem);

  if (highlightedClusterItem) {
    yield* put(
      actionSetSelectedGroups({
        selected: highlightedClusterItem.segments.reduce((a, ss) => {
          const newSelected = ss.subsegmentIds.reduce<{
            [key: string]: boolean;
          }>((total, current) => {
            total[current] =
              getActiveState(highlightedClusterItem.segments, selected) === -1;
            return total;
          }, {});

          Object.assign(a, newSelected);
          return a;
        }, {}),
      })
    );
  }
};

/**
 * Use l/r arrow, a/d keys to step through playhead.
 */
export function controlPlayhead() {
  const keysOfInterest = ['arrowright', 'arrowleft', 'a', 'd'];
  let downCount = 0;
  return function* (e: Action['payload']) {
    const key = e.key.toLowerCase();
    if (keysOfInterest.includes(key)) {
      downCount += e.type === 'keydown' ? 1 : 0;
    }

    const isShiftPressed = e.shiftKey;
    const isAltPressed = e.altKey;
    const isCtrlPressed = isControlPressed(e);

    if (
      e.type === 'keydown' &&
      isShiftPressed &&
      !isAltPressed &&
      !isCtrlPressed
    ) {
      if (key === 'arrowup') {
        const minWindowMs = 0;
        yield* put(actionSeekVideo({ startTimeMs: minWindowMs }));
        yield* put(componentActions.onUpdateCurrentTime(minWindowMs));
        return;
      }
      if (key === 'arrowdown') {
        yield* put(actionSeekVideo({ startTimeMs: Infinity }));
        return;
      }
    }

    if (e.type === 'keydown' && downCount === 2) {
      if (key === 'arrowright' || key === 'd') {
        yield* put(actionPlayVideo());
        return;
      }

      if (key === 'arrowleft' || key === 'a') {
        yield* put(actionPlayVideoBackwards());
        return;
      }
    }

    let jumpFrames = 1; // default jump size is 1 frame

    // keep legacy ctrl + l/r arrow behavior - likely will reassign to something more useful
    if (isCtrlPressed) {
      jumpFrames = 10;
    }

    // use modifiers to jump 1 sec or 10 sec
    let jumpSecs = 0.0;

    if (isShiftPressed) {
      jumpSecs = 1; // jump 1 sec
    }
    if (isAltPressed && isShiftPressed) {
      jumpSecs = 10; // jump 10 sec
    }
    if (isCtrlPressed && isShiftPressed) {
      const duration = yield* select(selectMediaDuration);
      if (duration < 60) {
        jumpSecs = duration; // jump duration sec
      } else {
        jumpSecs = 60; // jump 60 sec
      }
    }

    if (key === 'arrowleft' || key === 'a') {
      if (e.type === 'keyup') {
        if (downCount > 1) {
          yield* put(actionPauseVideo());
        } else {
          if (jumpSecs) {
            yield* put(actionJumpVideo({ seconds: -jumpSecs }));
          } else {
            yield* put(actionJumpVideo({ frames: -jumpFrames }));
          }
        }
        downCount = 0;
      }
    } else if (key === 'arrowright' || key === 'd') {
      if (e.type === 'keyup') {
        if (downCount > 1) {
          yield* put(actionPauseVideo());
        } else {
          if (jumpSecs) {
            yield* put(actionJumpVideo({ seconds: jumpSecs }));
          } else {
            yield* put(actionJumpVideo({ frames: jumpFrames }));
          }
        }
        downCount = 0;
      }
    }
  };
}

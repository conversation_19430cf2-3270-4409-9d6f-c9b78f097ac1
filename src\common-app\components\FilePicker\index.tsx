import {
  <PERSON><PERSON>,
  ThemeProvider,
  Toolt<PERSON>,
  Typo<PERSON>,
  Zoom,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import cn from 'classnames';
import { some } from 'lodash';
import {
  ReactNode,
  useEffect,
  useMemo,
  ChangeEvent,
  useCallback,
  useState,
} from 'react';
import { useStyles } from './styles';
import { I18nTranslate } from '@i18n';
import { guid } from '@common/shared/util';
import theme from '@redact/materialUITheme';
import Checkbox from '@mui/material/Checkbox';
import FaceIcon from '@mui/icons-material/Face';
import PersonIcon from '@mui/icons-material/Person';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import {
  ProgressDialog,
  FilePicker as FilePickerComponent,
} from '@veritone/glc-react';
import { useDispatch, useSelector } from 'react-redux';
import * as filePickerModule from '@common-modules/filePicker';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { CaseId } from '@common-modules/universal/models/Brands';
import { VirtualFolderNames } from '@redact-modules/casemedia/store';
import { selectSelectedFolders } from '@redact-modules/casemedia/selectors';
import {
  selectIngestionOptions,
  setIngestionOptions,
} from '@common-modules/engines/ingestion';
import {
  selectPickerState,
  selectPickerProgressPercent,
  selectPickerSuccess,
  selectPickerError,
  selectPickerWarning,
  selectPickerStatusMessage,
} from '@common-modules/filePicker/selectors';
import { selectOrganizationName } from '@common/user-onboarding/selectors/organization';
import {
  selectDefaultProfile,
  selectProfileList,
} from '@redact/state/modules/settingsProfile/selectors';
import { REDACT_APP_DEFAULT_PROFILE_SETTINGS } from '@helpers/constants';
import { actionFetchProfileList } from '@redact/state/modules/settingsProfile/actions';
// import Spinner from '@redact/pages/MediaDetails/Spinner';

const style = {
  checkbox: {
    '&.MuiButtonBase-root': {
      padding: '9px',
      color: 'rgba(0, 0, 0, 0.54)',
    },
    '&.Mui-checked': {
      color: 'primary.main',
    },
  },
};

const FilePickerContent = (props: Props) => {
  const {
    open,
    onPickCancelled,
    renderButton,
    govQARequestId,
    foiaXpressRequestId,
    casepointRequestId,
    nuixRequestId,
    exterroRequestId,
    onPick,
    caseId,
  } = props;
  const classes = useStyles();
  const [id] = useState(guid());
  const dispatch = useDispatch();
  const error = useSelector(selectPickerError(id));
  const success = useSelector(selectPickerSuccess(id));
  const warning = useSelector(selectPickerWarning(id));
  const pickerState = useSelector(selectPickerState(id));
  const selectedFolders = useSelector(selectSelectedFolders);
  const statusMessage = useSelector(selectPickerStatusMessage(id));
  const progressPercent = useSelector(selectPickerProgressPercent(id));
  const allProfiles = useSelector(selectProfileList);
  const defaultProfile = useSelector(selectDefaultProfile);
  const [selectedProfileId, setSelectedProfileId] = useState<string>(
    REDACT_APP_DEFAULT_PROFILE_SETTINGS.id
  );

  useEffect(() => {
    dispatch(actionFetchProfileList({ limit: 1000, offset: 0 }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // REDACT_APP_DEFAULT_PROFILE_SETTINGS.id
  // (defaultProfile || REDACT_APP_DEFAULT_PROFILE_SETTINGS).id
  // );
  useEffect(() => {
    setSelectedProfileId(
      (defaultProfile || REDACT_APP_DEFAULT_PROFILE_SETTINGS).id
    );
  }, [defaultProfile]);
  const { runHead, runPerson, runTranscription } = useSelector(
    selectIngestionOptions
  );

  const orgName = useSelector(selectOrganizationName);

  const handleProfileChange = useCallback(
    (event: SelectChangeEvent<string>) => {
      setSelectedProfileId(event.target.value);
    },
    []
  );

  const handleRunHead = useCallback(
    (evt: ChangeEvent<HTMLInputElement>) =>
      dispatch(setIngestionOptions({ runHead: evt.target.checked })),
    [dispatch]
  );

  const handleRunPerson = useCallback(
    (evt: ChangeEvent<HTMLInputElement>) =>
      dispatch(setIngestionOptions({ runPerson: evt.target.checked })),
    [dispatch]
  );

  const handleRunTranscription = useCallback(
    (evt: ChangeEvent<HTMLInputElement>) =>
      dispatch(setIngestionOptions({ runTranscription: evt.target.checked })),
    [dispatch]
  );

  const handlePick = () => dispatch(filePickerModule.pick(id));

  const cancel = useCallback(() => {
    dispatch(filePickerModule.endPick(id));
    onPickCancelled();
  }, [dispatch, id, onPickCancelled]);

  const onFilesSelected = useCallback(
    (files: File[]) => {
      dispatch(
        filePickerModule.uploadRequest({
          id,
          govQARequestId,
          foiaXpressRequestId,
          nuixRequestId,
          casepointRequestId,
          exterroRequestId,
          files,
          callback: onPick,
          caseId,
          settingsProfileId: selectedProfileId,
        })
      );
    },
    [
      caseId,
      casepointRequestId,
      dispatch,
      exterroRequestId,
      foiaXpressRequestId,
      govQARequestId,
      id,
      nuixRequestId,
      onPick,
      selectedProfileId,
    ]
  );

  const hasRequestId = useMemo(
    () =>
      govQARequestId ||
      foiaXpressRequestId ||
      casepointRequestId ||
      nuixRequestId ||
      exterroRequestId,
    [
      govQARequestId,
      foiaXpressRequestId,
      casepointRequestId,
      nuixRequestId,
      exterroRequestId,
    ]
  );

  const isShowBreadcrumb = useMemo(
    () => caseId && selectedFolders?.length > 0,
    [caseId, selectedFolders]
  );
  const getBreadcrumb = useCallback(() => {
    const paths: string[] = [];
    if (orgName) {
      paths.push(orgName);
    }
    (selectedFolders || []).forEach((f) => {
      if (f.name) {
        paths.push(f.name);
      }
    });
    paths.push(VirtualFolderNames.INPUT);
    return (
      <Breadcrumbs
        color="inherit"
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
      >
        {(paths || []).map((name: string) => (
          <Typography key={Math.random()} color="inherit">
            {name}
          </Typography>
        ))}
      </Breadcrumbs>
    );
  }, [orgName, selectedFolders]);

  const renderPickerDialog = useCallback(
    () => (
      <Dialog
        style={{ minHeight: 800 }}
        open={open}
        data-testid="file-picker-dialog"
      >
        {isShowBreadcrumb && (
          <div className={classes.folderPathBreadcrumb}>{getBreadcrumb()}</div>
        )}
        <div
          className={cn(classes.filePickerThemeOverride, {
            [classes.additionalFooterPadding]: hasRequestId,
          })}
        >
          <FilePickerComponent
            {...props}
            onRequestClose={cancel}
            onPickFiles={onFilesSelected}
            multiple={
              !some([
                govQARequestId,
                foiaXpressRequestId,
                casepointRequestId,
                nuixRequestId,
                exterroRequestId,
              ])
            }
            maxFiles={5}
            leftFooterContent={
              <div
                className={
                  govQARequestId ||
                  foiaXpressRequestId ||
                  nuixRequestId ||
                  casepointRequestId ||
                  exterroRequestId
                    ? classes.filePickerCheckboxes
                    : classes.filePickerCheckboxesWithBottom
                }
              >
                <div className={classes.filePickerOption}>
                  <span className={classes.selectProfileLabel}>
                    {I18nTranslate.TranslateMessage('settingsProfileTitle')}
                  </span>
                  <Select
                    name="settings-profile"
                    className={classes.selectInput}
                    value={selectedProfileId}
                    onChange={handleProfileChange}
                  >
                    <MenuItem
                      value={REDACT_APP_DEFAULT_PROFILE_SETTINGS.id}
                      className={classes.menuItem}
                    >
                      {REDACT_APP_DEFAULT_PROFILE_SETTINGS.data.profileName}
                    </MenuItem>
                    {(allProfiles || []).map((p) => (
                      <MenuItem
                        key={p.id}
                        value={p.id}
                        className={classes.menuItem}
                      >
                        <Tooltip
                          title={p.data.profileName}
                          placement="bottom-start"
                        >
                          <span>{p.data.profileName}</span>
                        </Tooltip>
                      </MenuItem>
                    ))}
                  </Select>
                </div>
                <div className={classes.filePickerOption}>
                  <Checkbox
                    checked={runHead}
                    color="primary"
                    onChange={handleRunHead}
                    data-testid="checkbox-run-head"
                    sx={style.checkbox}
                  />
                  <FaceIcon data-testid="face-icon" fontSize="medium" />
                  <Tooltip
                    slots={{
                      transition: Zoom,
                    }}
                    arrow
                    title={
                      <span>
                        {I18nTranslate.TranslateMessage(
                          'filePickerRunHeadTooltip'
                        )}
                      </span>
                    }
                    placement="top"
                    classes={{
                      tooltip: classes.tooltip,
                    }}
                  >
                    <span className={classes.filePickerContent}>
                      {I18nTranslate.TranslateMessage('filePickerRunHead')}
                    </span>
                  </Tooltip>
                </div>
                <div className={classes.filePickerOption}>
                  <Checkbox
                    checked={runPerson}
                    color="primary"
                    onChange={handleRunPerson}
                    data-testid="checkbox-run-person"
                    sx={style.checkbox}
                  />
                  <PersonIcon data-testid="person-icon" fontSize="medium" />
                  <Tooltip
                    slots={{
                      transition: Zoom,
                    }}
                    arrow
                    title={
                      <span>
                        {I18nTranslate.TranslateMessage(
                          'filePickerRunPersonTooltip'
                        )}
                      </span>
                    }
                    placement="top"
                    classes={{
                      tooltip: classes.tooltip,
                    }}
                  >
                    <span className={classes.filePickerContent}>
                      {I18nTranslate.TranslateMessage('filePickerRunPerson')}
                    </span>
                  </Tooltip>
                </div>
                <div className={classes.filePickerOption}>
                  <Checkbox
                    checked={runTranscription}
                    color="primary"
                    onChange={handleRunTranscription}
                    data-test="checkbox-run-transcription"
                    sx={style.checkbox}
                  />
                  <span
                    className={cn(
                      classes.transcriptionIcon,
                      'icon-transcription'
                    )}
                  />
                  <span className={classes.filePickerContent}>
                    {I18nTranslate.TranslateMessage('runTranscription')}
                  </span>
                </div>
                {govQARequestId ? (
                  <div className={classes.additionalID}>
                    {I18nTranslate.TranslateMessage('govqaID', {
                      govQARequestId,
                    })}
                  </div>
                ) : null}
                {foiaXpressRequestId ? (
                  <div className={classes.additionalID}>
                    {I18nTranslate.TranslateMessage('foiaXpressID', {
                      foiaXpressRequestId,
                    })}
                  </div>
                ) : null}
                {casepointRequestId ? (
                  <div className={classes.additionalID}>
                    {I18nTranslate.TranslateMessage('casepointID', {
                      casepointRequestId,
                    })}
                  </div>
                ) : null}
                {nuixRequestId ? (
                  <div className={classes.additionalID}>
                    {I18nTranslate.TranslateMessage('nuixID', {
                      nuixRequestId,
                    })}
                  </div>
                ) : null}
                {exterroRequestId ? (
                  <div className={classes.additionalID}>
                    {I18nTranslate.TranslateMessage('exterroID', {
                      exterroRequestId,
                    })}
                  </div>
                ) : null}
              </div>
            }
          />
        </div>
      </Dialog>
    ),
    [
      allProfiles,
      cancel,
      hasRequestId,
      casepointRequestId,
      classes.additionalID,
      classes.additionalFooterPadding,
      classes.filePickerCheckboxes,
      classes.filePickerCheckboxesWithBottom,
      classes.filePickerContent,
      classes.filePickerOption,
      classes.filePickerThemeOverride,
      classes.folderPathBreadcrumb,
      classes.selectInput,
      classes.selectProfileLabel,
      classes.tooltip,
      classes.transcriptionIcon,
      classes.menuItem,
      exterroRequestId,
      foiaXpressRequestId,
      getBreadcrumb,
      govQARequestId,
      handleProfileChange,
      handleRunHead,
      handleRunPerson,
      handleRunTranscription,
      isShowBreadcrumb,
      nuixRequestId,
      onFilesSelected,
      open,
      props,
      runHead,
      runPerson,
      runTranscription,
      selectedProfileId,
    ]
  );

  const renderProgressDialog = useCallback(() => {
    const completeStatus = warning
      ? 'warning'
      : error
        ? 'failure'
        : success
          ? 'success'
          : undefined;

    return (
      <Dialog data-test={'upload-ProgressDialog'} open={open}>
        <ProgressDialog
          // Todo: Investigate the flow of error and warning messages
          percentComplete={progressPercent}
          progressMessage={statusMessage}
          completeStatus={completeStatus}
        />
      </Dialog>
    );
  }, [error, open, progressPercent, statusMessage, success, warning]);

  const pickerComponent = useCallback(
    () =>
      ({
        selecting: renderPickerDialog,
        uploading: renderProgressDialog,
        complete: renderProgressDialog,
      })[pickerState](),
    [pickerState, renderPickerDialog, renderProgressDialog]
  );

  return (
    <>
      {/* {!selectedProfileId ? (
        <Spinner message="loading..." />
      ) : (
        ''
      )} */}
      {selectedProfileId ? pickerComponent() : ''}
      {renderButton?.({ handlePickFiles: handlePick })}
    </>
  );
};

const FilePicker = (props: Props) => (
  <ThemeProvider theme={theme}>
    <FilePickerContent {...props} />
  </ThemeProvider>
);

interface Props {
  onPick: () => void;
  govQARequestId?: string;
  exterroRequestId?: string;
  foiaXpressRequestId?: string;
  casepointRequestId?: string;
  nuixRequestId?: string;
  onPickCancelled: () => void;
  open: boolean;
  accept?: string[];
  allowUrlUpload?: boolean;
  caseId?: CaseId;
  renderButton?: (opts: any) => ReactNode;
  fileSizeLimit?: number | string;
}

export default FilePicker;

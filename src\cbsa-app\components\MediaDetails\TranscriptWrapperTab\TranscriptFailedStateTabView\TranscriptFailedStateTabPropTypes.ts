// import { DetailTDO } from '@common-modules/mediaDetails/models';
// import { processEngineRequestAction } from '@common-modules/engines/transcription';

import { TranscriptFailedStateTabPropsFromRedux } from '.';

// export interface TranscriptFailedStateTabPropTypes {
//   readonly tdo: DetailTDO | null;
//   readonly onStartJob: typeof processEngineRequestAction;
//   readonly videoLoaded: boolean;
//   readonly buttonTranscriptionDisable: boolean;
// }

export type TranscriptFailedStateTabPropTypes =
  TranscriptFailedStateTabPropsFromRedux & {
    videoLoaded: boolean;
  };

import { memo } from 'react';
import * as React from 'react';

import SearchView from './SearchView';

const Search = ({
  term,
  focus,
  numResults,
  onChange,
  onClear,
  onFocus,
  onSearch,
}: TranscriptionDisplayPropTypes) => {
  const onSetFocus = (dir: number) =>
    onFocus(
      focus + dir >= numResults
        ? 0
        : focus + dir < 0
          ? numResults - 1
          : focus + dir
    );

  return (
    <SearchView
      {...{
        term,
        focus,
        numResults,
        onSetFocus,
        onChange,
        onClear,
        onSearch,
      }}
    />
  );
};

export default memo(Search);

export interface TranscriptionDisplayPropTypes {
  readonly term: string;
  readonly focus: number;
  readonly numResults: number;
  readonly onChange: (term: string) => void;
  readonly onClear: () => void;
  readonly onFocus: React.Dispatch<React.SetStateAction<number>>;
  readonly onSearch: () => void;
}

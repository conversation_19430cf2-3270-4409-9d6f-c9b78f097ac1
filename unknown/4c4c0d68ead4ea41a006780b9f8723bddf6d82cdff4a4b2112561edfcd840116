import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles((_theme) => ({
  icon: {
    borderRadius: '0%',
    display: 'flex',
    marginLeft: 2,
    width: 30,
    height: 34,
    color: '#cfd8dc',
    borderLeft: '2px solid transparent',
    background: '#f2f2f2;',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    zIndex: 100,
  },
  thumb: {
    backgroundColor: '#2196F3',
  },
  thumbIconWrapper: {
    backgroundColor: '#2196F3',
  },
  track: {
    backgroundColor: '#2196F3',
  },
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: 30,
    height: 131,
    padding: 15,
    backgroundColor: '#f2f2f2',
    justifyContent: 'flex-end',
    color: '#000000',
  },
  slider: {
    borderRadius: 1,
    margin: 5,
  },
}));

export const useSliderStyles = makeStyles((_theme) => ({
  thumb: {
    backgroundColor: '#2196F3',
  },
  thumbIconWrapper: {
    backgroundColor: '#2196F3',
  },
  track: {
    backgroundColor: '#2196F3',
  },
  slider: {
    borderRadius: 1,
    margin: 5,
  },
}));

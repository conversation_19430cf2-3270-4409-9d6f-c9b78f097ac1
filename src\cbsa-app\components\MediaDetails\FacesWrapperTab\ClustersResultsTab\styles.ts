import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles((_theme) => ({
  overflowAuto: {
    overflow: 'auto',
  },

  fg10auto: {
    flex: '1 0 auto',
  },

  col1: {
    width: '50px',
  },

  col2: {
    width: '80px',
  },

  label: {
    height: '35px',
    maxWidth: '400px',
    marginBottom: '15px',
    marginRight: '10px',
  },

  col4: {
    width: '25px',
    flexDirection: 'column',
    marginTop: '30px',
  },

  colLen: {
    width: '40px',
    justifyContent: 'center',
    textAlign: 'center',
  },

  colTime: {
    width: '130px',
  },

  colInterp: {
    boxSizing: 'border-box',
    width: '55px',
    textAlign: 'center',
    justifyContent: 'center',
  },

  sort: {
    minHeight: '38px',
    maxHeight: '38px',
    borderBottom: '1px solid rgba(0, 0, 0, 0.5)',
    fontWeight: 200,
  },

  activeSort: {
    fontWeight: 500,
  },

  listContainer: {
    // overflow: auto,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    paddingRight: '0.5rem',
  },

  listRow: {
    position: 'relative',
    overflow: 'hidden',
    minHeight: '80px',
    borderTop: '1px solid rgba(255, 255, 255, 0.1)',
    transition: 'min-height 250ms',
    paddingTop: '1rem',
    paddingBottom: '1rem',
  },

  groupItem: {
    // CTODO this is for a segment,  may want to rename this and various data-test-id names
    minHeight: '34px',
    alignItems: 'baseline',
  },

  polyPreview: {
    overflow: 'hidden',
    justifyContent: 'space-between',
    maxHeight: '83px',
    '& img': {
      display: 'inline-block',
      width: '48px',
      height: '48px',
      borderRadius: '3px',
    },
  },

  clusterFooterObjectType: {
    display: 'flex',
    '& img': {
      width: '16px',
      height: '16px',
      marginRight: '5px',
    },
    span: {
      fontWeight: 500,
      fontSize: '14px',
    },
  },

  timePeriod: {
    cursor: 'pointer',
    borderBottom: '1px dashed #333',
    '&:hover': {
      borderBottom: '1px dashed #666',
    },
  },

  clickable: {
    display: 'flex',
    flexDirection: 'row',
    cursor: 'pointer',
    borderBottom: '1px dashed #333',
    '&:hover': {
      borderBottom: '1px dashed #666',
    },
    '&>div': {
      transform: 'scale(0.8)',
    },
  },

  expandable: {
    cursor: 'pointer',
    transition: 'transform 200ms',
  },

  open: {
    transform: 'rotate(90deg)',
  },

  highlightedSegment: {
    background: 'rgba(255, 255, 128, 0.1)',
    position: 'absolute',
    height: '36px',
    left: '0',
    width: '100%',
    marginTop: '-3px',
    borderLeft: '5px solid rgba(255, 255, 128, 0.4)',
    pointerEvents: 'none',
  },

  '@media (max-width: 1280px)': {
    col1: {
      width: '40px',
    },
    col2: {
      width: '60px',
    },
  },

  buttonDetectContainer: {
    marginBottom: '-30px',
    paddingTop: '0.5rem',
    paddingLeft: '1rem',
  },

  clusterListViewContainer: {
    color: '#ccc',
    height: '90%',
    marginTop: '-10px',
    paddingLeft: '1rem',
    paddingRight: '0.25rem',
    marginRight: '0.5rem',
  },

  filterItem: {},
}));

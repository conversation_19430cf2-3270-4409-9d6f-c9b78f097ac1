import { useSelector } from 'react-redux';

import makeStyles from '@mui/styles/makeStyles';
import { selectCasesSummary } from '@cbsa-modules/mainPage/selectors';

import Card from '@mui/material/Card';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid2';

import SummaryItem from './SummaryItem';

import { useIntl } from 'react-intl';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const useStyles = makeStyles((theme) => ({
  summaryCard: {
    padding: theme.spacing(2),
    boxShadow: theme.shadows[4],
  },
}));

const CasesSummaryContent = () => {
  const classes = useStyles();
  const intl = useIntl();

  const casesSummary = useSelector(selectCasesSummary);

  let summaryContent = <CircularProgress variant="indeterminate" />;

  if (casesSummary) {
    const { casesCreated, casesApproved, casesReopened, casesReadyForExport } =
      casesSummary;

    summaryContent = (
      <>
        <SummaryItem
          mainNumber={casesCreated}
          subtextHeadline={intl.formatMessage({
            id: 'casesCreated',
            defaultMessage: 'Cases Created',
          })}
          // subtextData="00"
          // subtextUnits={intl.formatMessage({ id: 'daysOld' })}
        />
        <SummaryItem
          mainNumber={casesApproved}
          subtextHeadline={intl.formatMessage({
            id: 'casesApproved',
            defaultMessage: 'Cases Approved',
          })}
          // subtextData="00"
          // subtextUnits={intl.formatMessage({ id: 'daysOld' })}
        />
        <SummaryItem
          mainNumber={casesReopened}
          subtextHeadline={intl.formatMessage({
            id: 'casesReopened',
            defaultMessage: 'Request Reopened',
          })}
          // subtextData="00"
          // subtextUnits={intl.formatMessage({ id: 'daysOld' })}
        />
        <SummaryItem
          mainNumber={casesReadyForExport}
          subtextHeadline={intl.formatMessage({
            id: 'readyForExport',
            defaultMessage: 'Ready for Export',
          })}
          // subtextData="00"
          // subtextUnits={intl.formatMessage({ id: 'daysOld' })}
        />
      </>
    );
  }

  return (
    <Card className={classes.summaryCard}>
      <Grid
        container
        direction="row"
        justifyContent="space-around"
        alignItems="center"
      >
        {summaryContent}
      </Grid>
    </Card>
  );
};

const CasesSummary = () => (
  <ThemeProvider theme={defaultTheme}>
    <CasesSummaryContent />
  </ThemeProvider>
);

export default CasesSummary;

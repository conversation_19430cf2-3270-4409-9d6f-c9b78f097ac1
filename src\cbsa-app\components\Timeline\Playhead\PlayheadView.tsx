import { memo, useEffect, useRef } from 'react';

import * as styles from '../styles.scss';

import { PlayheadViewPropTypes } from './PlayheadPropTypes';
import { mainRenderer } from './renderer';

import type { RendererOutput } from './renderer';

const PlayheadView = ({
  startWindowMs,
  stopWindowMs,
  minWindowMs,
  maxWindowMs,
  onSetStart,
  onSetStop,
  onSeekMedia,
}: PlayheadViewPropTypes) => {
  const containerId = useRef(`playhead_${Math.random()}`);

  const renderer = useRef<RendererOutput | null>(null);

  useEffect(() => {
    renderer.current = mainRenderer(
      containerId.current,
      minWindowMs,
      maxWindowMs,
      onSetStart,
      onSetStop,
      onSeekMedia
    );

    return () => renderer.current?.destroy();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (renderer.current) {
      renderer.current.setStart(startWindowMs);
    }
  }, [startWindowMs]);

  useEffect(() => {
    if (renderer.current) {
      renderer.current.setStop(stopWindowMs);
    }
  }, [stopWindowMs]);

  return (
    <div
      id={containerId.current}
      className={styles.playhead}
      data-testid="timeline-play-head"
    />
  );
};

export default memo(PlayheadView);

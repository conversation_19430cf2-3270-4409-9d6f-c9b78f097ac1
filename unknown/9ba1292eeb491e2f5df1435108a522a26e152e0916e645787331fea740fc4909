import { useCallback, useEffect } from 'react';

import Grid from '@mui/material/Grid2';
// import makeStyles from '@mui/styles/makeStyles';
import { useDispatch, useSelector } from 'react-redux';
import CasesTable from '@common-components/CaseDashboard/CasesTable';
import CasesSummary from '@common-components/CaseDashboard/CasesSummary';
import CasesManagement from '@cbsa-components/MainPage/CasesManagement';

import {
  FETCH_CASES,
  FETCH_SUMMARY,
  setCasesList,
  setCasesTotal,
} from '@cbsa-modules/mainPage/actions';
import * as selectors from '@cbsa-modules/mainPage/selectors';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';
import { TreeObjectId } from '@cbsa/state/modules/universal';

// const useStyles = makeStyles((theme) => ({
//   mainGrid: {
//     height: 'calc(100vh - 55px)',
//     flexWrap: 'nowrap',
//     overflow: 'hidden',
//     '&>.MuiGrid-item': {
//       marginTop: theme.spacing(3),
//       padding: '0 40px',
//     },
//   },
// }));

const MainPageComponentContent = () => {
  // const classes = useStyles();

  const dispatch = useDispatch();
  const searchText = useSelector(selectors.selectSearchText);
  const statusFilter = useSelector(selectors.selectStatusFilter);
  const showArchived = useSelector(selectors.selectShowArchived);
  const casesSortColumn = useSelector(selectors.selectCasesSortColumn);
  const casesSortOrder = useSelector(selectors.selectCasesSortOrder);
  const paginationAmount = useSelector(selectors.selectPaginationAmount);
  const paginationStart = useSelector(selectors.selectPaginationStart);

  // Fetch list of cases from server on page load and on filter criteria update
  useEffect(() => {
    // This emits an event for a saga listener
    dispatch(FETCH_CASES());

    return () => {
      dispatch(setCasesList([]));
      dispatch(setCasesTotal(0));
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    searchText,
    statusFilter,
    showArchived,
    casesSortColumn,
    casesSortOrder,
    paginationAmount,
    paginationStart,
  ]);

  useEffect(() => {
    // This emits an event for a saga listener
    dispatch(FETCH_SUMMARY());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onCaseClick = useCallback((caseId: TreeObjectId) => {
    window.location.href = `/case/${caseId}`;
  }, []);

  return (
    <Grid
      container
      direction="column"
      justifyContent="flex-start"
      alignItems="stretch"
      // TODO: update to use styled instead
      // classes={{
      //   root: classes.mainGrid,
      // }}
    >
      <CasesSummary />
      <CasesManagement />
      <CasesTable onCaseClick={onCaseClick} />
    </Grid>
  );
};

const MainPageComponent = () => (
  <ThemeProvider theme={defaultTheme}>
    <MainPageComponentContent />
  </ThemeProvider>
);

export default MainPageComponent;

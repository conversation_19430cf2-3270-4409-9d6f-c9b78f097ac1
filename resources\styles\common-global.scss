html,
body {
  height: 100%;
  font-size: 16px;
  line-height: 1.4;
  box-sizing: border-box;
  overflow: hidden;
  margin: 0px;
}

html {
  overflow: hidden;
}

:global {
  #root {
    height: 100%;
  }

  .video-react-mouse-display::after,
  .video-react-play-progress::after {
    font-size: 10px !important;
  }

  .video-react .video-react-control:focus::before,
  .video-react .video-react-control:hover::before,
  .video-react .video-react-control:focus {
    text-shadow: 0 0 1em #999;
  }

  .video-react .video-react-loading-spinner {
    display: block !important;
    opacity: 0 !important;
  }

  .video-react.video-react-seeking .video-react-loading-spinner {
    display: block;
    opacity: 0.85 !important;
    transition: opacity 100ms 500ms;
  }

  .video-react .video-react-loading-spinner {
    pointer-events: none;
  }
}

div[data-veritone-component='media-player']:fullscreen {
  > div {
    max-height: calc(100vh - 50px);
  }
  video {
    object-fit: contain;
    max-height: 100vh;
  }
}

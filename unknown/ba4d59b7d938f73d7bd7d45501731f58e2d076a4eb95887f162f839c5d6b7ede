import {
  all,
  fork,
  put,
  takeEvery,
  select,
  race,
  take,
} from 'typed-redux-saga/macro';
import { markWorkerAction } from '@utils';
import { redirect } from 'redux-first-router';
import moment from 'moment';
import { get } from 'lodash';
import {
  isShowTrimTool,
  toggleTrimTool,
  CREATE_TDO_FOR_TRIM_VIDEO_SUCCESS,
  SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO,
  SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO_SUCCESS,
} from './index';
import {
  ROUTE_HOME,
  ROUTE_MEDIA_DETAILS,
} from 'src/common-app/state/modules/routing';
import { REDIRECT_TO_HOME } from 'src/common-app/state/modules/mediaDetails';
import * as Services from '@redact-modules/mainPage/services';
import {
  UPSERT_ORGANIZATION_SDO_SUCCESS,
  UPSERT_ORGANIZATION_SDO_FAILURE,
} from '@redact-modules/mainPage/actions';
import { createEngineJobForTrimTDO } from '@common-modules/engines/ingestion/services';
import * as Actions from '@common-modules/engines/ingestion/actions';
import { selectConfig } from '@common-modules/engines/selectors';
import { TDOId } from '../universal/models/Brands';

export function* handleCloseModal() {
  yield* takeEvery(
    [REDIRECT_TO_HOME, ROUTE_HOME, ROUTE_MEDIA_DETAILS],
    toggleTrimToolHandle
  );
}
export function* toggleTrimToolHandle() {
  const isShowTrim = yield* select(isShowTrimTool);
  if (isShowTrim) {
    yield* put(toggleTrimTool() as any);
  }
}

export function* handleCreatedTDOSuccess() {
  // @ts-expect-error TODO: Use action creator for CREATE_TDO_FOR_TRIM_VIDEO_SUCCESS
  yield* takeEvery(CREATE_TDO_FOR_TRIM_VIDEO_SUCCESS, createdTDOTrimSuccess);
}

// TODO: Implement action creator for CREATE_TDO_FOR_TRIM_VIDEO_SUCCESS
export function* createdTDOTrimSuccess(action: {
  payload: {
    createTDOWithAsset: {
      id: string;
      name: string;
      primaryAsset: {
        signedUri: string;
      };
    };
  };
  meta: {
    variables: Record<string, any> & {
      sourceTDO?: TDOId;
      runAudioTranscription: boolean;
      runFaceDetection: boolean;
    };
  };
}) {
  const { id } = action.payload.createTDOWithAsset;
  const configs = yield* select(selectConfig);
  const { detectionCategory, transcriptionCategoryId } = configs;
  const {
    hasFaceDetectionEngineResults,
    hasAudioTranscriptionEngineResults,
    sourceTDO,
  } = action.meta.variables;
  const engineCategoriesRun: string[] = [];
  if (hasFaceDetectionEngineResults) {
    engineCategoriesRun.push(detectionCategory);
  }
  if (hasAudioTranscriptionEngineResults) {
    engineCategoriesRun.push(transcriptionCategoryId);
  }

  yield* put(
    Services.upsertOrganizationSDO({
      data: {
        tdoId: id,
        description: 'trimmed',
        status: 'Draft',
        modifiedDateTime: moment(new Date()).toISOString(),
        createdDateTime: moment(new Date()).toISOString(),
        engineCategoriesRun,
        sourceTDO,
      },
    })
  );

  const { successAction } = yield* race({
    successAction: take(UPSERT_ORGANIZATION_SDO_SUCCESS),
    _: take(UPSERT_ORGANIZATION_SDO_FAILURE),
  });

  if (successAction) {
    const { runAudioTranscription, runFaceDetection } = action.meta.variables;
    // process trim tdo and redirect to the tdo created
    if (runFaceDetection || runAudioTranscription) {
      const { id, primaryAsset } = action.payload.createTDOWithAsset;
      yield* put(
        createEngineJobForTrimTDO({
          tdoId: id,
          payload: {
            targetId: id,
            url: primaryAsset.signedUri,
          },
          runHead: runFaceDetection,
          runTranscription: runAudioTranscription,
        })
      );

      const { createJobSuccess, createJobFailed } = yield* race({
        createJobSuccess: take(Actions.CREATE_ENGINE_JOB_SUCCESS),
        createJobFailed: take(Actions.CREATE_ENGINE_JOB_FAILURE),
      });

      if (createJobSuccess || createJobFailed) {
        yield* put(redirect(ROUTE_MEDIA_DETAILS({ tdoId: id })));
      }
    } else {
      // skip process ( trim only )
      // get engine result from webworker to trim and save
      yield* put(
        markWorkerAction({
          type: SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO,
          payload: {
            tdoId: id,
            trimStartOffsetMs:
              get(action, 'meta.variables.trimFile.startTime') * 1000,
            trimStopOffsetMs:
              get(action, 'meta.variables.trimFile.endTime') * 1000,
          },
        })
      );
      const { successAction } = yield* race({
        successAction: take(SAVE_ENGINE_RESULTS_FOR_TRIM_VIDEO_SUCCESS),
      });
      if (successAction) {
        yield* put(redirect(ROUTE_MEDIA_DETAILS({ tdoId: id })));
      }
    }
  }
}

export default function* trimSaga() {
  yield* all([fork(handleCloseModal), fork(handleCreatedTDOSuccess)]);
}

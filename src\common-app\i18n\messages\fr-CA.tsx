import { LOCALES } from '../locales';
const getCommonMessages = () => ({
  schemaIdNotFound:
    'SchemaId introuvable. Veuillez vérifier le fichier de configuration du système.',
  review: 'Examen',
  uploadMedia: 'Télécharger des Médias',
  sortBy: 'Trier par',
  createdDate: 'Date de Création',
  lastModifiedDate: 'Date de la dernière modification',
  draft: 'Brouillon',
  pendingReview: "En attendant l'examen",
  complete: 'Complet',
  results: 'Résultats',
  redactedFiles: 'Fichiers Caviardés',
  save: 'Enregistrer',
  redactFile: 'Caviarder',
  trimAndRedact: 'Découper et Caviarder',
  setTrimRange: 'Régler la intervalle de découpage',
  clearTrimRange: 'Nettoyer la intervalle de découpage',
  none: 'Aucun',
  black_fill: 'Remplissage noir',
  outline: 'Esquisser',
  redacted: 'Caviardés',
  preview: 'Prévisualiser',
  videoOnly: 'Vidéo Seulement',
  detectObjects: 'DÉTECTER DES OBJETS',
  detectDescription:
    'Détectez et placez automatiquement des boîtes de délimitation sur les objets dans cette vidéo, ce qui rend la rédaction plus rapide et plus facile pour vous.',
  detectEmptyDescription: "Aucune aucun objet n'a été détecté dans ce média.",
  detectDeletedDescription: 'Tous les objets détectés ont été supprimés.',
  objectDetection: "Détection d'Objets",
  noPersonOfInterestImages: "AUCUNE IMAGE DE PERSONNE D'INTÉRÊT",
  POIReferenceImages: 'POI Images',
  images: 'POI Images',
  video: 'Vidéo',
  audio: 'Audio',
  other: 'Autre',
  comments: 'Commentaires',
  somethingWrong: 'Quelque Chose a Mal Tourné',
  home: 'Domicile',
  transcription: 'Transcription',
  errorTranscriptionRun:
    "Une erreur s'est produite lors de l'exécution du moteur de transcription. Voulez-vous réexécuter le moteur ?",
  errorObjectDetectionRun:
    "Une erreur s'est produite lors de l'exécution du moteur de détection d'objets. Voulez-vous réexécuter le moteur ?",
  transcribeAudio: "Transcrire l'Audio",
  transcriptionMessage:
    "Transcrivez automatiquement ce média pour caviarder commodément toute partie de l'audio.",
  noTranscribableAudio:
    "Aucun enregistrement audio transcriptible n'a été trouvé dans ce média.",
  deleteReactionTitle: 'Dupprimer le dernier fichier caviardé',
  deleteReactionContent: 'Voulez-vous supprimer le dernier fichier caviardé ?',
  previousVersions: 'Il reste {numVersions} version(s) précédente(s).',
  exitWithoutSaving: 'Quitter sans enregistrer',
  delete: 'Supprimer',
  remove: 'Supprimer',
  latestRedactedFile: 'Dernier Fichier Caviardé',
  version: 'Version',
  download: 'Télécharger',
  downloadZip: 'Télécharger ZIP',
  downloadAll: 'Télécharger Tout',
  exportZip: 'Exporter ZIP',
  exportZipWarning:
    "Le fichier zip comprend à la fois les médias caviardés et le journal d'audit. Attention : l'exportation vers un fichier zip est plus lente que le téléchargement direct de les médias caviardés ou le journal d'audit !",
  downloadAllMessage: "Téléchargera les médias expurgés et le journal d'audit.",
  downloadMedia: 'Télécharger Media',
  downloadAuditLog: 'Télécharger audit log',
  downloadRedactedTranscript: 'Télécharger la transcription caviardée',
  downloadRedactionReport: 'Télécharger le rapport de caviardage',
  sendToGovQa: 'Envoyer à GovQa',
  sendToFOIAXpress: 'Envoyer à le FOIAXpress',
  sendToCasepoint: 'Envoyer à le Casepoint',
  sendToExterro: 'Envoyer à le Exterro Review',
  sendToNuix: 'Envoyer à le Nuix Discover',
  sendToExternal: 'Envoyer à le Exterro',
  GovQAAuthentication: 'Authentification de le GovQA',
  add: 'Ajouter',
  cancel: 'Annuler',
  confirm: 'Confirmer',
  ok: 'Ok',
  mute: 'Muet',
  tone: 'Tonalité•e',
  blur: 'Flou',
  blackfill: 'Remplissage noir',

  headShape: 'Tête Forme',
  udrShape: 'UDR Forme',
  laptopShape: 'Ordinateur Forme',
  vehicleShape: 'Véhicule Forme',
  licensePlateShape: 'Plaque Forme',
  cardShape: "Carte d'identité (PII) Forme",
  notepadShape: 'Bloc-notes (PII) Forme',
  personShape: 'Personne Forme',

  headRedaction: 'Tête Caviardée',
  udrRedaction: 'UDR Caviardé',
  laptopRedaction: 'Ordinateur Caviardé',
  vehicleRedaction: 'Véhicule Caviardé',
  licensePlateRedaction: 'Plaque Caviardée',
  cardRedaction: "Carte d'identité (PII) Caviardée",
  notepadRedaction: 'Bloc-notes (PII) Caviardée',
  personRedaction: 'Personne Caviardée',

  shapes: 'Formes',
  rectangle: 'Rectangle',
  ellipse: 'Ellipse',

  featherTypeHead: 'Tête Type de plume (flou)',
  featherTypeUdr: 'UDR Type de plume (flou)',
  featherTypeLaptop: 'Ordinateur Type de plume (flou)',
  featherTypeVehicle: 'Véhicule Type de plume (flou)',
  featherTypeLicensePlate: 'Plaque Type de plume (flou)',
  light: 'Léger',
  medium: 'Moyen',
  heavy: 'Lourd',

  settings: 'Réglages',
  videoType: 'Type de vidéo',
  confidenceThreshold: 'Seuil de Confiance',
  legacyClustering: 'Groupement hérité',
  clusterSimThreshold: 'Seuil de similarité des grappes',
  verylowThreshold: 'Très faible (moins de groupes)',
  lowThreshold: 'Faible',
  mediumThreshold: 'Moyen',
  highThreshold: 'Elevé',
  veryhighThreshold: 'Très élevé (plus de groupes)',
  accuracySpeed: 'Précision ↔ Vitesse',
  trackBackwardsLimit: 'Suivre la limite arrière',
  seconds: 'secondes',
  trackForwardLimit: 'Suivre la limite avant',
  autoInterpolation: 'Interpolation automatique',
  milliseconds: 'millisecondes',
  millisecondsNote: 'millisecondes ({value})',
  manualInterpolationWindow: "Fenêtre d'interpolation manuelle",
  videoOffset: 'Décalage vidéo',
  fillType: 'Remplir',
  redactionType: 'Type de caviardage',
  detectionType: 'Type de détection',

  blurLevel: 'Niveau de flou',
  aTob: '{a} à {b}',
  allWorkWillBeReset: 'Tout le travail sera remis à zéro',
  blurLevelVehicle: 'Niveau de flou (Véhicule)',
  patchPreviousVersion: 'Corriger la version précédente',
  saveChanges: 'Enregistrer les changements',
  saveContentModal:
    'Voulez-vous enregistrer vos modifications avant de fermer ?',
  saveRunning: 'Enregistrement en course',
  saveRunningContentModal:
    "Êtes-vous sûr de vouloir quitter avant la fin de l'enregistrement ?",
  saveFailed: "Échec de l'enregistrement !",
  saveFailedContentModal:
    "L'enregistrement des modifications a échoué. Voulez-vous réessayer ?",
  request: 'Demande',
  notifications: 'Notifications',
  newNotifications: 'Nouvelles Notifications',
  dismissedNotifications: 'Notifications Rejetées',
  dismissAll: 'Rejeter Tout',
  clearAll: 'Effacer Tout',
  keyboardShortcuts: 'Raccourcis clavier',
  general: 'Général',
  redactMedia: 'Caviarder les médias',
  trackObject: "Suivre l'objet",
  trackForwardOnly: 'Piste en avant seulement',
  trackBackwardOnly: 'Piste en arrière seulement',
  zoomInTimeline: 'Zoom la chronologie',
  zoomOutTimeline: 'Zoom arrière chronologie',
  toggleUdrTimeline: 'Basculer la chronologie UDR',
  warningAssignMergedGroup:
    'Attention ! Le groupe est assigné à un groupe fusionné !',
  addRedactionConfiguration: 'Ajouter des effets personnalisés',
  editRedactionConfiguration: 'Modifier les effets personnalisés',
  restoreRedactionConfiguration: "Restaurer l'effet de rédaction",
  addEditRedactionConfigurationContent:
    "Vous êtes sur le point d'ajouter/modifier un effet de censure. Appliquer les modifications à",
  restoreRedactionConfigurationContent:
    'Vous êtes sur le point de restaurer un effet de rédaction. Appliquer le changement à',
  removeRedactionConfiguration: 'Supprimer les effets personnalisés',
  customEffects: 'Effets personnalisés',
  removeRedactionConfigurationContent:
    'Vous êtes sur le point de supprimer la configuration des effets personnalisés. Etes-vous sûr?',
  removeRedactionConfigurationContentMergedGroup:
    "Vous êtes sur le point de supprimer l'effet de rédaction personnalisé. Appliquer les modifications à",
  redactionEffects: 'Effets de rédaction',
  objectType: "Type d'objet",
  restoreDefault: 'Rétablir Défaut',
  redactionConfiguration: 'Configuration du caviardage',
  addRedactionCode: 'Ajouter un code de caviardage',
  editRedactionCode: 'Modifier un code de caviardage',
  addEditRedactionCodeContent:
    "Vous êtes sur le point d'ajouter/modifier un code de censure. Appliquer les modifications à",
  removeRedactionCode: 'Suppression un code de caviardage',
  removeRedactionCodeContent:
    'Vous êtes sur le point de supprimer le code de caviardage. Vous êtes sûr ?',
  removeRedactionCodeContentMergedGroup:
    'Vous êtes sur le point de supprimer le code de rédaction. Appliquer les modifications à',
  addCode: 'Ajouter un code',
  editCode: 'Modifier le code',
  chooseColorLabel: 'sélectionnez votre couleur',
  chooseCodeLabel: 'Choisissez le nom du code de caviardage',
  changeShape: 'Modifier la forme',
  changeShapeContent:
    'Vous êtes sur le point de modifier le type de forme. Appliquer le changement à',
  center: 'Centre',
  top: 'Haus',
  bottom: 'Bas',
  above: 'Au-dessus de',
  below: 'En dessous de',
  left: 'Gauche',
  right: 'Droite',
  addRedactionCodeLocationPlaceholder: '- Sélectionner la Position -',
  restoreDefaultColor: 'Restaurer la Couleur par Défaut',
  stickToVideo: 'Coller à la vidéo',
  setTimeStamp: "Définir l'horodateur",
  minVal: 'Valeur minimale: {value}',
  maxVal: 'Valeur maximale: {value}',
  errorExceededName: 'Valeur maximale : dépassé',
  errorExceededType: 'dépassé',
  errorStartAfterEndName: 'Le début ne peut pas suivre la fin',
  errorStartAfterEndType: 'Invalide',
  manualTrackingLocked: 'Suivi manuel (Verrouillé)',
  increaseRectSize: 'Augmenter la taille du rectangle',
  decreaseRectSize: 'Diminuer la taille du rectangle',
  increaseUDRSizeHorizontally: 'Augmenter la taille de l`UDR horizontalement',
  decreaseUDRSizeHorizontally: 'Diminuer la taille de l`UDR horizontalement',
  increaseUDRSizeVertically: 'Augmenter la taille de l`UDR verticalement',
  decreaseUDRSizeVertically: 'Diminuer la taille de l`UDR verticalement',
  playback: 'Relecture',
  playPause: 'Jouer/Pause',
  spaceBar: "barre d'espace",
  goBackFrame: 'Revenir en arrière',
  or: 'ou',
  advanceFrame: 'Avancer un cadre',
  start: 'Le Début',
  end: 'La Fin',
  jumpOneSecond: "Saut d'une seconde",
  jump10Seconds: 'Saut de 10 secondes',
  jumpOneMinute: "Saut d'une minute",
  jumpToStart: 'Aller au début',
  jumpToEnd: 'Aller à la fin',
  increasePlaybackSpeed: 'Augmenter la vitesse de lecture',
  decreasePlaybackSpeed: 'Réduire la vitesse de lecture',
  defaultPlaybackSpeed: 'Vitesse de lecture par défaut',
  returnToPreviousMarker: 'Retour au marqueur précédent',
  setMarkerToCurrent: 'Fixer le marqueur à la position actuelle',
  editor: 'Éditeur',
  deleteUDR: 'Supprimer UDR',
  deleteOverlay: 'Supprimer la superposition',
  deleteGroup: 'Supprimer le group',
  deleteGroupContent:
    'Vous êtes sur le point de supprimer le groupe. Appliquer le changement à',
  deleteGroupFailure: 'Échec de la suppression du groupe.',
  deleteInFrame: 'Supprimer dans le cadre',
  deleteLabel: 'Supprimer {label}',
  deleteLabelInFrame: 'Supprimer {label} dans le cadre',
  deleteLabelOverlay: 'Supprimer la superposition {label}',
  deleteLabelSegment: 'Supprimer le segment {label}',
  deleteLabelGroup: 'Supprimer le group {label}',
  resizeLabelSegment: 'Redimensionner le segment {label}',
  resize: 'Redimensionner',
  changePreview: "Modifier l'aperçu",
  selectNextOverlay: 'Parcourir la Superposition Surlignée',
  jumpNextGroupSegment: 'Aller à la segment suivant',
  IncreaseUDRSize: "Augmenter la taille de l'UDR",
  selectDeselectGroup: 'Rédiger/Dérédiger le Groupe',
  decreaseUDRSize: "Diminuer la taille de l'UDR",
  close: 'Fermer',
  reRunEngine: 'REDIFFUSION LE MOTEUR',
  continue: 'Continuer',
  DetectHeadsandObjects: 'Détecter Les Têtes et Les Objets',
  DetectHeadsandObjectsDes:
    "L'exécution de la détection de la tête et des objets remplacera tous les résultats précédents de la détection de la tête et des objets. Souhaitez-vous continuer ?",
  detectObjectsTitle: 'Détecter des objets',
  detectHeadObjectsDes:
    "Avertissement! La nouvelle exécution de la détection d'objet remplacera tous les résultats de détection précédents. Souhaitez-vous continuer? \n Sélectionnez les types d'objets que vous souhaitez détecter (au moins une option doit être sélectionnée)",
  object: 'Objet',
  auto: 'Auto',
  time: 'Temps',
  segmentType: 'Taper',
  manual: 'Manuel',
  interpolation: 'INTERPOLATION',
  saveChangesMessage: 'Voulez-vous enregistrer les modifications ?',
  selectedObjectProcessing: "L'objet sélectionné est en cours de traitement",
  ConfirmUnlinkingModalTitle: 'Confirmer le délien du CMS',
  ConfirmUnlinkingModalContent:
    'Vous êtes sur le point de délier les fichiers {tdoCount} du CMS. Vos ressources multimédia seront toujours présentes dans le CMS mais vous ne pourrez plus y accéder dans Redact.',
  unlink: 'Délier',
  uploadFailed: 'le téléchargement a échoué',
  processingNewMedia: 'Traitement des nouveaux médias...',
  reqID: 'Req ID',
  fileRedacted: 'Fichier caviardé',
  redactingFile: 'Le fichier est en cours de caviarder',
  playVideo: 'Lire la vidéo',
  noFilesUploaded: 'Aucun fichier téléchargé',
  uploadFilesToStarted: 'Téléchargez vos fichiers pour commencer',
  uploadFile: 'Télécharger le fichier',
  noResultsFound: 'Aucun résultat trouvé',
  notFoundDes: 'Veuillez essayer une autre sélection.',
  confirmDeletionModalTitle: 'Confirmer la suppression',
  confirmDeletionModalContent:
    "Vous êtes sur le point de supprimer les fichiers {tdoCount}. Vous ne serez pas en mesure d'annuler cette action.",
  confirmMultiDeletionModalTitle: 'Suppression de plusieurs fichiers',
  confirmMultiDeletionModalContent:
    'Vous êtes sur le point de supprimer les fichiers {tdoCount}. Vous êtes sûr ?',
  filePickerRunHeadTooltip:
    "Détecter des têtes, des ordinateurs et des plaques d'immatriculation",
  filePickerRunPersonTooltip:
    "Détecter des personnes, des ordinateurs et des plaques d'immatriculation",
  filePickerRunHead: 'Détecter des têtes et des objets',
  filePickerRunPerson: 'Détecter des personnes et des objets',
  runTranscription: 'Exécuter la transcription',
  ingest: 'Ingérer',
  ingestFromUrl: "Ingérer depuis l'URL",
  govqaID: 'ID de la demande GovQA : {govQARequestId}',
  foiaXpressID: 'ID de la demande FOIAXpress : {foiaXpressRequestId}',
  casepointID: 'Casepoint ID : {casepointRequestId}',
  nuixID: 'NUIX ID : {nuixRequestId}',
  exterroID: 'Exterro ID : {exterroRequestId}',
  engineProcessingTimeDependDes:
    'Cela peut prendre un certain temps en fonction de la longueur de la vidéo.',
  engineProcessingWaitDes1:
    'Vous pouvez attendre que le traitement se termine ici',
  engineProcessingWaitDes2: 'ou revenir à la',
  homePage: 'ACCUEIL',
  set: 'Régler',
  unredact: 'Ne Pas Caviarder',
  unredactAll: 'Ne Pas Caviarder Tout',
  editNote: 'Modifier la Note',
  deleteNote: 'Supprimer la Note',
  addNote: 'Ajouter une Note',
  redact: 'Caviarder',
  redactAll: 'Caviarder Tout',
  maskVoice: 'Masquer la Voix',
  unmaskVoice: 'Dévoiler la Voix',
  notesFormHelpTextLabels:
    'Séparez chaque étiquette par une virgule et/ou un espace.',
  notesFormLabels: 'Étiquettes',
  notesFormNotes: 'Remarques',
  searchViewPlaceholder: 'Cliquer sur Entrer pour rechercher...',
  forbidden: 'Interdit',
  logoutMessage:
    'Veuillez patienter pendant que vous êtes redirigé vers la page de connexion.',
  logoutWait: 'Déconnexion...',
  tdosGridHasMore: 'Faites défiler vers le bas pour charger plus de médias...',
  pageNotFound: 'Page Non Trouvée',
  pageNotFoundDes:
    "La page que vous recherchez a été déplacée, supprimée ou n'existe pas.",
  overviewTitle: 'Télécharger les Médias à caviarder',
  id: 'id',
  action: 'action',
  auditLogDialogTitle: "Journal d'Audit des Dossiers",
  approve: 'Approuver',
  approveCaseTitle: 'Approuver Le Dossier',
  approveCaseContent: 'Êtes-vous sûr de vouloir approuver ce dossier ?',

  archive: 'Archiver',
  archiveCaseTitle: 'Archiver Le Dossier',
  archiveCaseContent:
    'Êtes-vous sûr de vouloir archiver ce dossier ? Une fois archivé, le dossier sera supprimé de la vue sur le tableau de bord à moins que la bascule "Afficher tout" ne soit activée.',

  removeFilterTitle: 'Suppression De La Sortie',
  removeFilterHeads:
    'Veuillez confirmer : les têtes caviardées ne doivent PAS être incluses dans la vidéo finale.',
  removeFilterUdrs:
    'Veuillez confirmer : les UDR caviardés ne doivent PAS être incluses dans la vidéo finale.',
  removeFilterLicensePlates:
    "Veuillez confirmer : les plaques d'immatriculation expurgées ne doivent PAS être incluses dans la vidéo finale.",
  removeFilterLaptops:
    'Veuillez confirmer : les ordinateurs portables expurgés ne doivent PAS être incluses dans la vidéo finale.',
  removeFilterNotepads:
    'Veuillez confirmer : les blocs-notes caviardés ne doivent PAS être incluses dans la vidéo finale.',
  removeFilterCards:
    "Veuillez confirmer : les cartes d'identité expurgées ne doivent PAS être incluses dans la vidéo finale.",
  removeFilterPersons:
    'Veuillez confirmer : les personnes caviardées ne doivent PAS être incluses dans la vidéo finale.',
  removeFilterConfirmDialog:
    "Êtes-vous sûr de vouloir supprimer ce filtre de la sortie ? Lorsqu'il est retiré, l'état du filtre ne peut pas être récupéré.",

  reopen: 'Rouvrir',
  reopenCaseTitle: 'Rouvrir Le Dossier',
  reopenCaseContent: 'Êtes-vous sûr de vouloir reouvrir ce dossier ?',

  date: 'date',
  deleteCaseTitle: 'Supprimer Le Dossier',
  deleteCaseContent:
    'Êtes-vous sûr de vouloir supprime ce dossier ? Une fois supprimé, le dossier ne peut pas être récupéré.',
  deleteCaseRequest:
    'Vous devez supprimer tous les médias de ce boîtier avant de supprimer.',

  deleteTdoTitle: 'Supprimer Le Sélection',
  deleteTdoContent: 'Êtes-vous sûr de vouloir supprimer le média sélectionné ?',

  export: 'Exporter',
  exportCaseTitle: 'Exporter Case',
  exportCaseContent: 'Êtes-vous sûr de vouloir exporter ce dossier ?',
  logs: 'Journaux',
  hash: 'hachage',

  user: 'usager',
  process: 'Traiter',
  processCaseTitle: 'Traiter Le Dossier',
  processCaseContent:
    'Lancez la recherche de POI sur toutes les vidéos. Veuillez vous assurer que vous avez téléchargé toutes les images et vidéos de POI. Voulez-vous continuer ?',

  userProfileMenu: "Menu Profil de l'Usager",
  signOut: 'Se Déconnecter',
  casesCreated: 'Dossiers Créés',
  numOfDaysOld: '# Jours',
  averageTimeProcessing: 'Temps de Traitement Moyen',
  casesInQueue: 'Dossiers en Attente',
  currentFailed: 'Échec actuel',
  statusDropdown: 'Liste Déroulante des Statuts',
  allStatuses: 'Tous les Statuts',
  showArchived: 'Afficher les Archives',
  newCase: 'Nouveau Dossier', // or 'Créer un Dossier'
  newImage: 'Nouveau Image',
  hideArchived: 'Cacher les Archives',
  deleting: 'Supprimant',
  archived: 'Archivé•e',
  new: 'Nouveau',
  pending: 'En attente',
  processing: 'En traitement',
  processed: 'Traité•e',
  error: 'Erreur',
  readyForReview: "Prêt pour l'examen",
  approved: 'Approuvé•e',
  readyForExport: "Prêt pour l'Exportation",
  deleted: 'Supprimé•e',
  deleteFolder: 'Supprimer le dossier',
  submit: 'Soumettre',
  caseName: 'Nom du Dossier',
  caseNameRequired: 'Nom du Dossier Requis',
  folderName: 'Nom du Classeur',
  folderNameRequired: 'Nom du Classeur Requis',
  deselectAll: 'Désélectionner Tout',
  selectAll: 'Sélectionner Tout',
  dropToUploadMedia: 'Déposer pour Télécharger des Médias',
  dragAndDrop: 'Glisser-Déposer',
  orClickToAddMedia: 'ou Cliquez pour Ajouter un Média',
  viewAuditLog: "Afficher le journal d'audit",
  processCase: 'Traiter le Dossier',
  approveCase: 'Approuver le Dossier',
  reopenCase: 'Rouvrir le Dossier',
  archiveCase: 'Archiver le Dossier',
  deleteCase: 'Supprimer le Dossier',
  exportCase: 'Exporter le Dossier',
  caseReviewManagement: "Gestion de l'examen des dossiers",
  addMedia: 'Ajouter des Médias',
  searchPlaceholder: 'Recherche...',
  searchCodePlaceholder: 'Search for code',
  loadingCodes: 'Codes De Chargement...',
  noCodes: 'Pas de Codes Disponibles...',
  daysOld: 'Jours',
  casesApproved: 'Dossiers Approuvés',
  casesReopened: 'Dossiers Rouverts',
  noItemsToDisplay: 'Aucun Élément à Afficher',
  lastUpdated: 'Dernière Mise à Jour',
  age: 'Âge',
  status: 'Statut',
  mediaDetails: 'Détails de Média',
  caseDetails: 'Détails de Dossier',
  caseActions: 'Actions de Dossier',
  caseMedia: 'Médias de Dossier',
  of: 'de',
  perPage: 'Par Page',
  details: 'Détails',
  archiveCaseConfirmDialog:
    'Êtes-vous sûr de vouloir archiver ce dossier ? Une fois archivée, l\'affaire ne sera plus visible sur le tableau de bord, sauf si l\'option "Voir tout" est activée.',
  deleteCaseConfirmDialog:
    "Êtes-vous sûr de vouloir supprimer cette affaire ? Une fois supprimée, l'affaire ne peut pas être récupérée.",
  back: 'Retourner',
  deleteMessage:
    "Êtes-vous sûr de vouloir supprimer ceci ? Lorsqu'il est supprimé, il ne peut pas être récupéré.",
  moveToVirtualFolderNotAllowed:
    "Les dossiers ou les demandes ne peuvent pas être imbriqués dans les dossiers d'entrée ou de sortie.",
  invalidFileMove:
    'Déplacement invalide, les dossiers ne peuvent être déplacés que vers des demandes.',
  activeCases: 'Dossiers actifs',
  approvedCases: 'Dossiers approuvés',
  archivedCases: 'Dossiers archivées',
  reviewCases: "Cas en cours d'examen",
  noCases: 'Pas de dossiers',
  openCase: 'Ouvrir le dossier',
  sourceMedia: 'Médias sources',
  redactedMedia: 'Médias caviardés',
  noMedia: 'Pas de Médias',

  exportPending: 'Cette dossier sera exportée dans un instant.',
  exportRunning: "Cette dossier s'exporte en ce moment même !",
  exportComplete:
    'Ce dossier est prêt à être téléchargé ! Veuillez cliquer sur "télécharger" ci-dessous.',
  exportFailed: "Cette dossier n'a pas réussi à exporter.",
  exportDefault: 'Veuillez attendre que votre exportation commence.',

  readyForReviewTitle: 'Prêt à examiner le dossier',
  readyForReviewTitleContent:
    'Êtes-vous sûr que ce dossier est prêt à être examiné ?',

  caseIsNew: 'Le dossier est nouveau',
  caseIsProcessing: 'Le dossier est en cours de traitement',
  caseHasAnError: 'Le dossier a une erreur',
  caseIsReadyForReview: 'Le dossier est prête à être examinée',
  caseHasBeenApproved: 'Le dossier a été approuvé',
  caseIsReadyForExport: "Le dossier est prête pour l'exportation",
  caseHasBeenDeleted: 'Le dossier a été supprimée',
  caseStatusWasNotUpdated: "Le statut de le dossier n'a pas été mis à jour",

  isBeingProcessed: 'est en cours de traitement',
  couldNotBeProcessed: "n'a pas pu être traité",

  newCaseCreated: 'Nouveau cas créé',
  caseDeleted: 'Cas supprimé',
  caseArchived: 'Cas archivé',
  noNotifications: 'Aucune notification',

  displayUnselectedOverlays: 'Afficher les Superpositions Non Rédigées',
  displayUnselectedOverlaysTitle: 'Afficher les Superpositions Non Rédigées',
  displayUnselectedOverlaysContent:
    "Avertissement ! La prévisualisation des superpositions non rédigées peut entraîner un retard de rendu. Un décalage peut se produire lorsqu'un grand nombre de superpositions sont visibles.",

  timelineUdr: 'UDR',
  timelineHead: 'TÊTE',
  timelinePerson: 'PERSONNE',
  timelinePoi: 'POI ',
  timelinePlate: 'PLAQUE',
  timelineVehicle: 'VÉHICLE',
  timelineLaptop: 'ORDINATEUR ',
  timelineNotepad: 'BLOC-NOTES (PII)',
  timelineCard: "CARTE D'IDENTITE (PII)",
  timelineTranscription: 'AUDIO',
  timelineComments: 'COMMENTAIRES',
  created: 'Créé•e',
  running: 'En cours',
  view: 'Voir',
  navigationDiscardWarning:
    'Vous avez des modifications non enregistrées sur cette page. Voulez-vous quitter cette page et annuler vos modifications ou rester sur cette page ?',
  mostRecent: 'Plus récent',
  all: 'Tout',
  head: 'Tête',
  face: 'Tête',
  poi: 'POI',
  udr: 'UDR',
  laptop: 'Ordinateur',
  manualInterp: 'Manuel',
  plate: 'Plaque',
  plateOrVehicle: 'Plaque / Véhicule',
  licensePlate: 'Plaque',
  vehicle: 'Véhicule',
  notepad: 'Bloc-notes (PII)',
  card: "Carte d'identité (PII)",
  person: 'Personne',
  fetchDetAssets: "Récupération des actifs de détection d'objets...",
  procDetAssets: "Traitement des actifs de détection d'objets...",
  caseStatusChanged: "Le statut de l'affaire a été modifié",
  caseStatusNotChanged: "Le statut de l'affaire n'a pas été modifié",
  caseNotDeleted: "Le cas n'a pas été supprimé",
  fileDeleted: 'Fichier supprimé',
  fileFailedDeleted: 'Échec de la suppression du fichier',
  fileFailedCreateTDO: "Échec de la création du fichier d'exportation TDO",
  exportTdoNotCreated: "Le TDO d'exportation n'a pas pu être créé",
  caseCouldNotExported: "Le cas n'a pas pu être exporté",
  failedToFetchCase: 'Échec de la récupération du cas',
  caseNameChange: "Le nom de l'affaire a été changé en {caseName}",
  caseNameChangeFail: "Échec du changement de nom de l'affaire: {error}",
  notiDismissFail: 'La notification ne pouvait pas être rejetée',
  notiClearFail: "La notification n'a pas pu être effacée",
  failedToFetchMedia: 'Échec de la récupération du média',
  failedToFetchComments: 'Échec de la récupération des commentaires',
  fileUploadSuccess: 'Fichier téléchargé avec succès',
  fileUploadFail: 'Le téléchargement du fichier a échoué',
  latestRedactDeleted: 'Le dernier fichier caviardé a été supprimé avec succès',
  latestRedactDeletedFail:
    'La suppression du dernier fichier caviardé a échoué',
  downloadMediaFail: 'La tentative de téléchargement du média a échoué',
  procNotFinishedWait:
    "Le traitement de ce fichier n'est pas terminé. Attendez que l'ingestion soit terminée.",
  engineRetryError: "Une erreur s'est produite lors de la réessai du moteur.",
  successfulRedact:
    'Fichier caviardé avec succès. Cliquez sur Afficher le fichier pour le consulter.',
  engineJobFailureMsg:
    "Le moteur n'a pas réussi à fonctionner avec l'identifiant de moteur suivant: {engineId}",

  casemediatitle: 'MÉDIAS DE CAS',
  viewAllCases: 'Afficher tous les cas',
  addCase: 'Ajouter un dossier',
  addFolder: 'Ajouter un classeur',
  createdSuccessfully: 'a créé avec succès',
  failedToCreate: "n'a pas créé",
  caseDashboard: 'Tableau de Bord du Cas',
  viewAllMedia: 'Voir tous les médias',
  searchFoldersAndCases: 'Chercher de Dossiers et Cas...',
  searchResults: 'Résultats de Cherche',
  search: 'Chercher',
  name: 'Nom',
  dateCreated: 'Date de Création',

  noCommentsFound: 'Aucun commentaire trouvé dans ce média.',
  addNewComment: 'Ajouter un nouveau commentaire',
  timeSyncCommentTooltip: 'Sauter au cadre',
  timeSyncMediaTime: 'Heure des médias {time}',
  postedOn: 'Publié•e',
  editedOn: 'Modifié•e',
  comment: 'Commenter',
  successfullySavedComment: 'Le commentaire enregistré avec succès.',
  failedToSaveComment: "Échec de l'enregistrement du commentaire.",
  failedToFetchUser: "N'a pas réussi à récupérer l'utilisateur.",

  read: 'Lu',
  markAsRead: 'Marquer comme lu',
  edit: 'Modifier',
  hide: 'Cacher',
  unhide: 'Afficher',
  filterDetections: 'Filtrer Détections',
  removeFromOutput: 'Retirer de la Sortie',
  viewHidden: 'Vue Cachée',
  viewFilter: 'Vue Filtre',
  hidden: 'Cachée',
  hideFilter: 'Cacher Filtre',
  sortByTimestamp: 'Trier par Horodateur',
  sortByDateCreated: 'Trier par Date de Création',
  viewPeopleFilter: 'Vue du filtre des personnes',
  hidePeopleFilter: 'Cacher du filtre des personnes',
  sortAscend: 'Trier en ordre croissant',
  sortDescend: 'Trier par ordre décroissant',
  commenter: 'Commentateur',
  legacyMediasLinkText: 'Afficher Média non attribué',
  legacyMediasTitle: 'Média non attribué',
  profileName: 'Profile Name',
  isDefault: 'Défaut',
  createdBy: 'Créé par',
  modifiedBy: 'Modifié par',
  createdOn: 'Créé',
  modifiedOn: 'Modifié',

  redaction: 'Caviardage',
  headDetection: 'Détection de Tête',
  bodyDetection: 'Détection de Corps',
  personLocation: 'Localisation de la Personne',
  personOfInterest: "Personne d'Intérêt",

  menuOptionDeleteFolder: 'Supprimer',
  menuOptionRenameFolder: 'Renommer',
  menuOptionFolderDetails: 'Détails du dossier',
  menuOptionMoveFolder: 'Déplacements',
  menuOptionDownloadAll: 'Télécharger tout',

  menuOptionEditFileAttributes: 'Modifier les attributs',
  menuOptionAddTagFile: 'Ajouter une étiquette',
  menuOptionReviewFile: 'Examiner le fichier',
  menuOptionViewLogFile: 'Afficher le journal',
  menuOptionCopyFile: 'Copier le fichier',
  menuOptionPlayFile: 'Lire le fichier',
  menuOptionDownloadFile: 'Télécharger le fichier',
  menuOptionDeleteFile: 'Supprimer le fichier',
  addRequest: 'Ajouter une demande',
  searchFoldersAndRequests: 'Recherche dans les dossiers et les demandes...',
  requestName: 'Nom de la demande',
  requestNameRequired: 'Nom de la demande requis',
  moveFolder: 'Déplacer le dossier',
  confirmMove: 'Déplacements',
  noFoldersFound: 'Aucun dossier trouvé.',

  moveFolderSuccess: 'Le déplacement du dossier ou de la demande est réussi.',
  moveFolderFailure: 'Échec du déplacement du dossier ou de la demande.',
  renameSuccess: 'Le renommage a été effectué avec succès.',
  folderCannotNestedMoreThanTwoLevel:
    'Les dossiers ne peuvent pas être imbriqués à plus de deux niveaux.',
  requestShouldBeOneLevelDeep:
    'Les demandes doivent être imbriquées au moins à un niveau de profondeur dans un dossier principal.',
  containsNestedFolders:
    'Le dossier sélectionné contient des dossiers imbriqués. Les dossiers ne peuvent pas être imbriqués sur plus de deux niveaux.',
  foldersShouldNestedInRequest:
    'Les dossiers ne peuvent pas être imbriqués dans les demandes.',
  requestCannotNestedInRequests:
    'Les demandes ne peuvent pas être imbriquées les unes dans les autres.',
  nameExistsInFolder:
    'Ce nom existe déjà pour ce dossier sélectionné. Désolé, les noms en double ne sont pas autorisés.',
  nameExistsCheckFailed: "Échec de la vérification de l'existence du nom pour",
  errorAddingMedia: "Une erreur s'est produite lors de l'ajout du média.",
  errorUpdatingMedia:
    "Une erreur s'est produite lors de la mise à jour des médias.",
  errorRemovingMedia:
    "Une erreur s'est produite lors de la suppression du support.",
  errorRemovingFiles:
    "Une erreur s'est produite lors de la suppression d'un ou plusieurs fichiers.",
  errorPreloadingMedia:
    "Une erreur s'est produite lors du préchargement de la liste des médias.",
  errorUnlinkingFiles:
    "Une erreur s'est produite lors du déliaison d'un ou plusieurs fichiers.",
  invalidRequestToIngest:
    'Le fichier doit être téléchargé vers la demande. Veuillez sélectionner une demande valide à ingérer.',
  selectRequestToIngest: 'Sélectionnez une demande à ingérer',
  confirmSelectRequest: 'Sélectionnez',
  noRedactPermission:
    "Veuillez vous assurer que l'utilisateur a le rôle d'éditeur REDACT.",
  fetchRootFolderFailed: 'Impossible de récupérer les dossiers.',
  createRootFolderFailed: "Échec de la création du dossier racine de l'org.",
  sourceAndDestSame:
    "Le dossier sélectionné ne peut pas être déplacé à l'intérieur de lui-même.",
  folderAlreadyExitsInParent:
    'Le dossier sélectionné existe déjà dans le dossier parent.',
  requestAlreadyExitsInParent:
    'La demande sélectionnée existe déjà dans le parent.',
  inValidSource: 'Mouvement non valide. Veuillez réessayer.',
  inValidDest: 'Dossier cible non valide. Veuillez réessayer.',
  code: 'Code',
  codeName: 'Nom de Code',
  description: 'Description',
  codeFontColor: 'Couleur de la Police du Code',
  redactions: 'Caviardages',
  redactionCodes: 'Codes de Caviardage',
  addNewCode: 'Ajouter un nouveau code',
  settingsProfile: 'Profils',
  settingsProfileTitle: 'Profil des Paramètres: ',
  addNewSettingsProfile: 'Ajouter un nouveau profil',
  deleteSettingsProfile: 'Supprimer le profil',
  fetchProfileListFailure:
    "Une erreur inattendue s'est produite lors de l'extraction de la liste des profils.",
  createSettingsProfileFailure:
    "Une erreur inattendue s'est produite lors de la création du profil de paramètres.",
  updateSettingsProfileFailure:
    "Une erreur inattendue s'est produite lors de la mise à jour du profil des paramètres.",
  deleteSettingsProfileFailure:
    "Une erreur inattendue s'est produite lors de la suppression du profil des paramètres.",

  codeColor: 'Code Couleur',
  createdDateTime: "Date d'ajout",
  modifiedDateTime: 'Date de modification',
  definingRedactedAreas: 'Définition des zones caviardées',
  redactionCodeDetails: 'Détails du code de rédemption',
  colorDescription:
    "Choisissez une couleur pour la police de votre code de rédaction lorsqu'il est affiché à l'écran.",
  saveRedactionCodeSuccess:
    "L'enregistrement du code de rédemption avec succès.",
  savedRedactionCodeFailure:
    'Une erreur est survenue lors de la sauvegarde du code de rédaction.',
  fetchRedactionCodesFailure:
    "Une erreur s'est produite lors de l'enregistrement du code de rédaction: {responseCode}",
  deleteRedactionCode: 'Supprimer le code de rédemption',
  deleteRedactionCodeSuccess: 'Suppression du code de rédaction réussie.',
  deleteRedactionCodeFailure:
    "Une erreur s'est produite lors de la suppression du code de rédaction.",
  checkRedactionCodeExistsSuccess:
    "Échec de l'enregistrement. Il semble que le code ou le nom de code de réaction existe déjà. Veuillez vérifier et utiliser un autre code ou nom de code.",
  checkRedactionCodeExistsFailure:
    "Une erreur s'est produite lors de la validation du code de rédaction existant.",
  illuminateFolderNameMsg:
    "Le nom de dossier 'Illuminate App' n'est pas autorisé au niveau de la racine car il est réservé à l'application Illuminate. Veuillez fournir un nom de dossier différent.",
  moveFileToCaseSuccess: 'Fichier déplacé avec succès.',
  moveFileToCaseFailure:
    "Une erreur inattendue s'est produite lors du déplacement du fichier. Veuillez réessayer plus tard.",
  menuOptionMoveFile: 'Déplacer un fichier',
  sourceAndDestCaseOfFileSame:
    'Impossible de se déplacer car les demandes de la source et de la destination sont identiques.',
  tdoIsLocked:
    "Ce fichier est en cours d'édition par {name}. Veuillez patienter ou actualiser la page pour réessayer.",
  cannotDeleteFileInUse:
    "Impossible de supprimer -- fichier en cours d'utilisation.",
  cannotDeleteFileJobsInProcessing:
    'Impossible de supprimer le dossier car certains dossiers sont en cours de traitement. Veuillez réessayer plus tard.',
  downloadAllJobFailure:
    "Une erreur inattendue s'est produite lors du téléchargement de tous les fichiers de sortie. Veuillez réessayer plus tard.",
  downloadAllComplete: 'Télécharger Tous complétés.',
  editNameTDOSuccess: 'Nom TDO mis à jour avec succès.',
  editNameTDOFailed: 'La mise à jour TDO a échoué.',

  noRedactionsTitle: 'Aucun caviardage sélectionné',
  noRedactionsText:
    "Aucun caviardage n'a été sélectionné pour ce dossier. Si c'est le cas, veuillez confirmer pour continuer.",
  detectHeadObjects: 'Tête et objets',
  detectPersonObjects: 'Personne et Objets',

  undo: 'Annuler la dernière action',
  redo: "Rétablir l'action précédente",
  cannotUndo: 'Ne peut pas annuler la dernière action',
  cannotRedo: "Ne peut pas rétablir l'action précédente",
  groupingTools: 'Outils de Groupement',
  mergeUnmergeSelect: 'Fusionner/Défusionner la Sélection',
  mergeUnmerge: 'Fusionner/Défusionner',
  selectGroupRange: 'Sélectionner une Plage de Groupes',

  processTranscriptionSuccess: 'La transcription est terminée.',
  mergedGroup: 'Groupe fusionné',
  mixed: 'Mélangé',
  allSegments: 'Tous les segments',
  pinToTop: 'Épingler en haut',
  unpin: 'Désépingler',
  mergeWithPinnedGroup: 'Fusionner avec le groupe épinglé',
  mergeWithNamedGroup: 'Fusionner avec un groupe nommé',
  merge: 'Fusionner',
  unmerge: 'Défusionner',
  clear: 'Effacer',
  select: 'Sélectionner',
  deselect: 'Désélectionner',
  unMergeFromGroup: 'Dissocier du groupe',
  selectSegment: 'Sélectionner un segment',
  deselectSegment: 'Désélectionner un segment',
  deleteSegment: 'Supprimer un segment',
  selected: 'Sélectionné',
  and: 'et',
  group: 'Groupe',
  segment: 'Segment',
  apply: 'Appliquer',
  groupNameEnterText: 'Saisir un nom de groupe',
  mergeConfirm: 'Confirmation de fusion',
  unmergeConfirm: 'Confirmation de la désunion',
  mergeConfirmText:
    "Création d'un nouveau groupe fusionné. Veuillez confirmer le nom du groupe :",
  mergeMultipleText:
    'La sélection comprend plusieurs groupes nommés ! Veuillez sélectionner un nom pour le groupe fusionné :',
  unmergeConfirmText:
    'Déplacement du(des) segment(s) vers un nouveau groupe. Veuillez confirmer le nom du groupe.',
  labelTextfield: 'Entrez un nom de groupe',
  mergeTextBox: 'Groupe fusionné',
  rotationPreview: 'Aperçu de la rotation',
  rotateConfirmTitle: 'Confirmation de la rotation',
  rotateConfirmDes:
    "Attention ! La rotation supprimera tous les UDR existants et les objets détectés ! \nVous pourrez reprendre l'édition après le traitement de la vidéo.",
  selectDetectOption:
    "Sélectionnez les types d'objets que vous souhaitez détecter.",
  rotateVideoSuccess: 'Traitement de la rotation vidéo.',
  rotateVideoFailure:
    "Une erreur inattendue s'est produite lors de la rotation de la vidéo.",
  errorFetchingAuditLog:
    "Erreur inattendue lors de la récupération des journaux d'audit. Veuillez réessayer.",
  errorFetchingTDOState: "Erreur lors de la récupération de l'état TDO.",
  errorFetchingGovQAIntegrationInfo:
    "Impossible de récupérer les informations d'intégration GovQA.",
  missingGovQAIntegrationConfig:
    "La configuration de l'intégration GovQA est incomplète.",
  errorFetchingExternalIntegrationInfo:
    "Impossible de récupérer les informations d'intégration externe.",
  missingExternalIntegrationConfig:
    "La configuration de l'intégration externe est incomplète.",
  errorFetchingFOIAXpressIntegrationInfo:
    "Impossible de récupérer les informations d'intégration FOIAXpress.",
  missingFOIAXpressIntegrationConfig:
    "La configuration de l'intégration FOIAXpress est incomplète.",
  errorFetchingCasepointIntegrationInfo:
    "Impossible de récupérer les informations d'intégration Casepoint.",
  missingCasepointIntegrationConfig:
    "La configuration de l'intégration Casepoint est incomplète.",
  errorFetchingExterroIntegrationInfo:
    "Impossible de récupérer les informations d'intégration Exterro.",
  missingExterroIntegrationConfig:
    "La configuration de l'intégration Exterro est incomplète.",
  errorFetchingNuixIntegrationInfo:
    "Impossible de récupérer les informations d'intégration Nuix.",
  missingNuixIntegrationConfig:
    "La configuration de l'intégration Nuix est incomplète.",
  errorUnknown: 'Erreur inconnue.',
  errorSendingFile:
    "Impossible d'envoyer le fichier. Veuillez contacter votre administrateur.",
});

export default {
  [LOCALES.FRENCH]: {
    ...getCommonMessages(),
  },
  [LOCALES.REDACT_FRENCH]: {
    ...getCommonMessages(),
    // Override common values
    caseActions: "Demande d'actions",
    casesCreated: 'Demandes créées',
    casesApproved: 'Demandes approuvées',
    casesReopened: 'Réouverture des demandes',
    caseName: 'Nom de la demande',
    caseNameRequired: 'Nom de la demande requis',
    caseDeleted: 'Demande supprimée',
    caseArchived: 'Demande archivée',
    caseDashboard: 'Tableau de bord des demandes',
    archiveCase: "Demande d'archives",
    deleteCase: 'Demande de suppression',
    deleteCaseConfirmDialog:
      'Êtes-vous sûr de vouloir supprimer cette demande ? Une fois supprimée, la demande ne peut être récupérée.',
    archiveCaseContent:
      "Êtes-vous sûr de vouloir archiver cette demande ? Une fois archivée, la demande ne sera plus visible sur le tableau de bord, sauf si l'option 'Afficher tout' est activée.",
  },
};

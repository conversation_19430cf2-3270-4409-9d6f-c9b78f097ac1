### Steps to create comments schema using core-graphql queries

1. Create new data registry
```
mutation createDataRegistry {
  createDataRegistry(input: {
    id: "607a873f-cad5-483f-be6c-c606cce340af"
    name: "Redact Notification"
    description: "Schema for notification of Redact App"
    source: "field deprecated"
  }) {
    id
  }
}
```

2. Create Schema Draft
```
# Note: Use the `dataRegistryId` value from the `createDataRegistry` mutation.
mutation createSchemaDraft {
  upsertSchemaDraft(
    input: {
      dataRegistryId: "607a873f-cad5-483f-be6c-c606cce340af"
      majorVersion: 1
      schema: {
        type: "object"
        title: "Redact Notification"
        properties: {
          jobId: { type: "string" }
          sdoId: { type: "string" }
          tdoId: { type: "string" }
          status: { type: "string" }
          userId: { type: "string" }
          deleted: { type: "boolean" }
          tdoName: { type: "string" }
          engineName: { type: "string" }
          tdoModifiedTime: { type: "string" }
        }
        description: "Notification for Redact App"
      }
    }
  ) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}
```

3. Publish Schema Draft

```
# Note: Pass in the Schema ID for the `id` value
mutation publishSchemaDraft($id: ID!) {
  updateSchemaState(input: {
    id: $id
    status: published
  }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```


import { orderBy, sumBy } from 'lodash';
import { useMemo } from 'react';

import { selectSortBy } from '@common-modules/mediaDetails/selectors';
import { ClusterItem } from '@common-modules/mediaDetails/models';

// TODO This can be optimized
export const useSort = (
  clusterList: ReadonlyArray<ClusterItem>,
  sortBy: ReturnType<typeof selectSortBy>
) =>
  useMemo(() => {
    if (sortBy.column === 'startTimeMs') {
      return orderBy(
        clusterList,
        ['type', (c) => c.segments[0]?.startTimeMs],
        ['asc', sortBy.direction]
      );
    } else if (sortBy.column === 'autoInterp') {
      return orderBy(
        clusterList,
        ['type', (c) => sumBy(c.segments, 'numAutoInterpolations')],
        ['asc', sortBy.direction]
      );
    } else if (sortBy.column === 'manualInterp') {
      return orderBy(
        clusterList,
        ['type', (c) => sumBy(c.segments, 'isManualInterpolation')],
        ['asc', sortBy.direction]
      );
    }
    return clusterList;
  }, [clusterList, sortBy.column, sortBy.direction]);

import { size, reduce } from 'lodash';
import { DeletedOverlaysCache } from '../store.models';

export const DELETE_OVERLAY_CACHE_INTERVAL = 10000;

// removes expired overlay and group ids from DeletedOverlaysCache instance
// based on DELETE_OVERLAY_CACHE_INTERVAL
export const cleanDeleteOverlayCache = (
  deletedOverlaysCache: DeletedOverlaysCache
): DeletedOverlaysCache => {
  let { overlayIdsMap, overlaySubsegmentIdsMap } = deletedOverlaysCache;

  if (size(overlayIdsMap) > 0) {
    const currentTime = new Date().getTime();

    overlayIdsMap = reduce<typeof overlayIdsMap, { [key: string]: number }>(
      overlayIdsMap,
      (acc, timestamp, id) => {
        // has DELETE_OVERLAY_CACHE_INTERVAL passed since it was deleted
        if (timestamp + DELETE_OVERLAY_CACHE_INTERVAL > currentTime) {
          acc[id] = timestamp;
        }

        return acc;
      },
      {}
    );
  }

  if (size(overlaySubsegmentIdsMap) > 0) {
    const currentTime = new Date().getTime();

    overlaySubsegmentIdsMap = reduce<
      typeof overlaySubsegmentIdsMap,
      { [key: string]: number }
    >(
      overlaySubsegmentIdsMap,
      (acc, timestamp, id) => {
        if (timestamp + DELETE_OVERLAY_CACHE_INTERVAL > currentTime) {
          acc[id] = timestamp;
        }

        return acc;
      },
      {}
    );
  }

  return {
    overlayIdsMap,
    overlaySubsegmentIdsMap,
  };
};

import { CreateRootFolderResponse } from '../model/responses';
import { callGQL } from '../api/callGraphql';
import { createRootFolderQuery } from '../api/queries';
import { Messages } from '../errors/messages'
import { Logger } from '../logger';
import { RequestHeader } from '../model/requests';

export const createRootFolderAdapter = async (
  headers: RequestHeader,
) => {
  const id = await callGQL<CreateRootFolderResponse>(
    headers,
    createRootFolderQuery,
  )
    .then((res) => res.createRootFolder?.id)
    .catch((err) => {
      Logger.error(Messages.createRootFolder + JSON.stringify(err));
    });
  return id;
};

import { defaultState } from '../store';
import { applyPatches, produce } from 'immer';
import { GetActionCreatorPayloadT } from '@utils';
import { CaseReducer, createReducer } from '@reduxjs/toolkit';
import * as actions from '@common-modules/mediaDetails/actions';
import {
  CANCEL_JOB,
  PROCESS_ENGINE_REQUEST_SUCCESS,
} from '@common-modules/engines/optical-tracking/actions';

type Re<P = unknown> = CaseReducer<
  typeof defaultState,
  { payload: P; type: string }
>;

export const onUndoAction: Re = (state) =>
  produce(state, (draft) => {
    if (draft.history.past.length) {
      const undoAction = draft.history.past.pop();
      if (undoAction !== undefined) {
        if (undoAction !== null && 'inversePatches' in undoAction) {
          const { inversePatches } = undoAction;
          applyPatches(draft, inversePatches);
        }
        draft.history.future.push(undoAction);
        draft.tdoIsChanged = true;
      }
    }
  });

const onRedoAction: Re = (state) =>
  produce(state, (draft) => {
    if (draft.history.future.length) {
      const redoAction = draft.history.future.pop();
      if (redoAction !== undefined) {
        if (redoAction !== null && 'patches' in redoAction) {
          const { patches } = redoAction;
          applyPatches(draft, patches);
        }
        draft.history.past.push(redoAction);
        draft.tdoIsChanged = true;
      }
    }
  });

/* Add an undo placeholder so keep up to date with web worker */
const onPatchEmptyUndo: Re<
  GetActionCreatorPayloadT<typeof actions.PATCH_EMPTY_UNDO_ACTION>
> = (state, { payload: { clearFuture } }) =>
  produce(state, (draft) => {
    draft.history.past.push(null);

    if (clearFuture) {
      draft.history.future = [];
    }
  });

const onRemoveEngineJobHistory: Re<
  GetActionCreatorPayloadT<typeof PROCESS_ENGINE_REQUEST_SUCCESS>
> = (state, { payload }) =>
  produce(state, (draft) => {
    draft.history.past = draft.history.past.filter(
      (history) =>
        !(
          history !== null &&
          'inverseAction' in history &&
          'jobId' in history.inverseAction.payload &&
          history.inverseAction.creator === CANCEL_JOB &&
          history.inverseAction.payload.jobId === payload.jobId
        )
    );
    draft.history.future = draft.history.future.filter(
      (history) =>
        !(
          history !== null &&
          'inverseAction' in history &&
          'jobId' in history.inverseAction.payload &&
          history.inverseAction.creator === CANCEL_JOB &&
          history.inverseAction.payload.jobId === payload.jobId
        )
    );
  });

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(actions.PATCH_UNDO_ACTION, onUndoAction)
    .addCase(actions.PATCH_REDO_ACTION, onRedoAction)
    .addCase(actions.PATCH_EMPTY_UNDO_ACTION, onPatchEmptyUndo)
    .addCase(PROCESS_ENGINE_REQUEST_SUCCESS, onRemoveEngineJobHistory);
});

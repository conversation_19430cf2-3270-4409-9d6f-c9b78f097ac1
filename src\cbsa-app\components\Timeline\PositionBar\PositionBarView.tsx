import { forwardRef, Ref, useImperativeHandle, useRef } from 'react';

import { formatTime } from '../Playhead/renderer/utils';
import * as styles from './styles.scss';

// import { START_DRAG_AND_DROP_POSITION_BAR } from '@redact-state/modules/mediaDetails';

const PositionBarView = (
  { currentTime }: PositionBarViewPropTypes,
  ref: Ref<HTMLDivElement>
) => {
  const divRef = useRef<HTMLDivElement>(null);
  useImperativeHandle(ref, () => divRef.current!); // TODO: Why is this safe? Is this even the right hook usage?
  return (
    <div
      ref={divRef}
      className={styles.wrapper}
      data-testid="timeline-position-bar"
    >
      <div className={styles.time}>
        {formatTime(currentTime, 3)}
        <div />
      </div>
      <div className={styles.content}>
        <div className={styles.handle} />
        <div className={styles.triangle} />
        <div className={styles.position} />
      </div>
    </div>
  );
};

export default forwardRef(PositionBarView);

export interface PositionBarViewPropTypes {
  readonly currentTime: number;
}

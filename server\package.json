{"name": "redact-api", "version": "1.0.0", "description": "Rest api for redact", "main": "index.js", "repository": "https://github.com/veritone/redact-app", "license": "MIT", "scripts": {"build": "tsc --project ./", "start:dev": "ts-node-dev --files src/server.ts", "debug": "ts-node-dev --files --inspect-brk -- server/index.ts", "start:prod": "node dist/server.js", "lint": "eslint . && yarn run lint:tsc", "lint:ts": "eslint \"./src/**/*.{ts,tsx}\" --max-warnings 0", "lint:tsc": "tsc -p tsconfig.json --noEmit --checkJs false", "lint:fix": "eslint . --fix", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@veritone/core-logger": "^1.0.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.2", "graphql": "^16.10.0", "graphql-request": "^6.1.0", "http": "^0.0.1-security", "http-errors": "^2.0.0", "https": "^1.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "openapi-types": "^12.1.3", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/http-errors": "^2.0.5", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.19", "@types/node": "^22.15.33", "@types/swagger-ui-express": "^4.1.8", "eslint": "^9.29.0", "jest": "^29.7.0", "supertest": "^7.1.1", "ts-essentials": "^10.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsc-files": "^1.1.4", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0"}, "resolutions": {"semver": "^7.5.3", "@types/express": "^4.17.21"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}}
### Steps to create settings profile schema using core-graphql queries

1. Create new data registry 
```
mutation createSettingsProfileDataRegistry {
  createDataRegistry(input: {
    id: "7ffb8993-1c0b-4210-a2f8-b407159a51f1"
    name: "redact-settings-profile"
    description: "Redact Settings Profile"
    source: "field deprecated"
  }) {
    id
  }
}

```

2. Create Schema Draft
```
# Note: Use the `dataRegistryId` value from the `createDataRegistry` mutation.
mutation createSettingsProfileSchemaDraft {
  upsertSchemaDraft(input: {
      dataRegistryId: "7ffb8993-1c0b-4210-a2f8-b407159a51f1",
      majorVersion: 1,
      schema: {
        type: "object",
        title: "redact-settings-profile-schema",
        description: "Redact Settings Profile",
        required: [
          "profileName",
          "createdBy",
          "modifiedBy",
          "settings"
        ],
        properties: {
          profileName: {
            type: "string",
          },
          isDefault: {
            type: "boolean",
          },
          createdBy: {
            type: "string"
          },
          modifiedBy: {
            type: "string"
          },
          settings: {
            type: "object",
          },
        },            
      },
  },
  ) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}
    
```

3. Publish Schema Draft

```
# Note: Pass in the Schema ID for the `id` value
mutation publishSchemaDraft($id: ID!) {
  updateSchemaState(input: {  id: $id , status: published }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```


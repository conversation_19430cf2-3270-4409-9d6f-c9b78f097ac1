const checkCheckbox = (selector: string, shouldCheck: boolean) => {
  cy.get(selector)
    .find('[type="checkbox"]')
    .then(($checkbox) => {
      const isChecked = $checkbox.prop('checked');
      if (shouldCheck && !isChecked) {
        return cy.wrap($checkbox).check();
      } else if (!shouldCheck && isChecked) {
        return cy.wrap($checkbox).uncheck();
      } else {
        return cy.wrap($checkbox);
      }
    });
};

Cypress.Commands.add('OpenUploadFileModal', () => {
  cy.get('[data-testid="main-upload-file-button"]')
    .should('be.visible')
    .click();
  cy.get(`[data-testid="filePickerBody"]`).should('be.visible');
  cy.contains('Run Head and Object Detection').should('be.visible');
  cy.contains('Run Transcription').should('be.visible');
});

Cypress.Commands.add('SelectFile', ({ videoName }: { videoName: string }) => {
  cy.fixture(videoName, 'binary', { timeout: 120000 })
    .then(Cypress.Blob.binaryStringToBlob)
    .then((fileContent) => {
      cy.get('input[type="file"]').attachFile({
        fileContent,
        fileName: videoName,
        mimeType: 'video/mp4',
        encoding: 'utf8',
      });
      return;
    });
  cy.get('[data-testid="itemNameText"]').should('contain', videoName);
});

Cypress.Commands.add('ClickToUpload', () => {
  cy.get('[data-veritone-element="picker-footer-Upload-button"]').click();
  cy.get('[data-test="upload-ProgressDialog"]').should('be.visible');
  cy.get('[data-test="upload-ProgressDialog"]').should('not.exist');
});

Cypress.Commands.add('CheckTDOWrapperOfNewUploadFile', () => {
  cy.get('[data-testId="mainpage-medialist"]').within(() => {
    const checkProcessing = () => {
      cy.get('[data-testid="tdo-wrapper"]').eq(0).as('tdoWrapper');
      cy.get('@tdoWrapper')
        .find('[data-test="tdo-wrapper-processing-text"]')
        .invoke('text')
        .then((processingText) => {
          console.log('processingText', processingText);
          if (processingText === 'Processing New Media...') {
            cy.get('@tdoWrapper')
              .invoke('attr', 'data-tdo-id')
              .as('tdoIdValue');
          } else {
            checkProcessing();
          }
          return;
        });
    };
    checkProcessing();
  });
});

Cypress.Commands.add(
  'UploadFileWithoutEngine',
  ({ videoName }: { videoName: string }) => {
    cy.OpenUploadFileModal();
    cy.SelectFile({ videoName });
    checkCheckbox('[data-testid="checkbox-run-head"]', false);
    checkCheckbox('[data-testid="checkbox-run-person"]', false);
    checkCheckbox('[data-test="checkbox-run-transcription"]', false);
    cy.ClickToUpload();
    cy.CheckTDOWrapperOfNewUploadFile();
  }
);

Cypress.Commands.add(
  'UploadFileWithEngine',
  ({
    videoName,
    transcriptionState,
  }: {
    videoName: string;
    transcriptionState?: string;
  }) => {
    cy.OpenUploadFileModal();
    cy.SelectFile({ videoName });

    if (transcriptionState) {
      const states = transcriptionState
        .split(',')
        .map((state) => state.trim())
        .filter((state) => state !== '');
      checkCheckbox(
        '[data-testid="checkbox-run-head"]',
        states.includes('head')
      );
      checkCheckbox(
        '[data-testid="checkbox-run-person"]',
        states.includes('person')
      );
      checkCheckbox(
        '[data-test="checkbox-run-transcription"]',
        states.includes('transcription')
      );
    } else {
      // Old logic: Check all three checkboxes if transcriptionState is not provided
      cy.get('[data-testid="checkbox-run-head"]')
        .find('[type="checkbox"]')
        .check();
      cy.get('[data-testid="checkbox-run-person"]')
        .find('[type="checkbox"]')
        .check();
      cy.get('[data-test="checkbox-run-transcription"]')
        .find('[type="checkbox"]')
        .check();
    }

    cy.ClickToUpload();
    cy.CheckTDOWrapperOfNewUploadFile();
  }
);

Cypress.Commands.add(
  'CheckJobComplete',
  ({ videoName, jobName }: { videoName: string; jobName: string }) => {
    cy.get('[data-veritone-element="notification-button"]')
      .should('be.visible')
      .click();
    cy.get('[data-test="entry"]')
      .eq(0)
      .should('be.visible')
      .within(($ele) => {
        cy.wrap($ele).contains(videoName).should('be.visible');
        cy.wrap($ele).contains(jobName).should('be.visible');
        let check = true;
        while (check) {
          cy.wrap($ele)
            .children('div')
            .last()
            .children('div')
            .first()
            .invoke('text')
            .as('status');
          cy.get('@status')
            .invoke('text')
            .then((ele) => {
              if (ele === 'complete') {
                check = false;
              } else if (ele === 'failed') {
                cy.log(`${jobName} failed!`);
                check = false;
              } else {
                cy.wait(30000); // eslint-disable-line cypress/no-unnecessary-waiting
              }
              return;
            });
        }
      });

    cy.get('[data-test="notificationWindow"]')
      .children('[data-test="header"]')
      .within((ele) => {
        cy.wrap(ele).get('button').should('be.visible').click();
      });
  }
);

Cypress.Commands.add(
  'CheckJobCompleteDashboard',
  ({ videoName, jobName }: { videoName: string; jobName: string }) => {
    cy.LoginLandingPage();
    cy.get('img[id="notificationImage"]')
      .should('be.visible')
      .as('notification');
    cy.get('@notification').click();

    cy.get('div[title="Notifications"]')
      .should('be.visible')
      .children('div')
      .last()
      .as('notificationContainer');

    cy.get('@notificationContainer').within(($ele) => {
      cy.wrap($ele).get('div').children().first().as('notificationStatus');
    });

    cy.get('@notificationStatus')
      .should('be.visible')
      .within(($ele) => {
        cy.wrap($ele).contains(videoName).should('be.visible');
        cy.wrap($ele).contains(jobName).should('be.visible');
        cy.get('div[title="status"]').invoke('text').should('eq', 'complete');
      });
    cy.getDataIdCy({ idAlias: 'ArrowDropUpIcon' }).click();
  }
);

import {
  Before,
  Given,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import {
  Colors,
  DataTestSelector,
  DataVeritoneSelector,
  MediaFieldName,
  RedactionEffect,
  SettingsTabName,
  VideoResult,
} from '../../../support/helperFunction/mediaDetailHelper';

import '../common/mediaPlayerCommonStep';

Before(() => {
  cy.LoginLandingPage();
});

Given('The user opens the media details settings', () => {
  cy.get(
    `[data-veritone-element="${DataVeritoneSelector.MediaSettingsBtn}"]`
  ).click();
});

Then('The user selects {string} tab', (tabName: SettingsTabName) => {
  cy.getDataIdCy({ idAlias: 'global-settings-modal-dialog-content' }).within(
    () => {
      cy.getByRoles('tab').contains(tabName).click();
    }
  );
});

When('The user saves the settings', () => {
  cy.getDataIdCy({
    idAlias: DataTestSelector.InterpolationSettingsModalSaveBtn,
  })
    .should('be.visible')
    .then(($button) => {
      const isEnabled = !$button.prop('disabled');
      if (isEnabled) {
        cy.getDataIdCy({
          idAlias: DataTestSelector.InterpolationSettingsModalSaveBtn,
        }).click();
        cy.getDataIdCy({
          idAlias: DataTestSelector.GlobalSettingsModalTitle,
        }).should('not.exist');
      } else {
        cy.getDataIdCy({
          idAlias: DataTestSelector.InterpolationSettingsModalCancelBtn,
        }).click();
      }
      return;
    });
});

When(
  'The user sees {string} in group name at the right panel',
  (name: string) => {
    if (name.toUpperCase() === 'PLATE' || name.toUpperCase() === 'VEHICLE') {
      cy.getDataIdCy({ idAlias: 'clusterList-row' })
        .contains(name.toUpperCase())
        .parents('[data-testid="clusterList-row"]')
        .within(() => {
          cy.getDataIdCy({ idAlias: 'time-period-span' })
            .should('be.visible')
            .click();
          cy.getDataIdCy({ idAlias: 'check-box-container' })
            .find(':first-child')
            .first()
            .click();
        });
    }
    return;
  }
);

When(
  'The user clicks on {string} radio in Overlay Preview',
  (previewName: string) => {
    cy.get(
      `[aria-label="Overlay Preview"] label:contains("${previewName}")`
    ).click();
  }
);

Then(
  'The user draws an {string} at coordinates {int}, {int}, {int}, {int}',
  (type: string, x1: number, y1: number, x2: number, y2: number) => {
    if (type.toLowerCase() === 'udr') {
      cy.DrawAnUdr({ orderNumber: 0, x1, y1, x2, y2 });
      cy.contains('p', `${VideoResult.OverlayGroup} 1`).should('be.visible');
    }
  }
);

Then(
  'The user sees {string} for {string} {int} should have {string} color',
  (
    cssElement: string,
    redactionType: string,
    udrOrder: number,
    colorValue: Colors
  ) => {
    cy.get(
      `.react-draggable[data-testid="rnd-box"][type="${redactionType}"]:eq(${udrOrder - 1})`
    ).within(() => {
      cy.get('[data-testid^="rnd-box-"]').should(
        'have.css',
        cssElement,
        colorValue
      );
    });
  }
);

When(
  'The user sets {string} level to {int} in Settings',
  (blurLevelName: string, blurLevel: number) => {
    cy.get(
      `[data-testid="${blurLevelName}-blur-level-slider-container"]`
    ).within(() => {
      cy.get('input[type="number"]').clear();
      cy.get('input[type="number"]').type(`${blurLevel}`);
    });
  }
);
Then(
  'Item {int} type {string} should have blur attribute',
  (udrOrder: number, type: string) => {
    cy.get(
      `.react-draggable[data-testid="rnd-box"][type="${type}"]:eq(${udrOrder - 1})`
    ).within(() => {
      cy.get('[data-testid^="rnd-box-"]').should(($el) => {
        const filterValue = $el.css('backdrop-filter');
        expect(filterValue).to.match(/blur\(\d+px\)/);
      });
    });
  }
);

When('The user scroll to {string} list menu', (menuName: string) => {
  cy.contains('tr', `${menuName}`).scrollIntoView();
  cy.contains('tr', `${menuName}`).should('be.visible');
});

When('The user opens menu pop-up of {string} shape', (type: string) => {
  cy.get(`[data-testid="${type}-shape-type-container"]`).within(() => {
    cy.get('[data-testid="feather-type-select-container"]').click();
  });
});

When('The user changes to {string}', (value: string) => {
  cy.get('ul[role="listbox"]').should('be.visible');
  cy.get('ul[role="listbox"] li[aria-selected="true"]')
    .should('exist')
    .invoke('text')
    .then(() => cy.contains('ul[role="listbox"] li', `${value}`).click());
});

Then(
  'The shape of the {string} {int} is Ellipse',
  (type: string, udrOrder: number) => {
    cy.get(
      `.react-draggable[data-testid="rnd-box"][type="${type}"]:eq(${udrOrder - 1})`
    ).within(() => {
      cy.get('[data-testid^="rnd-box-"]').should(($el) => {
        const filterValue = $el.css('border-radius');
        expect(filterValue).to.match(/50%/);
      });
    });
  }
);

When(
  'The user selects {string} from {string} dropdown in Redaction Video',
  (redactionEffect: RedactionEffect, redactionType: string) => {
    cy.getDataIdCy({
      idAlias: `${redactionType}-redaction-type-container`,
    }).within(() => {
      cy.getDataIdCy({ idAlias: `video-redaction-select-container` }).click();
    });
    cy.get('li').contains(redactionEffect).should('be.visible').click();
  }
);

When('The user opens filter menu', () => {
  cy.get('[data-testid="cluster-list-filter-container"]').within(() => {
    cy.get('[data-testid="filter-button"]').click();
  });
});

When('The user selects {string} in filter menu', (filterName: string) => {
  cy.get(`[data-testid="${filterName}-filter-show-checkbox"]`).click();
});

When('The user closes the filter menu', () => {
  cy.get('body').type('{esc}');
  cy.get('[data-testid="cluster-list-filter-menu-popover"]').should(
    'not.exist'
  );
});

When(
  'The user clicks on the time period of {string} in the cluster list',
  (name: string) => {
    if (name.toUpperCase() !== 'PLATE' && name.toUpperCase() !== 'VEHICLE') {
      cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow })
        .first()
        .within(() => {
          cy.getDataIdCy({ idAlias: DataTestSelector.TimePeriodSpan })
            .should('be.visible')
            .click();
        });
      cy.get('[data-testid="cluster-group-items-container"]')
        .should('be.visible')
        .first()
        .within(() => {
          cy.get('[data-testid="check-box-container"]').then(($container) => {
            const isChecked =
              $container.find('[data-testid="checkbox-svg"]').length > 0;
            if (!isChecked) {
              return cy.get('[data-testid="check-box-container"] > *').click();
            }
            return;
          });
        });
    }
    return;
  }
);

When(
  'The user input {string} in the {string} interpolation',
  (value: string, interpolationType: string) => {
    cy.get(
      `[data-testid="${interpolationType}-interpolation-type-container"]`
    ).within(() => {
      cy.get('input[type="number"]').clear();
      cy.get('input[type="number"]').type(`${value}`);
    });
  }
);

Then('The user saves interpolation settings', () => {
  cy.get(
    '[data-testid="interpolation-duration-setting-modal-save-button"]'
  ).click();
});

When('The user changes video type to {string}', (videoType: string) => {
  cy.get('[data-testid="render-radio-group"]').contains(videoType).click();
});

When('The user sets face Confidence threshold to {string}', (value: string) => {
  cy.get('[data-testid="face-detection-threshold-slider-container"]')
    .find('input[type="number"]')
    .clear();
  cy.get('[data-testid="face-detection-threshold-slider-container"]')
    .find('input[type="number"]')
    .type(value);
});

When(
  'The user changes cluster similarity threshold to {string}',
  (threshold: string) => {
    cy.get('[data-testid="cluster-sim-threshold-select-container"]').click();
    cy.get('ul[role="listbox"]')
      .should('be.visible')
      .contains(threshold)
      .click();
  }
);

Then(
  'The user sets tracking {string} limit to {string}',
  (direction: string, value: string) => {
    cy.get(`[data-testid="tracking-${direction}-limit-sec-container"]`)
      .find('input[type="number"]')
      .clear();
    cy.get(`[data-testid="tracking-${direction}-limit-sec-container"]`)
      .find('input[type="number"]')
      .type(value);
  }
);

When(
  'The user opens menu for {string} {int}',
  (type: string, udrOrder: number) => {
    cy.get('[data-test="overlay-container"]').click({ force: true });
    cy.get('[data-test="overlay-container"]').then(() => {
      cy.get(
        `.react-draggable[data-testid="rnd-box"][type="${type}"]:eq(${udrOrder - 1})`
      ).rightclick('right', { force: true });
      return;
    });
  }
);

When('The user clicks on {string} in menu', (udrMenuValue: string) => {
  cy.get('ul').find(`li:contains(${udrMenuValue})`).click();
});

Then('The user saves new color', () => {
  cy.get('[data-testid="add-redaction-code-menu"]').within(() => {
    cy.get('button[type="button"]:contains("Save")').click();
  });
});

When('The user opens menu pop-up for {string}', (type: string) => {
  cy.get(`[data-testid="${type}-type-container"]`).within(() => {
    cy.get('#mui-component-select-audioType').click();
  });
});

When('The user runs transcription', () => {
  cy.get('body').then(($body) => {
    if (
      $body.find('[data-testid="faces-null-state-transcription-button"]').length
    ) {
      cy.get('[data-testid="faces-null-state-transcription-button"]')
        .should('exist')
        .as('transcriptionButton')
        .click({ force: true });
    }
    return;
  });
});

Then('The user plays the video', () => {
  cy.get('#video-default-control-bar > *').within(() => {
    cy.get('.video-react-play-control').click();
  });
});

When(
  'The user sets {string} to {int} in Interpolation settings',
  (fieldName: MediaFieldName, value: number) => {
    let selector: string;
    let alias: string;

    switch (fieldName) {
      case MediaFieldName.AutoField:
        selector = '[name="autoInterpolationMax"]';
        alias = 'auto';
        break;
      case MediaFieldName.Manual:
        selector = '[name="manualInterpolationMax"]';
        alias = 'manual';
        break;
      case MediaFieldName.Offset:
        selector = '#videoOffset';
        alias = 'offset';
        break;
      default:
        throw new Error(`Invalid field name: ${fieldName}`);
    }

    cy.get(selector).as(alias);
    cy.get(`@${alias}`).clear();
    cy.get(`@${alias}`).type(value.toString());
  }
);

Then(
  'The {string} value should be {int} in Interpolation settings',
  (fieldName: string, value: number) => {
    let alias: string;

    switch (fieldName) {
      case MediaFieldName.AutoField:
        alias = 'auto';
        break;
      case MediaFieldName.Manual:
        alias = 'manual';
        break;
      case MediaFieldName.Offset:
        alias = 'offset';
        break;
      default:
        throw new Error(`Invalid field name: ${fieldName}`);
    }

    cy.get(`@${alias}`).should('have.value', value.toString());
  }
);

Then('The media details setting is closed', () => {
  cy.getDataIdCy({
    idAlias: DataTestSelector.GlobalSettingsModalTitle,
  }).should('not.exist');
});

Then('The detection quantity should be {int}', (value: number) => {
  cy.get('[data-test="detection-quantity"]')
    .first()
    .should('have.text', value.toString());
});

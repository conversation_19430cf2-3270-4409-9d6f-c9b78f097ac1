import { union } from 'lodash';
import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import type { Time } from 'veritone-types';
import * as Services from '../services';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { all, put, select, takeLatest } from 'typed-redux-saga/macro';
import { actionLogAuditEvent, basicUserQueryIsSupported } from '../actions';
import {
  selectCommentUsers,
  selectIsBasicUserQuerySupported,
} from '@common-modules/mediaDetails';

export function* fetchMediaComments() {
  yield* takeLatest(Actions.FETCH_MEDIA_COMMENTS, function* ({ payload }) {
    const { tdoId, limit, offset } = payload;
    yield* put(Services.fetchMediaComments({ tdoId, limit, offset }));
  });
}

export function* fetchMediaCommentsSuccess() {
  yield* takeLatest(
    Actions.FETCH_MEDIA_COMMENTS_SUCCESS,
    function* ({
      payload: {
        structuredDataObjects: { records },
      },
    }) {
      const users = yield* select(selectCommentUsers);
      const userIds = records.reduce<string[]>(
        (prev, { data: { createdBy } }) => union(prev, [createdBy]),
        []
      );

      yield* all(
        userIds.map(
          (userId: string) =>
            !users[userId] && put(Actions.fetchMediaCommentsUser(userId))
        )
      );
    }
  );
}

export function* updateComment() {
  yield* takeLatest(Actions.UPDATE_COMMENT, function* ({ payload }) {
    const { comment, modify, isUndo } = payload;
    yield* put(Services.updateComment({ comment, modify, isUndo }));
    yield* put(
      actionLogAuditEvent(
        `${
          !comment.done
            ? 'New Comment'
            : comment.archived
              ? 'Hide Comment'
              : 'Update Comment'
        } ${JSON.stringify(comment)}`
      )
    );
  });
}

export function* updateCommentSuccess() {
  yield* takeLatest(Actions.UPDATE_COMMENT_SUCCESS, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'successfullySavedComment',
          defaultMessage: 'Successfully saved comment.',
        }),
        variant: 'success',
      })
    );
  });
}

export function* updateCommentFailure() {
  yield* takeLatest(Actions.UPDATE_COMMENT_FAILURE, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'failedToSaveComment',
          defaultMessage: 'Failed to save comment.',
        }),
        variant: 'error',
      })
    );
  });
}

export function* fetchMediaCommentsUser() {
  yield* takeLatest(Actions.FETCH_MEDIA_COMMENTS_USER, function* ({ payload }) {
    const isBasicUserQuerySupported = yield* select(
      selectIsBasicUserQuerySupported
    );

    if (isBasicUserQuerySupported) {
      const { userId } = payload;
      yield* put(Services.fetchMediaCommentsUser({ userId }));
    }
  });
}

export interface VeritoneError {
  readonly message: string;
  readonly name: string;
  readonly time_thrown: Time;
  readonly path: string[];
  readonly location: { line: number; column: number }[];
  readonly data: {
    field: string;
    rightsGranted: string[];
    rightsRequired: string[];
    type: string;
  };
}

export function* fetchMediaCommentsUserFailure() {
  yield* takeLatest(
    Actions.FETCH_MEDIA_COMMENTS_USER_FAILURE,
    function* ({ payload }) {
      const { message } = payload[0] || { message: 'Unknown Error' };
      if (message === 'Cannot query field "basicUserInfo" on type "Query".') {
        yield* put(basicUserQueryIsSupported());
      } else {
        const intl = sagaIntl();
        yield* put(
          enqueueSnackbar({
            message: intl.formatMessage({
              id: 'failedToFetchUser',
              defaultMessage: 'Failed to fetch user.',
            }),
            variant: 'error',
          })
        );
      }
    }
  );
}

export function* fetchMediaUser() {
  yield* takeLatest(Actions.FETCH_MEDIA_USER, function* () {
    yield* put(Services.fetchMediaUser());
  });
}

export function* fetchMediaUserFailure() {
  yield* takeLatest(Actions.FETCH_MEDIA_USER_FAILURE, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'failedToFetchUser',
          defaultMessage: 'Failed to fetch user.',
        }),
        variant: 'error',
      })
    );
  });
}

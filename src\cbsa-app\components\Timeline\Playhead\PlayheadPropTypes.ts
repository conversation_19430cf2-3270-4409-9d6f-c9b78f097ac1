import * as React from 'react';
import { TimelinePropTypes } from '../TimelinePropTypes';

export interface PlayheadPropTypes
  extends Pick<TimelinePropTypes, 'onSeekMedia'> {
  readonly minWindowMs: number;
  readonly maxWindowMs: number;
  readonly startWindowMs: number;
  readonly stopWindowMs: number;
  readonly onSetStart: React.Dispatch<React.SetStateAction<number>>;
  readonly onSetStop: React.Dispatch<React.SetStateAction<number>>;
}

export interface PlayheadViewPropTypes
  extends Pick<
    PlayheadPropTypes,
    'minWindowMs' | 'maxWindowMs' | 'startWindowMs' | 'stopWindowMs'
  > {
  readonly onSetStart: (ms: number) => void;
  readonly onSetStop: (ms: number) => void;
  readonly onSeekMedia: (ms: number) => void;
}

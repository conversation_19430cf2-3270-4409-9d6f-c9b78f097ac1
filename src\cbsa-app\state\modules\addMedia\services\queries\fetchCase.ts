import { AIWareNotification } from '../../AddMediaStore';
import {
  CaseId,
  CaseStatus,
  SummaryTDO,
  TreeObjectId,
} from '@cbsa-modules/universal';

export const FETCH_TDOS_QUERY = `
  query fetchTdos($caseId: ID!) {
    folder(id: $caseId) {
      childTDOs(offset: 0, limit: 100) {
        records {
          id
          name
          details
          status
          thumbnailUrl
          primaryAsset(assetType: "media") {
            id
            signedUri
            contentType
          }
          streams {
            uri
            protocol
          }
          assets {
            records {
              assetType
              signedUri
              details
            }
          }
          jobs(offset: 0, limit: 100) {
            records {
              id
              createdDateTime
              modifiedDateTime
              status
              tasks {
                records {
                  id
                  name
                  engine {
                    id
                    name
                    category {
                      id
                      name
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }`;

export interface FETCH_TDOS_QUERY_RESPONSE {
  folder: {
    childTDOs: {
      records: SummaryTDO[];
    };
  };
}

export const FETCH_CASE_DETAILS_QUERY = `
  query fetchCaseDetails($caseId: ID!, $schemaId: ID!) {
    structuredDataObjects(
      schemaId: $schemaId,
      filter: {
        folderTreeObjectId: $caseId,
      }
    ) {
      records {
        id
        data
      }
    }
  }`;

export interface FETCH_CASE_DETAILS_RESPONSE {
  structuredDataObjects: {
    records: {
      id: CaseId;
      data: {
        createdDateTime: string;
        modifiedDateTime: string;
        id: CaseId;
        caseName: string;
        status: CaseStatus;
        folderTreeObjectId: TreeObjectId;
        archive: 'archived' | 'reopened' | '';
      };
    }[];
  };
}

export const FETCH_CASE_NOTIFICATIONS_QUERY = `
  query fetchCaseNotifications($caseId: ID!, $schemaId: ID!) {
    structuredDataObjects(
      schemaId: $schemaId,
      limit: 1000,
      filter: {
        folderTreeObjectId: $caseId
     }) {
      records {
        id
        data
      }
    }
  }`;

export interface FETCH_CASE_NOTIFICATIONS_QUERY_RESPONSE {
  structuredDataObjects: {
    records: AIWareNotification[];
  };
}

import type { JobStatus, TaskStatus } from 'veritone-types';
import { VeritoneFile } from '@redact-modules/mainPage/models';
import { GlobalSettings } from './GlobalSettings';
import { FolderId, TDOId } from '@common-modules/universal/models/Brands';

export interface DetailTDO {
  readonly id: TDOId;
  readonly name: string;
  readonly status: 'downloaded' | 'recording' | 'recorded';
  readonly thumbnailUrl: string;
  readonly modifiedDateTime: string;
  readonly startDateTime: string;
  readonly stopDateTime: string;
  readonly foldersTreeObjectIds: FolderId[];
  readonly details: {
    readonly name?: string;
    readonly settings: GlobalSettings;
    // veritoneFile may not be set until ingestion has occurred
    readonly veritoneFile?: VeritoneFile;
    readonly tags?: ReadonlyArray<Tag | null>;
    readonly govQARequestId?: string;
    readonly foiaXpressRequestId?: string;
    readonly casepointRequestId?: string;
    readonly nuixRequestId?: string;
    readonly exterroRequestId?: string;
    readonly veritoneProgram?: {
      sourceType: string;
    };
    readonly redact?: {
      readonly trimInterval?: {
        startTimeMs: number;
        stopTimeMs: number;
      };
    };
    [key: string]: any;
  };
  readonly primaryAsset: null | {
    readonly id: string;
    readonly name: string | null;
    readonly description: string | null;
    readonly signedUri: string;
    readonly details?: {
      readonly virtualAsset?: boolean;
    };
    readonly jsondata: {
      readonly mediaDuration?: number;
    };
    readonly contentType: string;
  };
  readonly redactedMediaAssets?: {
    readonly records: ReadonlyArray<RedactedMediaAsset>;
  };
  readonly auditLogAssets?: {
    readonly records: ReadonlyArray<AuditLogAsset>;
  };
  readonly redactExportAssets?: {
    readonly records: ReadonlyArray<RedactExportAsset>;
  };
  readonly waveformAssets: {
    readonly records: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
      readonly contentType: string;
      readonly modifiedDateTime: string;
      readonly signedUri: string;
      readonly details: Record<string, any>;
      readonly fileData: {
        readonly md5sum: string;
      };
    }>;
  };
  readonly thumbnailAssets?: {
    readonly records: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
      readonly contentType: string;
      readonly signedUri: string;
      readonly details: Record<string, any>;
    }>;
  };
  readonly transcriptAssets?: {
    readonly records: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
      readonly contentType: string;
      readonly signedUri: string;
      readonly details: Record<string, any>;
    }>;
  };
  readonly tasks?: {
    readonly records: ReadonlyArray<TdoTask>;
  };
  readonly streams: ReadonlyArray<{
    readonly protocol: 'hls' | 'dash';
    readonly uri: string;
  }>;
}

export interface RedactedMediaAsset {
  readonly id: string;
  readonly name: string;
  readonly contentType: string;
  readonly modifiedDateTime: string;
  readonly signedUri: string;
  readonly details: Record<string, any>;
  readonly fileData: {
    readonly md5sum: string;
  };
}

export interface AuditLogAsset {
  readonly id: string;
  readonly name: string;
  readonly contentType: string;
  readonly signedUri: string;
  readonly details: Record<string, any>;
}

export interface RedactExportAsset {
  readonly id: string;
  readonly name: string;
  readonly contentType: string;
  readonly signedUri: string;
  readonly details?: Record<string, any>;
}

export interface Tag {
  readonly value: string;
  readonly redactionStatus: string;
}

export interface TdoTask {
  readonly id: string;
  readonly status: TaskStatus;
  readonly engineId: string;
  readonly jobId: string;
  readonly createdDateTime: string;
  readonly startedDateTime: string | null;
  readonly completedDateTime: string | null;
  readonly engine: {
    readonly id: string;
    readonly name: string;
    readonly categoryId: string;
    readonly state?: JobStatus;
  };
  readonly ioFolders: ReadonlyArray<{
    readonly referenceId: string;
  }>;
}

// // Interface when loading from TDO
// // Includes both Legacy and Modern Detail settings
// // Note Modern Settings does not include blurLevel,
// // blurLevelVehicle, or redactionType

import { difference } from 'lodash';
import { createSelector } from 'reselect';

import { selectConfigEngines } from '@common-modules/engines/selectors';
import { ViewSettings } from '../models';
import { namespace } from '../store';
import { DetectionAssetState } from '../store.models';

import moment from 'moment';
import { DAG_REFERENCE_IDS } from '@helpers/constants';

const selectStore = (s: { [namespace]: DetectionAssetState & ViewSettings }) =>
  s[namespace];

const selectTdo = createSelector(selectStore, (s) => s.tdo);

/**
 * Select TDO Tasks (up to default Graphql limit).
 */
export const selectTdoTasks = createSelector(
  selectTdo,
  (tdo) => tdo?.tasks?.records || []
);

/**
 * Select TDO Tasks for Redact's engines.
 */
export const selectTaskHistory = createSelector(
  selectTdoTasks,
  selectConfigEngines,
  (tasks, engines) => {
    const engineIds = Object.values(engines);
    return (tasks || []).filter((task) =>
      engineIds.some(
        (id) => task.engineId === id || task.engine.categoryId === id
      )
    );
  }
);

const COMPLETE_STATUSES: ReadonlyArray<string> = ['complete'];
const FAILED_STATUSES: ReadonlyArray<string> = [
  'aborted',
  'cancelled',
  'failed',
];

/**
 * Select completed tasks.
 */
export const selectCompletedTasks = createSelector(selectTaskHistory, (tasks) =>
  (tasks || []).filter((task) => COMPLETE_STATUSES.includes(task.status))
);

/**
 * Select failed tasks.
 */
export const selectFailedTasks = createSelector(selectTaskHistory, (tasks) =>
  (tasks || []).filter((task) => FAILED_STATUSES.includes(task.status))
);

/**
 * Select running tasks.
 */
export const selectRunningTasks = createSelector(
  selectTaskHistory,
  selectCompletedTasks,
  selectFailedTasks,
  (tasks, done, fail) => difference(tasks || [], [...done, ...fail]) || []
);

// This will only take most recent task and return if running
// export const selectMostRecentTaskByEngineIfRunning = createSelector(
//   selectTaskHistory,
//   selectCompletedTasks,
//   selectFailedTasks,
// (tasks, done, fail) => {
//   const tasksGroupByEngine = groupBy(tasks, (s) => s.engineId);
//   const latestLists = [];
//   for (const p in tasksGroupByEngine) {
//     if (Object.prototype.hasOwnProperty.call(tasksGroupByEngine, p)) {
//       const element = tasksGroupByEngine[p][0];
//       latestLists.push(element);
//     }
//   }
//   return difference(latestLists, [...done, ...fail]);
// }
// );

// /**
//  * Select Head Detection engines running tasks.
//  * @returns {Array<Task>}
//  */
export const selectDetectionRunning = createSelector(
  selectConfigEngines,
  selectRunningTasks,
  ({ detectionCategory: id }, tasks) =>
    (tasks || []).filter(
      (task) =>
        task.engine.categoryId === id ||
        task.ioFolders?.some(
          (folder) => folder.referenceId === DAG_REFERENCE_IDS['owFromHead']
        )
    )
);

// /**
//  * Select Face Detection engines failed tasks.
//  * @returns {Array<Task>}
//  */
export const selectDetectionFailed = createSelector(
  selectConfigEngines,
  selectFailedTasks,
  ({ detectionCategory: id }, tasks) =>
    (tasks || []).filter((task) => task.engine.categoryId === id)
);

// /**
//  * Select Face Detection engines completed tasks.
//  * @returns {Array<Task>}
//  */
export const selectDetectionCompleted = createSelector(
  selectConfigEngines,
  selectCompletedTasks,
  ({ detectionCategory: id }, tasks) =>
    (tasks || []).filter((task) => task.engine.categoryId === id)
);

// /**
//  * Select Optical Tracking engines running tasks.
//  * @returns {Array<Task>}
//  */
export const selectTrackingRunning = createSelector(
  selectConfigEngines,
  selectRunningTasks,
  (
    {
      opticalTrackingEngineCatagoryId: id,
      opticalTrackingEngineCategoryId: id2,
    },
    tasks
  ) =>
    (tasks || []).filter(
      (task) => task.engine.categoryId === id || task.engine.categoryId === id2
    )
);

// /**
//  * Select Blur Redaction engines running tasks.
//  * @returns {Array<Task>}
//  */
export const selectRedactionRunning = createSelector(
  selectConfigEngines,
  selectCompletedTasks,
  selectRunningTasks,
  ({ redactEngineId: id }, completedTasks, runningTasks) => {
    const running = (runningTasks || []).filter((task) => task.engineId === id);
    const completed = (completedTasks || []).filter(
      (task) => task.engineId === id
    );

    // only return running tasks newer than last completed task
    const completedTask = completed[0];
    if (completedTask && running.length > 0) {
      return running.filter(
        (task) =>
          moment.utc(task.createdDateTime) >
          moment.utc(completedTask.createdDateTime)
      );
    }

    return running;
  }
);

// /**
//  * Selector to determine if head and object detections has been completed
//  * @returns {boolean}
//  */
export const selectHasHeadDetectionCompleted = createSelector(
  selectCompletedTasks,
  (completedTasks) => {
    const headDetectionTaskCompleted = (completedTasks || []).find(
      (task) =>
        task.engine.name &&
        task.engine.name.includes('Head Detection') &&
        COMPLETE_STATUSES.includes(task.status)
    );
    const outputWriterTaskCompleted = (completedTasks || []).find(
      (task) =>
        task.engine.name &&
        task.engine.name.includes('Output Writer') &&
        task.ioFolders?.some(
          (folder) => folder.referenceId === DAG_REFERENCE_IDS['owFromHead']
        ) &&
        COMPLETE_STATUSES.includes(task.status)
    );
    if (headDetectionTaskCompleted && outputWriterTaskCompleted) {
      return true;
    }
    return false;
  }
);

// /**
//  * Select Transcription engines running tasks.
//  * @returns {Array<Task>}
//  */
export const selectTranscriptionRunning = createSelector(
  selectConfigEngines,
  selectCompletedTasks,
  selectRunningTasks,
  ({ transcriptionCategoryId: id }, completedTasks, runningTasks) => {
    const running = (runningTasks || []).filter(
      (task) =>
        task.engine.categoryId === id ||
        task.ioFolders?.some(
          (folder) =>
            folder.referenceId === DAG_REFERENCE_IDS['owFromTranscription']
        )
    );

    const completed = (completedTasks || []).filter(
      (task) =>
        task.engine.categoryId === id ||
        task.ioFolders?.some(
          (folder) =>
            folder.referenceId === DAG_REFERENCE_IDS['owFromTranscription']
        )
    );

    // only return runningTasks newer than last completed task
    const completedTask = completed[0];
    if (completedTask && running.length > 0) {
      return running.filter(
        (task) =>
          moment.utc(task.createdDateTime) >
          moment.utc(completedTask.createdDateTime)
      );
    }

    return running;
  }
);

// /**
//  * Select Transcription engines failed tasks.
//  * @returns {Array<Task>}
//  */
export const selectTranscriptionFailed = createSelector(
  selectConfigEngines,
  selectFailedTasks,
  ({ transcriptionCategoryId: id }, tasks) =>
    (tasks || []).filter((task) => task.engine.categoryId === id)
);

// /**
//  * Select Transcription engines completed tasks.
//  * @returns {Array<Task>}
//  */
export const selectTranscriptionCompleted = createSelector(
  selectConfigEngines,
  selectCompletedTasks,
  ({ transcriptionCategoryId: id }, tasks) =>
    (tasks || []).filter((task) => task.engine.categoryId === id)
);

/**
 * Select Download engines running tasks.
 */
export const selectDownloadRunning = createSelector(
  selectConfigEngines,
  selectRunningTasks,
  ({ downloadEngineId: id }, tasks) =>
    (tasks || []).filter((task) => task.engineId === id)
);

export const selectAllDownloadTask = createSelector(
  selectConfigEngines,
  selectTaskHistory,
  ({ downloadEngineId: id }, tasks) =>
    (tasks || []).filter((task) => task.engineId === id)
);

export const selectAllRedactTask = createSelector(
  selectConfigEngines,
  selectTaskHistory,
  ({ redactEngineId, legacyRedactEngineId }, tasks) =>
    (tasks || []).filter(
      (task) =>
        task.engineId === redactEngineId ||
        task.engineId === legacyRedactEngineId
    )
);

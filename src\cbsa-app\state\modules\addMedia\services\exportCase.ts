import moment from 'moment';
import call<PERSON>rap<PERSON><PERSON><PERSON><PERSON> from '@helpers/callGraph<PERSON><PERSON><PERSON>';
import { NOOP } from '@cbsa-modules/universal/actions';
import { Thunk, TDOId, TreeObjectId } from '@cbsa-modules/universal';
import {
  CREATE_EXPORT_TDO_QUERY,
  CreateExportTdoResponse,
  START_EXPORT_JOB_QUERY,
  StartExportJobResponse,
} from './queries/exportCase';
import {
  CREATE_EXPORT_TDO_SUCCESS,
  CREATE_EXPORT_TDO_FAILURE,
  START_EXPORT_JOB_SUCCESS,
  START_EXPORT_JOB_FAILURE,
} from '../actions';

export const createExportTdo: Thunk<{
  readonly caseId: TreeObjectId;
}> =
  ({ caseId }) =>
  async (dispatch, getState) =>
    await callGraph<PERSON>Api<CreateExportTdoResponse>({
      actionTypes: [NOOP, CREATE_EXPORT_TDO_SUCCESS, CREATE_EXPORT_TDO_FAILURE],
      query: CREATE_EXPORT_TDO_QUERY,
      variables: {
        input: {
          parentFolderId: caseId,
          startDateTime: moment(new Date()).toISOString(),
          stopDateTime: moment(new Date()).toISOString(),
          details: {
            isExport: true,
          },
        },
      },
      dispatch,
      getState,
    });

export const startExportJob: Thunk<{
  readonly caseId: TreeObjectId;
  readonly tdoId: TDOId;
  readonly clusterId?: string;
  readonly engineId: string;
}> =
  ({ caseId, tdoId, clusterId, engineId }) =>
  async (dispatch, getState) =>
    await callGraphQLApi<StartExportJobResponse>({
      actionTypes: [NOOP, START_EXPORT_JOB_SUCCESS, START_EXPORT_JOB_FAILURE],
      query: START_EXPORT_JOB_QUERY,
      variables: {
        caseId,
        tdoId,
        clusterId: clusterId ?? '',
        engineId,
      },
      dispatch,
      getState,
    });

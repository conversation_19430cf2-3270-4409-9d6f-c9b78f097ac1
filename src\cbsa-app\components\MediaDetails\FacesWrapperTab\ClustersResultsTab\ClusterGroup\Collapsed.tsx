// import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
// import { sumBy } from 'lodash';
import { ClusterItemGroup } from '@common-modules/mediaDetails/models';

import { SeekMediaTimePayload } from '../PropTypes';
// import * as styles from '../styles.scss';
import TimePeriod from '../TimePeriod';

const Collapsed = ({
  segments,
  onHighlightPoly,
}: ClusterGroupCollapsedPropTypes) => {
  const onClick = () => {
    const segment = segments[0];
    segment &&
      onHighlightPoly({
        startTimeMs: segment.startTimeMs,
        id: segment.objectIds[0] || '',
        type: segment.type,
        groupId: segment.groupId,
      });
  };

  // <Box display="flex" flex-direction="row" style={{ marginBottom: 34 }}>
  //   {/* <div data-test="detection-quantity" className={styles.colLen}>
  //     {segments.length}
  //   </div> */}
  return (
    <div data-test="time-period-udr">
      <TimePeriod
        startTimeMs={segments[0].startTimeMs}
        stopTimeMs={segments.at(-1)!.stopTimeMs} // Safe since segments is guaranteed to have at least one element
        onClick={onClick}
      />
    </div>
    /* <div data-test="auto-quantity">
        {sumBy(segments, 'numAutoInterpolations')}
      </div>
      <div data-test="manual-quantity">
        {sumBy(segments, 'isManualInterpolation') ? (
          <FiberManualRecordIcon fontSize="small" />
        ) : (
          ''
        )}
      </div> */
    // </Box>
  );
};

export default Collapsed;

export interface ClusterGroupCollapsedPropTypes {
  readonly segments: ClusterItemGroup['segments'];
  readonly onHighlightPoly: (payload: SeekMediaTimePayload) => void;
}

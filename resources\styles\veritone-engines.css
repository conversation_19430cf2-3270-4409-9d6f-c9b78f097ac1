@font-face {
  font-family: 'veritone-icons';
  src: url('icons/icomoon.eot');
  src: url('icons/icomoon.eot') format('embedded-opentype'),
    url('icons/icomoon.woff2') format('woff2'),
    url('icons/icomoon.ttf') format('truetype'),
    url('icons/icomoon.woff') format('woff'),
    url('icons/icomoon.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^='icon-engine-'],
[class*=' icon-engine-'] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'engine-icons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-engine-transcription:before {
  content: '\e900';
}
.icon-engine-sentiment:before {
  content: '\e901';
}
.icon-engine-fingerprint:before {
  content: '\e902';
}
.icon-engine-face:before {
  content: '\e903';
}
.icon-engine-object-detection:before {
  content: '\e904';
}
.icon-engine-translation:before {
  content: '\e905';
}
.icon-engine-music-detection:before {
  content: '\e906';
}
.icon-engine-gps:before {
  content: '\e907';
}
.icon-engine-tag:before {
  content: '\e908';
}
.icon-engine-audio-detection:before {
  content: '\e909';
}
.icon-engine-logo-detection:before {
  content: '\e90a';
}
.icon-engine-transcode:before {
  content: '\e90c';
}
.icon-engine-ingestion:before {
  content: '\e90b';
}

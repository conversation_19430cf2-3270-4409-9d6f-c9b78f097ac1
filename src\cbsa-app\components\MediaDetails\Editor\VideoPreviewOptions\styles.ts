import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles((_theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    height: '58px',

    '&>span': {
      margin: '16px 16px 0 0',
      lineHeight: '16px',
      '&>small': {
        fontSize: '12px',
      },
    },

    '& span': {
      color: ' rgba(207, 216, 220, 0.87)',
    },
  },
}));

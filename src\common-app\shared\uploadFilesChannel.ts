// adapted from
// https://decembersoft.com/posts/file-upload-progress-with-redux-saga/

import { buffers, channel, END } from 'redux-saga';

type ChannelContents =
  | {
      error: Error;
      success?: undefined;
      progress?: undefined;
      file: File;
      descriptor: UploadDescriptor;
    }
  | {
      error?: undefined;
      success: boolean;
      progress?: undefined;
      file: File;
      descriptor: UploadDescriptor;
    }
  | {
      error?: undefined;
      success?: undefined;
      progress: number;
      file: File;
      descriptor: UploadDescriptor;
    };

export interface UploadDescriptor {
  url: string;
  key: string;
  bucket: string;
  expiresInSeconds: number;
  getUrl: string;
  unsignedUrl: string;
  type?: string;
}

export default function uploadFilesChannel(
  uploadDescriptors: UploadDescriptor[],
  files: File[],
  method = 'PUT'
) {
  if (uploadDescriptors.length !== files.length) {
    throw new Error('Need an upload descriptor for each file to be uploaded!');
  }

  const chan = channel(buffers.sliding<ChannelContents>(2));
  let remainingFiles = files.length;

  const onFileProgress = (
    file: File,
    descriptor: UploadDescriptor,
    {
      lengthComputable,
      loaded,
      total,
    }: { lengthComputable: boolean; loaded: number; total: number }
  ) => {
    if (lengthComputable) {
      // todo: if multiple files with drastically different sizes are
      // uploaded at the same time, the mean percentage isn't a good
      // representation of remaining time.
      // ie. given files of size [1, 1, 10], with progress [100, 100, 20]%,
      // mean(100, 100, 20) ~= 75%, but the true remaining time should be
      // calculated on remaining/total size (1+1+2) / (1+1+10) = 33%
      const progress = (loaded / total) * 100;
      chan.put({ progress, file, descriptor });
    }
  };

  const onStatusCodeFailure = (file: File, descriptor: UploadDescriptor) => {
    chan.put({ error: new Error('Upload failed'), file, descriptor });
  };

  const onXHRError = (file: File, descriptor: UploadDescriptor) => {
    chan.put({ error: new Error('File upload error'), file, descriptor });
  };

  const onFileReadyStateChange = (
    file: File,
    descriptor: UploadDescriptor,
    { readyState, status }: XMLHttpRequest
  ) => {
    if (readyState === XMLHttpRequest.DONE) {
      remainingFiles -= 1;

      if (status >= 200 && status <= 300) {
        chan.put({ success: true, file, descriptor });
      } else {
        onStatusCodeFailure(file, descriptor);
      }

      if (remainingFiles === 0) {
        chan.put(END);
      }
    }
  };

  files.forEach((file, i) => {
    const descriptor = uploadDescriptors[i]!; // Safe due to previous length check
    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener(
      'progress',
      onFileProgress.bind(null, file, descriptor)
    );
    xhr.upload.addEventListener(
      'error',
      onXHRError.bind(null, file, descriptor),
      { once: true }
    );
    xhr.onreadystatechange = onFileReadyStateChange.bind(
      null,
      file,
      descriptor,
      xhr
    );
    // xhr.upload.addEventListener('abort', onFailure.bind(null, file, descriptor));
    xhr.open(method, descriptor.url, true);
    xhr.setRequestHeader('x-ms-blob-type', 'BlockBlob');
    if (file.type === '' && file.name.endsWith('.cme')) {
      descriptor.type = 'video/mp4';
      xhr.setRequestHeader('Content-Type', 'video/mp4');
    }
    xhr.send(file);
  });

  return chan;
}

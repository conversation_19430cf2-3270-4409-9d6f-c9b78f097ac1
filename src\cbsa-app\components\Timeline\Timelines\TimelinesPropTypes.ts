import { TimelinePropTypes } from '../TimelinePropTypes';
import {
  AudiowaveFrame,
  UDRsPolyAsset,
} from '@common-modules/mediaDetails/models';

export interface TimelinesPropTypes
  extends Pick<
    TimelinePropTypes,
    | 'mediaDuration'
    | 'progress'
    | 'selectedPolys'
    | 'transcriptRedactions'
    | 'selectedTimeSlices'
    | 'highlightedOverlay'
    | 'globalSettings'
    | 'onUnredactSlice'
    | 'onRedactSlice'
    | 'onSelectSlice'
    | 'onDeselectAll'
    | 'transcriptionList'
    | 'onSetSelectedUDRGroup'
    | 'onChangeUDRGroupLabel'
    | 'onChangeUDR'
    | 'onChangeUDRSubmit'
    | 'onUDRSelect'
    | 'onSetFaceHighlight'
    | 'selectedUDRGroupId'
  > {
  readonly startWindowMs: number;
  readonly stopWindowMs: number;
  readonly detectionCollections: TimelinePropTypes['detectionCollections'];
  readonly udrCollection: TimelinePropTypes['udrCollection'];
  readonly udrAsset: UDRsPolyAsset;
  readonly audiowaves: AudiowaveFrame[];
}

import { TDOId } from '@common-modules/universal/models/Brands';
export const namespace = 'vtn-redact-engine-ingestion';

/**
 * OpticalTracking Store
 *
 * interface OpticalTrackingStore {
 *  runHead: boolean;
 *  runTranscription: boolean;
 *  [tdoId: TDOId]: {
 *    [jobId: string]: {
 *      jobId: string;
 *      isRunning: boolean;
 *      status: 'pending' | 'queued' | 'running' | 'complete' | 'failed' | 'cancelled' | 'aborted'
 *      startedOn: Date;
 *      endedOn?: Date;
 *      error?: string;
 *    }
 *  }
 * }
 */

export interface JobState {
  jobId?: string;
  isRunning: boolean;
  status:
    | 'pending'
    | 'queued'
    | 'running'
    | 'complete'
    | 'failed'
    | 'cancelled'
    | 'aborted';
  startedOn: Date;
  endedOn?: Date;
  error?: string;
}

export interface IngestionTDOs {
  [tdoId: TDOId]: {
    [jobId: string]: JobState;
  };
}

export interface IngestionStore {
  runHead: boolean;
  runPerson: boolean;
  runTranscription: boolean;
  tdos: IngestionTDOs;
}

export const defaultState: IngestionStore = {
  runHead: false,
  runPerson: false,
  runTranscription: false,
  tdos: {},
};

export const defaultJobSlice = (): JobState => ({
  jobId: undefined,
  isRunning: true,
  status: 'pending',
  startedOn: new Date(),
  endedOn: undefined,
  error: undefined,
});

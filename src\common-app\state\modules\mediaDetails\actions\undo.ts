import {
  ActionCreatorWithoutPayload,
  ActionCreatorWithPayload,
  createAction,
} from '@reduxjs/toolkit';
import { isString } from 'lodash';
import { workerActionCreator as workerActionCreator_ } from '@utils';

export const UNDO_ACTION = createAction(
  'vtn-redact-state-mediaDetails-view/UNDO_ACTION'
);
export const workerUndoAction = () =>
  workerActionCreator(UNDO_ACTION)(undefined);
export const PATCH_UNDO_ACTION = createAction(
  'vtn-redact-state-mediaDetails-view/PATCH_UNDO_ACTION'
);
export const workerPatchUndoAction = () =>
  workerActionCreator(PATCH_UNDO_ACTION)(undefined);
export const PATCH_EMPTY_UNDO_ACTION = createAction<{ clearFuture: boolean }>(
  'vtn-redact-state-mediaDetails-view/PATCH_EMPTY_UNDO_ACTION'
);

export const REDO_ACTION = createAction(
  'vtn-redact-state-mediaDetails-view/REDO_ACTION'
);
export const workerRedoAction = () =>
  workerActionCreator(REDO_ACTION)(undefined);
export const PATCH_REDO_ACTION = createAction(
  'vtn-redact-state-mediaDetails-view/PATCH_REDO_ACTION'
);
export const workerPatchRedoAction = () =>
  workerActionCreator(PATCH_REDO_ACTION)(undefined);

export const UPDATE_UNDO = createAction(
  'vtn-redact-state-mediaDetails-view/UPDATE_UNDO'
);
export const workerUpdateUndo = () =>
  workerActionCreator(UPDATE_UNDO)(undefined);
export const UPDATE_REDO = createAction(
  'vtn-redact-state-mediaDetails-view/UPDATE_REDO'
);
export const workerUpdateRedo = () =>
  workerActionCreator(UPDATE_REDO)(undefined);

// -----------------

function workerActionCreator<P>(
  typeOrActionCreator:
    | string
    | ActionCreatorWithPayload<P>
    | ActionCreatorWithoutPayload
) {
  if (isString(typeOrActionCreator)) {
    return workerActionCreator_<P>(typeOrActionCreator);
  } else {
    return workerActionCreator_<
      typeof typeOrActionCreator extends ActionCreatorWithoutPayload ? void : P
    >(typeOrActionCreator.type);
  }
}

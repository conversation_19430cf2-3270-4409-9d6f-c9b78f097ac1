// Import the function directly for unit testing

import { appConfigValue } from '@common/state/modules/app';

describe('appConfigValue', () => {
  it('returns valueJSON for JSON configType', () => {
    const config = {
      configType: 'JSON',
      configKey: 'featureFlags',
      value: null,
      valueJSON: { foo: 'bar' },
    };
    expect(appConfigValue(config)).toEqual({ foo: 'bar' });
  });

  it('parses value as JSON if valueJSON is missing', () => {
    const config = {
      configType: 'JSON',
      configKey: 'featureFlags',
      value: '{"foo": "bar"}',
      valueJSON: null,
    };
    expect(appConfigValue(config)).toEqual({ foo: 'bar' });
  });

  it('returns undefined for invalid JSON value', () => {
    const config = {
      configType: 'JSON',
      configKey: 'featureFlags',
      value: '{foo}',
      valueJSON: null,
    };
    expect(appConfigValue(config)).toBeUndefined();
  });

  it('returns undefined if both value and valueJSON are missing', () => {
    const config = {
      configType: 'JSON',
      configKey: 'featureFlags',
      value: null,
      valueJSON: null,
    };
    expect(appConfigValue(config)).toBeUndefined();
  });

  it('returns value for non-JSON configType', () => {
    const config = {
      configType: 'String',
      configKey: 'transcriptionEngineId',
      value: 'engineId',
      valueJSON: null,
    };
    expect(appConfigValue(config)).toBe('engineId');
  });
});

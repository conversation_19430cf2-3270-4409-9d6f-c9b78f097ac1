import { TDOId } from '@cbsa-modules/universal';
import { MediaComment } from '@common-modules/mediaDetails/models';
import { createAction } from '@reduxjs/toolkit';
import {
  FetchMediaCommentsResponse,
  FetchMediaCommentsUsersResponse,
  FetchMediaUserResponse,
} from '../services';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { VeritoneError } from '../saga/mediaComments';

export const FETCH_MEDIA_COMMENTS = createAction<{
  tdoId: TDOId;
  limit: number;
  offset: number;
}>('MEDIA_DETAILS/FETCH_MEDIA_COMMENTS');
export const FETCH_MEDIA_COMMENTS_SUCCESS =
  createGraphQLSuccessAction<FetchMediaCommentsResponse>(
    'MEDIA_DETAILS/FETCH_MEDIA_COMMENTS_SUCCESS'
  );
export const FETCH_MEDIA_COMMENTS_FAILURE = createGraphQLFailureAction(
  'MEDIA_DETAILS/FETCH_MEDIA_COMMENTS_FAILURE'
);

export const ADD_NEW_COMMENT = createAction<{
  commentId: string;
  currentTime: number;
  searchName: string;
  tdoId: TDOId;
  userId: string;
}>('MEDIA_DETAILS/ADD_NEW_COMMENT');

export const UPDATE_COMMENT = createAction<{
  comment: MediaComment;
  modify?: boolean;
  isUndo?: boolean;
}>('MEDIA_DETAILS/UPDATE_COMMENT');

export const UPDATE_COMMENT_SUCCESS = createGraphQLSuccessAction<{
  createStructuredData: {
    data: MediaComment;
  };
  isUndo?: boolean;
}>('MEDIA_DETAILS/UPDATE_COMMENT_SUCCESS');

export const UPDATE_COMMENT_FAILURE = createGraphQLFailureAction(
  'MEDIA_DETAILS/UPDATE_COMMENT_FAILURE'
);

export const REMOVE_COMMENT = createAction<{ commentId: string }>(
  'MEDIA_DETAILS/REMOVE_COMMENT'
);

export const FETCH_MEDIA_COMMENTS_USER = createAction<{
  userId: string;
}>('MEDIA_DETAILS/FETCH_MEDIA_COMMENTS_USER');
export const FETCH_MEDIA_COMMENTS_USER_SUCCESS =
  createGraphQLSuccessAction<FetchMediaCommentsUsersResponse>(
    'MEDIA_DETAILS/FETCH_MEDIA_COMMENTS_USER_SUCCESS'
  );
export const FETCH_MEDIA_COMMENTS_USER_FAILURE = createGraphQLFailureAction<
  VeritoneError[]
>('MEDIA_DETAILS/FETCH_MEDIA_COMMENTS_USER_FAILURE');

export const FETCH_MEDIA_USER = createAction('MEDIA_DETAILS/FETCH_MEDIA_USER');
export const FETCH_MEDIA_USER_SUCCESS =
  createGraphQLSuccessAction<FetchMediaUserResponse>(
    'MEDIA_DETAILS/FETCH_MEDIA_USER_SUCCESS'
  );
export const FETCH_MEDIA_USER_FAILURE = createGraphQLFailureAction(
  'MEDIA_DETAILS/FETCH_MEDIA_USER_FAILURE'
);

export const SELECT_COMMENT = createAction<{ commentId: string }>(
  'MEDIA_DETAILS/SELECT_COMMENT'
);
export const SELECT_COMMENT_ON_TIMELINE = createAction<{ commentId: string }>(
  'MEDIA_DETAILS/SELECT_COMMENT_ON_TIMELINE'
);

export const BASIC_USER_QUERY_IS_SUPPORTED = createAction(
  'MEDIA_DETAILS/BASIC_USER_QUERY_IS_SUPPORTED'
);

export const UPDATE_STATUS_HIDE_COMMENT = createAction<{
  isHideComment: boolean;
}>('MEDIA_DETAILS/UPDATE_STATUS_HIDE_COMMENT');

export const fetchMediaComments = (payload: {
  tdoId: TDOId;
  limit: number;
  offset: number;
}) => FETCH_MEDIA_COMMENTS(payload);

export const addNewComment = (payload: {
  commentId: string;
  currentTime: number;
  searchName: string;
  tdoId: TDOId;
  userId: string;
}) => ADD_NEW_COMMENT(payload);

export const updateComment = (payload: {
  comment: MediaComment;
  modify?: boolean;
}) => UPDATE_COMMENT(payload);

export const removeComment = (commentId: string) =>
  REMOVE_COMMENT({ commentId });

export const fetchMediaCommentsUser = (userId: string) =>
  FETCH_MEDIA_COMMENTS_USER({ userId });

export const fetchMediaUser = () => FETCH_MEDIA_USER();

export const selectComment = (commentId: string) =>
  SELECT_COMMENT({ commentId });

export const selectCommentsOnTimeline = (commentId: string) =>
  SELECT_COMMENT_ON_TIMELINE({ commentId });

export const basicUserQueryIsSupported = () => BASIC_USER_QUERY_IS_SUPPORTED();

export const updateStatusHideComment = (payload: { isHideComment: boolean }) =>
  UPDATE_STATUS_HIDE_COMMENT(payload);

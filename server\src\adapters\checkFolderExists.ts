import { CheckFolderExistsResponse } from '../model/responses';
import { CheckFolderExistsRequest, RequestHeader } from '../model/requests';
import { callGQL } from '../api/callGraphql';
import { checkFolderExistsQuery } from '../api/queries';
import { Messages } from '../errors/messages'
import { Logger } from '../logger';

export const checkFolderExistsAdapter = async (
  headers: RequestHeader,
  request: CheckFolderExistsRequest
) => {
  let response = undefined;
  try {
    const res = await callGQL<CheckFolderExistsResponse>( headers,
      checkFolderExistsQuery(request.folderId),
    );
    response = res.folder;

  } catch(err) {
    Logger.error(Messages.checkFolderExistsFail  + JSON.stringify(err));
  }
  return response;
};

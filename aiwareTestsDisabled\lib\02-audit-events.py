#!/usr/bin/env python3

# Purpose:
#   Test audit events.
# Prerequisites:
#   1) Python3: brew install python
#   2) requests package: pip3 install requests
# Usage:
#   ./02-audit-events.py

import datetime
import traceback
from test_suite import *

try:
    # Create a new test suite
    ts = TestSuite("01-audit-events")
    ts.print_msg(f"GQL: {ts.gql_uri}")

    # Get a session token to use in rest of tests
    ts.get_token()
    ts.print_msg(f"Token: {ts.token}")

    # Create a new TDO
    tdo_id = ts.create_tdo_with_asset()
    ts.print_msg(f"TDO: {tdo_id}")

    # Create an audit event
    audit_event_payload = {
        "action": "Edit Bounding Box {\"boundingPoly\":[{\"x\":0.38912817813121414,\"y\":0.3900481029022099},{\"x\":0.6519966814432577,\"y\":0.3900481029022099},{\"x\":0.6519966814432577,\"y\":0.7065022241247576},{\"x\":0.38912817813121414,\"y\":0.7065022241247576}],\"id\":\"db9a-48ff-b513\",\"currentPosition\":0}",
        "tdoId": tdo_id,
        "userName": ts.aiware_username,
        "timestamp": get_current_utc_time()
    }
    audit_event_id = ts.emit_audit_event(tdo_id, audit_event_payload)
    ts.print_msg("Audit event: " + audit_event_id)

    # Periodically check until audit event created successfully or timed out
    event_created_time = datetime.datetime.now()
    elapsed_time = datetime.timedelta(seconds=0)
    audit_event = None

    while elapsed_time.total_seconds() < ts.max_audit_events_check_secs:
        audit_events = ts.audit_events(tdo_id)
        now = datetime.datetime.now()
        elapsed_time = now - event_created_time
        ts.print_msg(f"{now} audit events: {audit_events}")
        audit_event = get_audit_event(audit_event_id, audit_events)
        if audit_event is not None:
            break
        time.sleep(ts.audit_events_check_interval_secs)

    if audit_event is None:
        ts.print_msg(f"Audit event {audit_event_id} not found", True)
        exit(1)

    if elapsed_time.total_seconds() > ts.max_audit_events_check_secs:
        ts.print_msg(f"Audit event {audit_event_id} took too long to complete. Limit is {max_job_secs} seconds.", True)
        exit(1)

    # Verify audit event has correct content
    for key in audit_event_payload.keys():
        expected = audit_event_payload[key]
        actual = audit_event['payload'][key]
        if actual != expected:
            ts.print_msg(f"Audit event {audit_event_id} has incorrect payload[{key}] value. Expected: {expected}. Actual: {actual}", True)
            exit(1)
    if audit_event['application'] != 'redact':
        ts.print_msg(f"Audit event {audit_event_id} has incorrect application value. Expected: redact. Actual: {audit_event['application']}", True)
        exit(1)

    ts.print_msg(f"Audit event completed successfully in {elapsed_time.total_seconds()} seconds.")

    # Clean up
    deleted_id = ts.delete_tdo(tdo_id)
    ts.print_msg(f"Deleted TDO {deleted_id} successfully.")

    exit(0)

except Exception as e:
    # capture any unhandled exception in log files
    ts.print_msg(traceback.format_exc(), True)
    exit(1)

finally:
    ts.out_log.close()
    ts.err_log.close()
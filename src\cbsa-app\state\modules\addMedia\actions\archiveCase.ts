import { Case } from '@cbsa-modules/universal';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { createAction } from '@reduxjs/toolkit';
import { ARCHIVE_CASE_QUERY_RESPONSE } from '../services/queries/archiveCase';

export const ARCHIVE_CASE = createAction<{
  caseDetails: Case;
  archive: boolean;
}>('CBSA/ARCHIVE_CASE');
export const ARCHIVE_CASE_SUCCESS =
  createGraphQLSuccessAction<ARCHIVE_CASE_QUERY_RESPONSE>(
    'CBSA/ARCHIVE_CASE_SUCCESS'
  );
export const ARCHIVE_CASE_FAILURE = createGraphQLFailureAction(
  'CBSA/ARCHIVE_CASE_FAILURE'
);

export const archiveCase = ({
  caseDetails,
  archive,
}: {
  caseDetails: Case;
  archive: boolean;
}) => ARCHIVE_CASE({ caseDetails, archive });

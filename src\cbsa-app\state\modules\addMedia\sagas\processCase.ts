import * as Actions from '../actions';
import * as Services from '../services';
import { modules } from '@veritone/glc-redux';
import { selectCaseDetails } from '../selectors';
import { enqueueSnackbar } from '@common-modules/snackbar';
import {
  all,
  put,
  select,
  takeEvery,
  takeLatest,
} from 'typed-redux-saga/macro';
import { sagaIntl } from '@common/i18n';

const { auth: authModule, config: configModule } = modules;

export function* processCase() {
  yield* takeLatest(Actions.PROCESS_CASE, function* ({ payload }) {
    const { images, media, batchId, caseDetails } = payload;

    const config = yield* select(configModule.getConfig);
    const oauthToken = yield* select(authModule.selectOAuthToken);
    const sessionToken = yield* select(authModule.selectSessionToken);
    const { glcApiRoot, veritoneAppId } = <any>config;
    // const endpoint = `${glcApiRoot}/${corsightEndpoint}`;
    const endpoint = `${glcApiRoot}/api/find-poi-job`;

    try {
      yield* all(
        media.map((file) => {
          const {
            id,
            name,
            primaryAsset: { signedUri },
          } = file;

          return put(
            Services.processCase({
              referenceImageUrls: images,
              videoUrl: signedUri,
              tdoId: id,
              batchId,
              name,
              endpoint,
              token: sessionToken || oauthToken,
              veritoneAppId,
            })
          );
        })
      );
    } catch {
      yield* put(
        Actions.changeCaseStatus({ caseDetails, caseStatus: 'error' })
      );
    }
  });
}

export function* processCaseSuccess() {
  yield* takeEvery(Actions.PROCESS_CASE_SUCCESS, function* ({ payload }) {
    const { name } = payload;
    const caseDetails = yield* select(selectCaseDetails);
    if (caseDetails) {
      yield* put(
        Actions.changeCaseStatus({ caseDetails, caseStatus: 'processing' })
      );
    }
    yield* put(
      enqueueSnackbar({
        message: `${name} ${sagaIntl().formatMessage({
          id: 'isBeingProcessed',
        })}`,
        variant: 'success',
      })
    );
  });
}

export function* processCaseFailure() {
  yield* takeEvery(Actions.PROCESS_CASE_FAILURE, function* ({ payload }) {
    const { name } = payload;
    yield* put(
      enqueueSnackbar({
        message: `${name} ${sagaIntl().formatMessage({
          id: 'couldNotBeProcessed',
        })}`,
        variant: 'error',
      })
    );
  });
}

import { isImage } from '@helpers/tdoHelper';
import { isEmpty } from 'lodash';
import { FetchCaseMediaResponse } from '../services';
import { MainStore } from '..';
import { WritableDraft } from 'immer';

export const fetchCaseMediaSuccess = (
  state: WritableDraft<MainStore>,
  { payload }: { payload: FetchCaseMediaResponse }
) => {
  const {
    folder: {
      id: caseId,
      name: caseName,
      childTDOs: { records: tdos },
    },
  } = payload;

  const filteredTdos = (tdos || []).filter((t) => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-assignment
    const softDeleteTimeTags = (t.details?.tags || []).filter(
      (t: any) => !isEmpty(t.toBeDeletedTime)
    );
    return (softDeleteTimeTags?.length || 0) < 1;
  });

  const [images, media] = (filteredTdos || []).reduce<
    [typeof filteredTdos, typeof filteredTdos]
  >(
    ([i, m], tdo) =>
      isImage(tdo)
        ? [[...i, tdo], m]
        : tdo?.details?.isExport !== true
          ? [i, [...m, tdo]]
          : [i, m],
    [[], []]
  );

  return {
    ...state,
    caseMedia: {
      caseId,
      caseName,
      images,
      media,
    },
  };
};

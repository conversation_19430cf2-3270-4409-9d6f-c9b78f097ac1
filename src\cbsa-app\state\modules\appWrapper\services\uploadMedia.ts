import moment from 'moment';
import { Thunk, UploadMedia } from '@cbsa-modules/universal';
import {
  UPLOAD_FILE_FAILURE,
  UPLOAD_FILE_SUCCESS,
} from '@cbsa-modules/appWrapper';
import {
  GET_SIGNED_WRITABLE_URL_QUERY,
  CREATE_TDO_QUERY,
  CREATE_ASSET_FOR_IMAGE_QUERY,
  SET_PRIMARY_ASSET_FOR_IMAGE_QUERY,
  CREATE_ENGINE_JOB_QUERY,
  GetSignedWritableUrlResponse,
  CreateTDOResponse,
  CreateAssetForImageResponse,
} from './queries/uploadMedia';
import { modules } from '@veritone/glc-redux';
import { fetchGraphQLApi } from '@helpers/callApi';
import { selectConfigEngines } from '@common-modules/engines/selectors';
import { formatTDOName } from '@utils';

const {
  auth: { selectOAuthToken, selectSessionToken },
  config: { getConfig },
  user: { selectUser },
} = modules;

export const uploadMedia: Thunk<{
  readonly uploadFile: UploadMedia;
  readonly error?: any;
}> =
  ({ uploadFile }) =>
  async (dispatch, getState) => {
    const startDateTime = new Date();
    const { file, caseId } = uploadFile;
    const { type: contentType } = file;

    const name = formatTDOName(file.name);

    const oauthToken = selectOAuthToken(getState());
    const sessionToken = selectSessionToken(getState());
    const { apiRoot, graphQLEndpoint, veritoneAppId } =
      getConfig<Window['config']>(getState());
    const authorization = {
      endpoint: `${apiRoot}/${graphQLEndpoint}`,
      token: sessionToken || oauthToken,
      veritoneAppId,
    };

    try {
      /* Get Signed Writable Url */
      const response = await fetchGraphQLApi<GetSignedWritableUrlResponse>({
        ...authorization,
        query: GET_SIGNED_WRITABLE_URL_QUERY,
        variables: { key: name },
      });
      if (!response.data?.getSignedWritableUrl) {
        throw new Error('Failed to get signed writable url');
      }

      const {
        data: {
          getSignedWritableUrl: { url, getUrl, unsignedUrl },
        },
      } = response;

      /* Upload File to Signed Writable URL */
      await fetch(url, { method: 'PUT', body: file })
        .then((response) => response.json())
        .then(async (result) => await result)
        .catch((error) => {
          throw error;
        });

      /* Create TDO */
      const tdoResponse = await fetchGraphQLApi<CreateTDOResponse>({
        ...authorization,
        query: CREATE_TDO_QUERY,
        variables: {
          input: {
            startDateTime: moment.utc(startDateTime).toDate().toISOString(),
            stopDateTime: moment.utc(startDateTime).toDate().toISOString(),
            name,
            parentFolderId: caseId,
            addToIndex: true,
            details: {
              tags: [
                {
                  value: 'in redaction',
                  redactionStatus: 'draft',
                },
              ],
              veritoneProgram: {
                programLiveImage: '',
              },
              veritoneFile: {
                fileName: name,
                fileType: file.type,
              },
            },
          },
        },
      });
      if (!tdoResponse.data?.createTDO) {
        throw new Error('Failed to create TDO');
      }

      const {
        data: { createTDO: tdo },
      } = tdoResponse;

      /* Check which engines need to be run */
      if (contentType.includes('image')) {
        const { id } = tdo;

        /* Create Asset */
        const response = await fetchGraphQLApi<CreateAssetForImageResponse>({
          ...authorization,
          query: CREATE_ASSET_FOR_IMAGE_QUERY,
          variables: {
            id,
            unsignedUrl,
            assetType: 'image',
            contentType,
          },
        });
        if (!response.data?.createAsset) {
          throw new Error('Failed to create asset');
        }

        const {
          data: {
            createAsset: { id: assetId },
          },
        } = response;

        /* Set Primary Asset */
        await fetchGraphQLApi({
          ...authorization,
          query: SET_PRIMARY_ASSET_FOR_IMAGE_QUERY,
          variables: {
            input: { id, primaryAsset: { id: assetId, assetType: 'media' } },
          },
        });
      } else {
        const { id } = tdo;
        const { email } = selectUser(getState());
        const { defaultClusterId: clusterId } = selectConfigEngines(getState());
        const { glcIngestionEngineId, fastChunkerEngineId, detectionEngineId } =
          getConfig<Window['config']>(getState());

        /* Create Engine Job for Head Detection and Transcription */
        await fetchGraphQLApi({
          ...authorization,
          query: CREATE_ENGINE_JOB_QUERY,
          variables: {
            id,
            clusterId: clusterId ?? '',
            batchId: `${caseId}_headDetection`,
            glcIngestionEngineId,
            fastChunkerEngineId,
            detectionEngineId,
            payload: {
              tdoId: id,
              url: getUrl,
              fileType: contentType,
              user: email,
              chunkSize: 10000000,
            },
          },
        });
      }
      /* Upload Successful */
      dispatch(UPLOAD_FILE_SUCCESS({ uploadFile }));
    } catch (error) {
      /* Upload Failed */
      dispatch(UPLOAD_FILE_FAILURE({ error, uploadFile }));
    }
  };

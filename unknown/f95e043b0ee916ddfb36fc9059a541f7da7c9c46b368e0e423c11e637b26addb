import { ViewSettings } from './models';
import { SELECT_RESULTS_TAB } from './actions';
import { DETECT_OPTIONS } from '../facesTabModule';
import { DEFAULT_FILTER_PARAMETERS } from '@helpers/constants';
import {
  DetectionAssetState,
  Extra,
  MaxDepthArray,
  Undo,
} from './store.models';

export const TDO_SETTINGS_NAMESPACE = 'tdoSettings';

const detectionAssetState: DetectionAssetState = {
  loaded: false,
  currentPosition: 0,
  tdo: null,
  tdoIsChanged: false,

  detectionClusterGroups: {},
  udrClusterGroups: {},
  clusterMap: {},

  isFetchingClusterListData: false,
  detectionCollections: undefined,
  transcription: undefined,
  transcriptionView: {
    selected: [],
    redactions: [],
    editNotes: undefined,
    currentEditWords: undefined,
  },
  progress: {
    udrs: 0,
    faceDetection: 0,
    transcription: 0,
  },

  facesSelectedEngineId: null,

  disableUnredactAuditLog: false,
  detectionSeriesLengthWithDeleted: undefined,
};

const viewSettingsState: ViewSettings = {
  playbackSpeed: 1,
  playbackDirection: 'forward',
  overlayPreview: 'outline',
  selectedPolyGroups: {},
  clusterMergeGroupIds: {},
  clusterMergeSegments: {},
  expandedPolyGroups: {},
  sortPolyGroupsBy: {
    column: 'startTimeMs',
    direction: 'asc',
  },
  filterParameters: DEFAULT_FILTER_PARAMETERS,
  showGlobalSettingsModal: false,
  highlightedOverlay: undefined,
  displayUnselectedOverlays: false,
  prevMarkerTime: 0,
  draggedOverlayId: null,
};

export type MainStore = DetectionAssetState & ViewSettings & Extra & Undo;

// TODO Finish store models.
export const defaultState: MainStore = {
  ...detectionAssetState,
  ...viewSettingsState,

  user: undefined,

  fetchingTdo: false,
  failedFetchingTdo: false,

  hasAudio: true,
  videoLoaded: false,
  thumbnailTracks: null,
  shakaPlayer: null,
  faceDetection: {
    isFetching: false,
    isProcessing: false,
    isReady: false,
    statusMessage: '',
  },

  udrCollection: undefined,

  udrsState: {
    udrBoundingPolyBeingUpdated: undefined,
    selectedUDRGroupId: undefined,
    udrAsset: {
      boundingPolys: {},
    },
    localUDRAsset: {
      boundingPolys: {},
    },
    regularModePlaybackSpeed: 1,
  },

  audiowaves: [],

  //  License Plates engine results tab
  licensePlatesEngineResults: null,
  licensePlatesSelectedEngineId: null,
  selectedLicensePlatesGroup: null,

  // SAVE button
  fetchingUpdateAsset: false,
  failedFetchingUpdateAsset: false,

  mediaDetailsPageSelectedTab: SELECT_RESULTS_TAB.type,
  isShowConfirmLeave: false,

  numberOfRedactedFile: 0,
  // position bar DND
  dragAndDropPositionBar: false,

  // update status
  fetchingUpdateStatus: false,
  failedUpdateStatus: false,

  // redacted asset play
  redactedAssetStarted: false,

  // download
  downloadStarted: false,
  fetchingCreateDownloadTask: false,
  pollingDownload: false,

  // pollingFileWatermark: new Date(),
  pollingDownloadStopped: false,
  downloadJobStatus: false,
  downloadJob: null,

  // Update MDP after Redaction job completes
  gettingRedactionJobStatus: false,
  failedGettingRedactionJobStatus: false,
  redactionJobStatus: false,
  redactionJobCreatedDateTime: null,
  redactionJob: null,

  // HEAD DETECTION BUTTON
  buttonDetectFaceDisable: false,

  // TRANSCRIPTION BUTTON
  buttonTranscriptionDisable: false,

  dataFetchedForDetectionType: {
    laptop: false,
    head: false,
    poim: false,
    licensePlate: false,
    card: false,
    notepad: false,
    person: false,
  },

  integrations: {
    govQa: {
      isFormShown: false,
      isSendingFile: false,
      isStatusDialogShown: false,
    },
    foiaXpress: {
      isFormShown: false,
      isSendingFile: false,
      isStatusDialogShown: false,
    },
    casepoint: {
      isFormShown: false,
      isSendingFile: false,
      isStatusDialogShown: false,
    },
    exterro: {
      isFormShown: false,
      isSendingFile: false,
      isStatusDialogShown: false,
    },
    nuix: {
      isFormShown: false,
      isSendingFile: false,
      isStatusDialogShown: false,
    },
    externalIntegration: {
      isFormShown: false,
      isSendingFile: false,
      isStatusDialogShown: false,
    },
  },

  deletedOverlaysCache: {
    overlayIdsMap: {},
    overlaySubsegmentIdsMap: {},
  },

  caseMedia: {
    caseId: '',
    caseName: '',
    images: [],
    media: [],
  },

  downloadUrl: '',

  isCaseMediaDrawerOpen: false,

  mediaComments: {},
  isHideComment: false,
  mediaCommentsUsers: {},
  isFetchingComments: false,
  selectedMediaComments: [],
  lastSaveMediaDataDisplayTime: '',
  isSaveRunning: false,
  isSaveFailed: false,
  isBasicUserQuerySupported: true,
  isPlayerSeeked: false,

  tdoReadonly: {
    isLocked: false,
    name: undefined,
  },
  detectObjects: {
    [DETECT_OPTIONS.HEAD_OBJECTS]: false,
    [DETECT_OPTIONS.PERSON_OBJECTS]: false,
  },
  showDetectOptionsDialog: false,

  history: {
    past: new MaxDepthArray(),
    future: [],
  },
};

export const namespace = 'vtn-redact-state-mediaDetails';

export interface RedactMediaDetailsState {
  [namespace]: MainStore;
}

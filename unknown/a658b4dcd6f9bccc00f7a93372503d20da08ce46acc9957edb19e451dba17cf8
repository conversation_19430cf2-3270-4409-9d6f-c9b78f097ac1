import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import * as getSchema from '../../src/adapters/getSchema';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { createCase } from '../../src/controllers/createCase';
import {
  checkFolderExistsQuery,
  checkNameExistsQuery,
  createContentTemplateQuery,
  createFolderQuery,
  createRequestSDOQuery,
} from '../../src/api/queries';

const schemaId = 'schemaId';
const limit = 100;
const offset = 0;

describe('createCase', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  const time = new Date();
  jest.spyOn(global, 'Date').mockReturnValue(time);

  const getSchemaId = jest.spyOn(getSchema, 'getSchemaIdAdapter');
  getSchemaId.mockImplementation(() => Promise.resolve(schemaId));

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('creates a case', async () => {
    const req = getMockReq({
      body: {
        parentFolderId: '123456789',
        name: 'test name',
        description: 'description',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() => Promise.resolve({
      folder: {
        id: '123456789',
        folderPath: [],
        contentTemplates: [],
      },
    }));

    callGQL.mockImplementationOnce(() => Promise.resolve({
      folder: {
        childFolders: {
          records: ['folder1'],
        },
      },
    }));

    callGQL.mockImplementationOnce(() => Promise.resolve({ createFolder: { id: '123456789', name: 'test name' , treeObjectId: req.body.parentFolderId } }));

    callGQL.mockImplementationOnce(() => Promise.resolve({ createStructuredData: { id: '123456789' } }));

    callGQL.mockImplementationOnce(() => Promise.resolve({ createFolderContentTemplate: { id: '123456789' } }));

    await createCase(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(5);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, checkFolderExistsQuery(req.body.parentFolderId));
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, checkNameExistsQuery(req.body.parentFolderId, req.body.name, limit, offset));
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createFolderQuery,
      {
        description: req.body.description,
        name: req.body.name,
        parentFolderId: req.body.parentFolderId,
        userId: undefined,
      });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createRequestSDOQuery,
      {
        name: req.body.name,
        folderTreeObjectId: req.body.parentFolderId,
        currentTime: time.toISOString(),
        schemaId,
      });
    expect(gql.callGQL).toHaveBeenCalledWith(
      req.headers,
      createContentTemplateQuery,
      {
        schemaId,
        folderId: req.body.parentFolderId,
        sdoId: req.body.parentFolderId,
      });
    expect(getSchema.getSchemaIdAdapter).toHaveBeenCalledTimes(1);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.InsertedSuccess);
    expect(res.json).toHaveBeenLastCalledWith({
      name: 'test name',
      caseId: '123456789',
    })
  });

  it('fails to create a case w/o name', async () => {
    const req = getMockReq({
      body: {
        parentFolderId: '123456789',
        name: '',
        description: 'description',
      },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await createCase(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.NameIsRequired });
  });
});

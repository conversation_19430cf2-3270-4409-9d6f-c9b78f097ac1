import { memo, useRef, useLayoutEffect, useState, useEffect } from 'react';
import { List, ListRowRenderer } from 'react-virtualized';

import { TranscriptionViewableWords } from '@common-modules/mediaDetails/models';

import * as styles from '../styles.scss';
import { TRANS_HEIGHT_PERCENT } from '@helpers/constants';

const len = (t: TranscriptionViewableWords) => t.stopTimeMs - t.startTimeMs;

const DisplayView = ({
  transcription,
  focusRowIdx,
  redactedWords,
  selectedWords,
  currWord,
  searchResults,
  searchFocus,

  onTranscriptionLinesReady,
}: DisplayViewPropTypes) => {
  const filteredTranscription = transcription.filter(
    (t) => t.words !== '...' || len(t) >= 2000
  );

  const origListContainerRef = useRef<HTMLDivElement>(null);
  const virtualListContainerRef = useRef<HTMLDivElement>(null);

  const [lines, setLines] = useState<TranscriptionViewableWords[][]>([]);

  const [parentContainerHeight, setParentContainerHeight] = useState<number>(0);
  const [parentContainerWidth, setParentContainerWidth] = useState<number>(0);

  const [containerWidth, setContainerWidth] = useState<number>(0);

  // NOTE: getTranscriptionLines need to be called only when shouldRenderVirtualList is false and only in
  // useLayoutEffect hook so that view already had a chance to finish rendering
  function getTranscriptionLines(
    container: HTMLElement,
    filteredTranscription: ReadonlyArray<TranscriptionViewableWords>
  ) {
    const words = container.querySelectorAll('span');
    if (!words) {
      return [];
    }
    // !s Justified by above check
    let startTop = words[0]!.offsetTop;
    const lines: TranscriptionViewableWords[][] = [[]];
    let lineNo = 0;
    for (let i = 0; i < words.length; i++) {
      const word = words[i]!;
      if (word.offsetTop > startTop) {
        startTop = word.offsetTop;
        lines[++lineNo] = [filteredTranscription[i]!];
      } else {
        lines[lineNo]!.push(filteredTranscription[i]!);
      }
    }

    return lines;
  }

  useEffect(() => {
    if (origListContainerRef.current) {
      setContainerWidth(origListContainerRef.current.clientWidth);
    }
  }, []);

  useEffect(() => {
    function resizeHandler() {
      const container = virtualListContainerRef.current;

      if (!container) {
        return;
      }

      const newWidth = container.clientWidth;

      if (newWidth !== containerWidth) {
        // NOTE: this will make shouldRenderVirtualList to evaluate to false and will cause
        // old list to render so that we can build a new transcriptionLines data structure
        // that relies on rendered layout
        setLines([]);
      }

      setContainerWidth(newWidth);
    }

    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
    };
  });

  useLayoutEffect(() => {
    const container = origListContainerRef.current;

    if (!container) {
      return;
    }
    const lines = getTranscriptionLines(container, filteredTranscription);

    onTranscriptionLinesReady(lines);
    setLines(lines);

    function resizeHandler() {
      const container = origListContainerRef.current;
      if (!container) {
        return;
      }
      setParentContainerHeight(window.innerHeight * TRANS_HEIGHT_PERCENT);
      setParentContainerWidth(window.innerWidth);
    }
    return () => {
      window.removeEventListener('resize', resizeHandler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [containerWidth]);

  useLayoutEffect(() => {
    const container = origListContainerRef.current;
    if (!container) {
      return;
    }
    const lines = getTranscriptionLines(container, filteredTranscription);

    onTranscriptionLinesReady(lines);
    setLines(lines);
    setParentContainerHeight(
      container.clientHeight === 0
        ? window.innerHeight * TRANS_HEIGHT_PERCENT
        : container.clientHeight
    );
    setParentContainerWidth(window.innerWidth);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const shouldRenderVirtualList = lines.length > 0;

  const renderItem: ListRowRenderer = ({ index, style }) => {
    const words = lines[index];
    if (!words) {
      return <div data-testid="display-view" key={index} style={style} />;
    }
    return (
      <div data-testid="display-view" key={index} style={style}>
        {words.map(({ id, words }) => {
          const classNames = [];
          if (currWord && id === currWord.id) {
            classNames.push(styles.tracking);
          }
          if (redactedWords[id]) {
            classNames.push(styles.redacted);
          }

          if (selectedWords[id]) {
            classNames.push(styles.focused);
          }

          for (let i = 0; i < searchResults.length; i++) {
            const searchResult = searchResults[i];

            if (searchResult?.[id]) {
              classNames.push(styles.searchHighlight);

              if (i === searchFocus) {
                classNames.push(styles.searchFocus);
              }
            }
          }

          return (
            <span
              data-testid="display-view-span"
              className={classNames.join(' ')}
              key={id}
              id={id}
            >
              {words}
            </span>
          );
        })}
      </div>
    );
  };

  const view = shouldRenderVirtualList ? (
    <div ref={virtualListContainerRef}>
      <List
        className={styles.vList}
        width={parentContainerWidth}
        height={parentContainerHeight}
        rowCount={lines.length}
        rowHeight={26}
        scrollToIndex={focusRowIdx}
        rowRenderer={renderItem}
      />
    </div>
  ) : (
    <div
      className={styles.transcriptionDisplay}
      data-veritone-element="transcription-view-result"
      ref={origListContainerRef}
    >
      {filteredTranscription.map((t) => (
        <span key={t.id} id={t.id}>
          {t.words}
        </span>
      ))}
    </div>
  );

  return view;
};

export default memo(DisplayView);

export interface DisplayViewPropTypes {
  readonly transcription: ReadonlyArray<TranscriptionViewableWords>;
  readonly focusRowIdx?: number;
  readonly redactedWords: {
    [id: string]: TranscriptionViewableWords | undefined;
  };
  readonly selectedWords: {
    [id: string]: TranscriptionViewableWords | undefined;
  };
  readonly searchResults: ReadonlyArray<
    Record<string, TranscriptionViewableWords>
  >;
  currWord: TranscriptionViewableWords | undefined;
  readonly searchFocus: number;
  onTranscriptionLinesReady: (
    transcriptionLines: TranscriptionViewableWords[][]
  ) => void;
}

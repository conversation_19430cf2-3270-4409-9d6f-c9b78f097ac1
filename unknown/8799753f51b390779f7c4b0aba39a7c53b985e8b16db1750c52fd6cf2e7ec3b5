import makeStyles from '@mui/styles/makeStyles';
import { ExtendedTheme } from '@cbsa/styles/materialThemes';

export const useStyles = makeStyles((theme) => ({
  caseDetails: {
    '& .CaseName': {
      color: '#70767B',
      fontSize: '14px',
      margin: '5px 0 10px 0',
    },

    '& .Age, & .Status': {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: '20px',

      '& .Title': {
        alignItems: 'center',
        color: theme.palette.primary.main,
        display: 'flex',
        fontSize: '14px',
        fontWeight: 'bold',
        gap: '7px',
        textTransform: 'uppercase',
      },

      '& .Value': {
        color: 'black',
        fontSize: '14px',
      },

      '&:last-of-type': {
        marginBottom: '5px',
      },
    },
  },
}));

export const useStatusStyles = makeStyles((theme: ExtendedTheme) => ({
  new: {
    color: theme.palette.primary.main,
  },
  approved: {
    color: theme.palette.success.main,
  },
  processing: {
    color: theme.palette.warning.main,
  },
  error: {
    color: theme.palette.error.main,
  },
  archived: {
    color: theme.palette.text.secondary,
  },
  readyforexport: {
    color: theme.palette.info.main,
  },
  readyforreview: {
    color: theme.palette.text.primary,
  },
  processed: {
    color: theme.palette.processed.main,
  },
}));

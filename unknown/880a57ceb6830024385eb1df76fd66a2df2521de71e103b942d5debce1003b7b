import createError from 'http-errors';
import { StatusCodes } from './statusCodes';

export const Errors = {
  BadRequest: (message: string) => createError(StatusCodes.BadRequest, message),
  Forbidden: (message: string) => createError(StatusCodes.Forbidden, message),
  Unauthorized: (message: string) => createError(StatusCodes.Unauthorized, message),
  InternalServerError: (message: string) => createError(StatusCodes.InternalServerError, message),
};

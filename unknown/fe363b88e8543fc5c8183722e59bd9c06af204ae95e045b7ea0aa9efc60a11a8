import {
  CaseId,
  FolderId,
  lookupLatestMediaCommentsSchemaId,
  lookupLatestTdoLockSchemaId,
  TDOId,
  Thunk,
} from '@cbsa-modules/universal';
import moment from 'moment/moment';
import * as Actions from './actions';
import { get, isEmpty } from 'lodash';
import { modules } from '@veritone/glc-redux';
import { DEFAULT_GLOBAL_SETTINGS, LOCK_REFRESH } from '@helpers/constants';
import {
  selectConfig,
  selectConfigEngines,
} from '@common-modules/engines/selectors';
import { local, selectGlobalSettings, selectTdo } from './selectors';
import {
  AudiowaveFrame,
  BaseGlobalSettings,
  DetailTDO,
  MediaComment,
  Tag,
} from './models';
import { getGlobalSettings } from '@common-modules/mediaDetails/helpers/legacy-global-settings-helper';
import callGraph<PERSON><PERSON><PERSON>, {
  GQLApiDispatch,
  GQLApiGetState,
} from '@helpers/callGraph<PERSON>A<PERSON>';
import {
  DOWNLOAD_ASSET_JOB_STATUS,
  DOWNLOAD_ASSET_JOB_STATUS_SUCCESS,
  DOWNLOAD_ASSET_JOB_STATUS_FAILURE,
  FETCH_DELETE_LAST_ASSET,
  FETCH_DELETE_LAST_ASSET_FAILURE,
  FETCH_DELETE_LAST_ASSET_SUCCESS,
  FETCH_REDACTED_MEDIA,
  FETCH_REDACTED_MEDIA_FAILURE,
  FETCH_REDACTED_MEDIA_SUCCESS,
  LOG_AUDIT_EVENT_INIT,
  LOG_AUDIT_EVENT_SUCCESS,
  LOG_AUDIT_EVENT_FAILURE,
  FETCH_STATUS_CHANGE,
  FETCH_STATUS_CHANGE_FAILURE,
  FETCH_STATUS_CHANGE_SUCCESS,
  FETCH_TDO,
  FETCH_TDO_SUCCESS,
  FETCH_TDO_WITH_FAILURE,
  REFRESH_TDO_TASKS,
  REFRESH_TDO_TASKS_SUCCESS,
  REFRESH_TDO_TASKS_FAILURE,
  REFRESH_TDO_PRIMARY_ASSET_ID,
  REFRESH_TDO_PRIMARY_ASSET_ID_SUCCESS,
  REFRESH_TDO_PRIMARY_ASSET_ID_FAILURE,
  FETCH_UPDATE_REDACTED_FILE_METRICS,
  FETCH_UPDATE_REDACTED_FILE_METRICS_FAILURE,
  FETCH_UPDATE_REDACTED_FILE_METRICS_SUCCESS,
  IN_REDACTION_TAG_KEY,
  POLL_DOWNLOAD_ASSET,
  POLL_DOWNLOAD_ASSET_FAILURE,
  POLL_DOWNLOAD_ASSET_SUCCESS,
  RETRIEVE_TDO_FROM_JOB,
  RETRIEVE_TDO_FROM_JOB_SUCCESS,
  RETRIEVE_TDO_FROM_JOB_FAILURE,
  REDACTION_JOB_STATUS,
  REDACTION_JOB_STATUS_FAILURE,
  REDACTION_JOB_STATUS_SUCCESS,
  FETCH_UPDATE_SETTINGS,
  FETCH_UPDATE_SETTINGS_SUCCESS,
  FETCH_UPDATE_SETTINGS_FAILURE,
  FETCH_LATEST_TDO_AUDIT_LOG_ASSET,
  FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS,
  FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE,
  FETCH_WAVEFORM_SUCCESS,
  FETCH_WAVEFORM_FAILURE,
  FETCH_CASE_MEDIA_SUCCESS,
  FETCH_CASE_MEDIA_FAILURE,
  FETCH_MEDIA_COMMENTS_SUCCESS,
  FETCH_MEDIA_COMMENTS_FAILURE,
  UPDATE_COMMENT_SUCCESS,
  UPDATE_COMMENT_FAILURE,
  FETCH_MEDIA_COMMENTS_USER_SUCCESS,
  FETCH_MEDIA_COMMENTS_USER_FAILURE,
  FETCH_MEDIA_USER_SUCCESS,
  FETCH_MEDIA_USER_FAILURE,
  TDO_LOCK_FOUND_SUCCESS,
  TDO_LOCK_FOUND_FAILURE,
  UpdateTDOLockRequest,
  UpdateTDOLockResponse,
  SECURE_TDO_LOCK_SUCCESS,
  SECURE_TDO_LOCK_FAILURE,
  TDO_NAME_CHANGE_SUCCESS,
  TDO_NAME_CHANGE_FAILURE,
  UPDATE_COMMENT,
  CREATE_ROTATE_VIDEO_JOB_SUCCESS,
  CREATE_ROTATE_VIDEO_JOB_FAILURE,
} from './actions';
import type { TaskStatus } from 'veritone-types';
import { DeepWritable, Head } from 'ts-essentials';
import { GetActionCreatorPayloadT, formatTDOName } from '@utils';
import { arrayHasLength } from '@common/shared/util';
import getLastRedactedFile from '@helpers/getLastRedactedFile';
import { Action } from '@reduxjs/toolkit';
import { selectFeatureFlags } from '@common/user-permissions/selectors';
import { generateTasksAndRoutes } from '@helpers/engineJobHelper';

const {
  user: { selectUser },
} = modules;

export interface FETCH_TDO_QUERY_RESPONSE {
  temporalDataObject: {
    id: TDOId;
    name: string;
    status: 'downloaded' | 'recording' | 'recorded';
    foldersTreeObjectIds: FolderId[];
    thumbnailUrl: string;
    modifiedDateTime: string;
    startDateTime: string;
    stopDateTime: string;
    details: {
      name?: string;
      settings?: BaseGlobalSettings;
      tags?: ReadonlyArray<Tag>;
      isExport?: boolean;
    } & Record<string, any>;
    tasks: {
      records: {
        id: string;
        status: TaskStatus;
        createdDateTime: string;
        startedDateTime: string;
        completedDateTime: string;
        engineId: string;
        engine: {
          id: string;
          categoryId: string;
          name: string;
        };
        jobId: string;
        job: {
          status: string;
        };
        ioFolders: {
          referenceId: string;
        }[];
      }[];
    };
    primaryAsset: {
      readonly id: string;
      readonly name: string | null;
      readonly description: string | null;
      readonly signedUri: string;
      readonly details?: {
        readonly virtualAsset?: boolean;
      };
      readonly jsondata: {
        readonly mediaDuration?: number;
      };
      readonly contentType: string;
    };
    redactedMediaAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        modifiedDateTime: string;
        signedUri: string;
        details: Record<string, any>;
        fileData: {
          md5sum: string;
        };
      }[];
    };
    auditLogAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        signedUri: string;
        details: Record<string, any>;
      }[];
    };
    redactExportAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        signedUri: string;
        details: Record<string, any>;
      }[];
    };
    waveformAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        modifiedDateTime: string;
        signedUri: string;
        details: Record<string, any>;
        fileData: {
          md5sum: string;
        };
      }[];
    };
    thumbnailAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        signedUri: string;
        details: Record<string, any>;
      }[];
    };
    transcriptAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        signedUri: string;
        details: Record<string, any>;
      }[];
    };
    streams: Array<{
      protocol: 'hls' | 'dash';
      uri: string;
    }>;
  };
}

interface TASKS_QUERY_RESPONSE {
  temporalDataObject: {
    tasks: FETCH_TDO_QUERY_RESPONSE['temporalDataObject']['tasks'];
  };
}

export function getFetchTdoSuccessPayload(tdoInfo: FETCH_TDO_QUERY_RESPONSE): {
  temporalDataObject: DetailTDO;
} {
  const settings = tdoInfo?.temporalDataObject?.details?.settings; // important type difference for type cleanup
  return {
    temporalDataObject: {
      ...tdoInfo.temporalDataObject,
      details: {
        ...tdoInfo.temporalDataObject.details,
        settings: settings
          ? getGlobalSettings(settings)
          : DEFAULT_GLOBAL_SETTINGS,
      },
    },
  };
}

export const fetchTdo =
  (id: string) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const limit = 300;

    const query = `
      query($id: ID!, $limit: Int) {
        temporalDataObject(id: $id) {
          id
          name
          status
          foldersTreeObjectIds
          thumbnailUrl
          modifiedDateTime
          startDateTime
          stopDateTime
          details
          tasks (limit: $limit) {
            records {
              id
              status
              #taskOutput
              createdDateTime
              startedDateTime
              completedDateTime
              engineId
              engine {
                id
                categoryId
                name
              }
              jobId
              job {
                status
              }
              ioFolders {
                referenceId
              }
            }
          }
          primaryAsset(assetType: "media") {
            id
            name
            description
            signedUri
            jsondata
            contentType
          }
          redactedMediaAssets: assets(assetType: ["redacted-media", "redacted-media-clip"]) {
            records {
              id
              name
              contentType
              modifiedDateTime
              signedUri
              details
              fileData {
                md5sum
              }
            }
          }
          auditLogAssets: assets(limit: 1, orderBy: createdDateTime, orderDirection: desc, assetType: ["redact-audit-log", "blur-audit-log"]) {
            records {
              id
              name
              contentType
              signedUri
              details
            }
          }
          redactExportAssets: assets(limit: 1, orderBy: createdDateTime, orderDirection: desc, assetType: ["redact-export", "redacted-audit-log"]) {
            records {
              id
              name
              contentType
              signedUri
              details
            }
          }
          waveformAssets: assets(assetType: "waveform-csv") {
            records {
              id
              name
              contentType
              modifiedDateTime
              signedUri
              details
              fileData {
                md5sum
              }
            }
          }
          thumbnailAssets: assets(assetType: "thumbnail-sprite") {
            records {
              id
              name
              contentType
              signedUri
              details
            }
          }
          transcriptAssets: assets(limit: 1, orderBy: createdDateTime, orderDirection: desc, assetType: "redacted-transcript") {
            records {
              id
              name
              contentType
              signedUri
              details
            }
          }
          streams {
            protocol
            uri
          }
        }
      }
    `;

    const tdoInfo = await callGraphQLApi<FETCH_TDO_QUERY_RESPONSE>({
      actionTypes: [FETCH_TDO, '', FETCH_TDO_WITH_FAILURE],
      query,
      variables: { id, limit },
      dispatch,
      getState,
      bailout: undefined,
    });

    // check if need load additional tasks
    if (
      tdoInfo &&
      get(tdoInfo, 'temporalDataObject.tasks.records', []).length === limit
    ) {
      const taskQuery = `
      query($id: ID!, $offset: Int, $limit: Int) {
        temporalDataObject: temporalDataObject(id: $id) {
          tasks(limit: $limit, offset: $offset) {
            records {
              id
              status
              createdDateTime
              startedDateTime
              completedDateTime
              engineId
              engine {
                id
                categoryId
                name
              }
              jobId
              job {
                status
              }
              ioFolders {
                referenceId
              }
            }
          }
        }
      }
    `;

      let allTasks = tdoInfo.temporalDataObject.tasks?.records || [];

      let offset = 0;
      let taskCount = allTasks.length;
      // load more tasks
      while (taskCount === limit) {
        offset += limit;
        const result = await callGraphQLApi<TASKS_QUERY_RESPONSE>({
          actionTypes: ['', '', FETCH_TDO_WITH_FAILURE],
          query: taskQuery,
          variables: {
            id: id,
            limit: limit,
            offset: offset,
          },
          bailout: undefined,
          dispatch,
          getState,
        });

        // concatenate additional tasks
        allTasks = allTasks.concat(
          result?.temporalDataObject?.tasks?.records || []
        );
        taskCount = result?.temporalDataObject?.tasks?.records?.length || 0;
      }
      if (tdoInfo?.temporalDataObject?.tasks) {
        tdoInfo.temporalDataObject.tasks.records = allTasks;
      } else {
        tdoInfo.temporalDataObject.tasks = {
          records: allTasks,
        };
      }
    }

    if (tdoInfo) {
      dispatch(FETCH_TDO_SUCCESS(getFetchTdoSuccessPayload(tdoInfo)));
    }

    return tdoInfo;
  };

export interface REFRESH_TDO_TASKS_QUERY_RESPONSE {
  temporalDataObject: {
    id: TDOId;
    tasks: {
      records: {
        id: string;
        status: TaskStatus;
        createdDateTime: string;
        startedDateTime: string;
        completedDateTime: string;
        engineId: string;
        engine: {
          id: string;
          categoryId: string;
          name: string;
        };
        jobId: string;
        job: {
          status: string;
        };
        ioFolders: {
          referenceId: string;
        }[];
      }[];
    };
  };
}

export const refreshTdoTasks =
  (id: string, limit: number) =>
  async (
    dispatch: Head<
      Parameters<typeof callGraphQLApi<REFRESH_TDO_TASKS_QUERY_RESPONSE>>
    >['dispatch'],
    getState: Head<
      Parameters<typeof callGraphQLApi<REFRESH_TDO_TASKS_QUERY_RESPONSE>>
    >['getState']
  ) => {
    const query = `
      query($id: ID!, $limit: Int) {
        temporalDataObject(id: $id) {
          id
          tasks (limit: $limit) {
            records {
              id
              status
              #taskOutput
              createdDateTime
              startedDateTime
              completedDateTime
              engineId
              engine {
                id
                categoryId
                name
              }
              jobId
              job {
                status
              }
              ioFolders {
                referenceId
              }
            }
          }
        }
      }
    `;

    return await callGraphQLApi<REFRESH_TDO_TASKS_QUERY_RESPONSE>({
      actionTypes: [
        REFRESH_TDO_TASKS,
        REFRESH_TDO_TASKS_SUCCESS,
        REFRESH_TDO_TASKS_FAILURE,
      ],
      query,
      variables: { id, limit },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface RefreshTdoPrimaryAssetResponse {
  temporalDataObject: {
    id: string;
    primaryAsset: {
      id: string;
    };
  };
}
export const refreshTdoPrimaryAsset =
  (id: string) =>
  async (
    dispatch: GQLApiDispatch<RefreshTdoPrimaryAssetResponse>,
    getState: GQLApiGetState
  ) => {
    const query = `
      query($id: ID!) {
        temporalDataObject(id: $id) {
          id
          primaryAsset(assetType: "media") {
            id
          }
        }
      }
    `;

    return await callGraphQLApi<RefreshTdoPrimaryAssetResponse>({
      actionTypes: [
        REFRESH_TDO_PRIMARY_ASSET_ID,
        REFRESH_TDO_PRIMARY_ASSET_ID_SUCCESS,
        REFRESH_TDO_PRIMARY_ASSET_ID_FAILURE,
      ],
      query,
      variables: { id },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export const fetchWaveformCsv =
  (signedUri: string) => async (dispatch: (action: Action) => void) => {
    try {
      const respText = await fetch(signedUri, {
        method: 'GET',
        headers: {
          'Content-Type': 'text/csv',
        },
      }).then((resp) => resp.text());

      const respRows = respText.split('\n').filter((row) => row);
      const metadataRow = respRows.shift()?.split(',');
      if (arrayHasLength(metadataRow, 4)) {
        const respMetadata = {
          stepSize: parseFloat(metadataRow[2].split('=')[1] || ''),
          scale: parseFloat(metadataRow[3].split('=')[1] || ''),
        };

        if (
          isFinite(respMetadata.stepSize) &&
          isFinite(respMetadata.scale) &&
          respMetadata.scale !== 0 &&
          respMetadata.stepSize !== 0
        ) {
          const waveforms = respRows.map(
            (row: string, i: number): AudiowaveFrame => {
              const rowNumbers = row
                .split(',')
                .map((_string) => parseFloat(_string));

              return {
                startTime: i * respMetadata.stepSize,
                stopTime: (i + 1) * respMetadata.stepSize,
                minValue: (rowNumbers[0] || 0) / respMetadata.scale,
                maxValue: (rowNumbers[1] || 0) / respMetadata.scale,
              };
            }
          );

          dispatch(FETCH_WAVEFORM_SUCCESS(waveforms));
          return;
        }
      }
      throw new Error('Malformed waveform CSV');
    } catch (err) {
      dispatch(
        FETCH_WAVEFORM_FAILURE(
          err instanceof Error ? err.message : 'Unknown error'
        )
      );
    }
  };

export type DeleteLastAssetResponse = Record<
  string,
  {
    id: string;
  }
>;
export const deleteLastAsset =
  (action: { payload: { id?: string; ids?: string[] } }) =>
  async (
    dispatch: Head<Parameters<typeof callGraphQLApi>>['dispatch'],
    getState: Head<Parameters<typeof callGraphQLApi>>['getState']
  ) => {
    const id = action.payload?.id;
    const ids = action.payload?.ids;
    if (!id && !ids) {
      return;
    }

    const query = id
      ? `
      mutation ($id: ID!){
        deleteAsset(id: $id) {
          id
        }
      }
    `
      : `mutation deleteMultipleAssets{
        ${ids! // Safe due to prior check
          .map(
            (
              id: string,
              ind: number
            ) => `deleteAsset${ind}: deleteAsset(id: "${id}") {
              id
        }`
          )
          .join(',')}
      }`;

    return await callGraphQLApi<DeleteLastAssetResponse>({
      actionTypes: [
        FETCH_DELETE_LAST_ASSET,
        FETCH_DELETE_LAST_ASSET_SUCCESS,
        FETCH_DELETE_LAST_ASSET_FAILURE,
      ],
      query,
      variables: id ? { id } : {},
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface UpdateSettingsOnDetailsResponse {
  updateTDO: {
    id: TDOId;
    details: Record<string, any>;
  };
}
export const updateSettingsOnDetails =
  (tdoId: TDOId, settings: Record<string, any>) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const details = {
      settings,
    };

    const query = `
      mutation updateTDO($tdoId: ID!, $details: JSONData){
        updateTDO( input: {
          id: $tdoId
          details: $details
        })
        {
          id
          details
        }
      }
    `;

    return await callGraphQLApi<UpdateSettingsOnDetailsResponse>({
      actionTypes: [
        FETCH_UPDATE_SETTINGS,
        FETCH_UPDATE_SETTINGS_SUCCESS,
        FETCH_UPDATE_SETTINGS_FAILURE,
      ],
      query,
      variables: { tdoId, details },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface UpdateNameResponse {
  updateTDO: {
    id: TDOId;
    name: string;
    details: DeepWritable<DetailTDO['details']>;
  };
}

export const updateNameOnDetails =
  (nameUpdate: string) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    // TODO: Fix getState typing
    const state = getState() as Parameters<typeof local>[0];
    const tdo = local(state).tdo;
    const tdoId = tdo?.id;
    if (!tdoId) {
      return;
    }

    const name = formatTDOName(nameUpdate);

    const query = `
      mutation updateNameTDO($tdoId: ID!, $details: JSONData, $name: String){
        updateTDO( input: {
          id: $tdoId,
          name: $name,
          details: $details
        })
        {
          id
          name
          details
        }
      }
    `;

    return await callGraphQLApi<UpdateNameResponse>({
      actionTypes: ['NOOP', TDO_NAME_CHANGE_SUCCESS, TDO_NAME_CHANGE_FAILURE],
      query,
      variables: {
        tdoId,
        name,
        details: {
          ...tdo.details,
          name,
          veritoneFile: {
            ...tdo.details.veritoneFile,
            fileName: name,
            filename: name,
          },
        },
      },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface UpdateStatusResponse {
  updateTDO: {
    id: TDOId;
    details: DetailTDO['details'];
  };
}
export const updateStatusOnDetails =
  () => async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    // TODO: Type state properly
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const state = getState();
    const tdo = local(state).tdo;
    const tdoId = tdo?.id;
    if (!tdoId) {
      return;
    }

    const query = `
      mutation updateTDO($tdoId: ID!, $details: JSONData) {
        updateTDO(input: {
          id: $tdoId
          details: $details
        })
        {
          id
          details
        }
      }
    `;

    let detailTags = tdo.details?.tags;
    // handle case with empty tags
    if (!detailTags?.length) {
      detailTags = [{ value: IN_REDACTION_TAG_KEY, redactionStatus: 'Draft' }];
    }

    return await callGraphQLApi<UpdateStatusResponse>({
      actionTypes: [
        FETCH_STATUS_CHANGE,
        FETCH_STATUS_CHANGE_SUCCESS,
        FETCH_STATUS_CHANGE_FAILURE,
      ],
      query,
      variables: {
        tdoId: tdoId,
        details: { ...tdo.details, tags: detailTags },
      },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface SendEventResponse {
  emitAuditEvent: {
    id: string;
  };
}
export const sendEvent =
  (_schemaId: string, triggerAction: { payload: string }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    // TODO: Type state properly
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const state = getState();
    const tdo = local(state).tdo;
    const tdoId = tdo?.id;
    if (!tdo) {
      return;
    }
    const md5sum = get(getLastRedactedFile(tdo), 'fileData.md5sum');
    const userName = selectUser(state).userName;
    const action = triggerAction.payload;
    const payload = {
      action,
      tdoId,
      md5sum,
      userName,
      timestamp: new Date().toISOString(),
    };
    const query = `mutation ($payload: JSONData!, $application: String){
      emitAuditEvent (input: {
        application: $application
        payload: $payload
      }) {
        id
      }
    }
  `;

    return await callGraphQLApi<SendEventResponse>({
      actionTypes: [
        LOG_AUDIT_EVENT_INIT,
        LOG_AUDIT_EVENT_SUCCESS,
        LOG_AUDIT_EVENT_FAILURE,
      ],
      query,
      variables: { payload, application: 'redact' },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface PollToDownloadResponse {
  temporalDataObject: {
    assets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        modifiedDateTime: string;
        assetType: string;
        signedUri: string;
        details: Record<string, any>;
        sourceData: {
          taskId: string;
          sourceId: string;
        };
      }[];
    };
  };
}
export const pollToDownload =
  (tdoId: TDOId) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const state = getState() as Parameters<typeof local>[0];
    const id = local(state)?.tdo?.id || tdoId;
    const query = `
      query ($id: ID!){
      temporalDataObject(id: $id) {
          assets(assetType: ["redact-export", "redacted-audit-log"]) {
            records {
              id
              name
              contentType
              modifiedDateTime
              assetType
              signedUri
              details
              sourceData{
                taskId
                sourceId
              }
            }
          }
        }
      }
    `;

    return await callGraphQLApi<PollToDownloadResponse>({
      actionTypes: [
        POLL_DOWNLOAD_ASSET,
        POLL_DOWNLOAD_ASSET_SUCCESS,
        POLL_DOWNLOAD_ASSET_FAILURE,
      ],
      query,
      variables: { id },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface RetrieveTdoIdFromJobIdResponse {
  job: {
    targetId: TDOId;
    tasks: {
      records: {
        engineId: string;
      }[];
    };
  };
}
export const retrieveTdoIdFromJobId =
  (id: string) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const query = `
      query ($id: ID!) {
        job(id: $id) {
          targetId
          tasks {
            records {
              engineId
              #taskOutput
            }
          }
        }
      }
    `;

    return await callGraphQLApi<RetrieveTdoIdFromJobIdResponse>({
      actionTypes: [
        RETRIEVE_TDO_FROM_JOB,
        RETRIEVE_TDO_FROM_JOB_SUCCESS,
        RETRIEVE_TDO_FROM_JOB_FAILURE,
      ],
      query,
      variables: { id },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface UpdateLastRedactedFileMetricsResponse {
  updateAsset: {
    id: string;
  };
}
export const updateLastRedactedFileMetrics =
  () => async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const state = getState() as Parameters<typeof local>[0];
    const tdo = local(state).tdo;
    if (!tdo) {
      return;
    }
    const lastRedacted = getLastRedactedFile(tdo);
    const query = `
      mutation ($id: ID!, $details: JSONData!){
        updateAsset(input: {
          id: $id,
          details: $details
        }) {
          id
        }
      }
    `;

    return await callGraphQLApi<UpdateLastRedactedFileMetricsResponse>({
      actionTypes: [
        FETCH_UPDATE_REDACTED_FILE_METRICS,
        FETCH_UPDATE_REDACTED_FILE_METRICS_SUCCESS,
        FETCH_UPDATE_REDACTED_FILE_METRICS_FAILURE,
      ],
      query,
      variables: { id: lastRedacted?.id, details: lastRedacted?.details },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface FecthDownloadAssetStatusResponse {
  job: {
    status: string;
    createdDateTime: string;
  };
}
export const fetchDownloadAssetStatus =
  (payload: { jobId: string }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const jobId = payload.jobId;

    const query = `query($jobId: ID!) {
      job(id: $jobId) {
        status
        createdDateTime
      }
    }`;

    return await callGraphQLApi<FecthDownloadAssetStatusResponse>({
      actionTypes: [
        DOWNLOAD_ASSET_JOB_STATUS,
        DOWNLOAD_ASSET_JOB_STATUS_SUCCESS,
        DOWNLOAD_ASSET_JOB_STATUS_FAILURE,
      ],
      query,
      variables: { jobId },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface FetchRedactionJobStatusResponse {
  job: {
    status: string;
    createdDateTime: string;
  };
}
// Update MDP after Redaction job completes
export const fetchRedactionJobStatus =
  (payload: { jobId: string }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const jobId = payload.jobId;

    const query = `query($jobId: ID!) {
      job(id: $jobId) {
        status
        createdDateTime
      }
    }`;

    return await callGraphQLApi<FetchRedactionJobStatusResponse>({
      actionTypes: [
        REDACTION_JOB_STATUS,
        REDACTION_JOB_STATUS_SUCCESS,
        REDACTION_JOB_STATUS_FAILURE,
      ],
      query,
      variables: { jobId },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface FetchRedactedMediaResponse {
  temporalDataObject: {
    redactedMediaAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        modifiedDateTime: string;
        signedUri: string;
        details: Record<string, any>;
        fileData: {
          md5sum: string;
        };
      }[];
    };
    auditLogAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        signedUri: string;
        details: Record<string, any>;
      }[];
    };
    redactExportAssets: {
      records: {
        id: string;
        name: string;
        contentType: string;
        signedUri: string;
        details: Record<string, any>;
      }[];
    };
  };
}
export const fetchRedactedMedia =
  (_id: string) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const state = getState() as Parameters<typeof local>[0];
    const id = local(state).tdo?.id; // TypeError when back to homepage: Cannot read property 'id' of null
    const query = `
      query($id: ID!) {
        temporalDataObject(id: $id) {
          redactedMediaAssets: assets(assetType: ["redacted-media", "redacted-media-clip"]) {
            records {
              id
              name
              contentType
              modifiedDateTime
              signedUri
              details
              fileData {
                md5sum
              }
            }
          }
          auditLogAssets: assets(assetType: ["redact-audit-log", "blur-audit-log"]) {
            records {
              id
              name
              contentType
              signedUri
              details
            }
          }
          redactExportAssets: assets(assetType: ["redact-export", "redacted-audit-log"]) {
            records {
              id
              name
              contentType
              signedUri
              details
            }
          }
        }
      }
    `;

    return await callGraphQLApi<FetchRedactedMediaResponse>({
      actionTypes: [
        FETCH_REDACTED_MEDIA,
        FETCH_REDACTED_MEDIA_SUCCESS,
        FETCH_REDACTED_MEDIA_FAILURE,
      ],
      query,
      variables: { id },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface FetchLatestTDOAuditLogAssetResponse {
  temporalDataObject: {
    assets: {
      records: {
        id: string;
        name: string;
        assetType: string;
        contentType: string;
        signedUri: string;
      }[];
    };
  };
}

export const fetchLatestTDOAuditLogAsset =
  (tdoId: TDOId) =>
  async (
    dispatch: GQLApiDispatch<FetchLatestTDOAuditLogAssetResponse>,
    getState: GQLApiGetState
  ) => {
    const query = `
      query fetchLatestAuditLogAsset {
        temporalDataObject(id: "${tdoId}") {
          assets(limit: 1, orderBy: createdDateTime, orderDirection: desc, assetType: ["redact-audit-log", "blur-audit-log"]) {
            records {
              id
              name
              assetType
              contentType
              signedUri
            }
          }
        }
      }
    `;

    return await callGraphQLApi<FetchLatestTDOAuditLogAssetResponse>({
      actionTypes: [
        FETCH_LATEST_TDO_AUDIT_LOG_ASSET,
        FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS,
        FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE,
      ],
      query,
      variables: {},
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export interface FetchCaseMediaResponse {
  folder: {
    id: string;
    name: string;
    childTDOs: {
      records: {
        id: TDOId;
        name: string;
        status: string;
        details: Record<string, any>;
        primaryAsset: {
          id: string;
          signedUri: string;
          contentType: string;
        };
        jobs: {
          records: {
            id: string;
            createdDateTime: string;
            modifiedDateTime: string;
            status: string;
          }[];
        };
      }[];
    };
  };
}
export const fetchCaseMedia: Thunk<{
  readonly caseId: CaseId;
}> =
  ({ caseId }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const query = `
    query fetchCaseMedia($caseId: ID!) {
      folder(id: $caseId) {
      id
      name
        childTDOs(offset: 0, limit: 1000) {
          records {
            id
            name
            status
            details
            primaryAsset(assetType: "media") {
              id
              signedUri
              contentType
            }
            jobs(offset: 0, limit: 1000) {
              records {
                id
                createdDateTime
                modifiedDateTime
                status
              }
            }
            streams {
              protocol
              uri
            }
          }
        }
      }
    }`;

    return await callGraphQLApi<FetchCaseMediaResponse>({
      actionTypes: ['', FETCH_CASE_MEDIA_SUCCESS, FETCH_CASE_MEDIA_FAILURE],
      query,
      variables: { caseId },
      dispatch,
      getState,
    });
  };

export interface FetchMediaCommentsResponse {
  structuredDataObjects: {
    offset: number;
    records: Array<{ data: MediaComment }>;
  };
}

export const fetchMediaComments: Thunk<{
  readonly tdoId: TDOId;
  readonly limit: number;
  readonly offset: number;
}> =
  ({ tdoId, limit, offset }) =>
  async (
    dispatch: GQLApiDispatch<FetchMediaCommentsResponse>,
    getState: GQLApiGetState
  ) => {
    const schemaId = await lookupLatestMediaCommentsSchemaId(
      dispatch,
      getState
    );
    if (!schemaId) {
      return;
    }

    const query = `
      query fetchComments($schemaId: ID!, $limit: Int!, $offset: Int!, $tdoId: ID!) {
        structuredDataObjects(
          limit: $limit
          offset: $offset
          schemaId: $schemaId
          filter: {
            tdoId: $tdoId
          }
        ) {
          offset
          records {
            data
          }
        }
      }
      `;

    return await callGraphQLApi<FetchMediaCommentsResponse>({
      actionTypes: [
        '',
        FETCH_MEDIA_COMMENTS_SUCCESS,
        FETCH_MEDIA_COMMENTS_FAILURE,
      ],
      query,
      variables: { schemaId, limit, offset, tdoId },
      dispatch,
      getState,
    });
  };

export const updateComment: Thunk<
  GetActionCreatorPayloadT<typeof UPDATE_COMMENT>
> =
  ({ comment, modify = true, isUndo }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const currentTime = new Date().toISOString();
    const { commentId: sdoId, createdDateTime } = comment;
    const schemaId = await lookupLatestMediaCommentsSchemaId(
      dispatch,
      getState
    );

    if (!schemaId) {
      return;
    }

    const updatedComment = {
      ...comment,
      createdDateTime: createdDateTime || currentTime,
      modifiedDateTime:
        modify && !isUndo ? currentTime : comment.modifiedDateTime,
      done: true,
      mediaTimestamp: Math.trunc(comment.mediaTimestamp),
    };
    const query = `
      mutation addComment($schemaId: ID!, $sdoId: ID!, $comment: JSONData!) {
        createStructuredData(input: {
          id: $sdoId
          schemaId: $schemaId
          data: $comment
        }) {
          data
        }
      }
      `;

    return await callGraphQLApi<{
      createStructuredData: { data: MediaComment };
    }>({
      actionTypes: ['', UPDATE_COMMENT_SUCCESS, UPDATE_COMMENT_FAILURE],
      query,
      variables: { schemaId, sdoId, comment: updatedComment },
      dispatch: (a) => dispatch({ ...a, payload: { ...a.payload, isUndo } }),
      getState,
    });
  };

export interface FetchMediaCommentsUsersResponse {
  basicUserInfo: {
    id: string;
    imageUrl: string | null;
    firstName: string;
    lastName: string;
  };
}

export const fetchMediaCommentsUser: Thunk<{
  readonly userId: string;
}> =
  ({ userId }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const query = `
      query getUser($userId: ID!) {
        basicUserInfo(id: $userId) {
          id
          imageUrl
          firstName
          lastName
        }
      }
      `;

    return await callGraphQLApi<FetchMediaCommentsUsersResponse>({
      actionTypes: [
        '',
        FETCH_MEDIA_COMMENTS_USER_SUCCESS,
        FETCH_MEDIA_COMMENTS_USER_FAILURE,
      ],
      query,
      variables: { userId },
      dispatch,
      getState,
    });
  };

export interface FetchMediaUserResponse {
  me: {
    id: string;
    firstName?: string;
    lastName?: string;
    imageUrl?: string | null;
    email?: string;
  };
}
export const fetchMediaUser =
  () => async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const query = `
      query getMe {
        me {
          id
          firstName
          lastName
          imageUrl
          email
        }
      }
    `;
    return await callGraphQLApi<FetchMediaUserResponse>({
      actionTypes: ['', FETCH_MEDIA_USER_SUCCESS, FETCH_MEDIA_USER_FAILURE],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

export const deleteAllHeadDetectionResults =
  () => async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const { detectionEngineId } = selectConfig(getState());
    const tdo = selectTdo(getState());

    const assetQuery = `
      query getAssets($id: ID!) {
        temporalDataObject(id: $id) {
          assets(assetType: "vtn-standard") {
            records {
              id
              jsondata
            }
          }
        }
      }
    `;
    interface AssetQueryResponse {
      temporalDataObject: {
        assets: {
          records: {
            id: string;
            jsondata: any;
          }[];
        };
      };
    }

    const deleteAssetMutation = `
      mutation deleteAsset($id: ID!) {
        deleteAsset(id: $id) {
          id
        }
      }
    `;

    const response = await callGraphQLApi<AssetQueryResponse>({
      actionTypes: ['', '', ''],
      query: assetQuery,
      variables: { id: tdo?.id },
      dispatch,
      getState,
    });
    if (!response?.temporalDataObject) {
      throw new Error('Failed to fetch assets');
    }

    const {
      temporalDataObject: {
        assets: { records },
      },
    } = response;

    records
      .filter(({ jsondata }) => jsondata.sourceEngineId === detectionEngineId)
      .forEach(({ id }) => {
        void callGraphQLApi({
          actionTypes: ['', '', ''],
          query: deleteAssetMutation,
          variables: { id },
          dispatch,
          getState,
        });
      });
  };

export const findTdoLock: Thunk<UpdateTDOLockRequest> =
  (newLockInfo) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const { tdoId } = newLockInfo;
    const schemaId = await lookupLatestTdoLockSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const query = `
      query findTdoLock($schemaId: ID!, $tdoId: ID!) {
        structuredDataObjects(
          schemaId: $schemaId
          filter: { tdoId: $tdoId }
          orderBy: {
            field: modifiedDateTime
            direction: asc
          }
        ) {
          records {
            id
            data
          }
        }
      }`;

    const response = await callGraphQLApi<{
      structuredDataObjects: {
        records: [{ id: string; data: UpdateTDOLockResponse }];
      };
      errors: [Error];
    }>({
      actionTypes: ['', '', ''],
      query,
      variables: { schemaId, tdoId },
      dispatch,
      getState,
      throwOnError: true,
    });

    const error = response.errors?.[0];
    if (error) {
      dispatch(TDO_LOCK_FOUND_FAILURE({ newLockInfo, error }));
      return;
    }

    /* Find oldest, valid lock */
    const records = response?.structuredDataObjects?.records;
    const record = records?.find(({ data: { lastAccessed } }) => {
      const lockedTime = moment(lastAccessed);
      const currentTime = moment(newLockInfo.lastAccessed);
      const timeDifference = moment
        .duration(currentTime.diff(lockedTime))
        .asMilliseconds();

      const isExpired = timeDifference > LOCK_REFRESH * 5;
      return !isExpired;
    });

    if (record?.data && record?.id) {
      dispatch(
        TDO_LOCK_FOUND_SUCCESS({
          tdoLock: { ...record.data, id: record.id },
          newLockInfo,
        })
      );
    } else {
      dispatch(TDO_LOCK_FOUND_FAILURE({ newLockInfo }));
    }
  };

export interface SecureTdoLockResponse {
  createStructuredData: {
    id: string;
    data: Omit<UpdateTDOLockResponse, 'id'>;
  };
}
export const secureTdoLock: Thunk<
  Partial<UpdateTDOLockResponse> & Omit<UpdateTDOLockResponse, 'id'>
> =
  ({ id, name, tdoId, userId, lastAccessed }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const schemaId = await lookupLatestTdoLockSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const query = `
      mutation updateTdoLock($schemaId: ID!, $id: ID, $name: ID!, $tdoId: ID!, $userId: ID!, $lastAccessed: String!) {
        createStructuredData(
          input: {
            id: $id
            schemaId: $schemaId
            data: {
              name: $name
              tdoId: $tdoId
              userId: $userId
              lastAccessed: $lastAccessed
            }
          }
        ) {
          id
          data
        }
      }`;

    const response = await callGraphQLApi<SecureTdoLockResponse>({
      actionTypes: ['', SECURE_TDO_LOCK_SUCCESS, SECURE_TDO_LOCK_FAILURE],
      query,
      variables: { schemaId, id, name, tdoId, userId, lastAccessed },
      dispatch,
      getState,
      throwOnError: true,
    });

    // TODO: I don't think errors is possible with `throwOnError: true` - validate
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const error = (response as any).errors?.[0];
    if (isEmpty(id) || error) {
      Actions.checkTdoLock({ name, userId, tdoId, lastAccessed });
    } else {
      dispatch(
        Actions.removeTdoLock(tdoId, userId, response?.createStructuredData?.id)
      );
    }

    return response;
  };

export const removeTdoLock: Thunk<{
  tdoId: string;
  userId: string;
  dontDeleteId?: string;
}> =
  ({ tdoId, userId, dontDeleteId }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const schemaId = await lookupLatestTdoLockSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const findTdoLocksQuery = `
      query findTdoLock($schemaId: ID!, $tdoId: ID!, $userId: ID!) {
        structuredDataObjects(
          schemaId: $schemaId
          filter: { tdoId: $tdoId, userId: $userId }
          orderBy: {
            field: modifiedDateTime
            direction: asc
          }
        ) {
          records { id }
        }
      }`;

    const deleteSdoMutation = `
      mutation deleteTdoLock($schemaId: ID!, $id: ID!) {
        deleteStructuredData(input: {
          id: $id
          schemaId: $schemaId
        }) {
          id
        }
      }`;

    const data = await callGraphQLApi<{
      structuredDataObjects: {
        records: [{ id: string }];
      };
      searchMedia: { jsondata: { results: [UpdateTDOLockResponse] } };
    }>({
      actionTypes: ['', '', ''],
      query: findTdoLocksQuery,
      variables: { schemaId, tdoId, userId },
      dispatch,
      getState,
    });

    const tdoLocks = data?.structuredDataObjects?.records;
    tdoLocks
      ?.filter(({ id }) => id !== dontDeleteId)
      .map(({ id }) => {
        void callGraphQLApi({
          actionTypes: ['', '', ''],
          query: deleteSdoMutation,
          variables: { schemaId, id },
          dispatch,
          getState,
        });
      });
  };

export const updateTDOStatus =
  (status: string) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const state = getState() as Parameters<typeof local>[0];
    const tdo = local(state).tdo;
    const tdoId = tdo?.id;
    if (!tdoId) {
      return;
    }

    const query = `
      mutation updateTDO($tdoId: ID!, $status: String) {
        updateTDO(input: {
          id: $tdoId
          status: $status
        })
        {
          id
          status
        }
      }
    `;

    return await callGraphQLApi<{
      updateTDO: {
        id: string;
        status: string;
      };
    }>({
      actionTypes: ['', '', ''],
      query,
      variables: {
        tdoId: tdoId,
        status,
      },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

export const rotateVideo =
  (
    tdoId: string,
    rotationDegrees: number,
    detectObjects: {
      headObjects: boolean;
      personObjects: boolean;
    }
  ) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    // TODO: Type result of getState properly
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const state = getState();

    const {
      glcIngestionEngineId,
      defaultClusterId,
      detectionEngineId,
      outputWriterEngineId,
    } = selectConfigEngines(state);

    const runDetection =
      detectObjects.headObjects || detectObjects.personObjects;

    const {
      faceDetectionThreshold,
      videoType,
      detectionRate,
      legacyClustering,
      clusterSimThreshold,
    } = selectGlobalSettings(state);

    const featureFlags = selectFeatureFlags(state);

    const [tasks, routes] = generateTasksAndRoutes({
      glcIngestionEngineId,
      outputWriterEngineId,
      ingestionPayload: {
        rotate: rotationDegrees,
      },
      runDetection,
      ...(runDetection && {
        detectionOptions: {
          engineId: detectionEngineId,
          confidenceThreshold: (faceDetectionThreshold || 60) / 100,
          videoType,
          detectionRate,
          detectHead: detectObjects.headObjects,
          detectNotepad: featureFlags.detectNotepads,
          detectCard: featureFlags.detectCards,
          detectPerson: detectObjects.personObjects,
          legacyClustering:
            featureFlags.devLegacyCluster && legacyClustering
              ? legacyClustering
              : undefined,
          clusterSimThreshold:
            (!featureFlags.devLegacyCluster || !legacyClustering) &&
            clusterSimThreshold
              ? clusterSimThreshold
              : undefined,
        },
      }),
    });

    const input = {
      targetId: tdoId,
      clusterId: defaultClusterId,
      tasks,
      routes,
    };

    const query = `mutation($input: CreateJob!) {
      createJob(input: $input) {
        id
        targetId
        status
      }
    }`;

    return await callGraphQLApi({
      actionTypes: [
        '',
        CREATE_ROTATE_VIDEO_JOB_SUCCESS,
        CREATE_ROTATE_VIDEO_JOB_FAILURE,
      ],
      query,
      variables: {
        input,
      },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

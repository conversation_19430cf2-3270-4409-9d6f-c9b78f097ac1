import Konva from 'konva';
import { map } from 'rxjs/operators';
import { includes } from 'lodash';
import { search } from '@worker';
import { aToB } from '../../Playhead/renderer/utils';
import { MapRenderProps } from '../../renderer';
import { DETECTION_TYPES } from '@helpers/constants';

function getOffsetTime(currentTime: number, videoOffset: number) {
  return Math.max(currentTime - videoOffset, 0);
}

export const mapRender = (layer: Konva.Layer) =>
  map(
    ({
      startMs,
      stopMs,
      collection,
      selected,
      width,
      videoOffset,
      color,
    }: MapRenderProps) => {
      layer.destroyChildren();

      const p2m = aToB(0, width, startMs, stopMs);

      let rect: Konva.Rect | undefined;
      let rectUnselected: Konva.Rect | undefined;

      for (let px = 0; px < width; px++) {
        const currentTime = getOffsetTime(p2m(px), videoOffset);
        const polys = search(collection, currentTime);
        const isHit = polys.some(
          (p) =>
            !!selected[p.subsegmentId] &&
            includes(DETECTION_TYPES, p.type) &&
            !p.isDeleted
        );

        if (isHit && !rect) {
          // close any rectUnselected
          if (rectUnselected) {
            rectUnselected.setSize({
              width: px - rectUnselected.x(),
              height: 32,
            });
            layer.add(rectUnselected);
            rectUnselected = undefined;
          }

          rect = new Konva.Rect({
            x: px,
            y: 0,
            fill: color,
          });
        } else if ((!isHit || px === width - 1) && rect) {
          rect.setSize({ width: px - rect.x(), height: 32 });
          layer.add(rect);
          rect = undefined;
        }

        // draw unselected
        if (!isHit) {
          const isUnselected = polys
            .filter(
              (p) => currentTime >= p.startTimeMs && currentTime <= p.stopTimeMs
            )
            .some(
              (p) =>
                !selected[p.subsegmentId] &&
                includes(DETECTION_TYPES, p.type) &&
                !p.isDeleted
            );

          if (isUnselected && !rectUnselected) {
            rectUnselected = new Konva.Rect({
              x: px,
              y: 0,
              fill: color,
              opacity: 0.1,
            });
          } else if ((!isUnselected || px === width - 1) && rectUnselected) {
            rectUnselected.setSize({
              width: px - rectUnselected.x(),
              height: 32,
            });
            layer.add(rectUnselected);
            rectUnselected = undefined;
          }
        }
      }

      return layer;
    }
  );

import call<PERSON>rap<PERSON><PERSON><PERSON><PERSON>, {
  GQLApiDispatch,
  GQLApiGetState,
} from '@helpers/callGraphQLA<PERSON>';

import {
  NOOP,
  // GET_ENGINE_RESULTS_FROM_URI_FAILURE,
  // GET_ENGINE_RESULTS_FROM_URI_SUCCESS,
  CANCEL_JOB_FAILURE,
  CANCEL_JOB_SUCCESS,
  // CREATE_ENGINE_JOB_SUCCESS,
  // CREATE_ENGINE_JOB_FAILURE,
  CHECK_JOB_STATUS_FAILURE,
  CHECK_JOB_STATUS_SUCCESS,
  // QUERY_ENGINE_RESULTS_RECORD_SUCCESS,
  // QUERY_ENGINE_RESULTS_RECORD_FAILURE,
} from './actions';
import { TDOId } from '../../universal/models/Brands';
import type { JobStatus } from 'veritone-types';
import { markWorkerAction } from '@utils';
// import { selectOpticalEngineId } from './selectors';
// const { user } = modules;

/**
 * Load engine results from the JSON file.
 * @param {{ signedUri: string }} payload
 */
// export const getEngineResultsFromURIService = ({ signedUri }) => async (
//   dispatch,
//   getState
// ) => {
//   return await callApi({
//     signedUri,
//     actionTypes: [
//       NOOP,
//       GET_ENGINE_RESULTS_FROM_URI_SUCCESS,
//       GET_ENGINE_RESULTS_FROM_URI_FAILURE,
//     ],
//     dispatch,
//     getState,
//     bailout: undefined,
//   });
// };

/**
 * Query for the engine results to get the JSON data URI.
 * @param {{ tdoId: TDOId }} payload
 */
// export const queryEngineResultsRecordService = ({ tdoId }) => async (
//   dispatch,
//   getState
// ) => {
//   const query = `
//     query($id: ID!) {
//       temporalDataObject(id: $id) {
//         assets(type: "redacted-udr-tracking-result" orderBy: createdDateTime limit: 1) {
//           records {
//             signedUri
//           }
//         }
//       }
//     }
//   `;

//   return await callGraphQLApi({
//     actionTypes: [
//       NOOP,
//       QUERY_ENGINE_RESULTS_RECORD_SUCCESS,
//       QUERY_ENGINE_RESULTS_RECORD_FAILURE,
//     ],
//     query,
//     variables: { id: tdoId },
//     dispatch,
//     getState,
//   });
// };

/**
 * Create an optical tracking job.
 * @param {{ engineId: string; payload: ??? }} payload
 */
// export const createEngineJobService = ({ tdoId, payload }) => async (
//   dispatch,
//   getState
// ) => {
//   const state = getState();
//   const engineId = selectOpticalEngineId(state);
//   const email = user.selectUser(state).email;
//   const query = `
//     mutation ($id: ID!, $engineId: ID!, $payload: JSONData!){
//      createJob(input: {
//         targetId: $id,
//         tasks: [
//           {
//             engineId: $engineId,
//             payload: $payload
//           }
//         ]
//       }) {
//         id
//         targetId
//       }
//     }
//   `;

//   return await callGraphQLApi({
//     actionTypes: [NOOP, CREATE_ENGINE_JOB_SUCCESS, CREATE_ENGINE_JOB_FAILURE],
//     query,
//     variables: {
//       id: tdoId,
//       engineId,
//       payload: {
//         ...payload,
//         user: email,
//       },
//     },
//     dispatch,
//     getState,
//   });
// };

/**
 * Cancel a running job.
 * @param {{ jobId: string }} action
 */
export interface CancelJobServiceResponse {
  cancelJob: {
    id: string;
    message: string;
  };
}
export const cancelJobService =
  ({ jobId }: { jobId: string }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const query = `
      mutation cancelJob($id: ID!) {
        cancelJob(id: $id){
          id
          message
        }
      }
    `;

    return await callGraphQLApi<CancelJobServiceResponse>({
      actionTypes: [NOOP, CANCEL_JOB_SUCCESS, CANCEL_JOB_FAILURE],
      query,
      variables: { id: jobId },
      dispatch: (a) => dispatch(markWorkerAction(a)),
      getState,
      bailout: undefined,
    });
  };

/**
 * Get the status of a job.
 * @param {{ jobId: string }} action
 */
export interface CheckJobStatusServiceResponse {
  job: {
    id: string;
    targetId: TDOId;
    status: JobStatus; // TODO: I don't think this is JobStatus, according to the document, it may be TaskStatus
  };
}
export const checkJobStatusService =
  ({ jobId }: { jobId: string }) =>
  async (dispatch: GQLApiDispatch, getState: GQLApiGetState) => {
    const query = `query($jobId: ID!) {
      job(id: $jobId) {
        id
        targetId
        status
      }
    }`;

    return await callGraphQLApi<CheckJobStatusServiceResponse>({
      actionTypes: [NOOP, CHECK_JOB_STATUS_SUCCESS, CHECK_JOB_STATUS_FAILURE],
      query,
      variables: { jobId },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

import Konva from 'konva';
import { OperatorFunction } from 'rxjs';
import { AudioRedactionSlice } from '@common-modules/mediaDetails/models/Transcription';

import { TranscriptionPropTypes } from './TranscriptionPropTypes';

export type MapRenderOperator = (
  layer: Konva.Layer
) => OperatorFunction<MapRenderProps, Konva.Layer>;

export interface MapRenderProps {
  readonly audiowaves: TranscriptionPropTypes['audiowaves'];
  readonly startMs: number;
  readonly stopMs: number;
  readonly list: TranscriptionPropTypes['list'];
  readonly selected: TranscriptionPropTypes['selected'];
  readonly width: number;
  readonly timeRange: AudioRedactionSlice;
}

export interface RendererOutput {
  render: (
    {
      audiowaves,
      startWindowMs,
      stopWindowMs,
      list,
      selected,
    }: TranscriptionPropTypes,
    timeRange: AudioRedactionSlice
  ) => void;
  destroy: () => void;
}

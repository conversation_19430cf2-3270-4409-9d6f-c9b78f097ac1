// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`React testing library test renders correctly FacesWrapperTab with ClustersResultsTab 1`] = `
<DocumentFragment>
  <div
    class="MuiBox-root css-j7qwjs"
    data-testid="cluster-results-tab-view"
    style="width: 100%; height: 100%;"
  >
    <div
      class="makeStyles-clusterListViewContainer-24 MuiBox-root css-j7qwjs"
    >
      <div
        class="MuiBox-root css-1k6yql2"
        style="min-height: 60px; color: rgb(153, 153, 153);"
      >
        <div
          class="makeStyles-filterItem-25"
          style="margin: 0px 5px; cursor: pointer;"
        >
          All
        </div>
        <div
          class="makeStyles-filterItem-25"
          style="margin: 0px 5px; cursor: pointer;"
        >
          UDR
        </div>
        <div
          class="makeStyles-filterItem-25"
          style="margin: 0px 5px; cursor: pointer;"
        >
          Head
        </div>
        <div
          class="makeStyles-filterItem-25"
          style="margin: 0px 5px; cursor: pointer;"
        >
          POI
        </div>
        <div
          class="makeStyles-filterItem-25"
          style="margin: 0px 5px; cursor: pointer;"
        >
          Manual
        </div>
        <div
          class="makeStyles-filterItem-25"
          style="margin: 0px 5px; cursor: pointer;"
        >
          Laptop
        </div>
        <div
          class="makeStyles-filterItem-25"
          style="margin: 0px 5px; cursor: pointer;"
        >
          License plate / vehicle
        </div>
      </div>
      <div
        class="makeStyles-fg10auto-2 makeStyles-sort-11 MuiBox-root css-15ums5i"
        style="padding-bottom: 0.5rem;"
      >
        <div
          class="makeStyles-col1-3 MuiBox-root css-70qvj9"
        >
          <div
            data-testid="checkbox"
            data-veritone-component="check-box"
          >
            <div
              class="checkBox"
              data-testid="checkbox-content"
            >
              <div
                class="whiteBox"
              />
            </div>
          </div>
        </div>
        <div
          class="makeStyles-fg10auto-2 MuiBox-root css-1hyoz7m"
        >
          <div
            class="makeStyles-colTime-9 makeStyles-activeSort-12 MuiBox-root css-1hyoz7m"
          >
            <span
              class="makeStyles-clickable-19"
            >
              Most Recent
              <div
                style="margin-left: 0.25rem;"
              >
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-ptiqhd-MuiSvgIcon-root"
                  data-testid="ArrowUpwardIcon"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="m4 12 1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"
                  />
                </svg>
              </div>
            </span>
          </div>
        </div>
      </div>
      <div
        class="makeStyles-listContainer-13"
        data-testid="results-tab-object"
        id="cluster-list-container"
      >
        <div
          style="overflow: auto; will-change: transform; height: 344px; width: 100%;"
        >
          <div
            style="position: relative; width: 100%; min-height: 100%; height: 116px;"
          >
            <div
              class="makeStyles-listRow-14 MuiBox-root css-1xhj18k"
              data-test="clusterList-row"
              id="cluster-string"
              style="min-height: 116px; position: absolute; top: 0px; left: 0px; width: 100%; height: 116px;"
            >
              <div
                class="MuiBox-root css-lvyu5j"
              >
                <div
                  data-testid="checkbox"
                  data-veritone-component="check-box"
                >
                  <div
                    class="checkBox"
                    data-testid="checkbox-content"
                  >
                    <div
                      class="whiteBox"
                    />
                  </div>
                </div>
              </div>
              <div
                class="makeStyles-polyPreview-16 MuiBox-root css-1irlvzw"
              >
                <img
                  data-testid="q-image"
                  loading="lazy"
                  src=""
                  style="border: 1px solid #00a077; width: 65px; height: 80px;"
                />
              </div>
              <div
                class="makeStyles-fg10auto-2 MuiBox-root css-j7qwjs"
              >
                <div
                  class="makeStyles-label-5 MuiBox-root css-0"
                  title=""
                >
                  <section
                    class="cTEwrapper wrapperClass"
                    data-testid="click-to-edit"
                  >
                    <span
                      class="cTEtext textClass"
                      data-testid="click-to-edit-span"
                    >
                      <p
                        class="editP"
                        data-testid="click-to-edit-p"
                      />
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium editIcon css-i4bv87-MuiSvgIcon-root"
                        data-testid="click-to-edit-icon-edit"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
                        />
                      </svg>
                    </span>
                  </section>
                </div>
                <div
                  style="display: flex; flex-direction: row; justify-content: space-between; margin-right: 5px; margin-top: 12px;"
                >
                  <div
                    data-test="detection-name"
                    style="margin-left: 10px;"
                  >
                    <img
                      alt="face"
                      src=""
                    />
                    <span
                      style="color: rgb(0, 160, 119);"
                    >
                      Head
                    </span>
                  </div>
                  <div>
                    <div
                      data-test="time-period-udr"
                    >
                      <span
                        class="makeStyles-timePeriod-18"
                      >
                        00:00 - 00:00
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`React testing library test renders correctly FacesWrapperTab with EngineProcessing 1`] = `
<DocumentFragment>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column css-cxlod3-MuiGrid-root"
    data-testid="engine-processing-root"
    id="engine-processing-root"
  >
    <div
      class="MuiGrid-root MuiGrid-item tabName css-13i4rnv-MuiGrid-root"
      data-testid="engine-processing-tab-name"
    >
      Object Detection
    </div>
    <div
      class="MuiGrid-root MuiGrid-container css-1lym95h-MuiGrid-root"
    >
      <span
        class="MuiCircularProgress-root MuiCircularProgress-indeterminate progress MuiCircularProgress-colorPrimary css-18lrjg1-MuiCircularProgress-root"
        data-testid="engine-processing-circular-progress"
        role="progressbar"
        style="width: 60px; height: 60px;"
      >
        <svg
          class="MuiCircularProgress-svg css-1idz92c-MuiCircularProgress-svg"
          viewBox="22 22 44 44"
        >
          <circle
            class="MuiCircularProgress-circle MuiCircularProgress-circleIndeterminate MuiCircularProgress-circleDisableShrink css-79nvmn-MuiCircularProgress-circle"
            cx="44"
            cy="44"
            fill="none"
            r="21.5"
            stroke-width="1"
          />
        </svg>
      </span>
    </div>
    <div
      class="MuiGrid-root MuiGrid-item processingText css-13i4rnv-MuiGrid-root"
      style="color: black;"
    >
      <h6
        class="MuiTypography-root MuiTypography-h6 MuiTypography-alignCenter css-14lt4z8-MuiTypography-root"
        data-testid="engine-processing-title"
      >
        Detecting Objects...
      </h6>
      <p
        class="MuiTypography-root MuiTypography-body2 MuiTypography-alignCenter css-1f0n3rh-MuiTypography-root"
        data-test="typo-body2"
        data-testid="engine-processing-message"
      >
        This may take some time depending on video length.
      </p>
      <p
        class="MuiTypography-root MuiTypography-body2 MuiTypography-alignCenter css-1f0n3rh-MuiTypography-root"
        data-test="typo-body2"
        data-testid="engine-processing-goto-home"
      >
        You can wait for the processing to finish here
        <br />
        or go back to the
        <span
          class="homeButton"
          data-testid="loading-state-home-page-button"
          data-veritone-element="loading-state-home-page-button"
        >
          HOMEPAGE
        </span>
      </p>
    </div>
  </div>
</DocumentFragment>
`;

exports[`React testing library test renders correctly FacesWrapperTab with TabErrorState 1`] = `
<DocumentFragment>
  <div
    class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 makeStyles-container-26 css-1ps6pg7-MuiPaper-root"
    data-testid="faces-error-state-tab"
    data-veritone-component="faces-error-state-tab"
  >
    <div
      class="MuiGrid-root MuiGrid-container makeStyles-tabNameWrapper-27 css-11lq3yg-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 makeStyles-tabName-28 css-1idn90j-MuiGrid-root"
      >
        Object Detection
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column makeStyles-content-33 css-t0zib5-MuiGrid-root"
    >
      <div
        class="MuiGrid-root css-vj1n65-MuiGrid-root"
      >
        <img
          class="makeStyles-errorStateIcon-30"
          src=""
        />
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-6 css-1osj8n2-MuiGrid-root"
        style="width: 292px; max-width: 292px;"
      >
        <p
          class="MuiTypography-root MuiTypography-body2 MuiTypography-alignCenter makeStyles-message-31 css-1f0n3rh-MuiTypography-root"
        >
          There was an error running Object Detection engine. Would you like to rerun the engine again?
        </p>
      </div>
      <div
        class="MuiGrid-root css-vj1n65-MuiGrid-root"
      >
        <button
          class="MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButtonBase-root makeStyles-buttonDerp-32 css-110v46k-MuiButtonBase-root-MuiButton-root"
          data-veritone-element="face-error-state-rerun-button"
          tabindex="0"
          type="button"
        >
          RERUN ENGINE
          <span
            class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
          />
        </button>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`React testing library test renders correctly FacesWrapperTab with TabNullState 1`] = `
<DocumentFragment>
  <div
    class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 makeStyles-container-34 css-1ps6pg7-MuiPaper-root"
    data-testid="faces-null-state-tab"
    data-veritone-component="faces-null-state-tab"
  >
    <div
      class="MuiGrid-root MuiGrid-container makeStyles-tabNameWrapper-35 css-11lq3yg-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 makeStyles-tabName-36 css-1idn90j-MuiGrid-root"
      >
        Object Detection
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column makeStyles-content-41 css-t0zib5-MuiGrid-root"
    >
      <div
        class="MuiGrid-root css-vj1n65-MuiGrid-root"
      >
        <img
          class="makeStyles-nullStateIcon-38"
          src=""
        />
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-6 css-156ssuc-MuiGrid-root"
      >
        <p
          class="MuiTypography-root MuiTypography-body2 MuiTypography-alignCenter makeStyles-message-39 css-vyz0hw-MuiTypography-root"
          data-testid="faces-null-state-tab-message"
        >
          Automatically detect and place bounding boxes on objects within this video, making redaction faster and easier for you.
        </p>
      </div>
      <div
        class="MuiGrid-root css-vj1n65-MuiGrid-root"
      >
        <button
          class="MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButtonBase-root makeStyles-buttonDerp-40 css-110v46k-MuiButtonBase-root-MuiButton-root"
          data-veritone-element="faces-null-state-detect-faces-button"
          tabindex="0"
          type="button"
        >
          DETECT HEADS AND OBJECTS
          <span
            class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
          />
        </button>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

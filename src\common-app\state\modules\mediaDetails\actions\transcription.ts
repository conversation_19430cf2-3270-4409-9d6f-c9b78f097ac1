import { createAction } from '@reduxjs/toolkit';
import {
  AudioRedactionSlice,
  AudioRedactionDetails,
  TranscriptionViewable,
  TranscriptionViewableWords,
} from '../models';
import { IndividualRedactionCode } from '@redact-modules/redactionCodes/models';
export const UNREDACT_WORDS_LOG = createAction<UnredactWordsRequest>(
  'vtn-component-transcription/UNREDACT_WORDS_LOG'
);
export const actionUnredactWordsLog = (payload: UnredactWordsRequest) =>
  UNREDACT_WORDS_LOG(payload);

export const SELECT_WORDS = createAction<SelectWordsRequest>(
  'vtn-component-transcription/SELECT_WORDS'
);
export type SelectWordsRequest = TranscriptionViewable['transcription'];
export const actionSelectWords = (payload: SelectWordsRequest) =>
  SELECT_WORDS(payload);

export const SELECT_RANGE_WORDS = createAction<SelectRangeWordsRequest>(
  'vtn-component-transcription/SELECT_RANGE_WORDS'
);
export type SelectRangeWordsRequest = [
  TranscriptionViewableWords,
  TranscriptionViewableWords,
];
export const actionRangeSelectWords = (payload: SelectRangeWordsRequest) =>
  SELECT_RANGE_WORDS(payload);

export const DESELECT_WORDS = createAction<DeselectWordsRequest>(
  'vtn-component-transcription/DESELECT_WORDS'
);
export type DeselectWordsRequest = TranscriptionViewable['transcription'];
export const actionDeselectWords = (payload: DeselectWordsRequest) =>
  DESELECT_WORDS(payload);

export const DESELECT_ALL = createAction<DeselectAllRequest>(
  'vtn-component-transcription/DESELECT_ALL'
);
export type DeselectAllRequest = void;
export const actionDeselectAll = (payload: DeselectAllRequest) =>
  DESELECT_ALL(payload);

export const REDACT_WORDS = createAction<RedactWordsRequest>(
  'vtn-component-transcription/REDACT_WORDS'
);
export type RedactWordsRequest = TranscriptionViewable['transcription'];
export const actionRedactWords = (payload: RedactWordsRequest) =>
  REDACT_WORDS(payload);

export const UNREDACT_WORDS = createAction<UnredactWordsRequest>(
  'vtn-component-transcription/UNREDACT_WORDS'
);
export type UnredactWordsRequest = TranscriptionViewable['transcription'];
export const actionUnredactWords = (payload: UnredactWordsRequest) =>
  UNREDACT_WORDS(payload);

export const REDACT_SLICE = createAction<RedactSliceRequest>(
  'vtn-component-transcription/REDACT_SLICE'
);
export interface RedactSliceRequest {
  redact: Array<AudioRedactionSlice>;
  unredact?: Array<AudioRedactionSlice>;
}
export const actionRedactSlice = (payload: RedactSliceRequest) =>
  REDACT_SLICE(payload);

export const SELECT_SLICE = createAction<SelectSliceRequest>(
  'vtn-component-transcription/SELECT_SLICE'
);
export type SelectSliceRequest = AudioRedactionSlice;
export const actionSelectSlice = (payload: SelectSliceRequest) =>
  SELECT_SLICE(payload);

export const UNREDACT_SLICE = createAction<UnredactSliceRequest>(
  'vtn-component-transcription/UNREDACT_SLICE'
);
export type UnredactSliceRequest = Array<AudioRedactionSlice>;
export const actionUnredactSlice = (payload: UnredactSliceRequest) =>
  UNREDACT_SLICE(payload);

export const DISABLE_UNREDACT_SLICE_LOG = createAction<boolean>(
  'vtn-component-transcription/DISABLE_UNREDACT_SLICE_LOG'
);
export const actionDisableUnredactSliceLog = (
  disableUnredactAuditLog: boolean
) => DISABLE_UNREDACT_SLICE_LOG(disableUnredactAuditLog);

export const UNREDACT_ALL = createAction(
  'vtn-component-transcription/UNREDACT_ALL'
);
export type UnredactAllRequest = void;
export const actionUnredactAll = () => UNREDACT_ALL();

export const VIEW_NOTES = createAction<ViewNotesRequest>(
  'vtn-component-transcription/VIEW_NOTES'
);
export type ViewNotesRequest = AudioRedactionSlice;
export const actionViewNotes = (payload: ViewNotesRequest) =>
  VIEW_NOTES(payload);

export const SET_CURRENT_WORDS_FOR_NOTES = createAction<string>(
  'vtn-component-transcription/SET_CURRENT_WORDS_FOR_NOTES'
);
export const actionSetCurrentWordsForNotes = (payload: string) =>
  SET_CURRENT_WORDS_FOR_NOTES(payload);

export const CLOSE_NOTES = createAction(
  'vtn-component-transcription/CLOSE_NOTES'
);
export const actionCloseNotes = () => CLOSE_NOTES();

export const UPDATE_NOTES = createAction<UpdateNotesRequest>(
  'vtn-component-transcription/UPDATE_NOTES'
);
export type UpdateNotesRequest = AudioRedactionSlice & {
  2: AudioRedactionDetails;
};
export const actionUpdateNotes = (payload: UpdateNotesRequest) =>
  UPDATE_NOTES(payload);

export const DELETE_NOTES = createAction<DeleteNotesRequest>(
  'vtn-component-transcription/DELETE_NOTES'
);
export type DeleteNotesRequest = AudioRedactionSlice;
export const actionDeleteNotes = (payload: DeleteNotesRequest) =>
  DELETE_NOTES(payload);

export const ADD_REDACTION_CODE_TO_TRANSCRIPTION =
  createAction<AddRedactionCodeToTranscriptionRequest>(
    'vtn-component-transcription/ADD_REDACTION_CODE_TO_TRANSCRIPTION'
  );
export interface AddRedactionCodeToTranscriptionRequest {
  word: TranscriptionViewableWords;
  redactionCode: IndividualRedactionCode;
}
export const actionAddRedactionCodeToTranscription = (
  payload: AddRedactionCodeToTranscriptionRequest
) => ADD_REDACTION_CODE_TO_TRANSCRIPTION(payload);

export const REMOVE_REDACTION_CODE_FROM_TRANSCRIPTION =
  createAction<RemoveRedactionCodeFromTranscriptionRequest>(
    'vtn-component-transcription/REMOVE_REDACTION_CODE_FROM_TRANSCRIPTION'
  );
export type RemoveRedactionCodeFromTranscriptionRequest =
  TranscriptionViewableWords;
export const actionRemoveRedactionCodeFromTranscription = (
  payload: RemoveRedactionCodeFromTranscriptionRequest
) => REMOVE_REDACTION_CODE_FROM_TRANSCRIPTION(payload);

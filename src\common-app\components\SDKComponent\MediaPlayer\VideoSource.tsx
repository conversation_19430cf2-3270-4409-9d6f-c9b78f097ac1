/* cspell:words mpegurl webvtt xywh */
import shaka from 'shaka-player';
import { find, includes, isUndefined } from 'lodash';
import { memo, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { DetailTDO } from '@common/state/modules/mediaDetails/models';
import {
  actionMediaHasAudio,
  actionVideoHasLoaded,
  selectThumbnailAssets,
  setThumbnailTracks,
  setShakaPlayer,
} from '@common-modules/mediaDetails';
import { Action } from '@reduxjs/toolkit';
import { RESET_TEXT_TRACKS } from '@common/state/modules/player';

shaka.polyfill.installAll();

const getTimeStringFromDuration = (duration: number) => {
  const date = new Date(0);
  date.setSeconds(duration);
  return date.toISOString().substring(11, 23);
};

const getStreamUri = (streams: Props['streams'], protocol: string) => {
  const stream = find(streams, { protocol });
  return stream?.uri;
};

const VideoSource = ({ video, src, streams, isMediaPlayer }: Props) => {
  const dispatch = useDispatch();
  const thumbnailAssets = useSelector(selectThumbnailAssets);
  const [source, setSource] = useState<string | null>(null);
  const [videoHasLoaded, setVideoHasLoaded] = useState(false);

  useEffect(() => {
    const playerSrc = src;
    const player = loadPlayer({
      video,
      playerSrc,
      streams,
      thumbnailAssets,
      setThumbnailTracks,
      setShakaPlayer,
      isMediaPlayer,
    });
    if (!videoHasLoaded) {
      video?.addEventListener('loadeddata', () => setVideoHasLoaded(true));
    }
    video?.load();
    return () => {
      video?.removeEventListener('loadeddata', () => setVideoHasLoaded(true));
      setShakaPlayer(null);
      dispatch(RESET_TEXT_TRACKS());
      void player?.destroy();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [src, streams, video]);

  useEffect(() => {
    if (videoHasLoaded) {
      dispatch(actionVideoHasLoaded(true));
      // Supports Chrome, firefox, Safari, IE and Edge.
      const hasAudio = Boolean(
        video?.mozHasAudio ||
          Boolean(video?.webkitAudioDecodedByteCount) ||
          Boolean(video?.audioTracks?.length)
      );
      // If none of the browser specific checks above have a valid response, then setting the value to true by default so user can run transcript.
      if (
        video &&
        isUndefined(video.mozHasAudio) &&
        isUndefined(video.webkitAudioDecodedByteCount) &&
        isUndefined(video.audioTracks)
      ) {
        dispatch(actionMediaHasAudio(true));
      } else {
        dispatch(actionMediaHasAudio(hasAudio));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [videoHasLoaded]);

  const loadPlayer = ({
    video,
    playerSrc,
    streams,
    thumbnailAssets,
    setThumbnailTracks,
    setShakaPlayer,
    isMediaPlayer,
  }: {
    video: HTMLVideoElement | undefined;
    playerSrc: string | null;
    streams: ReadonlyArray<{ protocol: string; uri: string }> | null;
    thumbnailAssets: DetailTDO['thumbnailAssets'] | null;
    setThumbnailTracks: (thumbnailTracks: shaka.Track) => Action<string>;
    setShakaPlayer: (shakaPlayer: shaka.Player) => Action<string>;
    isMediaPlayer?: boolean;
  }) => {
    const dashUri = getStreamUri(streams, 'dash');
    const hlsUri = getStreamUri(streams, 'hls');
    const localStreamUri = dashUri || hlsUri;
    // check if browser supports playing hls & dash with shaka player
    const browserSupportsShaka = shaka.Player.isBrowserSupported();
    let sourceUri;
    if (video && localStreamUri && browserSupportsShaka) {
      const shakaPlayer = loadShakaPlayer(
        video,
        localStreamUri,
        thumbnailAssets,
        (shakaPlayer) => dispatch(setThumbnailTracks(shakaPlayer)),
        (newTrack) => dispatch(setShakaPlayer(newTrack)),
        isMediaPlayer
      );
      return shakaPlayer;
    } else if (hlsUri && video?.canPlayType('application/vnd.apple.mpegurl')) {
      // iOS does not work with the shaka-player. check if browser has native HLS support,
      // if it does then set src to HLS manifest (primarily for safari on iOS)
      sourceUri = hlsUri;
    } else {
      sourceUri = playerSrc;
    }

    if (sourceUri && sourceUri !== source) {
      setSource(sourceUri);
    }
  };

  return source ? <source src={source || undefined} /> : null;
};

function loadShakaPlayer(
  video: HTMLVideoElement | undefined,
  streamUri: string,
  thumbnailAssets: DetailTDO['thumbnailAssets'] | null,
  setThumbnailTracks: (thumbnailTracks: shaka.Track) => void,
  setShakaPlayer: (shakaPlayer: shaka.Player) => void,
  isMediaPlayer?: boolean
) {
  const shakaPlayer = new shaka.Player(video);
  shakaPlayer.configure({
    streaming: {
      durationBackoff: 0.005,
    },
  });

  let domain: string;
  try {
    const { apiRoot } = window.config;
    domain = new URL(apiRoot).hostname.split('.').slice(-2).join('.');
  } catch {
    domain = 'veritone.com';
  }
  // TODO if session cookie is not available, will need to set Authorization header on request using auth token
  if (
    includes(streamUri, `${domain}/media-streamer/stream`) ||
    includes(streamUri, `${domain}/v3/stream/`)
  ) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    shakaPlayer?.getNetworkingEngine()?.registerRequestFilter(function (
      type: shaka.net.NetworkingEngine.RequestType,
      request: any
    ) {
      if (type === shaka.net.NetworkingEngine.RequestType.MANIFEST) {
        request.allowCrossSiteCredentials = true;
      }
    });

    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    shakaPlayer?.getNetworkingEngine()?.registerResponseFilter(
      // TODO: unclear why this is async -- may be required by registerResponseFilter
      async (
        type: shaka.net.NetworkingEngine.RequestType,
        response: { data: ArrayBuffer }
        // eslint-disable-next-line require-await, @typescript-eslint/require-await
      ) => {
        if (type === shaka.net.NetworkingEngine.RequestType.MANIFEST) {
          const manifestText = shaka.util.StringUtils.fromUTF8(response.data);
          const updatedManifestText = manifestText.replace(
            'mimeType="video/mp4" codecs="mp4a.40.2"',
            'mimeType="audio/mp4" codecs="mp4a.40.2"'
          );
          const updatedManifestBuffer =
            shaka.util.StringUtils.toUTF8(updatedManifestText);
          response.data = updatedManifestBuffer;
        }
        return;
      }
    );
  }
  shakaPlayer
    .load(streamUri)
    .then(async () => {
      if (thumbnailAssets && isMediaPlayer) {
        const thumbnailAsset = thumbnailAssets.records[0];
        if (thumbnailAsset) {
          // Must replace & with a valid escaped char
          const thumbnailSpriteUri = thumbnailAsset.signedUri.replace(
            /&/g,
            '&amp;'
          );
          const details = thumbnailAsset.details;
          // TODO: `details` should be typed and validated before use here
          const numCols = details['numColumns'] as number;
          const numThumbnails = details['numThumbnails'] as number;
          const thumbnailDuration = details['thumbnailDuration'] as number;
          const thumbnailWidth = details['thumbnailWidth'] as number;
          const thumbnailHeight = details['thumbnailHeight'] as number;
          const mediaDuration = details['mediaDuration'] as number;
          const lines: string[] = [];
          for (
            let curThumbnail = 0;
            curThumbnail < numThumbnails;
            curThumbnail++
          ) {
            const currentRow = Math.floor(curThumbnail / numCols); // Must be a rounded int value
            const currentCol = curThumbnail % numCols;
            const startDuration = getTimeStringFromDuration(
              curThumbnail * thumbnailDuration
            );
            const endSeconds =
              numThumbnails !== curThumbnail
                ? (curThumbnail + 1) * thumbnailDuration
                : mediaDuration;
            const endDuration = getTimeStringFromDuration(endSeconds);
            const pixelX = currentCol * thumbnailWidth;
            const pixelY = currentRow * thumbnailHeight;
            if (curThumbnail === 0) {
              lines.push('WEBVTT');
            }
            lines.push(`${startDuration} --> ${endDuration}`);
            lines.push(
              `${thumbnailSpriteUri}#xywh=${pixelX},${pixelY},${thumbnailWidth},${thumbnailHeight}\n`
            );
          }
          const vtt = lines.join('\n');
          const blob = new File([vtt], 'track.vtt', { type: 'text/vtt' });
          const thumbnailSpriteUrl = URL.createObjectURL(blob);
          const newTrack = await shakaPlayer.addThumbnailsTrack(
            thumbnailSpriteUrl,
            'text/vtt'
          );
          // Sets in Redux store to be used in ControlBar
          setShakaPlayer(shakaPlayer);
          setThumbnailTracks(newTrack);
          URL.revokeObjectURL(thumbnailSpriteUrl);
        }
      }
      return;
    })
    .catch((err) => {
      console.error('error loading video with shaka player', err);
    });
  return shakaPlayer;
}

interface Props {
  video?: HTMLVideoElement;
  src: string | null;
  streams: ReadonlyArray<{ protocol: string; uri: string }> | null;
  isVideoChild: boolean /* Important. The Component with `isVideoChild` attribute will be added into video-react `Video` component. */;
  isMediaPlayer?: boolean;
}

export default memo(VideoSource);

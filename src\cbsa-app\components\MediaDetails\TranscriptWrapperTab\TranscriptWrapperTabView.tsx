import { memo } from 'react';

import { TabState } from '@common-modules/mediaDetails/models';
import EngineProcessing from '../../EngineProcessing';
import TranscriptionConnection from '../../Transcription';
import TranscriptNullStateTab from './TranscriptNullStateTab';
import { TranscriptWrapperTabPropTypes } from './TranscriptWrapperTabPropTypes';
import TranscriptFailedStateTab from './TranscriptFailedStateTabView';
import CircularProgress from '@mui/material/CircularProgress';

import { Grid2 as Grid, Paper, Typography } from '@mui/material';

import * as styles from './styles.scss';

const route = ({
  onGoHomePage,
  tabState,
  videoLoaded,
}: TranscriptWrapperTabPropTypes) => {
  switch (tabState) {
    case TabState.ENGINE_RUNNING:
      return (
        <EngineProcessing
          tabName="Transcription"
          title="Transcribing Audio..."
          progressSize={60}
          onGoHomePage={onGoHomePage}
          textColor="rgba(255,255,255,0.87)"
        />
      );
    case TabState.LOADING:
      return <TranscriptLoadingStateTab />;
    case TabState.EMPTY:
      return <TranscriptEmptyStateTab />;
    case TabState.NOT_RUN:
      return <TranscriptNullStateTab videoLoaded={videoLoaded} />;
    case TabState.FAILED:
      return <TranscriptFailedStateTab videoLoaded={videoLoaded} />;
    default:
      return <TranscriptionConnection />;
  }
};

const TranscriptLoadingStateTab = () => (
  <Paper
    className={styles.container}
    data-veritone-component="transcription-empty-state-tab"
    data-testid="transcription-empty-state-tab"
  >
    <Grid container className={styles.tabNameWrapper}>
      <Grid size={{ xs: 12 }} className={styles.tabName}>
        Transcription
      </Grid>
    </Grid>
    <Grid
      container
      alignItems="center"
      direction="column"
      className={styles.content}
    >
      <Grid>
        <CircularProgress
          variant="indeterminate"
          disableShrink
          thickness={2}
          size={60}
          classes={{
            colorPrimary: styles.progress,
          }}
        />
      </Grid>
    </Grid>
  </Paper>
);

const TranscriptEmptyStateTab = () => (
  <Paper
    className={styles.container}
    data-veritone-component="transcription-empty-state-tab"
    data-testid="transcription-empty-state-tab"
  >
    <Grid container className={styles.tabNameWrapper}>
      <Grid size={{ xs: 12 }} className={styles.tabName}>
        Transcription
      </Grid>
    </Grid>
    <Grid
      container
      alignItems="center"
      direction="column"
      className={styles.content}
    >
      <Grid>
        <i className={'icon-transcription ' + styles.nullStateIcon} />
      </Grid>
      <Grid size={{ xs: 6 }}>
        <Typography
          align="center"
          variant="body2"
          color={'secondary'}
          className={styles.message}
        >
          No transcribable audio was found in this media.
        </Typography>
      </Grid>
    </Grid>
  </Paper>
);

const TranscriptWrapperTab = (props: TranscriptWrapperTabPropTypes) => (
  <Grid
    id="TranscriptWrapperTab"
    size="grow"
    container
    direction="column"
    justifyContent="flex-start"
    // TODO: update to use styled instead
    // classes={{ container: styles.overflowHidden }}
  >
    {route(props)}
  </Grid>
);

export default memo(TranscriptWrapperTab);

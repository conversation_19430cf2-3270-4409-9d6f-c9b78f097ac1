import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';

const TrimDialog = ({
  showConfirmationModal,
  handleClose,
  handleTrim,
}: TrimDialogProps) => (
  <Dialog
    open={showConfirmationModal}
    onClose={handleClose}
    style={{ zIndex: 19999 }}
    data-testid="trim-dialog"
  >
    <DialogTitle data-testid="trim-dialog-title">
      Continue with Trimming?
    </DialogTitle>
    <DialogContent data-testid="trim-dialog-content">
      <DialogContentText data-testid="trim-dialog-content-text">
        Trimming will delete any UDR, head detects or transcription before and
        after user-defined trim region. <br />
        Do you wish to proceed?
      </DialogContentText>
    </DialogContent>
    <DialogActions data-testid="trim-dialog-action">
      <Button
        onClick={handleClose}
        variant="text"
        data-testid="trim-dialog-button-cancel"
      >
        {' '}
        cancel{' '}
      </Button>
      <Button
        onClick={handleTrim}
        variant="contained"
        color="primary"
        style={{ backgroundColor: '#2196F3' }}
        data-testid="trim-dialog-button-trim"
      >
        {' '}
        proceed{' '}
      </Button>
    </DialogActions>
  </Dialog>
);

export default TrimDialog;

export interface TrimDialogProps {
  readonly showConfirmationModal: boolean;
  readonly handleClose: () => void;
  readonly handleTrim: () => void;
}

import { all, fork, take, takeLatest } from 'typed-redux-saga/macro';
import { DOWNLOAD_QSG, REQUEST_USER_ORGANIZATION_SUCCESS } from '../actions';

// interface Action<P> {
//   type: string;
//   payload: P;
// }

export function* trackingUserInfoSagas() {
  yield* all([fork(watchQSGDownload), fork(onWatchUserInfo)]);
}

const sendEvent = <D extends Record<string, any>>(name: string, metadata?: D) =>
  window.Intercom?.('trackEvent', name, metadata);

function* onWatchUserInfo() {
  // let lastLicenseType: string | undefined;
  let lastRole: string | undefined;
  yield* takeLatest(REQUEST_USER_ORGANIZATION_SUCCESS, function* ({ payload }) {
    // if (
    //   payload.me.organization.jsondata.billing &&
    //   payload.me.organization.jsondata.billing.type !== lastLicenseType
    // ) {
    //   lastLicenseType =
    //     payload.me.organization.jsondata.billing?.type;
    //   yield sendEvent('License - Type', { license: lastLicenseType });
    // }
    if (payload.me.organization.jsondata.accountProfile !== lastRole) {
      // TODO: fix types - jsondata should be validated before dispatch
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      lastRole = payload.me.organization.jsondata.accountProfile;
      yield sendEvent('User - Role', { role: lastRole });
    }
  });
}

// Redact: QSG Download
function* watchQSGDownload() {
  // TODO: Tracking through pendo link ?
  yield* take(DOWNLOAD_QSG);
  yield sendEvent('Redact: QSG Download');
}

import { CSSProperties } from 'react';
import { GUID } from '@common/shared/util';
import {
  BoundingPolyRect,
  PolyCenter,
} from '@common-modules/mediaDetails/models/BoundingPoly';
import {
  GlobalSettings,
  OverlayPreviewOptionsType,
  UDRsPolyAssetGroup,
  UDRsPolyAssetGroupSeriesItem,
} from '@common/state/modules/mediaDetails/models';
import { GroupedBoundingPoly } from '@common/web-worker';
import { GroupIdAndType } from '@common/state/modules/mediaDetails';
import { IndividualRedactionCode } from '@redact/state/modules/redactionCodes/models';
import { OBJECT_TYPE } from '@helpers/constants';

export interface OverlayPropType {
  readonly playerPaused: boolean;
  readonly currentTime: number;
  readonly confirmLabel?: string;
  readonly readOnly?: boolean;
  readonly addOnly?: boolean;
  readonly overlayPositioningContext?: OverlayPosition;
  readonly wrapperStyles: CSSProperties;
  readonly defaultBoundingBoxStyles?: CSSProperties;
  readonly unselectedBoundingBoxStyles?: CSSProperties;
  readonly stagedBoundingBoxStyles?: CSSProperties;
  readonly stylesByObjectType?: Record<OBJECT_TYPE | 'disabled', CSSProperties>;
  readonly toolBarOffset?: number;
  readonly onAddBoundingBox?: (
    data: {
      boundingPoly: BoundingPolyRect;
      id: string;
      groupId: string;
    },
    opts?: { shift: boolean }
  ) => void;
  readonly onDeleteBoundingBox?: (boundingBox: GroupedBoundingPoly) => void;
  readonly onChangeBoundingBox?: (boundingBox: GroupedBoundingPoly) => void;
  readonly onChangeBoundingBoxStart?: (
    boundingBox: GroupedBoundingPoly
  ) => void;
  readonly onChangeBoundingBoxStop?: (
    boundingBox: GroupedBoundingPoly,
    hasChanged?: boolean
  ) => void;
  readonly onSprayPaintBoxChangeStop?: (
    boundingBox: GroupedBoundingPoly
  ) => void;
  readonly onBoxShapeChange?: (
    boundingBox: GroupedBoundingPoly,
    udrGroupIds: string[],
    detectionGroups: GroupIdAndType[]
  ) => void;
  readonly onBoxRedactionChange?: (
    boundingBox: GroupedBoundingPoly,
    udrGroupIds: string[],
    detectionGroups: GroupIdAndType[]
  ) => void;
  readonly onBoxCodeChange?: (
    objectId: string,
    RedactionCode: IndividualRedactionCode | undefined,
    udrGroupIds: string[],
    detectionGroups: GroupIdAndType[]
  ) => void;
  readonly initialBoundingBoxPolys?: Array<GroupedBoundingPoly>;
  readonly showUnselectedBoundingPolySeries?: Array<GroupedBoundingPoly>;
  readonly setSelectedGroupsByGroupId?: (groupId: string) => void;
  readonly setMergeGroupId?: (groupId: string) => void;
  readonly setMergeClusterId?: (clusterId: string, selected: boolean) => void;
  readonly actionMenuItems?: ReadonlyArray<{
    label: string;
    onClick: () => void;
  }>;
  readonly autofocus?: boolean;
  readonly highlightedOverlay?: {
    id: string;
    groupId: string;
    timeMs: number;
    highlightGroupUDR?: {
      [x: string]: UDRsPolyAssetGroupSeriesItem;
    };
  };
  readonly selectedUDRGroupId?: GUID;
  readonly maxMs?: number;
  readonly udrGroup?: UDRsPolyAssetGroup | null;
  readonly lastActivePoly?: GroupedBoundingPoly | null;
  readonly isTimeStampOpen?: boolean;
  readonly onCloseTimeStamp?: () => void;
  readonly clearLocalOverlayBeingUpdated?: () => void;
  readonly isRedactionConfigOpen?: boolean;
  readonly onCloseRedactionConfig?: () => void;
  readonly isAddRedactionCodeOpen?: boolean;
  readonly onCloseAddRedactionCode?: () => void;
  readonly overlayPreviewOption?: OverlayPreviewOptionsType;
  readonly globalSettings: GlobalSettings;
  readonly featureFlags: Record<string, any>;
  readonly boundingBoxScale?: number;
}

export type BoundingPolyObjectNew = Omit<
  GroupedBoundingPoly,
  'boundingPoly'
> & {
  boundingPoly: PolyCenter;
};

export type BoundingBoxPoly = GroupedBoundingPoly & BoundingPolyRect;

export interface OverlayPosition {
  top: number;
  left: number;
  width: number;
  height: number;
}

export interface StagedBoundingBoxPosition {
  x: number;
  y: number;
  origX: number;
  origY: number;
  width: number;
  height: number;
}

.upgradeAccountDescription {
  text-align: center;
  margin-bottom: 25px;
  font-size: 16px;
  line-height: 26px;
  color: #4a4a4a;
}
.payPerUserNote {
  text-align: right;
  margin-top: 10px;
  color: #666;
  font-size: 12px;
  line-height: 24px;
}
.upgradeTypeBox {
  border: 2px solid #e4f1fd;
  border-radius: 4px;
  background-color: #fff;
  width: 320px;

  .upgradeType {
    text-align: center;
    font-size: 18px;
    line-height: 30px;
    padding: 5px 0;
    font-weight: 500;
    border-bottom: 1px solid #e4f1fd;
    text-transform: uppercase;
  }

  .priceInfo {
    padding-top: 20px;

    .basePrice {
      color: rgb(74, 74, 74, 0.3);
      font-size: 36px;
      font-weight: 500;
      text-decoration: line-through;
    }

    .pricePerHr {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      padding: 0 15px;

      .price {
        color: #4a4a4a;
        font-size: 36px;
        font-weight: 500;
        margin-right: 10px;
      }

      .perHour {
        color: #666;
        font-size: 15px;
        line-height: 25px;
        padding-bottom: 6px;
      }
    }
  }

  .billingInfo {
    color: #666;
    padding: 18px 40px 10px;
    font-size: 13px;
    line-height: 25px;
    text-align: center;
  }

  .perkList {
    border-top: 1px solid #e4f1fd;
    padding-top: 20px;

    li {
      color: #666;
      font-size: 12px;
      line-height: 24px;
    }
  }

  .letsTalk {
    font-size: 24px;
    font-weight: 500;
    text-align: center;
    padding-top: 20px;
  }

  .letsTalkDescription {
    color: #666;
    font-size: 13px;
    line-height: 25px;
    text-align: center;
    padding: 10px 40px;
  }

  .actionContainer {
    padding: 0 16px 18px;

    .upgradeActionButton {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 60px;
      background-color: #2196f3;
      .btnContent {
        display: flex;
        flex-direction: column;
        .bottom {
          color: #fff;
          font-size: 12px;
          line-height: 12px;
          text-align: center;
          text-transform: lowercase;
          font-weight: 400;
        }
      }
      .btnContent > * {
        flex: 1 100%;
      }
    }
  }

  .getStartedActionButton {
    padding: 25px 16px 18px;
  }

  .intervalSelectorContainer {
    padding-top: 20px;

    .intervalSelector {
      width: 90px;
      font-size: 11px;
      border-radius: 100px;
      text-transform: capitalize;
    }

    .containedSelectedInterval {
      background-color: #2196f3;
    }

    .textSelectedInterval {
      color: #9b9b9b;
    }
  }
}

.upgradeTypeBoxLink {
  color: #666;
}

import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { ThemeProvider, useTheme } from '@mui/material/styles';
import FileMenu from './components/FileMenu';
import Logo from '@resources/images/cbsa-logo.svg';

import { actionInitAiware } from '@common-modules/notification/actions';
import { defaultTheme } from '@cbsa/styles/materialThemes';

let timer: null | number = null;
let widget = '';
const AppBarContent = ({ title }: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const theme = useTheme();
  const dispatch = useDispatch();

  // Wait till the aiware.js has loaded to the window object
  useEffect(() => {
    waitTillAiwareLoaded((aiware) => {
      dispatch(actionInitAiware(aiware));
      const apiRoot = window.config.apiRoot || '';
      const graphQLEndpoint = window.config.graphQLEndpoint || '';
      const applicationId =
        window.config.veritoneAppId || '766e9916-9536-47e9-8dcb-dc225654bab3';

      aiware.init(
        {
          applicationId,
          baseUrl: `${apiRoot}/${graphQLEndpoint}`,
          withAuth: true,
          // betaFeatures: true,
        },
        () => setIsLoaded(true)
      );
    });
    return () => {
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // mounts the aiware widget;
  useEffect(() => {
    if (isLoaded) {
      // isLoaded guards against window.aiware not existing
      widget = window.aiware!.mountWidget({
        name: 'APP_BAR',
        elementId: 'CBSA',
        config: {
          logoSrc: Logo,
          backgroundColor: theme.palette.primary.main,
          help: true,
          leftNav: true,
          zIndex: 10000,
          onClickAppbarMenu: () => setIsOpen(!isOpen),
          searchBarMountId: 'page-title',
        },
      });

      const pageTitleElement = document.getElementById('page-title');
      const pageTitle = document.createElement('h2');
      pageTitle.innerText = title || '';
      pageTitle.style.fontSize = '18px';
      pageTitle.style.fontFamily = 'Dosis';
      pageTitle.style.fontWeight = '600';
      pageTitle.style.textAlign = 'center';
      pageTitle.style.flex = '1';

      if (title !== 'undefined — ') {
        pageTitleElement?.firstChild
          ? pageTitleElement.replaceChild(
              pageTitle,
              pageTitleElement.firstChild
            )
          : pageTitleElement?.appendChild(pageTitle);
      }
    }
    return () => {
      if (widget) {
        window.aiware?.unmountWidget(widget);
        widget = '';
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, isLoaded, title]);

  // take a function as an input, runs it once aiware
  // is loaded onto window object
  const waitTillAiwareLoaded = (
    inputFn: (aiware: NonNullable<typeof window.aiware>) => unknown
  ) => {
    if (!isLoaded && window.aiware) {
      if (timer) {
        window.clearTimeout(timer);
        timer = null;
      }
      inputFn(window.aiware);
    } else {
      timer = window.setTimeout(() => {
        timer = null;
        waitTillAiwareLoaded(inputFn);
      }, 1000);
    }
  };

  return <FileMenu isOpen={isOpen} />;
};

interface Props {
  readonly title?: string;
}

const AppBar = ({ title }: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <AppBarContent title={title} />
  </ThemeProvider>
);

export default AppBar;

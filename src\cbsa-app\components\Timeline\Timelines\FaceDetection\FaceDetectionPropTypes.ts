import { TimelinesPropTypes } from '../TimelinesPropTypes';
import { BoundingPolyTree } from '@worker';

export interface FaceDetectionPropTypes {
  readonly startWindowMs: TimelinesPropTypes['startWindowMs'];
  readonly stopWindowMs: TimelinesPropTypes['stopWindowMs'];
  readonly collection: BoundingPolyTree | undefined;
  readonly selected: TimelinesPropTypes['selectedPolys'];
  readonly videoOffset: number;
  readonly color?: string;
}

.fileMenu {
  position: absolute;
  top: 0;
  left: 0;
  background: white;
  box-shadow: 0 3px 10px #24242459;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: calc(100vh - 55px);
  transition: width ease 0.3s;
  white-space: nowrap;
  z-index: 999;

  &Background {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgb(0, 0, 0, 0.2);
    transition: all ease 0.3s;
    z-index: 998;

    &.closed {
      background: none;
      z-index: -1;
    }
  }

  &Title {
    color: #2a323c;
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
    padding: 20px;
  }

  &Search {
    padding: 0 20px 20px;
  }

  &Container {
    flex: 1;
    display: flex;
    width: 668px;
    overflow: hidden;

    &Case {
      padding-right: 20px;
      width: 324px;
    }

    &File {
      border-left: 0.5px solid #003d7e4d;
      padding-left: 20px;
      width: 344px;

      &Dropzone {
        height: 200px;
        margin: 20px 20px 0 0;
      }
    }

    &Link {
      color: #5c5c5c;
      font-size: 14px;
      font-weight: bold;
      text-decoration: none;
      text-transform: uppercase;

      &:hover {
        color: #005c7e;
        text-decoration: none;
      }
    }
  }
}

Feature: Optical Tracking

  Background: The user is in a video
    Given The user uploads file "lucy.mp4" with transcription "off" 
    Given The user is on "lucy.mp4" File Detail Screen

  @e2e @mdp @regression
  Scenario: The user tracks the video at a specific times
    Given The user stops the video at 2 seconds
    Then The user draws an UDR at coordinates 100, 100, 200, 200
    Then The user tracks the video

  @e2e @mdp @regression
  Scenario: Delete multiple test files
    Given The user deletes files
      | lucy.mp4 |
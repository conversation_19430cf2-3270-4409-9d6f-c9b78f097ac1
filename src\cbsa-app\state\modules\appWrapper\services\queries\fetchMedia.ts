import type { JobStatus } from 'veritone-types';
import { CaseStatus, TDOId } from '@cbsa-modules/universal';

export interface FETCH_MEDIA_RESPONSE {
  folder: {
    childTDOs: {
      records: {
        id: TDOId;
        name: string;
        status: CaseStatus;
        primaryAsset: {
          id: string;
          signedUri: string;
          contentType: string;
        };
        assets: {
          records: {
            assetType: string;
            signedUri: string;
          }[];
        };
        jobs: {
          records: {
            id: string;
            createdDateTime: string;
            modifiedDateTime: string;
            status: JobStatus;
            tasks: {
              records: {
                engine: {
                  name: string;
                };
              }[];
            };
          }[];
        };
      }[];
    };
  };
}

export const FETCH_MEDIA_QUERY = `
  query fetchMedia($offset: Int, $limit: Int, $caseId: ID!) {
    folder(id: $caseId) {
      childTDOs(offset: $offset, limit: $limit) {
        records {
          id
          name
          status
          primaryAsset(assetType: "media") {
            id
            signedUri
            contentType
          }
          assets {
            records {
              assetType
              signedUri
            }
          }
          jobs(offset: $offset, limit: $limit) {
            records {
              id
              createdDateTime
              modifiedDateTime
              status
              tasks {
                records {
                  engine {
                    name
                  }
                }
              }
            }
          }
        }
      }
    }
  }`;

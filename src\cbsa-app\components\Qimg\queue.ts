/* eslint-disable testing-library/await-async-queries */
// TODO: We should just change the conflicting function name

let INSERT_ORDER = 0;

export interface QueueItem<P = unknown> {
  readonly priority: number;
  readonly insertOrder?: number;
  readonly key: string;
  readonly payload: P;
}

const imageQueue: Required<QueueItem>[] = [];
const imageCache: Record<string, QueueItem> = {};

export const add = (item: QueueItem): boolean => {
  if (item.key && !findByKey(item.key)) {
    imageQueue.push({ ...item, insertOrder: INSERT_ORDER++ });
    imageQueue.sort(
      (a, b) => b.priority - a.priority || b.insertOrder - a.insertOrder
    );
    return true;
  }
  return false;
};

export const remove = (payload: QueueItem['payload']): boolean => {
  const index = imageQueue.findIndex((i) => i.payload === payload);
  if (index !== -1) {
    imageQueue.splice(index, 1);
  }
  return index !== -1;
};

export const has = (item: QueueItem) => hasKey(item.key);
export const hasKey = (key: string) => !!imageCache[key];

export const size = () => imageQueue.length;

export const next = <P>() => {
  const item = imageQueue.pop();
  if (item) {
    imageCache[item.key] = item;
  }
  return item as QueueItem<P>;
};

export const clear = () => {
  Object.keys(imageCache).forEach((k) => delete imageCache[k]);
};

export const findByKey = <P>(key: string) => imageCache[key] as QueueItem<P>;

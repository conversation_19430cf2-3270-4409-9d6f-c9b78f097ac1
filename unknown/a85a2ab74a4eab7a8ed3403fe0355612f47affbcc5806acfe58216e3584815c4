import { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { I18nTranslate } from '@common/i18n';
import makeStyles from '@mui/styles/makeStyles';
import CloudUpload from '@mui/icons-material/CloudUpload';
import * as styles from './index.scss';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const useStyles = makeStyles((theme) => ({
  root: {
    color: theme.palette.primary.main,
    fontSize: '60px',
  },
}));

const DropzoneContent = ({ acceptedTypes, onUpload }: Props) => {
  const muiStyles = useStyles();
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptedTypes,
    onDrop: useCallback(
      (acceptedFiles: File[]) => {
        acceptedFiles.forEach(onUpload);
      },
      [onUpload]
    ),
    useFsAccessApi: false,
  });

  return (
    <div {...getRootProps()} className={styles.dropzone} data-testid="dropzone">
      <input {...getInputProps()} data-testid="drop-input" />
      <div
        className={
          isDragActive ? styles.dropzoneContentActive : styles.dropzoneContent
        }
      >
        <CloudUpload classes={muiStyles} data-testid="cloud-upload" />
        {isDragActive ? (
          <div className={styles.dropzoneContentActiveUploadMedia}>
            {I18nTranslate.TranslateMessage('dropToUploadMedia')}
          </div>
        ) : (
          <>
            <div className={styles.dropzoneContentDragAndDrop}>
              {I18nTranslate.TranslateMessage('dragAndDrop')}
            </div>
            <div className={styles.dropzoneContentAddFile}>
              {I18nTranslate.TranslateMessage('orClickToAddMedia')}
            </div>
            <div className={styles.dropzoneContentFileTypes}>
              {Object.keys(acceptedTypes).join(', ')}
            </div>
            <div className={styles.dropzoneContentUploadMedia}>
              {I18nTranslate.TranslateMessage('uploadMedia')}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

interface Props {
  acceptedTypes: Record<string, string[]>;
  onUpload: (file: File) => void;
}

const Dropzone = ({ acceptedTypes, onUpload }: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <DropzoneContent acceptedTypes={acceptedTypes} onUpload={onUpload} />
  </ThemeProvider>
);

export default Dropzone;

Feature: Global Settings

  @e2e @mdp @global-settings @regression
  Scenario: Prepare test data
    Given The user uploads file "fullObject.mp4" with transcription "head, person"   

  @e2e @mdp @global-settings @regression
  Scenario: Set "<redactionType>" redaction effect with "<value>" in Setting
    Given The user resets and goes to video "fullObject.mp4"
    Given The user opens the media details settings
    Then The user selects "Redaction" tab
    When The user selects "<value>" from "<type>" dropdown in Redaction Video
    When The user saves the settings
    Then The user draws an "<redactionType>" at coordinates 100, 100, 200, 200
    When The user opens filter menu
    When The user selects "all" in filter menu
    When The user selects "<menuName>" in filter menu
    When The user closes the filter menu
    When The user sees "<type>" in group name at the right panel
    When The user clicks on the time period of "<type>" in the cluster list
    When The user clicks on "Redacted" radio in Overlay Preview
    Then The user sees "<cssElement>" for "<redactionType>" 1 should have "<colors>" color

    Examples:
      | value     | cssElement   | colors             | type    | menuName     | redactionType | groupName       |
      | Blackfill | color        | rgb(34, 34, 34)    | udr     | udr          | udr           | Overlay Group 1 |
      | Outline   | border-color | rgb(255, 255, 255) | udr     | udr          | udr           | Overlay Group 1 |
      | Blackfill | color        | rgb(34, 34, 34)    | head    | head         | head          | Unnamed Group   |
      | Outline   | border-color | rgb(255, 255, 255) | head    | head         | head          | Unnamed Group   |
      | Blackfill | color        | rgb(34, 34, 34)    | laptop  | laptop       | laptop        | Unnamed Group   |
      | Outline   | border-color | rgb(255, 255, 255) | laptop  | laptop       | laptop        | Unnamed Group   |
      | Blackfill | color        | rgb(34, 34, 34)    | vehicle | licensePlate | vehicle       | Unnamed Group   |
      | Outline   | border-color | rgb(255, 255, 255) | vehicle | licensePlate | vehicle       | Unnamed Group   |
      | Blackfill | color        | rgb(34, 34, 34)    | plate   | licensePlate | licensePlate  | Unnamed Group   |
      | Outline   | border-color | rgb(255, 255, 255) | plate   | licensePlate | licensePlate  | Unnamed Group   |
  # TO DO: Can't detect Notepad and ID-card in test video (flaky)

  @e2e @mdp @global-settings @regression
  Scenario: Set blur level for "<redactedLevel>" in Setting
    Given The user is on "fullObject.mp4" File Detail Screen
    Given The user opens the media details settings
    Then The user selects "Redaction" tab
    When The user selects "Blur" from "<redactedLevel>" dropdown in Redaction Video
    When The user sets "<redactedLevel>" level to 1 in Settings
    When The user saves the settings
    Then The user draws an "<redactionType>" at coordinates 100, 100, 200, 200
    When The user opens filter menu
    When The user selects "all" in filter menu
    When The user selects "<menuName>" in filter menu
    When The user closes the filter menu
    When The user sees "<redactedLevel>" in group name at the right panel
    When The user clicks on the time period of "<redactedLevel>" in the cluster list
    When The user clicks on "Redacted" radio in Overlay Preview
    Then Item 1 type "<redactionType>" should have blur attribute

    Examples:
      | redactedLevel | redactionType | menuName     |
      | udr           | udr           | udr          |
      | head          | head          | head         |
      | laptop        | laptop        | laptop       |
      | vehicle       | vehicle       | licensePlate |
      | plate         | licensePlate  | licensePlate |
  # | IDcard        | IDcard        | IDcard       |
  # | notepad       | notepad       | notepad      |
  # TO DO: Can't detect Notepad and ID-card in test video (flaky)

  @e2e @mdp @global-settings @regression
  Scenario: Change shape for "<redactedShape>" in setting
    Given The user is on "fullObject.mp4" File Detail Screen
    Given The user opens the media details settings
    Then The user selects "Redaction" tab
    When The user scroll to "Shapes" list menu
    When The user opens menu pop-up of "<redactedShape>" shape
    When The user changes to "Ellipse"
    When The user saves the settings
    Then The user draws an "<redactionType>" at coordinates 100, 100, 200, 200
    When The user opens filter menu
    When The user selects "all" in filter menu
    When The user selects "<menuName>" in filter menu
    When The user closes the filter menu
    When The user sees "<redactedShape>" in group name at the right panel
    When The user clicks on the time period of "<redactedShape>" in the cluster list
    When The user clicks on "Redacted" radio in Overlay Preview
    Then The shape of the "<redactionType>" 1 is Ellipse

    Examples:
      | redactedShape | redactionType | menuName     |
      | udr           | udr           | udr          |
      | head          | head          | head         |
      | laptop        | laptop        | laptop       |
      | vehicle       | vehicle       | licensePlate |
      | plate         | licensePlate  | licensePlate |
  # | ID-card       | ID-card       | ID-card      |
  # | notepad       | notepad       | notepad      |
  # TO DO: Can't detect Notepad and ID-card in test video (flaky)

  @e2e @mdp @global-settings @regression
  Scenario: Verify object detection in setting
    Given The user is on "fullObject.mp4" File Detail Screen
    Given The user opens the media details settings
    Then The user selects "Object Detection" tab
    When The user changes video type to "CCTV"
    When The user sets face Confidence threshold to "50"
    When The user changes cluster similarity threshold to "Very Low (Less Clusters)"
    When The user saves the settings
  # Legacy Clustering is missing in test environment

  @e2e @mdp @global-settings @regression
  Scenario: Verify Tracking in setting
    Given The user is on "fullObject.mp4" File Detail Screen
    Given The user opens the media details settings
    Then The user selects "Tracking" tab
    Then The user sets tracking "backward" limit to "100"
    Then The user sets tracking "forward" limit to "200"
    When The user saves the settings

  @e2e @mdp @global-settings @regression
  Scenario: Change redaction type of audio
    Given The user is on "fullObject.mp4" File Detail Screen
    When The user navigates to "audio" Tab
    When The user runs transcription
    Given The user opens the media details settings
    Then The user selects "Redaction" tab
    When The user scroll to "Audio" list menu
    When The user opens menu pop-up for "audio"
    When The user changes to "Tone"
    When The user saves the settings
    Then The user plays the video
    Given The user opens the media details settings
    Then The user selects "Redaction" tab
    When The user scroll to "Audio" list menu
    When The user opens menu pop-up for "audio"
    When The user changes to "Mute"
    When The user saves the settings

  @e2e @mdp @global-settings @regression
  Scenario: Verify Auto Interpolation
    Given The user is on "fullObject.mp4" File Detail Screen
    Given The user opens the media details settings
    And The user selects "Interpolation" tab
    When The user sets "Auto Interpolation" to 0 in Interpolation settings
    Then The "Auto Interpolation" value should be 0 in Interpolation settings
    # The user sets "Auto Interpolation" to 5 in Interpolation settings
    And The user saves the settings
    Given The user opens the media details settings
    And The user selects "Interpolation" tab
    When The user sets "Auto Interpolation" to 5000 in Interpolation settings
    And The user sets "Manual Interpolation" to 5000 in Interpolation settings
    And The user saves the settings
    Then The media details setting is closed
    # TO DO: Assert changes for Interpolation

  @e2e @mdp @global-settings @regression
  Scenario: Verify Manual Interpolation
    Given The user is on "fullObject.mp4" File Detail Screen
    Given The user opens the media details settings
    And The user selects "Interpolation" tab
    When The user sets "Auto Interpolation" to 0 in Interpolation settings
    And The user sets "Manual Interpolation" to 0 in Interpolation settings
    Then The "Manual Interpolation" value should be 0 in Interpolation settings
    When The user sets "Manual Interpolation" to 5000 in Interpolation settings
    And The user saves the settings
    # TO DO: Assert changes for Interpolation

  @e2e @mdp @global-settings
  Scenario: Delete multiple test files
    Given The user deletes files
      | fullObject.mp4 |

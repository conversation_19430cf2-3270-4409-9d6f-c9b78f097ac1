import { defaultState } from './store';
import {
  <PERSON>ETCH_JOB_SDOS_SUCCESS,
  FETCH_JOB_STATUS_SUCCESS_REDUX,
  DELETE_JOB_SDO_SUCCESS,
  UPSERT_JOB_SDO_SUCCESS,
  DELETE_JOB_SDO_REDUX,
  RETRY_JOB_SUCCESS,
  INSERT_NOTIFICATION_REDUX_WHEN_NETWORK_ERROR,
  SHOW_OR_HIDE_CUSTOM_NOTIFICATION_LIST,
} from './actions';
import { listFailed, listComplete } from './selectors';
import { createReducer } from '@reduxjs/toolkit';

const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(FETCH_JOB_SDOS_SUCCESS, (state, action) => {
      const { structuredDataObjects } = action.payload;
      if (!structuredDataObjects?.records) {
        return state;
      }
      const { records } = structuredDataObjects;
      const finalDataSorted = (records || []).reduce<typeof state.sdos>(
        (acc, record) => {
          acc.push({ ...record.data, sdoId: record.id });
          return acc;
        },
        []
      );

      return {
        ...state,
        sdos: finalDataSorted,
      };
    })
    .addCase(FETCH_JOB_STATUS_SUCCESS_REDUX, (state, action) => {
      const { payload } = action;

      return {
        ...state,
        sdos: state.sdos.map((sdo) => {
          const job = payload[`jobId${sdo.jobId}`];
          if (job) {
            const { status } = job;
            return {
              ...sdo,
              status,
              deleted:
                listFailed.includes(status) || listComplete.includes(status)
                  ? false
                  : sdo.deleted,
            };
          }
          return sdo;
        }),
      };
    })
    .addCase(DELETE_JOB_SDO_SUCCESS, (state, action) => {
      const { id } = action.payload.deleteStructuredData;
      return {
        ...state,
        sdos: state.sdos.filter((val) => val.sdoId !== id),
      };
    })
    .addCase(UPSERT_JOB_SDO_SUCCESS, (state, action) => {
      const { data, id } = action.payload.createStructuredData;
      const { sdos } = state;
      const oldSdo = sdos.find((sdo) => sdo.sdoId === id);

      // change deleted = true when delete jobSdo preparing or running
      if (oldSdo) {
        return {
          ...state,
          sdos: state.sdos.map((sdo) => {
            if (sdo.sdoId === id) {
              return {
                ...sdo,
                deleted: true,
              };
            }
            return sdo;
          }),
        };
      }

      // add new jobSdo when create Sdo success
      return {
        ...state,
        sdos: [{ ...data, sdoId: id }, ...state.sdos],
      };
    })
    .addCase(DELETE_JOB_SDO_REDUX, (state, action) => {
      const { ids } = action.payload;
      return {
        ...state,
        sdos: state.sdos.filter((sdo) => {
          if (!sdo.sdoId) {
            return false;
          }
          return !ids.includes(sdo.sdoId);
        }),
      };
    })
    .addCase(RETRY_JOB_SUCCESS, (state, action) => {
      const { status, id } = action.payload.retryJob;
      return {
        ...state,
        sdos: state.sdos.map((sdo) => {
          if (sdo.jobId === id) {
            return {
              ...sdo,
              status,
            };
          }
          return sdo;
        }),
      };
    })
    .addCase(INSERT_NOTIFICATION_REDUX_WHEN_NETWORK_ERROR, (state, action) => {
      const { payload } = action;
      return {
        ...state,
        sdos: [payload, ...state.sdos],
      };
    })
    .addCase(SHOW_OR_HIDE_CUSTOM_NOTIFICATION_LIST, (state) => ({
      ...state,
      showCustomNotificationList: !state.showCustomNotificationList,
    }));
});

export default reducer;

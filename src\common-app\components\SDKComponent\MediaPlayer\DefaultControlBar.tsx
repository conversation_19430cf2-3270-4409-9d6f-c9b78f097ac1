// TODO: Fix player/manager types
import cx from 'classnames';
import * as styles from './styles.scss';
// import CutIcon from '../CutIcon';
import shaka from 'shaka-player';
import TrimSlider from '../TrimSlider';
import { useSelector } from 'react-redux';
import FullscreenToggle from './FullscreenToggle';
import { MutableRefObject, useState } from 'react';
import { PLAYBACK_RATES } from '@helpers/constants';
import RestartMediaButton from './RestartMediaButton';
import { isShowTrimTool as selectIsShowTrimTool } from '@common-modules/trim';
import {
  VolumeMenuButton,
  ControlBar,
  ReplayControl,
  ForwardControl,
  PlaybackRateMenuButton,
  PlayToggle,
  ProgressControl,
  Player,
} from 'video-react';
import RotateIcon from '../RotateIcon';

const DefaultControlBar = ({
  playerRef,
  togglePlayerFullscreen,
  isFullscreen,
  thumbnailTracks,
  shakaPlayer,
  onSeekPrevMarkerTime,
  videoReactHasStarted = true,
  hasVideo,
}: Props) => {
  const [startTime, setStartTime] = useState(0);
  const [endTime, setEndTime] = useState(0);

  const isShowTrimTool = useSelector(selectIsShowTrimTool);

  const thumbnailImageId = 'thumb-image';

  const manager = playerRef.current?.manager;
  let player, actions, store;
  if (manager) {
    // TODO: Fix player/manager types
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    player = manager.getState().player;
    actions = manager.getActions();
    store = manager.store;
  }

  function getSliderElement() {
    return window.document
      .getElementById('video-default-control-bar')
      ?.getElementsByClassName('video-react-slider-bar')[0];
  }

  const deleteThumbnailImage = () => {
    document.getElementById(thumbnailImageId)?.remove();
  };

  const createThumbnailImage = (thumbnailImage: shaka.Thumbnail | null) => {
    const sliderElement = getSliderElement();
    if (sliderElement && thumbnailImage) {
      // Remove any image shown in trackbar before displaying new one
      document.getElementById(thumbnailImageId)?.remove();
      let width = 200;
      if (thumbnailImage.width < thumbnailImage.height) {
        width *= thumbnailImage.width / thumbnailImage.height;
      }
      const offsetHeight = 40; // Offset Height from seekBar
      const scale = width / thumbnailImage.width;
      const thumbImageElement = document.createElement('img');
      thumbImageElement.id = thumbnailImageId;
      thumbImageElement.draggable = false;
      thumbImageElement.style.position = 'absolute';
      thumbImageElement.style.background = `url('${thumbnailImage.uris[0]}') -${thumbnailImage.positionX}px -${thumbnailImage.positionY}px`;
      thumbImageElement.style.left =
        sliderElement.clientWidth - (thumbnailImage.width / 2) * scale + 'px';
      thumbImageElement.style.top =
        '-' +
        (sliderElement.clientTop +
          thumbnailImage.height * scale +
          offsetHeight) +
        'px';
      thumbImageElement.style.transformOrigin = 'left top';
      thumbImageElement.style.display = 'block';
      thumbImageElement.style.height = thumbnailImage.height + 'px';
      thumbImageElement.style.width = thumbnailImage.width + 'px';
      thumbImageElement.style.transform = 'scale(' + scale + ')';
      sliderElement.insertBefore(thumbImageElement, sliderElement.firstChild);
    }
  };

  const setStartStopTime = (payload: {
    startTime?: number;
    endTime?: number;
  }) => {
    setStartTime(payload.startTime ? payload.startTime : startTime);
    setEndTime(payload.endTime ? payload.endTime : endTime);
  };

  const seekTime = async (time: number) => {
    if (thumbnailTracks && shakaPlayer) {
      const thumbnail = await shakaPlayer.getThumbnails(
        thumbnailTracks.id,
        Math.floor(time)
      );
      createThumbnailImage(thumbnail);
    }

    // call manager function
    manager?.getActions().handleSeekingTime(time);
  };

  const seekedEndTime = (time: number) => {
    deleteThumbnailImage();

    // call normal function
    manager?.getActions().handleEndSeeking(time);

    if (onSeekPrevMarkerTime) {
      onSeekPrevMarkerTime(time * 1000);
    }
  };

  return manager ? (
    <div
      className={cx('video-react', {
        'video-react-has-started': videoReactHasStarted,
      })}
      style={{ position: 'static' }}
      id="video-default-control-bar"
    >
      <ControlBar
        className={cx(styles.mediaPlayer)}
        // TODO: Fix player/manager types
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        player={player}
        manager={manager}
        actions={actions}
        // @ts-expect-error TODO: type store correctly
        store={store}
        /* Disabling Default Controls hide the Elapsed time / Total time */
        // disableDefaultControls
      >
        <RestartMediaButton
          order={1.1}
          data-veritone-element="control-bar-restart-media-button"
        />
        <ReplayControl
          seconds={10}
          order={1.2}
          data-veritone-element="control-bar-replay-control"
        />
        <ForwardControl
          seconds={10}
          order={1.3}
          data-veritone-element="control-bar-forward-control"
        />
        <PlayToggle order={2} data-veritone-element="control-bar-play-toggle" />
        {isShowTrimTool ? (
          <TrimSlider
            order={6}
            onChange={setStartStopTime}
            playerRef={playerRef}
          />
        ) : (
          <ProgressControl
            actions={{
              ...actions,
              handleSeekingTime: seekTime,
              handleEndSeeking: seekedEndTime,
              forward: () => {},
              replay: () => {},
            }}
            order={6}
            data-veritone-element="control-bar-progress-control"
          />
        )}
        <PlaybackRateMenuButton
          rates={[...PLAYBACK_RATES].reverse()}
          order={7.1}
          data-veritone-element="control-bar-playback-rate-button"
        />
        {hasVideo && <RotateIcon order={7.2} playerRef={playerRef} />}
        <VolumeMenuButton
          vertical
          order={7.3}
          data-veritone-element="control-bar-volume-button"
        />
        {/* Uncomment to enable trim feature. */}
        {/* <CutIcon
           order={10}
           data-veritone-element="control-bar-trim-button"
           startTime={startTime}
           endTime={endTime}
         /> */}
        <FullscreenToggle
          data-veritone-element="control-bar-fullscreen-toggle"
          onClick={togglePlayerFullscreen}
          isFullscreen={isFullscreen}
        />
      </ControlBar>
    </div>
  ) : null;
};

interface Props {
  playerRef: MutableRefObject<Player | null>;
  togglePlayerFullscreen: () => void;
  isFullscreen: boolean;
  thumbnailTracks?: shaka.Track | null;
  shakaPlayer?: shaka.Player | null;
  onSeekPrevMarkerTime?: (timeMs: number) => void;
  videoReactHasStarted?: boolean;
  hasVideo: boolean;
}

export default DefaultControlBar;

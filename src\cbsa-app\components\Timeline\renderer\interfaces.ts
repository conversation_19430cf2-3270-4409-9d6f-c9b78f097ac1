import Konva from 'konva';
import { OperatorFunction } from 'rxjs';

import { selectSelected } from '@common-modules/mediaDetails';
import { BoundingPolyTree } from '@worker';
import { TimelinesPropTypes } from '../Timelines/TimelinesPropTypes';

export interface RenderRequest {
  readonly startWindowMs: TimelinesPropTypes['startWindowMs'];
  readonly stopWindowMs: TimelinesPropTypes['stopWindowMs'];
  readonly collection: BoundingPolyTree | undefined;
  readonly selected: TimelinesPropTypes['selectedPolys'];
  readonly videoOffset?: number;
  readonly color?: string;
}

export type MapRenderOperator = (
  layer: Konva.Layer
) => OperatorFunction<MapRenderProps, Konva.Layer>;

export interface MapRenderProps {
  readonly startMs: number;
  readonly stopMs: number;
  readonly collection?: BoundingPolyTree;
  readonly selected: ReturnType<typeof selectSelected>;
  readonly width: number;
  readonly videoOffset: number;
  readonly color?: string;
}

import { Button, ThemeProvider } from '@mui/material';
import { clamp } from 'lodash';

import { buttonTheme } from '@redact/materialUITheme';
// import InviteAppButton from '../InviteAppButton';
import * as styles from './styles.scss';

const format = (ms: number) => {
  const h = (ms / 3600_000) | 0;
  const m = ((ms % 3600_000) / 60_000) | 0;
  return `${h} hour${h === 1 ? '' : 's'} and ${m} minutes`;
};

const UserUpgradeBarView = ({
  isFreeTrialUser,
  mediaProcessingRemainingMs,
  onUpgradeAccount,
}: UserUpgradeBarViewPropTypes) => (
  <div className={styles.timeRemainingBar}>
    <div>
      <strong>
        {format(clamp(mediaProcessingRemainingMs, 0, Infinity))}&nbsp;
      </strong>
      remaining on your free trial &nbsp; &nbsp;
    </div>
    {isFreeTrialUser ? (
      <div>
        <ThemeProvider theme={buttonTheme}>
          <Button color="primary" onClick={onUpgradeAccount}>
            UPGRADE NOW
          </Button>
        </ThemeProvider>
      </div>
    ) : null}
  </div>
);
/* <Grid container justifyContent="flex-end" item xs={7}>
      <MuiThemeProvider theme={buttonTheme}>
        <div>
          <InviteAppButton />
        </div>
      </MuiThemeProvider>
    </Grid> */

export default UserUpgradeBarView;

export interface UserUpgradeBarViewPropTypes {
  readonly isEnterprise: boolean;
  readonly isFreeTrialUser: boolean;
  readonly isSubscriber: boolean;
  readonly mediaProcessedMs: number;
  readonly mediaProcessingRemainingMs: number;
  readonly onUpgradeAccount: () => void;
}

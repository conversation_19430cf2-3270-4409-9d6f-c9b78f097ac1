import { TDOId } from '@cbsa-modules/universal';
import { createAction } from '@reduxjs/toolkit';

export const DELETE_TDO = createAction<{
  tdoId: TDOId;
}>('CBSA/DELETE_TDO');
export const DELETE_TDO_SUCCESS = createAction<{
  tdoId: TDOId;
}>('CBSA/DELETE_TDO_SUCCESS');

export const deleteTdo = (tdoId: TDOId) => DELETE_TDO({ tdoId });

export const deleteTdoSuccess = (tdoId: TDOId) => DELETE_TDO_SUCCESS({ tdoId });

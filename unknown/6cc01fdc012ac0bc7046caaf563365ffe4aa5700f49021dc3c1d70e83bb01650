import { useState } from 'react';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid2';
import AddIcon from '@mui/icons-material/Add';

import NewCaseDialog from '../NewCaseDialog';

import { I18nTranslate } from '@common/i18n';

const NewCaseButton = () => {
  const [showNewCaseDialog, setShowNewCaseDialog] = useState(false);

  return (
    <>
      <Grid
        size={{ xs: 2 }}
        container
        alignItems="stretch"
        justifyContent="flex-end"
      >
        <Button
          size="large"
          variant="contained"
          color="primary"
          onClick={() => {
            setShowNewCaseDialog(true);
          }}
        >
          <AddIcon />
          &nbsp;{I18nTranslate.TranslateMessage('newCase')}
        </Button>
      </Grid>
      <NewCaseDialog
        showNewCaseDialog={showNewCaseDialog}
        setShowNewCaseDialog={setShowNewCaseDialog}
      />
    </>
  );
};

export default NewCaseButton;

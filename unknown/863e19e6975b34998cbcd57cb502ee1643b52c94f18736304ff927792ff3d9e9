import { useSelector, useDispatch } from 'react-redux';

import Grid from '@mui/material/Grid2';
import Switch from '@mui/material/Switch';
import Typography from '@mui/material/Typography';

import { I18nTranslate } from '@common/i18n';

import { selectShowArchived } from '@cbsa-modules/mainPage/selectors';
import {
  setShowArchived,
  setPaginationStart,
} from '@cbsa-modules/mainPage/actions';

import makeStyles from '@mui/styles/makeStyles';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const useStyles = makeStyles((theme) => ({
  commonArchiveText: {
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  showArchiveText: {
    color: theme.palette.primary.main,
  },
  hideArchiveText: {
    color: theme.palette.text.secondary,
  },
}));

const ArchiveSwitchContent = () => {
  const dispatch = useDispatch();
  const classes = useStyles();

  const showArchived = useSelector(selectShowArchived);

  const archiveTextStyle = [classes.commonArchiveText];
  let archiveText = 'showArchived';
  if (showArchived) {
    archiveTextStyle.push(classes.showArchiveText);
  } else {
    archiveTextStyle.push(classes.hideArchiveText);
    archiveText = 'hideArchived';
  }

  return (
    <Grid size={{ xs: 2 }} container alignItems="center">
      <Switch
        checked={showArchived}
        onChange={() => {
          dispatch(setShowArchived(!showArchived));
          dispatch(setPaginationStart(1));
        }}
        color="primary"
      />
      <Typography classes={{ root: archiveTextStyle.join(' ') }}>
        &nbsp;{I18nTranslate.TranslateMessage(archiveText)}
      </Typography>
    </Grid>
  );
};

const ArchiveSwitch = () => (
  <ThemeProvider theme={defaultTheme}>
    <ArchiveSwitchContent />
  </ThemeProvider>
);

export default ArchiveSwitch;

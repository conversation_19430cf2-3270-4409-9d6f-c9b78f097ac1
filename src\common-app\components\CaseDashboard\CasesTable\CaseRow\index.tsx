import { useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { startCase } from 'lodash';
import moment from 'moment';

import Grid from '@mui/material/Grid2';
import IconButton from '@mui/material/IconButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';

import ConfirmDialog from '@common-components/ConfirmDialog';

import DeleteIcon from '@mui/icons-material/DeleteOutline';
import ArchiveIcon from '@mui/icons-material/Archive';
import LaunchIcon from '@mui/icons-material/Launch';
import MoreVertIcon from '@mui/icons-material/MoreVert';

import { Case } from '@cbsa-modules/universal';
import { CaseRowPropTypes } from '@cbsa-modules/mainPage/MainPageStore';
import { selectShowArchived } from '@cbsa-modules/mainPage/selectors';
import { deleteCase, archiveCase } from '@cbsa-modules/mainPage/actions';
import { useIntl } from 'react-intl';

import makeStyles from '@mui/styles/makeStyles';
import { defaultTheme, ExtendedTheme } from '@cbsa/styles/materialThemes';
import { ThemeProvider, styled } from '@mui/material';
import { typedObjectKeys } from '@utils';

const TableBodyRow = styled(Grid)(({ theme }) => ({
  '&.MuiGrid2-container': {
    fontSize: theme.typography.fontSize,
    padding: theme.spacing(3),
    borderBottom: `1px solid ${theme.palette.divider}`,
    '& > .MuiGrid-root.-MuiGrid-container.-MuiGrid-item': {
      '& > .-MuiTypography-root': {
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        marginRight: theme.spacing(3),
      },
    },
  },
}));

const useStyles = makeStyles((theme) => ({
  tableBodyRow: {
    fontSize: theme.typography.fontSize,
    padding: theme.spacing(3),
    borderBottom: `1px solid ${theme.palette.divider}`,
    '& > .MuiGrid-root.-MuiGrid-container.-MuiGrid-item': {
      '& > .-MuiTypography-root': {
        overflow: 'hidden',
        whiteSpace: 'nowrap',
        textOverflow: 'ellipsis',
        marginRight: theme.spacing(3),
      },
    },
  },
  boldText: {
    fontWeight: 'bold',
  },
  linkText: {
    cursor: 'pointer',
  },
  capitalizeText: {
    textTransform: 'capitalize',
  },
  caseActionIcon: {
    minWidth: '35px',
  },
}));

const useStatusStyles = makeStyles((theme: ExtendedTheme) => ({
  new: {
    color: theme.palette.primary.main,
  },
  approved: {
    color: theme.palette.success.main,
  },
  processing: {
    color: theme.palette.warning.main,
  },
  error: {
    color: theme.palette.error.main,
  },
  archived: {
    color: theme.palette.text.secondary,
  },
  readyforexport: {
    color: theme.palette.info.main,
  },
  readyforreview: {
    color: theme.palette.text.primary,
  },
  processed: {
    color: theme.palette.processed.main,
  },
}));

const isValidStatusKey = (
  key: string,
  validKeys: StatusKey[]
): key is StatusKey => validKeys.includes(key as StatusKey);

type StatusKey = keyof ReturnType<typeof useStatusStyles>;

const CaseRowContent = (props: CaseRowPropTypes) => {
  const { caseInfo, onCaseClick } = props;

  const intl = useIntl();

  const classes = useStyles();
  const dispatch = useDispatch();

  const statusColors = useStatusStyles();
  const validKeys = typedObjectKeys(statusColors);
  const statusColorKey = caseInfo.status.toLowerCase();
  const statusColor = isValidStatusKey(statusColorKey, validKeys)
    ? statusColors[statusColorKey]
    : '';

  const moreButtonRef = useRef(null);
  const [menuOpen, setMenuOpen] = useState(false);
  const [deleteModal, setDeleteModal] = useState<
    CaseRowPropTypes['caseInfo']['treeObjectId'] | null
  >(null);
  const [archiveModal, setArchiveModal] = useState<null | Case['treeObjectId']>(
    null
  );

  const showArchived = useSelector(selectShowArchived);

  const onCaseClickHandler = () => {
    if (caseInfo.treeObjectId) {
      onCaseClick(caseInfo.treeObjectId);
    }
  };

  return (
    <TableBodyRow container>
      <Grid container alignItems="center" size={{ xs: 2 }}>
        <Typography
          classes={{ root: [classes.boldText, classes.linkText].join(' ') }}
          onClick={onCaseClickHandler}
        >
          {caseInfo.name}
        </Typography>
      </Grid>
      <Grid container alignItems="center" size={{ xs: 2 }}>
        <Typography>
          {moment(caseInfo.createdDateTime).format('MMM DD, YYYY hh:mm A')}
        </Typography>
      </Grid>
      <Grid container alignItems="center" size={{ xs: 2 }}>
        <Typography>
          {moment(caseInfo.modifiedDateTime).format('MMM DD, YYYY hh:mm A')}
        </Typography>
      </Grid>
      <Grid container alignItems="center" size={{ xs: 2 }}>
        <Typography>
          {startCase(moment.utc(caseInfo.createdDateTime).fromNow(true))}
        </Typography>
      </Grid>
      <Grid container alignItems="center" size={{ xs: 2 }}>
        <Typography
          classes={{
            root: [classes.boldText, statusColor].join(' '),
          }}
        >
          {intl.formatMessage({
            id: caseInfo.status,
            defaultMessage: 'Untranslated Status',
          })}
        </Typography>
      </Grid>
      <Grid container justifyContent="flex-end" size={{ xs: 2 }}>
        <IconButton
          size="small"
          onClick={() => {
            setMenuOpen(true);
          }}
          ref={moreButtonRef}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          anchorEl={moreButtonRef.current}
          open={menuOpen}
          keepMounted
          onClose={() => {
            setMenuOpen(false);
          }}
        >
          <MenuItem onClick={onCaseClickHandler}>
            <ListItemIcon classes={{ root: classes.caseActionIcon }}>
              <LaunchIcon color="primary" />
            </ListItemIcon>
            <Typography color="primary">
              {intl.formatMessage({ id: 'details', defaultMessage: 'Details' })}
            </Typography>
          </MenuItem>
          <MenuItem
            onClick={() => {
              setMenuOpen(false);
              setArchiveModal(caseInfo.treeObjectId);
            }}
          >
            <ListItemIcon classes={{ root: classes.caseActionIcon }}>
              <ArchiveIcon classes={{ root: statusColors.processing }} />
            </ListItemIcon>
            <Typography classes={{ root: statusColors.processing }}>
              {showArchived
                ? intl.formatMessage({ id: 'reopen', defaultMessage: 'Reopen' })
                : intl.formatMessage({
                    id: 'archive',
                    defaultMessage: 'Archive',
                  })}
            </Typography>
          </MenuItem>
          <MenuItem
            onClick={() => {
              setMenuOpen(false);
              setDeleteModal(caseInfo.treeObjectId);
            }}
          >
            <ListItemIcon classes={{ root: classes.caseActionIcon }}>
              <DeleteIcon classes={{ root: statusColors.error }} />
            </ListItemIcon>
            <Typography classes={{ root: statusColors.error }}>
              {intl.formatMessage({ id: 'delete', defaultMessage: 'Delete' })}
            </Typography>
          </MenuItem>
        </Menu>
      </Grid>
      <ConfirmDialog
        isOpen={!!deleteModal}
        title={intl.formatMessage({
          id: 'deleteCase',
          defaultMessage: 'Delete Case',
        })}
        content={intl.formatMessage({
          id: 'deleteCaseConfirmDialog',
          defaultMessage:
            "Are you sure you'd like to delete this case? Once deleted, the case cannot be recovered.",
        })}
        confirmText={intl.formatMessage({
          id: 'delete',
          defaultMessage: 'Delete',
        })}
        onConfirm={() => {
          if (deleteModal) {
            // This emits an event for a saga listener
            dispatch(deleteCase(deleteModal));
          }

          setDeleteModal(null);
        }}
        cancelText={intl.formatMessage({
          id: 'cancel',
          defaultMessage: 'Cancel',
        })}
        onCancel={() => {
          setDeleteModal(null);
        }}
        warning
      />
      <ConfirmDialog
        isOpen={!!archiveModal}
        title={intl.formatMessage({
          id: 'archiveCase',
          defaultMessage: 'Archive Case',
        })}
        content={intl.formatMessage({
          id: 'archiveCaseConfirmDialog',
          defaultMessage:
            'Are you sure you\'d like to archive this case? Once archived, the case will be suppressed from view on the dashboard unless "View All" toggle is enabled.',
        })}
        confirmText={intl.formatMessage({
          id: 'archive',
          defaultMessage: 'Archive',
        })}
        onConfirm={() => {
          if (archiveModal) {
            // This emits an event for a saga listener
            dispatch(archiveCase(archiveModal));
          }
          setArchiveModal(null);
        }}
        cancelText={intl.formatMessage({
          id: 'cancel',
          defaultMessage: 'Cancel',
        })}
        onCancel={() => {
          setArchiveModal(null);
        }}
      />
    </TableBodyRow>
  );
};

const CaseRow = (props: CaseRowPropTypes) => (
  <ThemeProvider theme={defaultTheme}>
    <CaseRowContent {...props} />
  </ThemeProvider>
);

export default CaseRow;

import { Re } from '../reducers';
import { UPDATE_NOTIFICATION_QUERY_RESPONSE } from '../services/queries/updateNotification';

export const updateNotificationSuccess: Re<
  UPDATE_NOTIFICATION_QUERY_RESPONSE
> = (state, { payload }) => {
  const index = state.notifications.findIndex(
    ({ id }) => id === payload.createStructuredData.id
  );

  return {
    ...state,
    notifications: [
      ...state.notifications.slice(0, index),
      { ...payload.createStructuredData },
      ...state.notifications.slice(index + 1),
    ],
  };
};

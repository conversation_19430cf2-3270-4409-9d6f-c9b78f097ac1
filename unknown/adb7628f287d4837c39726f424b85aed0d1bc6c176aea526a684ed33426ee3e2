import express from 'express';
import { pick } from 'lodash';
import { Logger } from '../logger';
import { callGQL } from '../api/callGraphql';
import { Messages } from '../errors/messages';
import { deleteFileQuery} from '../api/queries';
import { StatusCodes } from '../errors/statusCodes';
import { DeleteFileResponse } from '../model/responses';
import { checkTdoIsLockedAdapter } from "../adapters/checkTdoIsLocked";
import { isTdoId } from '../validations/helpers';

export const deleteFile = async (
    req: express.Request,
    res: express.Response
  ) => {
    const tdoId = req.params.tdoId;
    if (!isTdoId(tdoId)) {
      return res.status(StatusCodes.BadRequest).json({ error: Messages.tdoIdRequired });
    }
    const headers = pick(req.headers, ['authorization']);
    const query = deleteFileQuery(tdoId);

    const isLocked = await checkTdoIsLockedAdapter(headers, tdoId);
    if (isLocked) {
      return res.status(StatusCodes.BadRequest).json({ error: Messages.fileIsLocked });
    }

    try {
      const { deleteTDO /*, response */ } = await callGQL<DeleteFileResponse>(headers, query);
      if (deleteTDO && deleteTDO.id === tdoId) {
        Logger.log('deleted tdoId: ' + tdoId);
        return res.status(StatusCodes.Success).json(deleteTDO);
      }
      // if (response) {
      //   return res.status(StatusCodes.BadRequest).json({ error: Messages.DeleteFileFail });
      // }
      return res.status(StatusCodes.BadRequest).json({ error: Messages.DeleteFileFail });
    } catch(err: any) {
      Logger.error(err);
      if (err?.response?.errors?.[0]?.name === "not_found") {
        return res.status(StatusCodes.BadRequest).json({ error: Messages.InvalidTdoId });
      } else {
        return res.status(StatusCodes.BadRequest).json(err);
      }
    }
  };

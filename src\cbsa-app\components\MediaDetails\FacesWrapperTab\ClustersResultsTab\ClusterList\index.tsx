import Box from '@mui/material/Box';
import { useStyles } from '../styles';
import Qimg from '@cbsa-components/Qimg';
import { getActiveState } from '../utils';
import ClusterGroup from '../ClusterGroup';
import Checkbox from '@cbsa-components/CheckBox';
import ClickToEdit from '@cbsa-components/ClickToEdit';
import { List, ListRowRenderer } from 'react-virtualized';
import PropTypes, { SeekMediaTimePayload } from '../PropTypes';
import { ClusterItem } from '@common-modules/mediaDetails/models';
import { memo, ReactNode, useMemo, useState, useEffect, useRef } from 'react';
import { ReactComponent as IconUDR } from '@resources/images/object_type/UDR_24x24px.svg';
import { ReactComponent as IconHead } from '@resources/images/object_type/Head_24x24px.svg';
import { ReactComponent as IconPlate } from '@resources/images/object_type/Plate_24x24px.svg';
import { ReactComponent as IconLaptop } from '@resources/images/object_type/Laptop_24x24px.svg';
import { ReactComponent as IconPOIM } from '@resources/images/object_type/POI_24x24px.svg';
import {
  UDR_COLOR,
  HEAD_COLOR,
  PLATE_COLOR,
  LAPTOP_COLOR,
  POIM_COLOR,
} from '@helpers/constants';
import { SvgIcon } from '@mui/material';

const COLLAPSED_ROW_HEIGHT = 116;

const ClusterListView = ({
  clusterList,
  selected,
  setSelectedGroups,
  changeClusterLabel,
  highlightedOverlay,
  onHighlightPoly,
  children: [ClusterListFilter, ClusterListSort],
}: ClusterListViewPropTypes) => {
  const classes = useStyles();
  const listContainerRef = useRef<HTMLDivElement>(null);
  const [listContainerHeight, setListContainerHeight] = useState<number>(0);
  const [listContainerWidth, setListContainerWidth] = useState<number>(0);
  const [scrollToIndex, setScrollToIndex] = useState<number | undefined>();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const rowHeights = useMemo(() => COLLAPSED_ROW_HEIGHT, [clusterList]);

  const active = (cl: ClusterItem) => getActiveState(cl.segments, selected);

  const handleChecked = (cl: ClusterItem) => () => {
    const isActive = active(cl) !== 1;
    return setSelectedGroups({
      selected: {
        [cl.id]: isActive,
        ...cl.segments
          .map((segment) =>
            segment.subsegmentIds.reduce<{ [key: string]: boolean }>(
              (ids, id) => {
                ids[id] = isActive;
                return ids;
              },
              {}
            )
          )
          .reduce((ss, s) => Object.assign(ss, s), {}),
      },
    });
  };

  const onHighlightPolyHandle = (payload: SeekMediaTimePayload) => {
    onHighlightPoly(payload);
  };

  const handleLabelEdited = (cl: ClusterItem) => (value: string) => {
    changeClusterLabel({
      clusterId: cl.id,
      type: cl.type,
      userLabel: value.trim(),
    });
  };

  // const onExpandMore = (cl: ClusterItem) => () => {
  //   const isExpandedLocal = { ...isExpanded, [cl.id]: !isExpanded[cl.id] };
  //
  //   if (isExpandedLocal[cl.id]) {
  //     const scrollToIndex = clusterList.findIndex(({ id }) => cl.id === id);
  //     setScrollToIndex(scrollToIndex);
  //   } else {
  //     setScrollToIndex(undefined);
  //   }
  //
  //   setExpanded(isExpandedLocal);
  // };

  const renderObjectType = (cl: ClusterItem) => {
    let IconComponent;
    let color;
    let text;
    let props = {};
    switch (cl.type) {
      case 'udr':
        IconComponent = IconUDR;
        color = UDR_COLOR;
        text = 'UDR';
        props = { stroke: UDR_COLOR };
        break;

      case 'head':
        IconComponent = IconHead;
        color = HEAD_COLOR;
        text = 'Head';
        props = { fill: HEAD_COLOR };
        break;

      case 'poim':
        IconComponent = IconPOIM;
        color = POIM_COLOR;
        text = 'POI';
        props = { stroke: POIM_COLOR };
        break;

      case 'licensePlate':
        IconComponent = IconPlate;
        color = PLATE_COLOR;
        text = 'Plate';
        props = { fill: PLATE_COLOR };
        break;

      case 'vehicle':
        IconComponent = IconPlate;
        color = PLATE_COLOR;
        text = 'Vehicle';
        props = { fill: PLATE_COLOR };
        break;

      case 'laptop':
        IconComponent = IconLaptop;
        color = LAPTOP_COLOR;
        text = 'Laptop';
        props = { fill: LAPTOP_COLOR };
        break;
      default:
        console.warn('Unknown type', cl.type);
        return null;
    }

    return (
      <div data-test="detection-name" style={{ marginLeft: '10px' }}>
        <SvgIcon style={{ width: 16, height: 16 }}>
          {IconComponent && <IconComponent {...props} />}
        </SvgIcon>
        <span style={{ color }}>{text}</span>
      </div>
    );
  };

  const renderThumbnailWithType = (cl: ClusterItem) => {
    let color;
    switch (cl.type) {
      case 'udr':
        color = UDR_COLOR;
        break;

      case 'head':
        color = HEAD_COLOR;
        break;

      case 'poim':
        color = POIM_COLOR;
        break;

      case 'licensePlate':
        color = PLATE_COLOR;
        break;

      case 'vehicle':
        color = PLATE_COLOR;
        break;

      case 'laptop':
        color = LAPTOP_COLOR;
        break;
      default:
        console.warn('Unknown type', cl.type);
        return null;
    }

    return (
      <Qimg
        src={cl.picUri}
        loading="lazy"
        style={{ border: `1px solid ${color}`, width: '65px', height: '80px' }}
      />
    );
  };

  useEffect(() => {
    const LAYOUT_DIMENSIONS: Record<string, number> = {
      APP_BAR: 55,
      PADDING_TOP: 24,
      POI_IMAGE_VIEWER: 240,
      CONTROL_BAR_1: 47,
      CONTROL_BAR_2: 46,
      PADDING_BOTTOM: 12,
    };

    let usedHeight = 0;
    let dim: number | undefined;
    for (const prop in LAYOUT_DIMENSIONS) {
      if ((dim = LAYOUT_DIMENSIONS[prop])) {
        usedHeight += dim;
      }
    }

    const calculatedContainerHeight = window.innerHeight - usedHeight;

    function resizeHandler() {
      const container = listContainerRef.current;

      if (!container) {
        return;
      }

      setListContainerHeight(
        // container.clientHeight > 0 ? container.clientHeight : window.innerHeight
        container.clientHeight > 0
          ? container.clientHeight
          : calculatedContainerHeight
      );
      setListContainerWidth(container.clientWidth || 0);
    }

    const container = listContainerRef.current;

    if (!container) {
      return;
    }

    setListContainerHeight(
      // container.clientHeight > 0 ? container.clientHeight : window.innerHeight
      container.clientHeight > 0
        ? container.clientHeight
        : calculatedContainerHeight
    );
    setListContainerWidth(container.clientWidth || 0);

    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
    };
  }, []);

  useEffect(() => {
    if (!highlightedOverlay) {
      return;
    }

    const scrollToIndex = clusterList.findIndex(({ groups }) =>
      groups.some((group) => highlightedOverlay?.groupId === group.id)
    );

    setScrollToIndex(scrollToIndex);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [highlightedOverlay]);

  const renderClusterRow: ListRowRenderer = ({ index, style }) => {
    const cl = clusterList[index];

    return (
      cl && (
        <Box
          data-test="clusterList-row"
          key={cl.id}
          id={`cluster-${cl.id}`}
          className={classes.listRow}
          display="flex"
          flexDirection="row"
          style={{
            minHeight: COLLAPSED_ROW_HEIGHT,
            ...style,
          }}
        >
          <Box marginRight="10px">
            <Checkbox
              onChange={handleChecked(cl)}
              checked={active(cl) !== -1}
              indeterminate={active(cl) === 0}
              data-veritone-element="face-item-check-box"
            />
          </Box>
          <Box
            display="flex"
            flexDirection="row"
            marginRight="5px"
            className={classes.polyPreview}
          >
            {renderThumbnailWithType(cl)}
          </Box>
          <Box
            display="flex"
            flexDirection="column"
            className={classes.fg10auto}
          >
            <Box
              className={classes.label}
              title={cl.userLabel ? cl.userLabel : ''}
            >
              <ClickToEdit
                wrapperClass="wrapperClass"
                inputClass="inputClass"
                textClass="textClass"
                value={cl.userLabel ? cl.userLabel : ''}
                endEditing={handleLabelEdited(cl)}
              />
            </Box>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginRight: '5px',
                marginTop: '12px',
              }}
            >
              {renderObjectType(cl)}
              <ClusterGroup
                segments={cl.segments}
                highlightedOverlay={highlightedOverlay}
                setSelectedGroups={setSelectedGroups}
                selected={selected}
                onHighlightPoly={onHighlightPolyHandle}
              />
            </div>
          </Box>
          {/* <Box display="flex">
            {cl.segments.length > 1 ? (
              <IconButton
                onClick={onExpandMore(cl)}
                style={{ color: 'white', background: 'transparent' }}
                data-veritone-element="face-item-expand-button"
              >
                <ExpandMore
                  className={classnames(
                    classes.expandable,
                    expanded(cl) && classes.open
                  )}
                />
              </IconButton>
            ) : null}
          </Box> */}
        </Box>
      )
    );
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      className={classes.clusterListViewContainer}
    >
      {ClusterListFilter}
      {ClusterListSort}
      <div
        data-testid="results-tab-object"
        id="cluster-list-container"
        className={classes.listContainer}
        ref={listContainerRef}
      >
        {clusterList.length > 0 && (
          <List
            width={listContainerWidth}
            height={listContainerHeight}
            rowCount={clusterList.length}
            rowHeight={rowHeights}
            rowRenderer={renderClusterRow}
            scrollToIndex={scrollToIndex}
          />
        )}
      </div>
    </Box>
  );
};

export type ClusterListViewPropTypes = Pick<
  PropTypes,
  | 'clusterList'
  | 'highlightedOverlay'
  | 'setSelectedGroups'
  | 'changeClusterLabel'
  | 'selected'
> & {
  readonly onHighlightPoly: (payload: SeekMediaTimePayload) => void;
  readonly children: Array<ReactNode>;
};

export default memo(ClusterListView);

import { useState } from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
} from '@cbsa-components/AddMedia/Accordion';
import { isImage } from '@helpers/tdoHelper';
import { I18nTranslate } from '@common/i18n';
import { styled } from '@mui/material/styles';
import {
  ApproveCaseDialog,
  ReadyForReviewCaseDialog,
  ArchiveCaseDialog,
  AuditLogDialog,
  DeleteCaseDialog,
  ExportCaseDialog,
  ProcessCaseDialog,
} from '@cbsa-components/AddMedia/Dialogs';
import {
  Button as MuiButton,
  CircularProgress,
  Typography,
} from '@mui/material';
import { Props, connector } from './props';
import { ValueOf } from 'ts-essentials';

const styles = {
  default: {
    background: '#005C7E',
    borderRadius: '3px',
    color: 'white',
    height: '40px',
    margin: '7.5px 0',
    '&:hover': {
      background: '#003F57',
    },
    '&.Mui-disabled': {
      background: '#E4E4E4',
      color: '#70767B',
    },
    width: '100%',
  },
  warning: {
    background: 'white',
    border: '3px solid #9E0000',
    borderRadius: '3px',
    color: '#9E0000',
    height: '40px',
    margin: '7.5px 0',
    '&:hover': {
      background: '#9E0000',
      color: 'white',
    },
    width: '100%',
  },
};

const CaseActions = ({
  tdos,
  loaders,
  caseDetails,
  auditEvents,
  auditLogsLoading,
  approveCase,
  readyForReviewCase,
  archiveCase,
  createExportTdo,
  deleteCase,
  processCase,
  startExportJob,
  getCaseAuditLogs,
}: Props) => {
  const [isProcessCaseDialogOpen, setProcessCaseDialogOpen] = useState(false);
  const [isViewLogAuditDialogOpen, setViewLogAuditDialogOpen] = useState(false);
  const [isApproveCaseDialogOpen, setApproveCaseDialogOpen] = useState(false);
  const [isReadyForReviewCaseDialogOpen, setReadyForReviewCaseDialogOpen] =
    useState(false);
  const [isArchiveCaseDialogOpen, setArchiveCaseDialogOpen] = useState(false);
  const [isDeleteCaseDialogOpen, setDeleteCaseDialogOpen] = useState(false);
  const [isExportCaseDialogOpen, setExportCaseDialogOpen] = useState(false);
  const [expanded, setExpanded] = useState(false);

  const { isDeletingCase, isProcessingCase } = loaders;

  const exportTdo = Object.values(tdos).find((tdo) => tdo.details?.isExport);
  const redactedTdos = Object.values(tdos).reduce<string[]>((redacted, tdo) => {
    const isRedacted = (tdo.assets?.records || []).find(
      (asset) => asset?.assetType === 'redacted-media'
    );
    return isRedacted ? [...redacted, tdo.id] : redacted;
  }, []);

  const isReadyForReview = caseDetails?.status === 'readyForReview';
  const isApproved = caseDetails?.status === 'approved';
  const isArchived = caseDetails?.archive === 'archived';
  const isProcessing = caseDetails?.status === 'processing';

  const canDelete = Object.values(tdos).length === 0;
  const isProcessDisabled =
    isProcessingCase ||
    Object.values(tdos).find(
      (tdo) => tdo?.jobs?.records?.[0]?.status === 'complete'
    ) === undefined ||
    Object.values(tdos).filter((tdo) => isImage(tdo)).length < 1;
  const isExportDisabled = redactedTdos.length === 0;

  const handleProcessCase = () => {
    const [images, media] = Object.values(tdos).reduce<
      [string[], ValueOf<typeof tdos>[]]
    >(
      ([i, m], tdo) =>
        isImage(tdo)
          ? [[...i, tdo?.primaryAsset?.signedUri], m]
          : !tdo?.details?.isExport &&
              tdo?.jobs?.records?.[0]?.status === 'complete'
            ? [i, [...m, tdo]]
            : [i, m],
      [[], []]
    );

    if (caseDetails) {
      processCase({
        images,
        media,
        batchId: `${caseDetails.treeObjectId}_personOfInterest`,
        caseDetails,
      });
    }
    setProcessCaseDialogOpen(false);
  };

  const Button = styled(MuiButton)(styles['default']);
  const WarningButton = styled(MuiButton)(styles['warning']);

  return (
    <>
      <Accordion expanded={expanded} onChange={() => setExpanded(!expanded)}>
        <AccordionSummary aria-controls="case-actions">
          <Typography>
            {I18nTranslate.TranslateMessage('caseActions')}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          {!isProcessing && (
            <>
              <Button
                disabled={isProcessDisabled}
                onClick={() => setProcessCaseDialogOpen(true)}
              >
                {isProcessingCase ? (
                  <>
                    {I18nTranslate.TranslateMessage('processing')}
                    <CircularProgress
                      thickness={2}
                      size={24}
                      style={{ color: '#005C7E', position: 'absolute' }}
                    />
                  </>
                ) : (
                  <>{I18nTranslate.TranslateMessage('processCase')}</>
                )}
              </Button>
              <hr />
            </>
          )}
          <Button onClick={() => setViewLogAuditDialogOpen(true)}>
            {I18nTranslate.TranslateMessage('viewAuditLog')}
          </Button>
          <hr />
          {isReadyForReview && (
            <Button onClick={() => setApproveCaseDialogOpen(true)}>
              {I18nTranslate.TranslateMessage('approveCase')}
            </Button>
          )}
          {!isReadyForReview && !isApproved && (
            <Button onClick={() => setReadyForReviewCaseDialogOpen(true)}>
              {I18nTranslate.TranslateMessage('readyForReview')}
            </Button>
          )}
          <Button onClick={() => setArchiveCaseDialogOpen(true)}>
            {I18nTranslate.TranslateMessage(
              isArchived ? 'reopenCase' : 'archiveCase'
            )}
          </Button>
          <WarningButton onClick={() => setDeleteCaseDialogOpen(true)}>
            {isDeletingCase ? (
              <>
                {I18nTranslate.TranslateMessage('deleting')}
                <CircularProgress
                  thickness={2}
                  size={24}
                  style={{ color: '#005C7E', position: 'absolute' }}
                />
              </>
            ) : (
              <>{I18nTranslate.TranslateMessage('deleteCase')}</>
            )}
          </WarningButton>
          <hr />
          <Button
            disabled={isExportDisabled}
            onClick={() => setExportCaseDialogOpen(true)}
          >
            {I18nTranslate.TranslateMessage('exportCase')}
          </Button>
        </AccordionDetails>
      </Accordion>
      <ProcessCaseDialog
        isOpen={isProcessCaseDialogOpen}
        onConfirm={handleProcessCase}
        onClose={() => setProcessCaseDialogOpen(false)}
      />
      <AuditLogDialog
        isOpen={isViewLogAuditDialogOpen}
        onClose={() => setViewLogAuditDialogOpen(false)}
        auditEvents={auditEvents}
        getCaseAuditLogs={getCaseAuditLogs}
        auditLogsLoading={auditLogsLoading}
      />
      <ReadyForReviewCaseDialog
        isOpen={isReadyForReviewCaseDialogOpen}
        onConfirm={() => {
          if (caseDetails) {
            readyForReviewCase(caseDetails);
            setReadyForReviewCaseDialogOpen(false);
          }
        }}
        onClose={() => setReadyForReviewCaseDialogOpen(false)}
      />
      <ApproveCaseDialog
        isOpen={isApproveCaseDialogOpen}
        onConfirm={() => {
          if (caseDetails) {
            approveCase(caseDetails);
            setApproveCaseDialogOpen(false);
          }
        }}
        onClose={() => setApproveCaseDialogOpen(false)}
      />
      <ArchiveCaseDialog
        isArchived={isArchived}
        isOpen={isArchiveCaseDialogOpen}
        onConfirm={() => {
          if (caseDetails) {
            archiveCase({ caseDetails, archive: !isArchived });
            setArchiveCaseDialogOpen(false);
          }
        }}
        onClose={() => setArchiveCaseDialogOpen(false)}
      />
      <DeleteCaseDialog
        canDelete={canDelete}
        isOpen={isDeleteCaseDialogOpen}
        onConfirm={() => {
          if (caseDetails) {
            deleteCase(caseDetails);
            setDeleteCaseDialogOpen(false);
          }
        }}
        onClose={() => setDeleteCaseDialogOpen(false)}
      />
      {caseDetails?.treeObjectId && (
        <ExportCaseDialog
          exportTdo={exportTdo}
          redactedTdos={redactedTdos}
          createExportTdo={() => {
            if (caseDetails?.treeObjectId) {
              createExportTdo(caseDetails.treeObjectId);
            }
          }}
          startExportJob={() => {
            if (exportTdo && caseDetails?.treeObjectId) {
              startExportJob({
                caseId: caseDetails.treeObjectId,
                tdoId: exportTdo.id,
              });
            }
          }}
          isOpen={isExportCaseDialogOpen}
          onConfirm={() => setExportCaseDialogOpen(false)}
          onClose={() => setExportCaseDialogOpen(false)}
        />
      )}
    </>
  );
};

export default connector(CaseActions);

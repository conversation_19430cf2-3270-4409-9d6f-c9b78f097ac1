import { assign, omit, pick } from 'lodash';
import { createSelector } from 'reselect';
import { modules } from '@veritone/glc-redux';
import { selectApplicationConfigs } from '../app';

const {
  config: { getConfig },
} = modules;

export const selectConfig = (state: any) => getConfig<Window['config']>(state);

export const selectConfigEngines = createSelector(
  selectConfig,
  selectApplicationConfigs,
  (config, applicationConfigs) => {
    const transcriptionEngineId = applicationConfigs.transcriptionEngineId as
      | string
      | undefined;
    const applicationConfigOverrides = transcriptionEngineId
      ? { transcriptionEngineId }
      : {};
    const transcriptionEngineOptions = omit(
      pick(applicationConfigs, 'transcriptionEngineOptions') || {},
      // remove keys that should not be overwritten
      'app',
      'user',
      'organizationId'
    );
    return assign(
      {
        outputWriterEngineId: '8eccf9cc-6b6d-4d7d-8cb3-7ebf4950c5f3',
        // legacy ingestion engines - keep for now to support old TDOs
        webstreamAdapterEngineId: '9e611ad7-2d3b-48f6-a51b-0a1ba40fe255',
        streamIngestorEngineId: '8bdb0e3b-ff28-4f6e-a3ba-887bd06e6440',
        streamIngestorDASHPlaybackSegmentCreatorId:
          '29694232-a4f5-4622-a422-b48f206ca52a',
        streamIngestorPlaybackSegmentCreatorId:
          '352556c7-de07-4d55-b33f-74b1cf237f25',
        // legacy redaction engine
        legacyRedactEngineId: '51263b9d-186b-4d09-a422-a8f12cc38f5c',
      },
      pick(config, [
        'apiRoot',
        'defaultClusterId',

        'glcIngestionEngineId',
        'fastChunkerEngineId',

        'redactEngineId',

        'downloadEngineCategoryId',
        'downloadEngineId',

        'opticalTrackingEngineCatagoryId',
        'opticalTrackingEngineCategoryId',
        'opticalTrackingEngineId',

        'detectionCategory',
        'detectionEngineId',

        'transcriptionCategoryId',
        'transcriptionEngineId',
        'poiDetectionEngineId',

        'voiceMaskEngineId',
      ]),
      applicationConfigOverrides,
      { transcriptionEngineOptions }
    );
  }
);

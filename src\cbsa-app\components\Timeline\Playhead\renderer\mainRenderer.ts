import Konva from 'konva';
import { clamp } from 'lodash';
import { getOffset } from '@helpers/mouseUtils';
import {
  animationFrameScheduler,
  BehaviorSubject,
  combineLatest,
  fromEvent,
  interval,
  Subject,
} from 'rxjs';
import {
  filter,
  map,
  sample,
  startWith,
  switchMap,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs/operators';

import { HEIGHT_PX, OFFSET_LEFT_X } from './constants';
import { interactRenderer } from './interactRenderer';
import { Render } from './interfaces';
import { ticksRenderer } from './ticksRenderer';
import { aToB } from './utils';

export interface RendererOutput {
  destroy: () => void;
  setStart: (ms: number) => void;
  setStop: (ms: number) => void;
}

/**
 * Effect to draw the timeline playhead.
 */
export const mainRenderer = (
  container: string,
  minWindowMs: number,
  maxWindowMs: number,
  onSetStart: (ms: number) => void,
  onSetStop: (ms: number) => void,
  onSeekMedia: (ms: number) => void
): RendererOutput => {
  // Setup Konva
  const stage = new Konva.Stage({
    container,
    height: HEIGHT_PX,
  });
  stage.width(stage.container().clientWidth);

  // Setup Observables
  const raf$ = interval(undefined, animationFrameScheduler);
  const destroy$ = new Subject<boolean>();

  const onSetStart$ = new BehaviorSubject(minWindowMs);
  const onSetStop$ = new BehaviorSubject(maxWindowMs);

  const onClick$ = fromEvent<WheelEvent>(stage.container(), 'mousedown').pipe(
    switchMap((down) =>
      fromEvent<WheelEvent>(document, 'mouseup').pipe(
        map((up) => ({ down, up }))
      )
    ),
    filter(({ down, up }) => up.timeStamp - down.timeStamp < 200),
    map(({ up }) => up)
  );
  const onMousewheel$ = fromEvent<WheelEvent>(
    stage.container(),
    'mousewheel'
  ).pipe(tap((evt) => evt.preventDefault()));
  const onResize$ = fromEvent(window, 'resize').pipe(
    map(() => ({ widthPx: stage.container().clientWidth })),
    startWith({ widthPx: stage.container().clientWidth })
  );
  const onWindowFocus$ = fromEvent(window, 'focus').pipe(startWith(0));

  const ticksLayer = new Konva.Layer({ listening: false });
  const interactLayer = new Konva.Layer();
  const drawers: ReadonlyArray<Render> = [
    ticksRenderer(stage, minWindowMs, maxWindowMs, ticksLayer),
    interactRenderer(
      stage,
      minWindowMs,
      maxWindowMs,
      interactLayer,
      (ms: number) => onSetStart$.next(ms),
      (ms: number) => onSetStop$.next(ms),
      onResize$.pipe(takeUntil(destroy$))
    ),
  ];

  stage.add(ticksLayer);
  stage.add(interactLayer);

  onMousewheel$
    .pipe(
      takeUntil(destroy$),
      withLatestFrom(onSetStart$, onSetStop$, (event, startMs, stopMs) => ({
        deltaY: event.deltaY,
        x: getOffset(event).x - OFFSET_LEFT_X,
        pxWidth: stage.container().clientWidth - OFFSET_LEFT_X,
        startMs,
        stopMs,
      })),
      map(({ deltaY, x, pxWidth, startMs, stopMs }) => {
        const pos = clamp(x / pxWidth, 0, 1);
        const delta = deltaY * ((stopMs - startMs) / 1000);
        const deltaS = delta * pos;
        const deltaE = delta * (1 - pos);
        return {
          startMs: Math.max(startMs - deltaS, minWindowMs),
          stopMs: Math.min(stopMs + deltaE, maxWindowMs),
        };
      }),
      filter(({ startMs, stopMs }) => stopMs - startMs > 2000)
    )
    .subscribe(({ startMs, stopMs }) => {
      onSetStart$.next(startMs);
      onSetStop$.next(stopMs);
    });

  /**
   * Handle on click events.
   */
  onClick$
    .pipe(
      takeUntil(destroy$),
      withLatestFrom(onSetStart$, onSetStop$, (event, startMs, stopMs) => ({
        offsetX: event.offsetX,
        startMs,
        stopMs,
        minLeftX: OFFSET_LEFT_X,
        maxRightX: stage.container().clientWidth - 5,
      }))
    )
    .subscribe(({ startMs, stopMs, offsetX, minLeftX, maxRightX }) => {
      const p2m = aToB(minLeftX, maxRightX, startMs, stopMs);
      const time = p2m(offsetX - minLeftX);
      onSeekMedia(time);
    });

  /**
   * Redraw when start/stop window changes.
   */
  combineLatest(
    [onSetStart$, onSetStop$, onResize$, onWindowFocus$],
    (startMs, stopMs, { widthPx }, _focus) => ({
      startMs,
      stopMs,
      widthPx,
    })
  )
    .pipe(
      takeUntil(destroy$),
      tap(({ startMs, stopMs, widthPx }) => {
        stage.width(widthPx);
        onSetStart(startMs);
        onSetStop(stopMs);
      }),
      sample(raf$),
      mapRender(drawers)
    )
    .subscribe((ls) => ls.forEach((l) => l.batchDraw()));

  return {
    destroy: (): void => {
      destroy$.next(true);
      stage.destroy();
    },
    setStart: (ms: number): void => {
      onSetStart$.next(ms);
    },
    setStop: (ms: number): void => {
      onSetStop$.next(ms);
    },
  };
};

/**
 * Observable operator to draw on ticks.
 */
const mapRender = (drawers: ReadonlyArray<Render>) =>
  map(({ startMs, stopMs }: { startMs: number; stopMs: number }) =>
    drawers.map((d) => d(startMs, stopMs))
  );

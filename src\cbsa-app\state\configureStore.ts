import { createContext } from 'react';
import { find, isString } from 'lodash';
import queryString from 'query-string';
import routes from '@cbsa-pages/routes';
import { boot } from '@common-modules/app';
import createSagaMiddleware from 'redux-saga';
import { sagas } from '@common-state/sagas/cbsa';
import { apiMiddleware } from 'redux-api-middleware';
import composeReducers from '@common-state/rootReducer';
import { cbsaReducers } from '@cbsa-modules/rootReducer';
import { createReduxWorkerMiddleware } from '@utils';
import { appWrapperSagas } from '@cbsa-modules/appWrapper';
import { userPermissionsReducers } from '@user-permissions';
import { connectRoutes, redirect } from 'redux-first-router';
import { userOnboardingReducers, userOnboardingSagas } from '@user-onboarding';
import {
  HIDE_BLOCK_NAVIGATION_MODAL,
  SHOW_BLOCK_NAVIGATION_MODAL,
} from '@common-modules/mediaDetails';
import { configureStore } from '@reduxjs/toolkit';
import { keepAlive } from '@common/shared/keepSagasAlive';

// TODO: What type should this be
let store: any;

const {
  reducer: routerReducer,
  middleware: routerMiddleware,
  enhancer: routerEnhancer,
  initialDispatch,
} = connectRoutes(routes, {
  querySerializer: queryString,
  initialDispatch: false,
  onBeforeChange: (dispatch, getState, { action }) => {
    const routeDefinition = routes[action.type];

    if (!isString(routeDefinition) && routeDefinition?.redirects) {
      const matchedRedirect = find(
        routeDefinition.redirects,
        ({ test }) => !!test(getState, action)
      );

      matchedRedirect && dispatch(redirect(matchedRedirect.to));
    }
  },
  displayConfirmLeave: (message, callback) => {
    const canLeave = (can: boolean) => {
      store.dispatch(HIDE_BLOCK_NAVIGATION_MODAL());
      return callback(can); // navigate to next route or stay where ur at
    };

    store.dispatch(SHOW_BLOCK_NAVIGATION_MODAL({ message, canLeave }));
  },
});

const { reduxWorkerMiddleware } = createReduxWorkerMiddleware(
  /* webpackChunkName: "redux-worker" */ new Worker(
    new URL('@worker/worker', import.meta.url)
  )
);

const sagaMiddleware = createSagaMiddleware();
const middlewares = [
  apiMiddleware,
  reduxWorkerMiddleware,
  routerMiddleware,
  sagaMiddleware,
];

const isDev = process.env.NODE_ENV === 'development';
if (isDev) {
  const { createLogger } = require(`redux-logger`);

  middlewares.push(
    createLogger({
      collapsed: true,
    })
  );
}

export default function configureAppStore() {
  store = configureStore({
    reducer: composeReducers({
      ...cbsaReducers,
      ...userOnboardingReducers,
      ...userPermissionsReducers,
      location: routerReducer,
    }),
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }).prepend(...middlewares),
    enhancers: (getDefaultEnhancers) =>
      getDefaultEnhancers().prepend(routerEnhancer as any),
    devTools: process.env.NODE_ENV !== 'production',
  });

  sagaMiddleware.run(keepAlive(sagas));
  sagaMiddleware.run(keepAlive(appWrapperSagas));
  sagaMiddleware.run(keepAlive([userOnboardingSagas]));

  initialDispatch?.();
  store.dispatch(boot());

  return store;
}

// @ts-expect-error TODO: no value was passed originally but it's required by type
// see https://github.com/DefinitelyTyped/DefinitelyTyped/pull/24509#issuecomment-382213106
export const StoreContext = createContext();

import {
  all,
  call,
  cancel,
  fork,
  put,
  race,
  select,
  takeEvery,
  take,
  delay,
} from 'typed-redux-saga/macro';
import { enqueueSnackbar } from '@common-modules/snackbar';

import { STOP_ALL_ENGINE_POLLING } from '../actions';
import * as Actions from './actions';
import { selectEngineStatus } from './selectors';
import {
  cancelJobService,
  checkJobStatusService,
  // createEngineJobService,
  // queryEngineResultsRecordService,
  // getEngineResultsFromURIService,
} from './services';
import { selectTdo } from '@common-modules/mediaDetails';
import { selectConfigEngines } from '../selectors';
import { ProcessEngineRequest } from './models/services';
import { TDOId } from '../../universal/models/Brands';
import { ActionCreatorWithPayload } from '@reduxjs/toolkit';
import { selectUDRCollection } from '@common-modules/mediaDetails/selectors';
import { search } from '@worker';

export function* saga() {
  yield* all([
    fork(onProcessEngineRequestOverLayAction),
    fork(onProcessEngineRequestAction),
    fork(onProcessEngineRequestSuccessAction),
    fork(onProcessEngineRequestFailureAction),
    // fork(onCreateEngineJobAction),
    fork(onStartPollingEngineResultsAction),
    fork(onCheckJobStatusAction),
    // fork(onQueryEngineResultsRecordAction),
    // fork(onGetEngineResultsFromURIAction),
    fork(onCancelJobAction),
    fork(onCancelJobSuccessAction),
    // fork(onCancelJobFailureAction),
  ]);
}

/**
 * Action watchers.
 */

export function* onProcessEngineRequestOverLayAction() {
  /**
   * @param { Action<ProcessEngineRequestWithOverlay> } action
   */
  yield* takeEvery(Actions.PROCESS_ENGINE_REQUEST_OVERLAY, function* (action) {
    const udrCollection = yield* select(selectUDRCollection);
    const collectionSeries = udrCollection?.collection
      ? search(udrCollection.collection, action.payload.timeMs).find(
          (p) => p.id === action.payload.overlay
        )
      : undefined;
    if (!collectionSeries) {
      return;
    }
    const boundingPolyPayload = {
      id: action.payload.id,
      overlay: action.payload.overlay,
      boundingPoly: collectionSeries.boundingPoly,
      timeMs: action.payload.timeMs,
      parentUdrId: collectionSeries.groupId,
      trackBack: action.payload.trackBack,
      trackForward: action.payload.trackForward,
    };
    yield* put(Actions.processEngineRequestAction(boundingPolyPayload));
  });
}

export function* onProcessEngineRequestAction() {
  /**
   * @param { Action<ProcessEngineRequest> } action
   */
  yield* takeEvery(Actions.PROCESS_ENGINE_REQUEST, function* (action) {
    yield* startEngineJob(action.payload);
  });
}

function* onProcessEngineRequestSuccessAction() {
  yield* takeEvery(Actions.PROCESS_ENGINE_REQUEST_SUCCESS, function* () {
    yield* put(
      enqueueSnackbar({
        message: 'An Optical Tracking process has completed.',
      })
    );
  });
}

function* onProcessEngineRequestFailureAction() {
  yield* takeEvery(Actions.PROCESS_ENGINE_REQUEST_FAILURE, function* (action) {
    yield* put(
      enqueueSnackbar({
        message: action.payload.errorMsg,
      })
    );
  });
}

function* onStartPollingEngineResultsAction() {
  yield* takeEvery(Actions.START_POLLING_ENGINE_RESULTS, function* (action) {
    yield* call(startPollingJobStatus, action.payload);
  });
}

function* onCheckJobStatusAction() {
  yield* takeEvery(Actions.CHECK_JOB_STATUS, function* (action) {
    yield* put(checkJobStatusService(action.payload));
  });
}

// function* onQueryEngineResultsRecordAction() {
//   yield takeEvery(Actions.QUERY_ENGINE_RESULTS_RECORD, function*(action) {
//     yield put(queryEngineResultsRecordService(action.payload));
//   });
// }

// function* onGetEngineResultsFromURIAction() {
//   yield takeEvery(Actions.GET_ENGINE_RESULTS_FROM_URI, function*(action) {
//     yield put(getEngineResultsFromURIService(action.payload));
//   });
// }

function* onCancelJobAction() {
  yield* takeEvery(Actions.CANCEL_JOB, function* (action) {
    yield* put(cancelJobService(action.payload));
  });
}

function* onCancelJobSuccessAction() {
  yield* takeEvery(Actions.CANCEL_JOB_SUCCESS, function* () {
    yield* put(
      enqueueSnackbar({
        message: 'The Optical Tracking process has been canceled.',
      })
    );
  });
}

// function* onCancelJobFailureAction() {
//   yield* takeEvery(Actions.CANCEL_JOB_FAILURE, function* () {
//     yield* put(
//       enqueueSnackbar({
//         message: "I'm sorry. The cancel request failed.",
//       })
//     );
//   });
// }

/**
 * Start engine job process.
 */

/**
 * @param { ProcessEngineRequest } arg
 */

export function* startEngineJob({
  id,
  overlay,
  boundingPoly,
  timeMs,
  parentUdrId,
  trackBack,
  trackForward,
}: ProcessEngineRequest) {
  const tdo = yield* select(selectTdo);
  if (!tdo) {
    return;
  }
  const { id: tdoId, name: tdoName } = tdo;

  const { opticalTrackingEngineId, defaultClusterId } =
    yield* select(selectConfigEngines);

  yield* put(
    Actions.createEngineJobAction({
      id,
      overlay,
      boundingPoly,
      timeMs,
      parentUdrId,
      trackBack,
      trackForward,
      tdoId,
      tdoName,
      opticalTrackingEngineId,
      clusterId: defaultClusterId,
    })
  );
  const { ok } = yield* race({
    ok: take(Actions.CREATE_ENGINE_JOB_SUCCESS),
    err: take(Actions.CREATE_ENGINE_JOB_FAILURE),
  });

  if (ok) {
    yield* put(
      Actions.startPollingEngineResultsAction({
        tdoId,
        jobId: ok.payload.createJob.id,
      })
    );
  }
}

export function* startPollingJobStatus({
  tdoId,
  jobId,
}: {
  tdoId: TDOId;
  jobId: string;
}) {
  while (true) {
    const { isRunning, status, error } = yield* select(
      selectEngineStatus(tdoId, jobId)
    );

    if (status === 'complete') {
      break;
    }
    if (!isRunning || error) {
      yield* put(Actions.stopPollingEngineResultsAction({ tdoId, jobId }));
      yield* cancel();
      return;
    }
    yield* put(Actions.checkJobStatusAction({ tdoId, jobId }));
    const { stop } = yield* race({
      ok: delay(10_000),
      stop: take([
        isActionOfTDO(Actions.STOP_POLLING_ENGINE_RESULTS, jobId),
        STOP_ALL_ENGINE_POLLING,
      ]),
    });
    if (stop) {
      yield* put(Actions.stopPollingEngineResultsAction({ tdoId, jobId }));
      yield* cancel();
      return;
    }
  }

  yield* put(
    Actions.processEngineRequestSuccessAction({
      tdoId,
      jobId,
    })
  );
}

export const isActionOfTDO =
  <P>(
    action: ActionCreatorWithPayload<P extends { jobId: string } ? P : never>,
    jobId: string
  ) =>
  ({ type, payload }: { type: string; payload?: { jobId?: string } }) =>
    type === action.type && payload?.jobId === jobId;

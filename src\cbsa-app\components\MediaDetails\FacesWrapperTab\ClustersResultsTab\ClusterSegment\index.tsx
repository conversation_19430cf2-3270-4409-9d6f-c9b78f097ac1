import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { memo } from 'react';
import Box from '@mui/material/Box';

import { ClusterSegment } from '@common-modules/mediaDetails/models';
import Checkbox from '@cbsa-components/CheckBox';
import PropTypes, { SeekMediaTimePayload } from '../PropTypes';
// TODO: Fix
// eslint-disable-next-line import/no-unresolved
import * as styles from '../styles.scss';
import TimePeriod from '../TimePeriod';

const ClusterSegmentView = ({
  segment: {
    id,
    groupId,
    startTimeMs,
    stopTimeMs,
    numAutoInterpolations,
    isManualInterpolation,
    subsegmentIds,
    objectIds,
    type,
  },
  selected,
  highlightedOverlay,
  setSelectedGroups,
  onHighlightPoly,
}: ClusterSegmentPropTypes) => {
  const active = !!selected[id];

  const isHighlighted =
    highlightedOverlay?.groupId === groupId &&
    startTimeMs <= highlightedOverlay.timeMs &&
    stopTimeMs >= highlightedOverlay.timeMs;

  const setSelectedGroup = () => {
    setSelectedGroups({
      selected: subsegmentIds.reduce<{ [key: string]: boolean }>((ss, i) => {
        ss[i] = !active;
        return ss;
      }, {}),
    });
  };

  const onClick = () => {
    const objectId = objectIds[0];
    if (objectId) {
      onHighlightPoly({
        startTimeMs,
        id: objectId,
        groupId,
        type,
      });
    }
  };

  return (
    <Box
      id={`cluster-group-item-${id}`}
      className={styles.groupItem}
      display="flex"
      flexDirection="row"
    >
      <div className={styles.colLen}>
        <Checkbox
          onChange={setSelectedGroup}
          checked={active}
          data-veritone-element="cluster-item-time-check-box"
        />
      </div>
      <div className={styles.colTime}>
        <TimePeriod
          startTimeMs={startTimeMs}
          stopTimeMs={stopTimeMs}
          onClick={onClick}
        />
      </div>
      <div className={styles.colInterp}>{numAutoInterpolations}</div>
      <div className={styles.colInterp}>
        {isManualInterpolation ? (
          <FiberManualRecordIcon fontSize="small" />
        ) : (
          ''
        )}
      </div>
      {isHighlighted ? <div className={styles.highlightedSegment} /> : null}
    </Box>
  );
};

export default memo(ClusterSegmentView);

export type ClusterSegmentPropTypes = Pick<
  PropTypes,
  'selected' | 'highlightedOverlay' | 'setSelectedGroups'
> & {
  readonly segment: ClusterSegment;
  readonly onHighlightPoly: (payload: SeekMediaTimePayload) => void;
};

import { TDOId } from '@common-modules/universal/models/Brands';
import { markWorkerAction } from '@utils';
import { ProcessEngineRequest } from './models/services';
import { createAction } from '@reduxjs/toolkit';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import {
  CancelJobServiceResponse,
  CheckJobStatusServiceResponse,
  QueryEngineResultsServiceResponse,
} from './services';

export const NOOP = 'noop';

/**
 * Begins the process of starting an Ingestion engine job.
 * Includes polling and loading results.
 */

export const PROCESS_ENGINE_REQUEST = createAction<ProcessEngineRequest>(
  'vtn/redact/engine/ingestion/PROCESS_ENGINE_REQUEST'
);
export const PROCESS_ENGINE_REQUEST_SUCCESS = createAction<{
  tdoId: TDOId;
  jobId: string;
}>('vtn/redact/engine/ingestion/PROCESS_ENGINE_REQUEST_SUCCESS');
export const PROCESS_ENGINE_REQUEST_FAILURE = createAction(
  'vtn/redact/engine/ingestion/PROCESS_ENGINE_REQUEST_FAILURE',
  (tdoId: TDOId, errorMsg: string) => ({
    payload: {
      tdoId,
      errorMsg,
    },
    error: true,
    // meta: undefined,
  })
);

export const MARK_TDO_UPLOADING_END = createAction<{ tdoId: TDOId }>(
  'vtn/redact/engine/ingestion/MARK_TDO_UPLOADING_END'
);

/**
 *
 * @param { ProcessEngineRequest } payload
 */
export const processEngineRequestAction = (payload: ProcessEngineRequest) =>
  PROCESS_ENGINE_REQUEST(payload);

export const processEngineRequestSuccessAction = ({
  tdoId,
  jobId,
}: {
  tdoId: TDOId;
  jobId: string;
}) => PROCESS_ENGINE_REQUEST_SUCCESS({ tdoId, jobId });

export const processEngineRequestFailureAction = ({
  tdoId,
  // jobId,
  errorMsg,
}: {
  tdoId: TDOId;
  // jobId: string;
  errorMsg: string;
}) => PROCESS_ENGINE_REQUEST_FAILURE(tdoId, errorMsg);

/**
 * Create engine job actions.
 */

export const CREATE_ENGINE_JOB = createAction<ProcessEngineRequest>(
  'vtn/redact/engine/ingestion/CREATE_ENGINE_JOB'
);

export const CREATE_ENGINE_JOB_SUCCESS = createGraphQLSuccessAction<{
  createJob: {
    id: string;
    targetId: TDOId;
    status: string;
  };
}>('vtn/redact/engine/ingestion/CREATE_ENGINE_JOB_SUCCESS');

export const CREATE_ENGINE_JOB_FAILURE = createGraphQLFailureAction<
  | Array<{
      message: string;
      data?: {
        engineId: string;
        errorId: string;
        requestId: string;
        correlationId: string;
      };
    }>
  | Error,
  { id: TDOId; tdoName: string },
  true
>('vtn/redact/engine/ingestion/CREATE_ENGINE_JOB_FAILURE');

/**
 * @param { ProcessEngineRequest } payload
 */
export const createEngineJobAction = (payload: ProcessEngineRequest) =>
  markWorkerAction(CREATE_ENGINE_JOB(payload));

/**
 * Check job status actions.
 */

export const CHECK_JOB_STATUS = createAction<{
  tdoId: TDOId;
  jobId: string;
}>('vtn/redact/engine/ingestion/CHECK_JOB_STATUS');
export const CHECK_JOB_STATUS_SUCCESS =
  createGraphQLSuccessAction<CheckJobStatusServiceResponse>(
    'vtn/redact/engine/ingestion/CHECK_JOB_STATUS_SUCCESS'
  );

export const CHECK_JOB_STATUS_FAILURE = createGraphQLFailureAction(
  'vtn/redact/engine/ingestion/CHECK_JOB_STATUS_FAILURE'
);

export const checkJobStatusAction = ({
  tdoId,
  jobId,
}: {
  tdoId: TDOId;
  jobId: string;
}) => CHECK_JOB_STATUS({ tdoId, jobId });

/**
 * Start and stop polling job status actions.
 */

export const START_POLLING_ENGINE_RESULTS = createAction<{
  tdoId: TDOId;
  jobId: string;
}>('vtn/redact/engine/ingestion/START_POLLING_ENGINE_RESULTS');
export const STOP_POLLING_ENGINE_RESULTS = createAction<{
  tdoId: TDOId;
  jobId: string;
}>('vtn/redact/engine/ingestion/STOP_POLLING_ENGINE_RESULTS');

export const startPollingEngineResultsAction = ({
  tdoId,
  jobId,
}: {
  tdoId: TDOId;
  jobId: string;
}) => START_POLLING_ENGINE_RESULTS({ tdoId, jobId });

export const stopPollingEngineResultsAction = ({
  tdoId,
  jobId,
}: {
  tdoId: TDOId;
  jobId: string;
}) => STOP_POLLING_ENGINE_RESULTS({ tdoId, jobId });

/**
 * Actions to get the metadata for engine results.
 */

export const QUERY_ENGINE_RESULTS = createAction<{
  tdoId: TDOId;
  engines: string[];
  mediaDuration: number;
}>('vtn/redact/engine/transcription/QUERY_ENGINE_RESULTS');
export const QUERY_ENGINE_RESULTS_SUCCESS =
  createGraphQLSuccessAction<QueryEngineResultsServiceResponse>(
    'vtn/redact/engine/transcription/QUERY_ENGINE_RESULTS'
  );
export const QUERY_ENGINE_RESULTS_FAILURE = createGraphQLFailureAction(
  'vtn/redact/engine/transcription/QUERY_ENGINE_RESULTS'
);

export const queryEngineResultsAction = ({
  tdoId,
  engineId,
  mediaDuration,
}: {
  tdoId: TDOId;
  engineId: string;
  mediaDuration: number;
}) => QUERY_ENGINE_RESULTS({ tdoId, engines: [engineId], mediaDuration });

/**
 * Actions to get the metadata for engine results.
 */

export const QUERY_ENGINE_RESULTS_RECORD = createAction<{
  tdoId: TDOId;
}>('vtn/redact/engine/ingestion/QUERY_ENGINE_RESULTS_RECORD');
export const QUERY_ENGINE_RESULTS_RECORD_SUCCESS =
  'vtn/redact/engine/ingestion/QUERY_ENGINE_RESULTS_RECORD_SUCCESS';
export const QUERY_ENGINE_RESULTS_RECORD_FAILURE =
  'vtn/redact/engine/ingestion/QUERY_ENGINE_RESULTS_RECORD_FAILURE';

export const queryEngineResultsRecordAction = ({ tdoId }: { tdoId: TDOId }) =>
  QUERY_ENGINE_RESULTS_RECORD({ tdoId });

/**
 * Actions to actually load the result data.
 */

export const GET_ENGINE_RESULTS_FROM_URI = createAction<{
  signedUri: string;
}>('vtn/redact/engine/ingestion/GET_ENGINE_RESULTS_FROM_URI');
export const GET_ENGINE_RESULTS_FROM_URI_SUCCESS =
  'vtn/redact/engine/ingestion/GET_ENGINE_RESULTS_FROM_URI_SUCCESS';
export const GET_ENGINE_RESULTS_FROM_URI_FAILURE =
  'vtn/redact/engine/ingestion/GET_ENGINE_RESULTS_FROM_URI_FAILURE';

export const getEngineResultsFromURIAction = ({
  signedUri,
}: {
  signedUri: string;
}) => GET_ENGINE_RESULTS_FROM_URI({ signedUri });

/**
 * Cancel a job actions.
 */

export const CANCEL_JOB = createAction<{
  tdoId: TDOId;
  jobId: string;
}>('vtn/redact/engine/ingestion/CANCEL_JOB');
export const CANCEL_JOB_SUCCESS =
  createGraphQLSuccessAction<CancelJobServiceResponse>(
    'vtn/redact/engine/ingestion/CANCEL_JOB_SUCCESS'
  );
export const CANCEL_JOB_FAILURE = createGraphQLFailureAction(
  'vtn/redact/engine/ingestion/CANCEL_JOB_FAILURE'
);

export const cancelJobAction = ({
  tdoId,
  jobId,
}: {
  tdoId: TDOId;
  jobId: string;
}) => CANCEL_JOB({ tdoId, jobId });

export const SET_INGESTION_OPTIONS = createAction<{
  runHead?: boolean;
  runPerson?: boolean;
  runTranscription?: boolean;
}>('vtn/redact/engine/ingestion/SET_INGESTION_OPTIONS');
/**
 * Set options (uses object assign).
 * @param {{ runHead?: boolean, runPerson?: boolean, runTranscription?: boolean }} payload
 */
export const setIngestionOptions = (payload: {
  runHead?: boolean;
  runPerson?: boolean;
  runTranscription?: boolean;
}) => SET_INGESTION_OPTIONS(payload);

export const actionMarkTdoUploadingEnd = (payload: { tdoId: TDOId }) =>
  MARK_TDO_UPLOADING_END(payload);

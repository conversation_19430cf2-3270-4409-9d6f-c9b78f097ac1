export const Messages = {
   createCaseSdo: "Unexpected error occurred while creating case sdo." ,
   createContentTemplate: "Unexpected error occurred while creating case content template." ,
   createCaseFail: 'Unexpected error occurred while creating a case.',
   createFolder: 'Unexpected error occurred while creating a folder. ',
   createTdoFail:  'Unexpected error occurred while creating an tdo to ingest media. ',
   createJobFail:  'Unexpected error occurred when processing createJob request. Maximum retry attempted.',
   ingestMediaFail:  'Unexpected error occurred while ingesting media.',
   FetchUserInfoFail: 'Unexpected error occurred while fetching user Info using token.',
   userNotFound: 'User was not found or not belong to this organization.',
   createRootFolder: 'Unexpected error occurred while creating the CMS Root folder.',
   checkFolderExistsFail: 'Unexpected error occurred while validating folder exist check.',
   checkTdoIsLockedFail: 'Unexpected error occurred while validating if TDO is locked.',
   checkFolderCaseIsLockedFail: 'Unexpected error occurred while validating if folder/case is locked.',
   ParentFolderNotExists: 'The given parentFolderId was not found. It does not exist or your organization does not have access to it.',
   NameIsRequired: 'Name is required to create a case.',
   NameExistsAlready: 'Folder with that name already exists. Please provide different name.',
   fetchSchemaError: 'Unexpected error occurred while fetching latest schemaId.',
   fetchRootFolderError: 'Unexpected error occurred while fetching root folderId.',
   InvalidToken: 'Not authorized. Invalid Token provided.',
   createFolderFail: 'Unexpected error occurred while creating folder.',
   DeleteFolderFail: 'Unexpected error occurred while deleting folder.',
   DeleteCaseFail: 'Unexpected error occurred while deleting case.',
   InvalidCaseId: 'Invalid caseId provided.',
   DeleteFileFail: 'Unexpected error occurred while deleting file.',
   InvalidTdoId: 'The given "tdoId" not found. It does not exist or your organization does not have access to it.',
   InvalidFolderId: 'The folder was not found. It does not exist or your organization does not have access to it.',
   getSchemaIdFail: 'Unexpected error occurred while fetching schemaId.',
   DeleteSdoFail: 'Unexpected error occurred while deleting related sdo of the folder.',
   requestIdOrRequestDetailRequired: 'existingCaseId or createNewCase is required.',
   deleteFolderSuccess: 'Deleted folder successfully.',
   deleteCaseSuccess: 'Deleted case successfully.',
   limitNotNumber: 'limit should be a number.',
   offsetNotNumber: 'offset should be a number.',
   notCaseDeleteEndpoint: 'Case cannot be deleted using \'deleteFolder\' endpoint. Please use \'deleteCase\' endpoint instead.',
   caseIdRequired: 'caseId is required.',
   folderIdRequired: 'folderId is required',
   createJobFailForTdo: 'createJob request failed after maximum retry attempt.',
   InvalidExistingCaseId: 'Invalid existingCaseId. Files cannot be ingested to the folder.',
   InvalidParentFolderId: 'Invalid parentFolderId. Cases cannot be created inside another case.',
   FolderDepthLimitReached: 'Folder cannot be nested more than two level deep.',
   CaseDepthLimitReached: 'Cases cannot be nested more than two level deep.',
   maxLimitReached: 'Limit should not be greater than max limit 9999.',
   fileIsLocked: 'Cannot delete file while it is in use.',
   folderIsLocked: 'Cannot delete folder while one of its files is in use.',
   caseIsLocked: 'Cannot delete case while one of its files is in use.',
   getUserInfoFail: 'Unexpected error occurred while getting user Info.',
   getAppConfigsFail: "Unexpected error occurred while getting app configs.",
   urlRequired: 'Minium one valid url required.',
   inValidUrls: "Invalid urls provided. Please validate the urls and try again.",
   getSdoIdFail: 'Unexpected error occurred while getting case sdoId.',
   deleteAllFilesFail:  'Unexpected error occurred while deleting related case files. Please try again later.',
   getFilesByCaseIdFail:  'Unexpected error occurred while fetching related case files using caseId. Please try again later.',
   checkCaseLockFail: 'Unexpected error occurred while verifying case lock. Please try again later.',
   checkFolderLockFail: 'Unexpected error occurred while verifying folder lock. Please try again later.',
   getChildFoldersFail:  'Unexpected error occurred while fetching child folders for lock check. Please try again later.',
   CaseFilesInProcessingState: 'Cannot delete case as some of the case files are currently in processing state. Please try again later.',
   FolderCaseFilesInProcessingState: 'Cannot delete folder as some of the case files are currently in processing state. Please try again later.',
   getFilesByTreeObjectIdFail:  'Unexpected error occurred while fetching related case files using treeObjectId. Please try again later.',
   tdoIdRequired: 'tdoId is required.'
}

import {  GetSdoResponse } from '../model/responses';
import { callGQL } from '../api/callGraphql';
import { RequestHeader } from '../model/requests';
import { Logger } from '../logger';
import { getSdoIdQuery } from '../api/queries';
import { Messages } from '../errors/messages';
import { FolderId } from '../model/brands';

export const getSdoIdAdapter = async (headers: RequestHeader, folderId: FolderId) => {
    const query = getSdoIdQuery(folderId);
    try {
      const res = await callGQL<GetSdoResponse>(headers, query);
      if (res) {
        return res.folder;
      }
    } catch(err) {
      Logger.error(Messages.notCaseDeleteEndpoint  + JSON.stringify(err));
    }
  return undefined;
};

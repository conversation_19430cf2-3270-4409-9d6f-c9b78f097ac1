import Konva from 'konva';
import { RefObject } from 'react';
import {
  animationFrameScheduler,
  asyncScheduler,
  combineLatest,
  fromEvent,
  interval,
  Subject,
} from 'rxjs';
import {
  distinctUntilChanged,
  map,
  sample,
  startWith,
  takeUntil,
  tap,
  throttleTime,
} from 'rxjs/operators';
import { AudiowaveFrame } from '@common-modules/mediaDetails/models/AudiowaveFrame';
import { AudioRedactionSlice } from '@common-modules/mediaDetails/models/Transcription';

import { MapRenderOperator, RendererOutput } from './interfaces';
import { TranscriptionPropTypes } from './TranscriptionPropTypes';

export const renderer = (
  { current: container }: RefObject<HTMLDivElement>,
  mapRender: MapRenderOperator
): RendererOutput => {
  if (!container) {
    return {
      render: () => {},
      destroy: () => {},
    };
  }

  // Setup Konva
  const canvasWidth = container.offsetWidth;
  const stage = new Konva.Stage({
    container,
    width: canvasWidth,
    height: 32,
  });
  const layer = new Konva.Layer({ listening: false });
  stage.add(layer);

  // Setup Subjects
  const onAudiowaves$ = new Subject<AudiowaveFrame[]>();
  const onStartWindowMs$ = new Subject<number>();
  const onStopWindowMs$ = new Subject<number>();
  const onList$ = new Subject<TranscriptionPropTypes['list']>();
  const onTimeRange$ = new Subject<AudioRedactionSlice>();
  const onSelected$ = new Subject<TranscriptionPropTypes['selected']>();
  const onResize$ = fromEvent(window, 'resize').pipe(
    map(() => canvasWidth),
    distinctUntilChanged(),
    startWith(canvasWidth)
  );
  const raf$ = interval(undefined, animationFrameScheduler);
  const onDestroy$ = new Subject<boolean>();

  // Setup Observables
  const audiowaves$ = onAudiowaves$.pipe(distinctUntilChanged());
  const startWindowMs$ = onStartWindowMs$.pipe(
    distinctUntilChanged(),
    throttleTime(50, asyncScheduler, { leading: true, trailing: true })
  );
  const stopWindowMs$ = onStopWindowMs$.pipe(
    distinctUntilChanged(),
    throttleTime(50, asyncScheduler, { leading: true, trailing: true })
  );
  const collection$ = onList$.pipe(distinctUntilChanged());
  const timeRange$ = onTimeRange$.pipe(distinctUntilChanged());
  const selected$ = onSelected$.pipe(distinctUntilChanged());

  /**
   * Redraw when Observables change.
   */
  combineLatest(
    [
      audiowaves$,
      startWindowMs$,
      stopWindowMs$,
      collection$,
      selected$,
      timeRange$,
      onResize$.pipe(tap((width) => stage.width(width))),
    ],
    (audiowaves, startMs, stopMs, list, selected, timeRange, width) => ({
      audiowaves,
      startMs,
      stopMs,
      list,
      selected,
      timeRange,
      width,
    })
  )
    .pipe(takeUntil(onDestroy$), sample(raf$), mapRender(layer))
    .subscribe((l) => l.batchDraw());

  return {
    render: (
      {
        audiowaves,
        startWindowMs,
        stopWindowMs,
        list,
        selected,
      }: TranscriptionPropTypes,
      timeRange: AudioRedactionSlice
    ) => {
      onAudiowaves$.next(audiowaves);
      onStartWindowMs$.next(startWindowMs);
      onStopWindowMs$.next(stopWindowMs);
      onSelected$.next(selected);
      if (list) {
        onList$.next(list);
      }
      if (timeRange) {
        onTimeRange$.next(timeRange);
      }
    },
    destroy: () => {
      onDestroy$.next(true);
      stage.destroy();
    },
  };
};

import { TDOId } from '@cbsa-modules/universal';
import { createAction } from '@reduxjs/toolkit';
export const TOGGLE_TDO_IMAGE = createAction<{
  tdoId: TDOId;
}>('CBSA/TOGGLE_TDO_IMAGE');
export const TOGGLE_TDO_MEDIA = createAction<{
  tdoId: TDOId;
}>('CBSA/TOGGLE_TDO_MEDIA');

export const toggleTdoImage = (tdoId: TDOId) => TOGGLE_TDO_IMAGE({ tdoId });

export const toggleTdoMedia = (tdoId: TDOId) => TOGGLE_TDO_MEDIA({ tdoId });

export * from './changeCaseStatus';
export * from './archiveCase';
export * from './auditLog';
export * from './deleteCase';
export * from './deleteTdo';
export * from './exportCase';
export * from './fetchCase';
export * from './updateCaseName';
export * from './processCase';
export * from './updateNotification';
export * from './setCaseDetails';
export * from './setLoader';

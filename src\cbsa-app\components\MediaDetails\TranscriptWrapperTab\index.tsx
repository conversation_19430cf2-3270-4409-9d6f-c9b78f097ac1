// import { connect } from 'react-redux';
// import { createSelector } from 'reselect';

// import {
//   goToMainPage,
//   selectAudioTabState,
//   selectorVideoLoaded,
// } from '@common-modules/mediaDetails';

// import TranscriptWrapperTabView from './TranscriptWrapperTabView';

// export default connect(
//   createSelector(
//     selectAudioTabState,
//     selectorVideoLoaded,
//     (tabState, videoLoaded) => ({
//       tabState,
//       videoLoaded,
//     })
//   ),
//   {
//     onGoHomePage: goToMainPage,
//   }
// )(TranscriptWrapperTabView);

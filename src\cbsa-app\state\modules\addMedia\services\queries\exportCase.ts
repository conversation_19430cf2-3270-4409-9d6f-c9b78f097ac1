import { TDOId } from '@cbsa/state/modules/universal';

export const CREATE_EXPORT_TDO_QUERY = `
  mutation createExportTdo($input: CreateTDO!) {
    createTDO(input: $input) {
      id
      name
      details
    }
  }`;
export interface CreateExportTdoResponse {
  createTDO: {
    id: TDOId;
    name: string;
    details: Record<string, any>;
  };
}

export const START_EXPORT_JOB_QUERY = `
  mutation startExportJob($caseId: ID!, $tdoId: ID!, $clusterId: ID!, $engineId: ID!) {
    createJob(input: {
      targetId: $tdoId,
      clusterId: $clusterId,
      tasks: [
        {
          engineId: $engineId,
          executionPreferences: {
            priority: -10
          },
          payload: {
            folderTreeObjectId: $caseId,
          },
        }
      ],
    }) {
      id
      targetId
      target {
        name
      }
      status
    }
  }`;
export interface StartExportJobResponse {
  createJob: {
    id: string;
    targetId: TDOId;
    target: {
      name: string;
    };
    status: string;
  };
}

import js from '@eslint/js';
import tseslint from 'typescript-eslint';

export default [
  {
    ignores: ["**/node_modules", "**/dist"],
  },
  js.configs.recommended,
  ...tseslint.configs.recommended,
 {
    files: ["**/*.ts", "test/**/*.ts"],

    languageOptions: {
        parser: tseslint.parser,
        parserOptions: {
          projectService: true,
        },
    },

    rules: {
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-non-null-assertion": "off",

      "@typescript-eslint/no-unused-vars": ["error", {
        args: "all",
        argsIgnorePattern: "^_",
        caughtErrors: "all",
        caughtErrorsIgnorePattern: "^_",
        destructuredArrayIgnorePattern: "^_",
        varsIgnorePattern: "^_",
        ignoreRestSiblings: true,
      }],
    },
}];
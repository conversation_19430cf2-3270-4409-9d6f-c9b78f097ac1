import {
  selectClusterList,
  selectFilterParametersFromStore,
  selectPolyGroups,
  selectTdoReadonly,
} from '../selectors';
import * as Actions from '../actions';
import { loadTdoData } from '.';
import { LOCK_REFRESH } from '@helpers/constants';
import { actionSetSelectedGroups, updateTdoReadonly } from '../actions';
import { enqueueSnackbar } from '@common-modules/snackbar';
import {
  all,
  delay,
  fork,
  put,
  select,
  takeLatest,
} from 'typed-redux-saga/macro';
import { IN_CHANGE_FILTER_PARAMETERS } from '@common/web-worker';
import {
  findTdoLock,
  removeTdoLock,
  secureTdoLock,
} from '@common-modules/mediaDetails/services';
import {
  // filterClusterList,
  getActiveState,
} from '@redact-components/MediaDetailsPage/FacesWrapperTab/ClustersResultsTab/utils';

// function* hideClusteredList(filteredClusterList: ClusterItem[]) {
//   let selectedPolys = {};
//   filteredClusterList.forEach((cl) => {
//     const polys = {
//       [cl.id]: false,
//       ...cl.segments
//         .map((segment) =>
//           segment.subsegmentIds.reduce<{ [key: string]: boolean }>((ids, id) => {
//             ids[id] = false;
//             return ids;
//           }, {})
//         )
//         .reduce((ss, s) => Object.assign(ss, s), {}),
//     };
//     selectedPolys = { ...selectedPolys, ...polys };
//   });
//   yield* put(actionSetSelectedGroups({ selected: { ...selectedPolys } }));
// }

export function* fetchFilterParameters() {
  yield* takeLatest(
    [
      Actions.SET_REMOVE_FILTER_TYPE,
      Actions.SET_SHOW_FILTER_TYPE,
      Actions.SET_FILTER_TOGGLE_ALL,
    ],
    function* ({ payload }) {
      const filterParameters = yield* select(selectFilterParametersFromStore);

      yield* put(
        IN_CHANGE_FILTER_PARAMETERS({
          filterParameters,
          preventUndo: payload.preventUndo,
        })
      );
    }
  );
}

export function* setSelectedGroupsByGroupId() {
  yield* takeLatest(
    Actions.SET_SELECTED_GROUPS_BY_GROUP_ID,
    function* ({ payload }) {
      const clusterList = yield* select(selectClusterList);
      const selectedPolyGroups = yield* select(selectPolyGroups);

      let selectedClusterItemGroup;
      for (const cluster of clusterList) {
        selectedClusterItemGroup = cluster.groups.find(
          (group) => group.id === payload
        );
        if (selectedClusterItemGroup) {
          break;
        }
      }

      // CTODO might want to select all segment in cluster

      let selected = {};
      if (selectedClusterItemGroup) {
        const isActive =
          getActiveState(
            selectedClusterItemGroup.segments,
            selectedPolyGroups
          ) !== 1;
        selected = {
          // [selectedClusterItemGroup.id]: isActive, // we don't need to include cluster/group ids
          ...selectedClusterItemGroup.segments
            .map((segment) =>
              segment.subsegmentIds.reduce<{ [key: string]: boolean }>(
                (ids, id) => {
                  ids[id] = isActive;
                  return ids;
                },
                {}
              )
            )
            .reduce((ss, s) => Object.assign(ss, s), {}),
        };
      }
      yield* put(actionSetSelectedGroups({ selected }));
    }
  );
}

export function* viewSaga() {
  yield* all([
    // fork(fetchFilterParametersWithRemoveFilterType),
    fork(fetchFilterParameters),
    fork(setSelectedGroupsByGroupId),
  ]);
}

export function* watchTdoLock() {
  yield* takeLatest(Actions.CHECK_TDO_LOCK, function* ({ payload }) {
    yield* put(findTdoLock(payload));
  });
}

export function* watchTdoLockFound() {
  yield* takeLatest(Actions.TDO_LOCK_FOUND_SUCCESS, function* ({ payload }) {
    const { tdoLock, newLockInfo } = payload;
    const { isLocked } = yield* select(selectTdoReadonly);

    const isOwnedByMe = newLockInfo.userId === tdoLock.userId;
    if (isOwnedByMe) {
      if (isLocked) {
        yield* fork(loadTdoData);
      }

      yield* put(updateTdoReadonly(false));
      yield* put(secureTdoLock({ ...newLockInfo, id: tdoLock.id }));
      yield* put(updateTdoReadonly(false));
      yield* put(
        secureTdoLock({
          ...newLockInfo,
          id: tdoLock.id,
          lastAccessed: newLockInfo.lastAccessed,
        })
      );
    } else {
      yield* put(updateTdoReadonly(true, tdoLock.name));
    }
  });
}

export function* watchTdoLockNotFound() {
  yield* takeLatest(Actions.TDO_LOCK_FOUND_FAILURE, function* ({ payload }) {
    const { error, newLockInfo } = payload;

    if (error) {
      yield* put(
        enqueueSnackbar({
          message: error.message,
          variant: 'error',
        })
      );
      yield* delay(LOCK_REFRESH / 12);
      yield* put(findTdoLock(newLockInfo));
    } else {
      const { isLocked } = yield* select(selectTdoReadonly);
      if (isLocked) {
        yield* fork(loadTdoData);
      }
      yield* put(updateTdoReadonly(false));
      yield* put(secureTdoLock(newLockInfo));
    }
  });
}

export function* watchRemoveTdoLock() {
  yield* takeLatest(Actions.REMOVE_TDO_LOCK, function* ({ payload }) {
    yield* put(removeTdoLock(payload));
  });
}

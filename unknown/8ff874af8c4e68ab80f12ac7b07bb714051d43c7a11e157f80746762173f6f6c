import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles((theme) => ({
  details: {
    position: 'absolute',
    background: 'white',
    borderRadius: '3px',
    boxShadow: theme.shadows[4],
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    width: '70px',
    top: '22px',
    right: '20px',
    padding: '5px 0',

    '& .Option': {
      color: theme.palette.primary.main,
      cursor: 'pointer',
      fontSize: '12px',
      padding: '2px 10px',

      '&:hover': {
        background: '#0000000',
      },
    },
  },
}));

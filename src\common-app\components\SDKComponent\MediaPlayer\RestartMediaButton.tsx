import classNames from 'classnames';

const RestartMediaButton = ({ actions, className }: Props) => {
  const handleClick = () => {
    if (actions) {
      actions.seek(0);
    }
  };

  return (
    <button
      data-testid="restart-media-button"
      className={classNames(
        className,
        'video-react-control',
        'video-react-button',
        'video-react-icon',
        'video-react-icon-skip-previous'
      )}
      onClick={handleClick}
      type="button"
    />
  );
};

interface Props {
  actions?: { seek: (time: number) => void };
  className?: string;
  order?: number;
}

export default RestartMediaButton;

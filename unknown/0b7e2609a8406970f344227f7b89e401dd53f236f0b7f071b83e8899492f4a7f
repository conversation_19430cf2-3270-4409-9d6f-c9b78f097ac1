import makeStyles from '@mui/styles/makeStyles';

const topOffset = '115px'; // appBarHeight + topBarHeight

export const useStyles = makeStyles((theme) => ({
  sidebar: {
    width: '300px',
    overflow: 'scroll',
    background: theme.palette.grey[100],
    height: `calc(100vh - ${topOffset})`,
    boxShadow: theme.shadows[4],

    '& hr': {
      borderTop: '0.5px solid #70767B',
      margin: '5px 0',
    },
  },
}));

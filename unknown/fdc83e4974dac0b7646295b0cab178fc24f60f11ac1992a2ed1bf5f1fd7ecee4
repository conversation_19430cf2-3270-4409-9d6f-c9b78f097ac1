import 'jest';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { healthcheck } from '../../src/controllers/healthcheck';

describe('healthcheck', () => {
  const { res } = getMockRes();
  const time = new Date();
  jest.spyOn(global, 'Date').mockReturnValue(time);

  it('retrieves redacted media and audit log files w/ tdoId', async () => {
    const req = getMockReq();
    await healthcheck(req, res);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
    expect(res.json).toHaveBeenCalledWith({ status: 'Alive', time });
  });
});

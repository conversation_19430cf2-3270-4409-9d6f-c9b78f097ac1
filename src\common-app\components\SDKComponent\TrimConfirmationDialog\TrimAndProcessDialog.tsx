import * as React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import FaceIcon from '@mui/icons-material/Face';

class TrimAndProcessDialog extends React.Component<
  TrimAndProcessDialogProps,
  TrimAndProcessDialogState
> {
  constructor(props: TrimAndProcessDialogProps) {
    super(props);
    this.state = {
      runFaceDetection: props.faceDetectionDone,
      runAudioTranscription: props.audioTranscriptionDone,
    };
  }

  componentDidUpdate(prevProps: TrimAndProcessDialogProps) {
    if (
      this.props.audioTranscriptionDone !== prevProps.audioTranscriptionDone
    ) {
      this.setState({
        runAudioTranscription: this.props.audioTranscriptionDone,
      });
    }

    if (this.props.faceDetectionDone !== prevProps.faceDetectionDone) {
      this.setState({ runFaceDetection: this.props.faceDetectionDone });
    }
  }

  handleRunTranscription = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({ runAudioTranscription: event.target.checked });
  };

  handleRunFaceDetection = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({ runFaceDetection: event.target.checked });
  };

  handleClickProcess = () => {
    const { runAudioTranscription, runFaceDetection } = this.state;
    const { handleTrimAndProcess, faceDetectionDone, audioTranscriptionDone } =
      this.props;
    handleTrimAndProcess(
      runFaceDetection && !faceDetectionDone,
      runAudioTranscription && !audioTranscriptionDone
    );
  };

  render() {
    const {
      showConfirmationModal,
      handleClose,
      handleTrim,
      faceDetectionDone,
      audioTranscriptionDone,
    } = this.props;
    const { runAudioTranscription, runFaceDetection } = this.state;
    return (
      <Dialog
        open={showConfirmationModal}
        onClose={handleClose}
        style={{ zIndex: 19999 }}
        data-testid="trim-process-dialog"
      >
        <DialogTitle data-testid="trim-process-dialog-title">
          {' '}
          Trim and Process{' '}
        </DialogTitle>
        <DialogContent
          style={{ width: 530 }}
          data-testid="trim-process-dialog-content"
        >
          <DialogContentText data-testid="trim-process-dialog-content-text">
            Select which cognition engines to run
          </DialogContentText>
          <div>
            <div>
              <Checkbox
                checked={runFaceDetection}
                onChange={this.handleRunFaceDetection}
                disabled={faceDetectionDone}
                color="primary"
              />
              <FaceIcon />
              <span style={{ paddingLeft: 5 }}>Run Head Detection</span>
            </div>
            <div>
              <Checkbox
                checked={runAudioTranscription}
                onChange={this.handleRunTranscription}
                disabled={audioTranscriptionDone}
                color="primary"
              />
              <span
                className="icon-transcription"
                style={{
                  paddingLeft: 2,
                  fontSize: '1.4rem',
                  verticalAlign: 'sub',
                }}
              />
              <span style={{ paddingLeft: 5 }}>Run Transcription</span>
            </div>
          </div>
        </DialogContent>
        <DialogActions data-testid="trim-process-dialog-action">
          <Button
            onClick={handleTrim}
            variant="text"
            data-testid="trim-process-dialog-button-trim"
          >
            {' '}
            skip processing{' '}
          </Button>
          <Button
            onClick={this.handleClickProcess}
            variant="contained"
            color="primary"
            style={{ backgroundColor: '#2196F3' }}
            data-testid="trim-process-dialog-button-process"
          >
            {' '}
            proceed{' '}
          </Button>
        </DialogActions>
      </Dialog>
    );
  }
}

export default TrimAndProcessDialog;

export interface TrimAndProcessDialogProps {
  readonly showConfirmationModal: boolean;
  readonly handleClose: () => void;
  readonly handleTrim: () => void;
  readonly handleTrimAndProcess: (
    runFaceDetection: boolean,
    runAudioTranscription: boolean
  ) => void;
  readonly audioTranscriptionDone: boolean;
  readonly faceDetectionDone: boolean;
}

export interface TrimAndProcessDialogState {
  runFaceDetection: boolean;
  runAudioTranscription: boolean;
}

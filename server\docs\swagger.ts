import { OpenAPIV3 } from 'openapi-types';
import Config from '../apiConfig.json';

const buildUrl = (url: string): OpenAPIV3.ServerObject => ({ url: `${url}` });
const getUrls = () => {
  const urls: OpenAPIV3.ServerObject[] = [];
  if (Config?.nodeEnv === 'localhost') {
    urls.push(buildUrl('http://localhost:9001'));
  } else {
    urls.push(buildUrl(`https://redact.${Config.publicDnsZoneName}`));
  }
  return urls;
};

export const swaggerDocument: OpenAPIV3.Document = {
  openapi: '3.0.1',
  info: {
    version: '1.1.0',
    title: 'Redact API',
    description:
      'Provides backend api services for external users to ingest media files to redact application and retrieve the related media assets.',
  },
  servers: getUrls(),
  security: [
    {
      bearerAuth: [],
    },
  ],
  paths: {
    '/api/v1/': {
      get: {
        summary: 'Home',
        responses: {
          200: {
            description: 'Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: {
                      type: 'string',
                      description: 'Redact API!',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/ping': {
      get: {
        summary: 'Health Check',
        responses: {
          200: {
            description: 'Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: {
                      type: 'string',
                      description: 'Alive',
                    },
                    time: {
                      type: 'string',
                      description: 'Current time.',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/isAuthorized': {
      get: {
        summary: 'To check token valid and authorized to access redact api.',
        responses: {
          200: {
            description: 'Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: {
                      type: 'string',
                      description: 'Authorized',
                    },
                    tokenType: {
                      type: 'string',
                      description: 'Session Token',
                    },
                  },
                },
              },
            },
          },
          401: {
            description: 'Unauthorized',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: 'Not authorized. Invalid Token provided.',
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/createFolder': {
      post: {
        summary: 'To create a new folder.',
        description: 'Not passing a parentFolderId will create a root folder.',
        // parameters: [
        //   {
        //     name: 'name',
        //     in: 'body',
        //     description: 'Name of the folder',
        //     required: true,
        //     schema: {
        //       type: 'string',
        //     }
        //   },
        //   {
        //     name: 'description',
        //     in: 'body',
        //     description: 'Description of the folder.',
        //     required: false,
        //     schema: {
        //       type: 'string',
        //     }
        //   },
        //   {
        //     name: 'parentFolderId',
        //     in: 'body',
        //     description: 'parentFolderId.',
        //     required: false,
        //     schema: {
        //       type: 'string',
        //     }
        //   }
        // ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/createFolder',
              },
            },
          },
        },
        responses: {
          201: {
            description: 'Created Successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    newFolderId: {
                      type: 'string',
                      description: 'New folder id.',
                    },
                    name: {
                      type: 'string',
                      description: 'Name of the folder.',
                    },
                  },
                },
              },
            },
          },
          400: {
            description: 'Bad Request',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      description: 'Error message.',
                    },
                  },
                },
              },
            },
          },
          401: {
            description: 'Unauthorized',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: 'Not authorized. Invalid Token provided.',
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/createCase': {
      post: {
        summary: 'To create a new case.',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/createFolder',
              },
            },
          },
        },
        responses: {
          201: {
            description: 'Created Successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    name: {
                      type: 'string',
                      description: 'Name of the case.',
                    },
                    caseId: {
                      type: 'string',
                      description: 'New case id.',
                    },
                  },
                },
              },
            },
          },
          400: {
            description: 'Bad Request',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      description: 'Error message.',
                    },
                  },
                },
              },
            },
          },
          401: {
            description: 'Unauthorized',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: 'Not authorized. Invalid Token provided.',
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/ingestMedia': {
      post: {
        summary: 'To ingest one or more media files to case.',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ingestMedia',
              },
            },
          },
        },
        responses: {
          201: {
            description: 'Created Successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    caseId: {
                      type: 'string',
                      description: 'Case id.',
                    },
                    ingestedJobs: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: {
                            type: 'string',
                            description: 'Ingested job id.',
                          },
                          targetId: {
                            type: 'string',
                            description: 'Target id.',
                          },
                          status: {
                            type: 'string',
                            description: 'Ingested job status.',
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          401: {
            description: 'Unauthorized',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: 'Not authorized. Invalid Token provided.',
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/fileStatus/{tdoId}': {
      get: {
        summary: 'To check the status of ingested file by tdoId.',
        parameters: [
          {
            name: 'tdoId',
            in: 'path',
            description: 'tdoId of ingested file.',
            required: true,
            schema: {
              type: 'string',
            },
          },
        ],
        responses: {
          200: {
            description: 'Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      description: 'tdoId of ingested file.',
                    },
                    name: {
                      type: 'string',
                      description: 'Name of the file.',
                    },
                    status: {
                      type: 'string',
                      description: 'Status of the file.',
                    },
                  },
                },
              },
            },
          },
          401: {
            description: 'Unauthorized',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: 'Not authorized. Invalid Token provided.',
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/caseFileList/{caseId}': {
      get: {
        summary: 'To retrieve the list of files by caseId.',
        parameters: [
          {
            name: 'caseId',
            in: 'path',
            description: 'caseId.',
            required: true,
            schema: {
              type: 'string',
            },
          },
          {
            name: 'limit',
            in: 'query',
            description: 'limit of records needed to be retrieved.',
            required: false,
            schema: {
              type: 'integer',
              minimum: 0,
              maximum: 9999,
            },
          },
          {
            name: 'offset',
            in: 'query',
            description: 'next page number.',
            required: false,
            schema: {
              type: 'integer',
              minimum: 0,
            },
          },
        ],
        responses: {
          200: {
            description: 'Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    case: {
                      type: 'object',
                      properties: {
                        name: {
                          type: 'string',
                          description: 'Name of the case.',
                        },
                        files: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              id: {
                                type: 'string',
                                description: 'tdoId of file.',
                              },
                              name: {
                                type: 'string',
                                description: 'Name of the file.',
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: 'Bad Request',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      description: 'Error message.',
                    },
                  },
                },
              },
            },
          },
          401: {
            description: 'Unauthorized',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: 'Not authorized. Invalid Token provided.',
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/redactedMedia/{tdoId}': {
      get: {
        summary: 'To retrieve redacted media and audit-log files by tdoId.',
        parameters: [
          {
            name: 'tdoId',
            in: 'path',
            description: 'id of file',
            required: true,
            schema: {
              type: 'string',
            },
          },
        ],
        responses: {
          200: {
            description: 'Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    redactedMediaAssets: {
                      type: 'object',
                      properties: {
                        records: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              type: {
                                type: 'string',
                                description: 'Type of asset.',
                              },
                              signedUri: {
                                type: 'string',
                                description: 'Signed uri of the asset.',
                              },
                              createdDateTime: {
                                type: 'string',
                                description: 'Created date time of the asset.',
                              },
                            },
                          },
                        },
                      },
                    },
                    auditLogAssets: {
                      type: 'object',
                      properties: {
                        records: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              type: {
                                type: 'string',
                                description: 'Type of asset.',
                              },
                              signedUri: {
                                type: 'string',
                                description: 'Signed uri of the asset.',
                              },
                              createdDateTime: {
                                type: 'string',
                                description: 'Created date time of the asset.',
                              },
                            },
                          },
                        },
                      },
                    },
                    redactedTranscriptAssets: {
                      type: 'object',
                      properties: {
                        records: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              type: {
                                type: 'string',
                                description: 'Type of asset.',
                              },
                              signedUri: {
                                type: 'string',
                                description: 'Signed uri of the asset.',
                              },
                              createdDateTime: {
                                type: 'string',
                                description: 'Created date time of the asset.',
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          400: {
            description: 'Bad Request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/mediaBadRequest',
                },
              },
            },
          },
          401: {
            description: 'Unauthorized',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: 'Not authorized. Invalid Token provided.',
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/mediaFileDetails/{tdoId}': {
      get: {
        summary: 'To retrieve all assets of ingested media by tdoId .',
        parameters: [
          {
            name: 'tdoId',
            in: 'path',
            description: 'id of file.',
            required: true,
            schema: {
              type: 'string',
            },
          },
        ],
        responses: {
          200: {
            description: 'Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    reviewStatus: {
                      type: 'string',
                      description: 'Review status of the file.',
                    },
                    isRedacted: {
                      type: 'boolean',
                      description: 'Is redacted.',
                    },
                    thumbnailUrl: {
                      type: 'string',
                      description: 'Thumbnail url of the file.',
                    },
                    redactedMediaFile: {
                      type: 'string',
                      description: 'Redacted media file url.',
                    },
                    auditLog: {
                      type: 'string',
                      description: 'Audit log url.',
                    },
                    redactedTranscript: {
                      type: 'string',
                      description: 'Url to the redacted transcript.',
                    },
                  },
                },
              },
            },
          },
          400: {
            description: 'Bad Request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/mediaBadRequest',
                },
              },
            },
          },
          401: {
            description: 'Unauthorized',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: 'Not authorized. Invalid Token provided.',
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/deleteFolder/{folderId}': {
      delete: {
        summary: 'To delete a folder by id.',
        parameters: [
          {
            name: 'folderId',
            in: 'path',
            description: 'folderId',
            required: true,
            schema: {
              type: 'string',
            },
          },
        ],
        responses: {
          204: {
            description: 'Delete Folder Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    folderId: {
                      type: 'string',
                      description: 'Deleted folder id.',
                    },
                    message: {
                      type: 'string',
                      description: 'Deleted folder successfully.',
                    },
                  },
                },
              },
            },
          },
          400: {
            description: 'Bad Request',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      description:
                        'The folder was not found. It does not exist or your organization does not have access to it.',
                    },
                  },
                },
              },
            },
          },
          401: {
            description: 'Unauthorized',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: 'Not authorized. Invalid Token provided.',
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/deleteCase/{caseId}': {
      delete: {
        summary: 'To delete a case by id.',
        parameters: [
          {
            name: 'caseId',
            in: 'path',
            description: 'caseId',
            required: true,
            schema: {
              type: 'string',
            },
          },
        ],
        responses: {
          200: {
            description: 'Delete Case Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    caseId: {
                      type: 'string',
                      description: 'Deleted case id.',
                    },
                    message: {
                      type: 'string',
                      description: 'Deleted case successfully.',
                    },
                  },
                },
              },
            },
          },
          400: {
            description: 'Bad Request',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      description: 'Invalid caseId provided.',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },

    '/api/v1/deleteFile/{tdoId}': {
      delete: {
        summary: 'To delete a file by id.',
        parameters: [
          {
            name: 'tdoId',
            in: 'path',
            description: 'tdoId of file needs to be deleted.',
            required: true,
            schema: {
              type: 'string',
            },
          },
        ],
        responses: {
          204: {
            description: 'Delete File Success',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    tdoId: {
                      type: 'string',
                      description: 'Deleted file id.',
                    },
                    message: {
                      type: 'string',
                      description: 'Deleted file successfully.',
                    },
                  },
                },
              },
            },
          },
          400: {
            description: 'Bad Request',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                      description: 'Error message',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
    },
    schemas: {
      createFolder: {
        type: 'object',
        required: ['name'],
        properties: {
          name: {
            type: 'string',
            description: 'Name of the folder or case name.',
          },
          description: {
            type: 'string',
            description: 'Description of the folder or case.',
          },
          parentFolderId: {
            type: 'string',
            description: 'Parent folderId.',
          },
        },
      },

      ingestMedia: {
        type: 'object',
        required: ['urls'],
        properties: {
          urls: {
            type: 'array',
            items: {
              type: 'string',
              description: 'Url of the file needed to be ingested.',
            },
          },
          runDetection: {
            type: 'boolean',
            description: 'Deprecated: Enable head detection.',
          },
          runHeadDetection: {
            type: 'boolean',
            description: 'Enable head detection.',
          },
          runPersonDetection: {
            type: 'boolean',
            description: 'Enable person detection.',
          },
          runTranscription: {
            type: 'boolean',
            description: 'Enable transcription.',
          },
          existingCaseId: {
            type: 'string',
            description: 'Existing case Id.',
          },
          createNewCase: {
            $ref: '#/components/schemas/createFolder',
          },
          // externalIds: {
          //   type: 'object',
          //   properties: {
          //     govQARequestId: {
          //       type: 'string',
          //       description: 'GovQA requestId.'
          //     },
          //     foiaXpressRequestId: {
          //       type: 'string',
          //       description: 'GovQA requestId.'
          //     },
          //     casepointRequestId: {
          //       type: 'string',
          //       description: 'GovQA requestId.'
          //     },
          //     nuixRequestId: {
          //       type: 'string',
          //       description: 'GovQA requestId.'
          //     },
          //     exterroRequestId: {
          //       type: 'string',
          //       description: 'GovQA requestId.'
          //     },
          //   }
          // }
        },
      },
      mediaBadRequest: {
        type: 'object',
        properties: {
          response: {
            type: 'object',
            properties: {
              errors: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    message: {
                      type: 'string',
                    },
                    name: {
                      type: 'string',
                    },
                    time_thrown: {
                      type: 'string',
                      format: 'date-time',
                    },
                    data: {
                      type: 'object',
                      properties: {
                        objectId: {
                          type: 'string',
                        },
                        objectType: {
                          type: 'string',
                        },
                        errorId: {
                          type: 'string',
                        },
                        requestId: {
                          type: 'string',
                        },
                      },
                    },
                    path: {
                      type: 'array',
                      items: {
                        type: 'string',
                      },
                    },
                    locations: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          line: {
                            type: 'integer',
                          },
                          column: {
                            type: 'integer',
                          },
                        },
                      },
                    },
                  },
                },
              },
              data: {
                type: 'object',
                properties: {
                  temporalDataObject: {
                    type: 'object',
                  },
                  errors: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        message: {
                          type: 'string',
                        },
                        name: {
                          type: 'string',
                        },
                        time_thrown: {
                          type: 'string',
                          format: 'date-time',
                        },
                        data: {
                          type: 'object',
                          properties: {
                            objectId: {
                              type: 'string',
                            },
                            objectType: {
                              type: 'string',
                            },
                            errorId: {
                              type: 'string',
                            },
                            requestId: {
                              type: 'string',
                            },
                          },
                        },
                        path: {
                          type: 'array',
                          items: {
                            type: 'string',
                          },
                        },
                        locations: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              line: {
                                type: 'integer',
                              },
                              column: {
                                type: 'integer',
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              status: {
                type: 'integer',
              },
              headers: {
                type: 'object',
              },
            },
          },
          request: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
              },
            },
          },
          expose: {
            type: 'boolean',
          },
          statusCode: {
            type: 'integer',
          },
          status: {
            type: 'integer',
          },
        },
      },
    },
  },
};

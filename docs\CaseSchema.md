### Steps to create comments schema using core-graphql queries

1. Create new data registry 
```
mutation createDataRegistry {
  createDataRegistry(input: {
    id: "6d50880d-1f24-4efd-96da-9189082ee93b"
    name: "redact-media-comment"
    description: "redact media comment"
    source: "veritone-1.datasets"
  }) {
    id
  }
}
```

2. Create Schema Draft
```
# Note: Use the `dataRegistryId` value from the `createDataRegistry` mutation.
mutation createSchemaDraft {
  upsertSchemaDraft(
    input: {
      dataRegistryId: "6d50880d-1f24-4efd-96da-9189082ee93b"
      majorVersion: 1
      schema: {
        type: "object"
        title: "redact-case-notification"
        required: [
          "caseName"
          "folderTreeObjectId"
          "createdDateTime"
          "modifiedDateTime"
          "status"
        ]
        properties: {
          status: { type: "string" }
          archive: { type: "string" }
          caseName: { type: "string" }
          priority: { type: "integer" }
          exportTdoId: { type: "string" }
          processingTime: { type: "integer" }
          createDateTime: { type: "dateTime" }
          modifiedDateTime: { type: "dateTime" }
          folderTreeObjectId: { type: "string" }
        }
        description: "redact case"
      }
    }
  ) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}
```

3. Publish Schema Draft

```
# Note: Pass in the Schema ID for the `id` value
mutation publishSchemaDraft($id: ID!) {
  updateSchemaState(input: {
    id: $id
    status: published
  }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```


import {
  useRef,
  RefObject,
  useLayoutEffect,
  useEffect,
  useState,
  SyntheticEvent,
} from 'react';
import * as React from 'react';
import Konva from 'konva';
import { find } from 'lodash';
import * as styles from '../styles.scss';
import { aToB } from '../../Playhead/renderer';
import { renderTimeline } from './renderTimeline';
import TextField from '@mui/material/TextField';
import { UDRGroupRow } from './UDRGroupRowPropTypes';
import TrimSlider from '../DurationAdjustment/TrimSlider';
import { UDRsPolyAssetGroupSeriesItem } from '@common-modules/mediaDetails/models';

export interface UDRResizeContainer {
  udrId: string;
  startPx: number;
  stopPx: number;
  startMinPx: number;
  stopMaxPx: number;
  isSelected: boolean | undefined;
  sprayPaint?: {
    startUDRId: string;
    endUDRId: string;
    idToUdrMap: { [id: string]: UDRsPolyAssetGroupSeriesItem };
  };
}

export interface UDRResizeContainerMap {
  [udrId: string]: UDRResizeContainer;
}

function getUDRResizeContainerMap(
  udrGroupSeries: readonly UDRsPolyAssetGroupSeriesItem[],
  startMs: number,
  stopMs: number,
  selectedPolys: UDRGroupRow['selectedPolys'],
  clientWidth: number,
  opts: {
    selectedUDRId?: string;
    edgeTypeBeingDragged?: string;
  }
): UDRResizeContainerMap {
  const isSprayPaintUDR = ({
    object: { overlayObjectType },
  }: UDRsPolyAssetGroupSeriesItem) => overlayObjectType === 'spray_paint_udr';

  const { selectedUDRId, edgeTypeBeingDragged } = opts;

  const newMillisecond2Point = aToB(startMs, stopMs, 0, clientWidth);

  let map: UDRResizeContainerMap = {};

  for (let i = 0; i < udrGroupSeries.length; i++) {
    const prevUDR = udrGroupSeries[i - 1];
    // currUDR is guaranteed to exist because of the for loop condition
    let currUDR = udrGroupSeries[i]!;
    let nextUDR = udrGroupSeries[i + 1];

    const startMinPx = prevUDR
      ? newMillisecond2Point(prevUDR.stopTimeMs - startMs)
      : newMillisecond2Point(startMs);

    const startPx = newMillisecond2Point(currUDR.startTimeMs - startMs);

    const spayPaintUDRSeriesFirstElement = currUDR;

    const sprayPaintSeriesIdsMap = { [currUDR.id]: currUDR };

    while (
      !!currUDR &&
      isSprayPaintUDR(currUDR) &&
      !!nextUDR &&
      isSprayPaintUDR(nextUDR) &&
      currUDR.stopTimeMs === nextUDR.startTimeMs &&
      (edgeTypeBeingDragged !== 'end' || currUDR.id !== selectedUDRId) &&
      (edgeTypeBeingDragged !== 'start' || nextUDR.id !== selectedUDRId)
    ) {
      i++;
      currUDR = nextUDR;
      nextUDR = udrGroupSeries[i + 1];

      sprayPaintSeriesIdsMap[currUDR.id] = currUDR;
    }

    const stopPx = newMillisecond2Point(currUDR.stopTimeMs - startMs);

    const stopMaxPx = nextUDR
      ? newMillisecond2Point(nextUDR.startTimeMs - startMs)
      : newMillisecond2Point(stopMs - startMs);

    const isFirstUDR = i === 0;

    map = {
      ...map,
      [spayPaintUDRSeriesFirstElement.id]: {
        udrId: spayPaintUDRSeriesFirstElement.id,
        startPx,
        stopPx,
        startMinPx: isFirstUDR ? 0 : startMinPx,
        stopMaxPx,
        isSelected: selectedPolys[currUDR.id],
        sprayPaint:
          currUDR.object.overlayObjectType === 'spray_paint_udr'
            ? {
                startUDRId: spayPaintUDRSeriesFirstElement.id,
                endUDRId: currUDR.id,
                idToUdrMap: sprayPaintSeriesIdsMap,
              }
            : undefined,
      },
    };
  }

  return map;
}

// NOTE: calling stopPropagation or stopImmediatePropagation, is not working
// for Konva events. When user clicks on the udr block we don't won't
// onUDRContainerClick to fire, so using this STOP_KONVA_EVENT_FLAG as a work around
let STOP_KONVA_EVENT_FLAG = false;

const UDRGroupRowComponent = (props: UDRGroupRow) => {
  const {
    UDRId,
    active,
    color,
    progress,
    udrGroup,
    selectedPolys,
    selectedUDRId,
    startMs,
    stopMs,
    onUDRSelect,
    onChangeUDRGroupLabel,
    onChangeUDR,
    onChangeUDRSubmit,
    onGroupLabelClick,
    onUDRContainerClick,
  } = props;

  const [layer, setLayer] = useState<Konva.Layer | undefined>();
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [udrGroupLabel, setUdrGroupLabel] = useState(udrGroup.userLabel);
  const [showUdrGroupLabelTextBox, setShowUdrGroupLabelTextBox] =
    useState(false);

  const [edgeTypeBeingDragged, setEdgeTypeBeingDragged] = useState<
    string | undefined
  >();

  const [udrGroupSeries, setUdrGroupSeries] = useState(udrGroup.series);

  const udrResizeContainerMap = getUDRResizeContainerMap(
    udrGroupSeries,
    startMs,
    stopMs,
    selectedPolys,
    containerWidth,
    {
      edgeTypeBeingDragged,
      selectedUDRId,
    }
  );

  useEffect(() => {
    setUdrGroupSeries(udrGroup.series);
  }, [udrGroup.series]);

  useEffect(() => {
    setUdrGroupLabel(udrGroup.userLabel);
  }, [udrGroup.userLabel]);

  const ref: RefObject<HTMLDivElement> = useRef<HTMLDivElement>(null);

  const inputRef: RefObject<HTMLInputElement> = useRef<HTMLInputElement>(null);

  function getStartStopTimeMsMapFromUDRChange(
    payload: { startTime: number; endTime: number; id: string },
    scaleParams: { clientWidth: number; startMs: number; stopMs: number }
  ): { startTimeMs: number; stopTimeMs: number } {
    const udrPxStart = payload.startTime;
    const udrPxEnd = payload.endTime;
    const point2Millisecond = aToB(
      0,
      scaleParams.clientWidth,
      scaleParams.startMs,
      scaleParams.stopMs
    );

    const startTimeMs = point2Millisecond(udrPxStart);
    const stopTimeMs = point2Millisecond(udrPxEnd);

    return {
      startTimeMs,
      stopTimeMs,
    };
  }

  const onChange = (payload: {
    startTime: number;
    endTime: number;
    id: string;
    type: 'start' | 'end';
  }) => {
    const container = ref.current;

    if (!container) {
      return;
    }

    let udr = find(udrGroupSeries, { id: payload.id });

    if (!udr) {
      return;
    }

    const { startTimeMs, stopTimeMs } = getStartStopTimeMsMapFromUDRChange(
      payload,
      {
        clientWidth: container.clientWidth,
        startMs,
        stopMs,
      }
    );

    if (udr.object.overlayObjectType !== 'spray_paint_udr') {
      onChangeUDR(udrGroup.groupId, payload.id, payload.type, {
        ...udr,
        startTimeMs,
        stopTimeMs,
      });

      return;
    }

    const udrResizeContainer = find(
      udrResizeContainerMap,
      ({ sprayPaint }) => !!sprayPaint && !!sprayPaint.idToUdrMap[payload.id]
    );

    const sprayPaint = udrResizeContainer?.sprayPaint;

    let edgeUDRId = payload.id;

    if (sprayPaint) {
      edgeUDRId =
        payload.type === 'start' ? sprayPaint.startUDRId : sprayPaint.endUDRId;

      udr = sprayPaint.idToUdrMap[edgeUDRId];
    }

    if (!udr) {
      return;
    }

    setEdgeTypeBeingDragged(payload.type);

    onChangeUDR(udrGroup.groupId, edgeUDRId, payload.type, {
      ...udr,
      startTimeMs,
      stopTimeMs,
    });
  };

  const onChangeSubmit = (payload: {
    startTime: number;
    endTime: number;
    id: string;
    type: 'start' | 'end';
  }) => {
    const container = ref.current;

    if (!container) {
      return;
    }

    let udr = find(udrGroupSeries, { id: payload.id });

    if (!udr) {
      return;
    }

    const { startTimeMs, stopTimeMs } = getStartStopTimeMsMapFromUDRChange(
      payload,
      {
        clientWidth: container.clientWidth,
        startMs,
        stopMs,
      }
    );

    if (udr.object.overlayObjectType !== 'spray_paint_udr') {
      onChangeUDRSubmit(udrGroup.groupId, payload.id, payload.type, {
        ...udr,
        startTimeMs,
        stopTimeMs,
      });

      return;
    }

    const udrResizeContainer = find(
      udrResizeContainerMap,
      ({ sprayPaint }) => !!sprayPaint && !!sprayPaint.idToUdrMap[payload.id]
    );

    const sprayPaint = udrResizeContainer?.sprayPaint;

    let edgeUDRId = payload.id;

    if (sprayPaint) {
      edgeUDRId =
        payload.type === 'start' ? sprayPaint.startUDRId : sprayPaint.endUDRId;

      udr = sprayPaint.idToUdrMap[edgeUDRId];
    }

    if (!udr) {
      return;
    }

    setEdgeTypeBeingDragged(undefined);

    onChangeUDRSubmit(udrGroup.groupId, edgeUDRId, payload.type, {
      ...udr,
      startTimeMs,
      stopTimeMs,
    });
  };

  function udrSelectHandle(udrId: string) {
    STOP_KONVA_EVENT_FLAG = true;

    let localUdr = udrGroupSeries.find((udr) => udr.id === udrId);

    if (!localUdr) {
      return;
    }

    if (localUdr.object.overlayObjectType !== 'spray_paint_udr') {
      onUDRSelect(udrGroup.groupId, udrId, localUdr);
      return;
    }

    const udrResizeContainer = find(
      udrResizeContainerMap,
      ({ sprayPaint }) => !!sprayPaint && !!sprayPaint.idToUdrMap[udrId]
    );

    const startUDRId = udrResizeContainer?.sprayPaint?.startUDRId;

    if (!startUDRId) {
      return;
    }

    localUdr = udrResizeContainer?.sprayPaint?.idToUdrMap[startUDRId];

    if (!startUDRId || !localUdr) {
      return;
    }

    onUDRSelect(udrGroup.groupId, startUDRId, localUdr);
  }

  useLayoutEffect(() => {
    const container = ref.current;
    if (!container) {
      return;
    }

    const width = container.clientWidth;

    setContainerWidth(width);
  }, []);

  useLayoutEffect(() => {
    const container = ref.current;
    if (!container) {
      return;
    }

    const width = container.clientWidth;

    const stage: Konva.Stage = new Konva.Stage({
      id: UDRId,
      container,
      width,
      height: 32,
    });

    const tempLayer = new Konva.Layer();
    setLayer(tempLayer);
    stage.add(tempLayer);

    return () => {
      stage.destroy();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [containerWidth]);

  useEffect(() => {
    function resizeHandler() {
      const container = ref.current;

      if (!container) {
        return;
      }

      const newWidth = container.clientWidth;

      setContainerWidth(newWidth);
    }

    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
    };
  }, []);

  useEffect(() => {
    if (!layer) {
      return;
    }

    if (layer) {
      const { removeEventListeners } = renderTimeline(
        layer,
        udrResizeContainerMap,
        { onClick: udrSelectHandle }
      );

      return () => {
        removeEventListeners();
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [udrResizeContainerMap, layer]);

  const showLabelTextBox = () => {
    setShowUdrGroupLabelTextBox(true);
  };

  function submitLabelChange({
    currentTarget: { value },
  }: React.KeyboardEvent<HTMLInputElement>) {
    onChangeUDRGroupLabel(udrGroup.groupId, value);
    setUdrGroupLabel(value);
    setShowUdrGroupLabelTextBox(false);
  }

  function handleKeyUp(event: React.KeyboardEvent<HTMLInputElement>) {
    if (event.key === 'Enter') {
      submitLabelChange(event);
    }

    if (event.key === 'Escape' && inputRef?.current) {
      inputRef.current.blur();
      setShowUdrGroupLabelTextBox(false);
    }
  }

  function stopTrimSliderEventPropagation(evt: SyntheticEvent) {
    evt.stopPropagation();
  }

  const udrResizeContainer = find(
    udrResizeContainerMap,
    ({ sprayPaint }, udrId) =>
      selectedUDRId === udrId ||
      (!!selectedUDRId && !!sprayPaint?.idToUdrMap[selectedUDRId])
  );

  const resizeContainerComponent = udrResizeContainer ? (
    <div
      onClick={stopTrimSliderEventPropagation}
      style={{
        position: 'absolute',
        width: '100%',
        top: 0,
        height: '100%',
        pointerEvents: 'none',
      }}
    >
      <TrimSlider
        order={6}
        onChange={onChange}
        onChangeSubmit={onChangeSubmit}
        playerRef={ref}
        udrStartMin={udrResizeContainer.startMinPx}
        udrStopMax={udrResizeContainer.stopMaxPx}
        udrStart={udrResizeContainer.startPx}
        udrEnd={udrResizeContainer.stopPx}
        barStartMs={props.startMs}
        barStopMs={props.stopMs}
        id={udrResizeContainer.udrId}
      />
    </div>
  ) : null;

  const handleGroupLabelClick = () => {
    onGroupLabelClick(UDRId);
  };

  const onBlurTextField = () => {
    setShowUdrGroupLabelTextBox(false);
  };

  const handleUDRContainerClick = () => {
    if (STOP_KONVA_EVENT_FLAG) {
      STOP_KONVA_EVENT_FLAG = false;
    } else {
      onUDRContainerClick(UDRId);
    }
  };

  return (
    <div
      className={styles.shorterTimelineContainer}
      data-test="timeline-group-udr-row"
    >
      <div
        data-test="timeline-udr-title"
        className={active ? styles.iconUdrGroupSelected : styles.iconUdrGroup}
        onDoubleClick={showLabelTextBox}
        style={{ borderColor: color, minWidth: '190px', overflowY: 'hidden' }}
        onClick={handleGroupLabelClick}
      >
        {showUdrGroupLabelTextBox ? (
          <TextField
            data-testid="udr-group-row-component-textfield"
            onKeyUp={handleKeyUp}
            onBlur={onBlurTextField}
            inputRef={inputRef}
            autoFocus
            multiline={false}
            defaultValue={udrGroupLabel}
            margin="normal"
            slotProps={{
              htmlInput: {
                style: { color: 'white', fontSize: 12, width: 150 },
              },
            }}
          />
        ) : (
          <label
            className={styles.groupLabel}
            data-testid="udr-group-row-component-label"
          >
            {udrGroupLabel}
          </label>
        )}
      </div>
      <div
        onClick={handleUDRContainerClick}
        style={{ position: 'relative', width: '100%', overflow: 'hidden' }}
      >
        <div style={{ minWidth: '30px' }} className={styles.timeline}>
          {/* Timeline rects will go in here */}
          <div ref={ref} />
        </div>
        {resizeContainerComponent}
      </div>
      {progress < 1 ? (
        <div
          data-test="moving-background"
          className={styles.progress}
          style={{ width: `${100 - progress * 100}%` }}
        />
      ) : null}
    </div>
  );
};

export default UDRGroupRowComponent;

import Menu from '@mui/material/Menu';
import { I18nTranslate } from '@i18n';
import { useSelector } from 'react-redux';
import MenuItem from '@mui/material/MenuItem';
import { selectFeatureFlags } from '@user-permissions';

const ContextMenuView = ({
  anchorEl,
  isRedacted,
  hasNotes,
  numAll,
  onRedact,
  onRedactAll,
  onUnredact,
  onUnredactAll,
  onEditNote,
  onDeleteNote,
  onClose,
}: Props) => {
  const { redactionCodes } = useSelector(selectFeatureFlags);

  return isRedacted ? (
    <Menu
      id="transcription-unredact-context-menu"
      anchorEl={anchorEl}
      open={Boolean(anchorEl)}
      onClose={onClose}
      data-testid="transcription-unredact-context-menu"
    >
      <MenuItem onClick={onUnredact}>
        {I18nTranslate.TranslateMessage('unredact')}
      </MenuItem>
      <MenuItem disabled={numAll < 2} onClick={onUnredactAll}>
        {I18nTranslate.TranslateMessage('unredactAll')} ({numAll})
      </MenuItem>
      {redactionCodes ? (
        <MenuItem onClick={onUnredact}>
          {I18nTranslate.TranslateMessage('addRedactionCode')}
        </MenuItem>
      ) : null}
      {hasNotes ? (
        <MenuItem onClick={onEditNote}>
          {I18nTranslate.TranslateMessage('editNote')}
        </MenuItem>
      ) : null}
      {hasNotes ? (
        <MenuItem onClick={onDeleteNote}>
          {I18nTranslate.TranslateMessage('deleteNote')}
        </MenuItem>
      ) : null}
      {!hasNotes ? (
        <MenuItem onClick={onEditNote}>
          {I18nTranslate.TranslateMessage('addNote')}
        </MenuItem>
      ) : null}
    </Menu>
  ) : (
    <Menu
      id="transcription-redact-context-menu"
      anchorEl={anchorEl}
      open={Boolean(anchorEl)}
      onClose={onClose}
      data-testid="transcription-redact-context-menu"
    >
      <MenuItem onClick={onRedact}>
        {I18nTranslate.TranslateMessage('redact')}
      </MenuItem>
      <MenuItem disabled={numAll < 2} onClick={onRedactAll}>
        {I18nTranslate.TranslateMessage('redactAll')} ({numAll})
      </MenuItem>
    </Menu>
  );
};

export interface Props {
  readonly anchorEl?: HTMLElement;
  readonly isRedacted: boolean;
  readonly hasNotes: boolean;
  readonly numAll: number;
  readonly onRedact: () => void;
  readonly onRedactAll: () => void;
  readonly onUnredact: () => void;
  readonly onUnredactAll: () => void;
  readonly onEditNote: () => void;
  readonly onDeleteNote: () => void;
  readonly onClose: () => void;
}

export default ContextMenuView;

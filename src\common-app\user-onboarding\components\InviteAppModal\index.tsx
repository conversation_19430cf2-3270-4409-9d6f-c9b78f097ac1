import { useState } from 'react';
import { connect } from 'react-redux';
import { createSelector } from 'reselect';

import {
  actionCloseSendEmailInvite,
  actionSendEmailInvite,
} from '../../actions';
import { selectInviteAppIsOpen } from '../../selectors';
import InviteAppModalView from './InviteAppModalView';

const InviteAppModal = ({
  isOpen,
  onSendInvite,
  onClose,
}: InviteAppModalPropTypes) => {
  const [isFormOpen, setIsFormOpen] = useState(true);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);

  const onSendInviteHandler = (emails: ReadonlyArray<string>) => {
    onSendInvite({ emails });
    setIsFormOpen(false);
    setIsConfirmOpen(true);
  };

  const onCloseHandler = () => {
    setIsFormOpen(true);
    setIsConfirmOpen(false);
    onClose();
  };

  return isOpen ? (
    <InviteAppModalView
      {...{
        isFormOpen,
        isConfirmOpen,
        onSendInvite: onSendInviteHandler,
        onClose: onCloseHandler,
      }}
    />
  ) : null;
};

export default connect(
  createSelector(selectInviteAppIsOpen, (isOpen) => ({ isOpen })),
  {
    onSendInvite: actionSendEmailInvite,
    onClose: actionCloseSendEmailInvite,
  }
)(InviteAppModal);

export interface InviteAppModalPropTypes {
  readonly isOpen: boolean;
  readonly onSendInvite: typeof actionSendEmailInvite;
  readonly onClose: () => void;
}

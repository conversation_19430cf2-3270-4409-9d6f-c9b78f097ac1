### Steps to create comments schema using core-graphql queries

1. Create new data registry 
```
mutation createDataRegistry {
  createDataRegistry(input: {
    id: "d1e5b70c-05cc-4732-a756-63b87d467703"
    name: "redact-case-notification"
    description: "Redact Case Notification for Redact app"
    source: "field deprecated"
  }) {
    id
  }
}
```

2. Create Schema Draft
```
# Note: Use the `dataRegistryId` value from the `createDataRegistry` mutation.
mutation createSchemaDraft {
  upsertSchemaDraft(
    input: {
      dataRegistryId: "d1e5b70c-05cc-4732-a756-63b87d467703"
      majorVersion: 1
      schema: {
        type: "object"
        title: "redact-case-notification"
        required: [
          "status"
          "batchId"
          "message"
          "caseName"
          "createdDateTime"
          "notificationType"
          "folderTreeObjectId"
        ]
        properties: {
          status: { type: "string" }
          batchId: { type: "string" }
          message: { type: "string" }
          caseName: { type: "string" }
          createdDateTime: { type: "dateTime" }
          notificationType: { type: "string" }
          folderTreeObjectId: { type: "string" }
        }
        description: "Redact Case Notification for Redact app"
      }
    }
  ) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}
```

3. Publish Schema Draft

```
# Note: Pass in the Schema ID for the `id` value
mutation publishSchemaDraft($id: ID!) {
  updateSchemaState(input: {
    id: $id
    status: published
  }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```


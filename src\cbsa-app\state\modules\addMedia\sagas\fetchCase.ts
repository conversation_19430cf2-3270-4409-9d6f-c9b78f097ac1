import { sagaIntl } from '@i18n';
import * as Services from '../services';
import {
  FETCH_CASE_DETAILS_SUCCESS,
  setCaseDetails,
  setLoaders,
} from '../actions';
import { all, delay, put, select, takeEvery } from 'typed-redux-saga/macro';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { ROUTE_HOME, selectCurrentRoutePayload } from '@common-modules/routing';

const FETCH_CASE_INTERVAL = 15_000;

export function* fetchCase() {
  let firstFetch = true;

  const { case_id: caseId } = yield* select(selectCurrentRoutePayload);

  const intl = sagaIntl();
  while (true) {
    try {
      yield* all([
        put(Services.fetchCaseDetails({ caseId })),
        put(Services.fetchTdos({ caseId })),
        put(Services.fetchCaseNotifications({ caseId })),
      ]);
    } catch {
      yield* put(
        enqueueSnackbar({
          message: intl.formatMessage({ id: 'failedToFetchCase' }),
          variant: 'error',
        })
      );

      if (firstFetch) {
        yield* put(ROUTE_HOME());
      }
    }

    firstFetch = false;
    yield* delay(FETCH_CASE_INTERVAL);
  }
}

export function* fetchCaseDetailsSuccess() {
  yield* takeEvery(FETCH_CASE_DETAILS_SUCCESS, function* ({ payload }) {
    const sdo = payload.structuredDataObjects?.records?.[0];

    if (!sdo || sdo.data?.status === 'deleted') {
      window.location.href = '/';
      return;
    }

    yield* put(
      setCaseDetails({
        createdDateTime: sdo.data.createdDateTime,
        modifiedDateTime: sdo.data.modifiedDateTime,
        id: sdo.id,
        name: sdo.data.caseName,
        status: sdo.data.status,
        treeObjectId: sdo.data.folderTreeObjectId,
        archive: sdo.data.archive,
      })
    );

    yield* put(
      setLoaders({
        isLoadingCaseDetails: false,
      })
    );
  });
}

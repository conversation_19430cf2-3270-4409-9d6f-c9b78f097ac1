import {
  Player,
  ControlBar,
  BigPlayButton,
  Shortcut,
  PreloadType,
} from 'video-react';
import cx from 'classnames';
import * as styles from './styles.scss';
import VideoSource from './VideoSource';
import { DeepReadonlyArray } from '@utils';
import { selectPlayer } from '@common-modules/player';
import { GroupedBoundingPoly } from '@common/web-worker';
import { selectFeatureFlags } from '@common/user-permissions';
import BoundingPolyOverlay from '../BoundingPolyOverlay/Overlay';
import WaveFormBackground from '@resources/images/audio_icon.svg';
import PlayButton from '@resources/images/play-button.svg';
import {
  CSSProperties,
  forwardRef,
  ForwardedRef,
  memo,
  MutableRefObject,
  useRef,
} from 'react';
import { useSelector, useStore } from 'react-redux';
import OverlayPositioningProvider from '../BoundingPolyOverlay/OverlayPositioningProvider';
import {
  GroupIdAndType,
  selectHighlightedOverlay,
  selectSelectedUDRGroupId,
} from '@common-modules/mediaDetails';
import { OverlayPropType } from '../BoundingPolyOverlay/OverlayTypes';
import {
  BoundingPolyRect,
  GlobalSettings,
  OverlayPreviewOptionsType,
  UDRsPolyAssetGroup,
} from '@common/state/modules/mediaDetails/models';
import { IndividualRedactionCode } from '@redact/state/modules/redactionCodes/models';
import { Button } from '@mui/material';

const wrapperStyles: CSSProperties = { zIndex: 100 };

const MediaPlayer = forwardRef<Player | null, Props>(
  (props: Props, forwardedRef) => {
    const {
      actionMenuItems,
      addOnly,
      autofocus,
      boundingBoxScale,
      boundingPolySeries,
      defaultBoundingBoxStyles,
      fluid = true,
      globalSettings,
      hasAudio,
      hasVideo,
      isAddRedactionCodeOpen,
      isRedactionConfigOpen,
      isTimeStampOpen,
      lastActivePoly,
      maxMs,
      onAddBoundingBox,
      onDeleteBoundingBox,
      onBoxCodeChange,
      onBoxRedactionChange,
      onBoxShapeChange,
      onCloseAddRedactionCode,
      onCloseRedactionConfig,
      onCloseTimeStamp,
      clearLocalOverlayBeingUpdated,
      onSprayPaintBoxChangeStart,
      onSprayPaintBoxChange,
      onSprayPaintBoxChangeStop,
      onChangeBoundingBoxStop,
      overlayPreviewOption,
      readOnly,
      setSelectedGroupsByGroupId,
      setMergeGroupId,
      setMergeClusterId,
      showUnselectedBoundingPolySeries,
      src,
      stagedBoundingBoxStyles,
      streams,
      stylesByObjectType,
      udrGroup,
      canvasRef,
    } = props;
    const store = useStore();
    const ref = useRef<HTMLDivElement>(null);
    const featureFlags = useSelector(selectFeatureFlags);
    const selectedUDRGroupId = useSelector(selectSelectedUDRGroupId);
    const highlightedOverlay = useSelector(selectHighlightedOverlay);
    const {
      currentTime,
      hasStarted,
      isActive,
      paused,
      videoWidth,
      videoHeight,
    } = useSelector(selectPlayer);
    const isRequestVideoFrameCallback =
      hasVideo && 'requestVideoFrameCallback' in HTMLVideoElement.prototype;

    // no need for useCallback because there is no memo on BoundingPolyOverlay/Overlay
    const handleAddBoundingBox = (
      newBox: {
        boundingPoly: BoundingPolyRect;
        id: string;
        groupId: string;
      },
      opts?: { shift: boolean }
    ) => onAddBoundingBox?.(newBox, currentTime * 1000, opts);

    const handleClickPlayVideo = () => {
      if (forwardedRef && 'current' in forwardedRef && forwardedRef.current) {
        forwardedRef.current.play();
      }
    };

    const width = ref.current?.clientWidth ?? 0;
    const height = ref.current?.clientHeight ?? 0;
    const horizontalLetterbox =
      height !== 0 && videoHeight !== 0
        ? width / height < videoWidth / videoHeight
        : undefined;

    return (
      <OverlayPositioningProvider
        key="OverlayPositioningProvider"
        contentHeight={videoHeight}
        contentWidth={videoWidth}
        fixedWidth={!fluid}
      >
        {hasStarted && (
          <BoundingPolyOverlay
            key="BoundingPolyOverlay"
            wrapperStyles={wrapperStyles}
            onAddBoundingBox={handleAddBoundingBox}
            onDeleteBoundingBox={onDeleteBoundingBox}
            onChangeBoundingBoxStart={onSprayPaintBoxChangeStart}
            onChangeBoundingBox={onSprayPaintBoxChange}
            onChangeBoundingBoxStop={onChangeBoundingBoxStop}
            onSprayPaintBoxChangeStop={onSprayPaintBoxChangeStop}
            onBoxShapeChange={onBoxShapeChange}
            onBoxRedactionChange={onBoxRedactionChange}
            onBoxCodeChange={onBoxCodeChange}
            initialBoundingBoxPolys={boundingPolySeries}
            showUnselectedBoundingPolySeries={showUnselectedBoundingPolySeries}
            setSelectedGroupsByGroupId={setSelectedGroupsByGroupId}
            setMergeGroupId={setMergeGroupId}
            setMergeClusterId={setMergeClusterId}
            boundingBoxScale={boundingBoxScale}
            actionMenuItems={actionMenuItems}
            addOnly={addOnly}
            readOnly={readOnly}
            stagedBoundingBoxStyles={stagedBoundingBoxStyles}
            stylesByObjectType={stylesByObjectType}
            defaultBoundingBoxStyles={defaultBoundingBoxStyles}
            autofocus={autofocus}
            maxMs={maxMs}
            udrGroup={udrGroup}
            lastActivePoly={lastActivePoly}
            isTimeStampOpen={isTimeStampOpen}
            onCloseTimeStamp={onCloseTimeStamp}
            clearLocalOverlayBeingUpdated={clearLocalOverlayBeingUpdated}
            isRedactionConfigOpen={isRedactionConfigOpen}
            onCloseRedactionConfig={onCloseRedactionConfig}
            isAddRedactionCodeOpen={isAddRedactionCodeOpen}
            onCloseAddRedactionCode={onCloseAddRedactionCode}
            overlayPreviewOption={overlayPreviewOption}
            globalSettings={globalSettings}
            highlightedOverlay={highlightedOverlay}
            playerPaused={paused}
            currentTime={currentTime}
            selectedUDRGroupId={selectedUDRGroupId}
            featureFlags={featureFlags}
          />
        )}
        {isRequestVideoFrameCallback && (
          <div ref={ref} className={styles.canvasMat}>
            {horizontalLetterbox !== undefined && (
              <canvas
                ref={canvasRef}
                className={styles.canvas}
                width={
                  horizontalLetterbox
                    ? width
                    : height * (videoWidth / videoHeight)
                }
                height={
                  horizontalLetterbox
                    ? width * (videoHeight / videoWidth)
                    : height
                }
                data-testid="canvas-player"
              />
            )}
          </div>
        )}
        <Player
          key="Player"
          className={cx(
            styles.mediaPlayer,
            isRequestVideoFrameCallback
              ? styles.mediaPlayerHidden
              : styles.mediaPlayerHeight
          )}
          ref={forwardedRef}
          store={store}
          {...{
            src,
            fluid: props.fluid,
            aspectRatio: props.aspectRatio,
            height: props.height,
            width: props.width,
            videoHeight,
            videoWidth,
            hasStarted,
            isActive,
            currentTime,
            paused,
            highlightedOverlay,
            selectedUDRGroupId,
          }}
          poster={hasAudio && !hasVideo ? WaveFormBackground : undefined}
        >
          {/* Prevent video-react from adding its own control bar */}
          <ControlBar autoHide className={styles.hiddenDummyControls} />
          <VideoSource isVideoChild src={src} streams={streams} isMediaPlayer />
          <BigPlayButton position="center" className={styles.mediaPlayButton} />
          <Shortcut
            shortcuts={[
              {
                keyCode: 32 /* Space bar */,
                // important: There is potential memory leak caused by udrCollection in
                // MediaPlayerView. The nested function will have a closure over a stale udrCollection
                // use the top level function to avoid the memory leak.
                handle: noop /* Override it's default handle */,
              },
            ]}
          />
          <Button
            disableRipple
            className={cx(
              styles.tdoButtonPlayVideoWrapper,
              !paused && styles.btnHide
            )}
            onClick={handleClickPlayVideo}
          >
            <div>
              <img
                className={styles.tdoButtonPlayBackground}
                src={PlayButton}
                alt="Play"
              />
            </div>
          </Button>
        </Player>
      </OverlayPositioningProvider>
    );
  }
);

function noop() {}

MediaPlayer.displayName = 'MediaPlayer';

interface Props {
  src: string;
  streams: DeepReadonlyArray<{
    protocol: string;
    uri: string;
  }>;
  hasAudio: boolean;
  hasVideo: boolean;
  boundingPolySeries?: GroupedBoundingPoly[];
  showUnselectedBoundingPolySeries?: GroupedBoundingPoly[];
  setSelectedGroupsByGroupId?: (groupId: string) => void;
  setMergeGroupId?: (groupId: string) => void;
  setMergeClusterId?: (clusterId: string, selected: boolean) => void;
  onAddBoundingBox?: (
    box: {
      boundingPoly: BoundingPolyRect;
      id: string;
      groupId: string;
    },
    position: number,
    opts?: { shift: boolean }
  ) => void;
  onDeleteBoundingBox?: OverlayPropType['onDeleteBoundingBox'];
  onChangeBoundingBox?: OverlayPropType['onChangeBoundingBox'];
  onChangeBoundingBoxStop?: (
    data: GroupedBoundingPoly,
    hasChanged?: boolean
  ) => void;
  defaultBoundingBoxStyles?: CSSProperties;
  stagedBoundingBoxStyles?: CSSProperties;
  stylesByObjectType?: Record<string, CSSProperties>;
  actionMenuItems?: DeepReadonlyArray<{
    label: string;
    onClick: () => void;
  }>;
  readOnly?: boolean;
  addOnly?: boolean;
  width?: string | number;
  height?: string | number;
  fluid?: boolean /* fluid = 100% width by default, see video-react docs */;
  autofocus?: boolean;
  forwardedReference?: ForwardedRef<Player>;
  onSprayPaintBoxChangeStart?: (data: GroupedBoundingPoly) => void;
  onSprayPaintBoxChange?: (data: GroupedBoundingPoly) => void;
  onSprayPaintBoxChangeStop?: (data: GroupedBoundingPoly) => void;
  onBoxShapeChange?: (
    data: GroupedBoundingPoly,
    udrGroupIds: string[],
    detectionGroups: GroupIdAndType[]
  ) => void;
  onBoxRedactionChange?: (
    data: GroupedBoundingPoly,
    udrGroupIds: string[],
    detectionGroups: GroupIdAndType[]
  ) => void;
  onBoxCodeChange?: (
    objectId: string,
    redactionCode: IndividualRedactionCode | undefined,
    udrGroupIds: string[],
    detectionGroups: GroupIdAndType[]
  ) => void;
  boundingBoxScale?: number;
  poster?: string;
  maxMs?: number;
  udrGroup?: UDRsPolyAssetGroup | null;
  lastActivePoly?: GroupedBoundingPoly | null;
  isTimeStampOpen?: boolean;
  onCloseTimeStamp?: () => void;
  clearLocalOverlayBeingUpdated?: () => void;
  isRedactionConfigOpen?: boolean;
  onCloseRedactionConfig?: () => void;
  isAddRedactionCodeOpen?: boolean;
  onCloseAddRedactionCode?: () => void;
  overlayPreviewOption?: OverlayPreviewOptionsType;
  globalSettings: GlobalSettings;
  onSelectBoundingBox?: (data: GroupedBoundingPoly) => void;
  aspectRatio?: string;
  preload?: PreloadType;
  canvasRef?: MutableRefObject<HTMLCanvasElement | null>;
}

export default memo(MediaPlayer);

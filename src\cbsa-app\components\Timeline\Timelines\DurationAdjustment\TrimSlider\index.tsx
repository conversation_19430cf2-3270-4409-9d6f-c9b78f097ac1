import { createRef, PureComponent, RefObject } from 'react';
import { connect, ConnectedProps } from 'react-redux';
import * as styles from './styles.scss';
import Draggable, { DraggableEventHandler } from 'react-draggable';
import Tooltip from '@mui/material/Tooltip';
import moment from 'moment';
import { clamp, isEqual } from 'lodash';

const GRABBER_WIDTH = 9;
const DEFAULT_TRIMBAR_OFFSET = 216;

type Props = PropsFromRedux & {
  duration: number;
  onChange: (change: {
    startTime: number;
    endTime: number;
    id: string;
    type: 'start' | 'end';
  }) => void;
  onChangeSubmit: (change: {
    startTime: number;
    endTime: number;
    id: string;
    type: 'start' | 'end';
  }) => void;
  playerRef: RefObject<HTMLDivElement>;
  udrStartMin: number;
  udrStopMax: number;
  udrStart: number;
  udrEnd: number;
  barStartMs: number;
  barStopMs: number;
  id: string;
  order: number;
};

class TrimSlider extends PureComponent<Props> {
  static defaultProps = {};

  constructor(props: Props) {
    super(props);
    const { udrStart, udrEnd } = props;
    this.state.startPosition.x = udrStart;
    this.state.rangePosition.x = udrStart;
    this.state.endPosition.x = udrEnd;
    this.state.rangeWidth = udrEnd - udrStart;
  }

  state = {
    maxOfX: 0,

    startPosition: { x: 0, y: 0 },
    endPosition: { x: 0, y: 0 },
    rangePosition: { x: 0, y: 0 },
    rangeWidth: 0,
    startTimeStamp: 0,
    endTimeStamp: 0,
  };

  componentDidMount() {
    const { startPosition, endPosition } = this.state;
    const offsetWidth = this.trimRef.current?.offsetWidth;
    if (offsetWidth !== undefined) {
      this.setState({
        maxOfX: offsetWidth,
      });
    }
    this.setStartEndTimeStamp(startPosition.x, endPosition.x);
  }

  shouldComponentUpdate(
    nextProps: Readonly<Props>,
    nextState: typeof TrimSlider.prototype.state
  ): boolean {
    const propChanged = !isEqual(this.props, nextProps);
    const stateChanged = !isEqual(this.state, nextState);

    return propChanged || stateChanged;
  }

  componentDidUpdate(prevProps: Props) {
    const hasChanged =
      this.props.udrStart !== prevProps.udrStart ||
      this.props.udrEnd !== prevProps.udrEnd;

    if (hasChanged) {
      if (this.props.udrEnd > this.props.udrStopMax) {
        this.setState({
          endPosition: { x: this.props.udrStopMax, y: 0 },
        });
      }
      this.setState({ startPosition: { x: this.props.udrStart, y: 0 } });
      this.setState({
        endPosition: { x: this.props.udrEnd - GRABBER_WIDTH, y: 0 },
      });
      this.setState({ rangePosition: { x: this.props.udrStart, y: 0 } });
      this.setState({ rangeWidth: this.props.udrEnd - this.props.udrStart });
    }
  }

  trimRef = createRef<HTMLDivElement>();

  // nodeRef and ref below are required to avoid console warnings around findDOMNode is deprecated in StrictMode
  nodeRefStart = createRef<HTMLDivElement>();
  nodeRefRange = createRef<HTMLDivElement>();
  nodeRefEnd = createRef<HTMLDivElement>();

  getUdrStartBounds = () => {
    const { udrStartMin } = this.props;
    const { endPosition } = this.state;
    return { min: udrStartMin, max: endPosition.x - GRABBER_WIDTH };
  };

  getUdrEndBounds = () => {
    const { udrStopMax } = this.props;
    const { startPosition } = this.state;
    return {
      min: startPosition.x + GRABBER_WIDTH,
      max: udrStopMax - GRABBER_WIDTH,
    };
  };

  getTrimbarLeftOffset = () =>
    this.trimRef?.current?.getBoundingClientRect().left ??
    DEFAULT_TRIMBAR_OFFSET;

  onStartBarDrag: DraggableEventHandler = (dragEvt, _position) => {
    // Calculate x from event taking into account the offset of the trim bar
    const { min, max } = this.getUdrStartBounds();
    const x = clamp(
      (dragEvt as MouseEvent).clientX - this.getTrimbarLeftOffset(),
      min,
      max
    );

    // Note: Ideal solution would use position x but doesn't update properly when onChange is called below
    // not entirely sure root cause but by using mouse position instead have to enforce bounds manually instead of
    // library enforcing it
    // const { x } = position;

    const endTime = this.state.endPosition.x + GRABBER_WIDTH;
    const width = endTime - x;
    const { startPosition, endPosition } = this.state;

    this.setState({
      rangePosition: { x: x, y: 0 },
      rangeWidth: width,
      startPosition: { x, y: 0 },
    });

    const { onChange } = this.props;

    onChange({
      startTime: x,
      endTime,
      id: this.props.id,
      type: 'start',
    });
    this.setStartEndTimeStamp(startPosition.x, endPosition.x);
  };

  onStartBarDragStop: DraggableEventHandler = (_event, position) => {
    const { onChangeSubmit } = this.props;
    const { x } = position;
    const endTime = this.state.endPosition.x + GRABBER_WIDTH;
    onChangeSubmit({
      startTime: x,
      endTime,
      id: this.props.id,
      type: 'start',
    });
  };

  onEndBarDrag: DraggableEventHandler = (dragEvt, _position) => {
    const { min, max } = this.getUdrEndBounds();
    // Calculate x from event taking into account the offset of the trim bar
    const x = clamp(
      (dragEvt as MouseEvent).clientX - this.getTrimbarLeftOffset(),
      min,
      max
    );

    // Note: Ideal solution would use position x but doesn't update properly when onChange is called below
    // not entirely sure root cause but by using mouse position instead have to enforce bounds manually instead of
    // library enforcing it
    // const { x } = position;
    const startTime = this.state.startPosition.x;
    const width = x - startTime;

    this.setState({
      rangeWidth: width,
      endPosition: { x, y: 0 },
    });

    const { onChange } = this.props;
    const { startPosition, endPosition } = this.state;

    onChange({
      startTime,
      endTime: x + GRABBER_WIDTH,
      id: this.props.id,
      type: 'end',
    });
    this.setStartEndTimeStamp(startPosition.x, endPosition.x + GRABBER_WIDTH);
  };

  onEndBarDragStop: DraggableEventHandler = (_event, position) => {
    const { x } = position;
    const startTime = this.state.startPosition.x;
    const { onChangeSubmit } = this.props;

    onChangeSubmit({
      startTime,
      endTime: x + GRABBER_WIDTH,
      id: this.props.id,
      type: 'end',
    });
  };

  getTimeByPosition = (x: number) => {
    const { barStartMs, barStopMs } = this.props;
    const { maxOfX } = this.state;
    // const numberOfSeconds = maxOfX ? (x / maxOfX) * duration : 0;
    // TODO: Justify the !
    const numberOfSeconds = maxOfX
      ? ((x / maxOfX) * (barStopMs - barStartMs)) / 1000.0 + barStartMs / 1000.0
      : ((x / this.trimRef.current!.offsetWidth) * (barStopMs - barStartMs)) /
          1000.0 +
        barStartMs / 1000.0;
    return moment().startOf('day').seconds(numberOfSeconds).format('H:mm:ss');
  };

  setStartEndTimeStamp = (startPositionX: number, endPositionX: number) => {
    this.setState({
      startTimeStamp: this.getTimeByPosition(startPositionX),
    });
    this.setState({
      endTimeStamp: this.getTimeByPosition(endPositionX),
    });
  };

  render() {
    const { rangeWidth, rangePosition, startTimeStamp, endTimeStamp } =
      this.state;
    const { udrStopMax, udrEnd, udrStart } = this.props;
    const udrMiddle =
      (Math.min(udrStopMax, udrEnd) + Math.max(0, udrStart)) / 2.0;
    return (
      <div
        data-testid="timeline-duration-adjustment-trim-slider-container"
        className={`${styles.trimWrapper} trimbar-tool-wrapper`}
        data-tag="trimbar-tool"
        ref={this.trimRef}
      >
        <Draggable
          // nodeRef and ref below are required to avoid console warnings around findDOMNode is deprecated in StrictMode
          nodeRef={this.nodeRefStart}
          axis="x"
          bounds={{
            left: this.getUdrStartBounds().min,
            right: this.getUdrStartBounds().max,
          }}
          position={{ x: Math.min(udrMiddle - GRABBER_WIDTH, udrStart), y: 0 }}
          onDrag={this.onStartBarDrag}
          onStop={this.onStartBarDragStop}
        >
          <Tooltip
            ref={this.nodeRefStart}
            data-testid="trim-slider-tooltip"
            style={{ pointerEvents: 'auto' }}
            title={startTimeStamp}
            placement="top"
          >
            <div className={styles.startTime} />
          </Tooltip>
        </Draggable>
        <Draggable
          nodeRef={this.nodeRefRange}
          axis="x"
          position={rangePosition}
          bounds="parent"
          cancel="div"
        >
          <div
            ref={this.nodeRefRange}
            className={styles.trimRange}
            style={{
              width: rangeWidth || `calc(100%)`,
              marginRight: rangePosition.x,
            }}
          />
        </Draggable>
        <Draggable
          nodeRef={this.nodeRefEnd}
          axis="x"
          bounds={{
            left: this.getUdrEndBounds().min,
            right: this.getUdrEndBounds().max,
          }}
          position={{
            x: Math.max(
              udrMiddle,
              Math.min(udrStopMax, udrEnd) - GRABBER_WIDTH
            ),
            y: 0,
          }}
          onDrag={this.onEndBarDrag}
          onStop={this.onEndBarDragStop}
        >
          <Tooltip
            ref={this.nodeRefEnd}
            data-testid="trim-slider-tooltip"
            style={{ pointerEvents: 'auto' }}
            title={endTimeStamp}
            placement="top"
          >
            <div className={styles.endTime} />
          </Tooltip>
        </Draggable>
      </div>
    );
  }
}

const mapState = (state: any) => ({
  duration: state.player.duration,
});

const mapDispatch = {};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(TrimSlider);

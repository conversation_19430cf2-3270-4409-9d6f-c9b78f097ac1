import { Case } from '@cbsa-modules/universal';
import { createGraphQLSuccessAction } from '@helpers/callGraphQLApi';
import { createAction } from '@reduxjs/toolkit';
import _ from 'lodash';

export const DELETE_CASE = createAction<{
  caseDetails: Case;
}>('CBSA/DELETE_CASE');

export const DELETE_CASE_SUCCESS = createAction<Case>(
  'CBSA/DELETE_CASE_SUCCESS'
);
export const DELETE_CASE_FAILURE = createGraphQLSuccessAction(
  'CBSA/DELETE_CASE_FAILURE'
);

export const deleteCase = (caseDetails: Case) => DELETE_CASE({ caseDetails });

import { Theme } from '@mui/material/styles';
import makeStyles from '@mui/styles/makeStyles';
import createStyles from '@mui/styles/createStyles';

export const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    container: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      textAlign: 'center',
      marginTop: '4em',
    },

    bigImage: {
      paddingBottom: 0,
    },

    headline: {
      width: '142px',
      color: 'rgba(0, 0, 0, 0.87)',
      fontFamily: 'Roboto, Arial, Helvetica, sans-serif',
      fontSize: '20px',
      fontWeight: 300,
      lineHeight: '24px',
      marginTop: '30px',
    },

    message: {
      width: '342px',
      color: 'rgba(0, 0, 0, 0.54)',
      fontFamily: 'Roboto, Arial, Helvetica, sans-serif',
      fontSize: '14px',
      fontWeight: 300,
      lineHeight: '16px',
      textAlign: 'center',
      marginTop: '10px',
    },

    actionButton: {
      backgroundColor: theme.palette.primary.main,
      borderRadius: '3px',
      width: '87px',
      color: theme.palette.background.paper,
      fontFamily: 'Roboto, Arial, Helvetica, sans-serif',
      fontSize: '14px',
      fontWeight: 500,
      lineHeight: '15px',
      marginTop: '20px',
    },
  })
);

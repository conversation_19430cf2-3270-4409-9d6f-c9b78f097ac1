import { Mouse<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>actN<PERSON>, useState } from 'react';
import { KeyboardArrowDown, MoreVert } from '@mui/icons-material';
import { IconButton, Menu, MenuItem, Slide } from '@mui/material';

const OverlayToolBar = ({
  visible,
  children,
  onMinimize,
  menuItems = [],
  bottomOffset = 0,
  focusedBoundingBoxId,
}: Props) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);

  const handleOpenMenu: MouseEventHandler<HTMLElement> = (event) =>
    setMenuAnchorEl(event.currentTarget);

  const handleCloseMenu: MouseEventHandler<HTMLElement> = (e) => {
    if (e.target instanceof HTMLElement) {
      const itemIndex = e.target.getAttribute('data-itemindex');

      if (itemIndex) {
        menuItems[Number(itemIndex)]?.onClick(focusedBoundingBoxId);
      }
    }

    setMenuAnchorEl(null);
  };

  return (
    <Slide in={visible} direction="up" data-testid="overlay-toolbar">
      <div
        style={{
          position: 'absolute',
          backgroundColor: 'rgb(243, 243, 243, 0.87)',
          display: 'flex',
          justifyContent: 'flex-end',
          alignItems: 'center',
          padding: 4,
          bottom: bottomOffset,
          width: '100%',
        }}
      >
        <div style={{ flexGrow: 1 }}>
          <IconButton
            onClick={onMinimize}
            data-veritone-element="overlay-toolbar-arrow-down-icon"
            data-test="icon-button"
            data-testid="on-minimize"
            size="large"
          >
            <KeyboardArrowDown data-testid="icon-arrow-down" />
          </IconButton>
        </div>

        {children}

        {!!menuItems.length && (
          <>
            <div
              style={{
                borderRight: '1px solid rgba(0, 0, 0, 0.54)',
                height: 25,
              }}
            >
              {' '}
            </div>

            <IconButton
              onClick={handleOpenMenu}
              data-veritone-element="overlay-toolbar-more-vert-icon"
              data-test="icon-button"
              data-testid="open-menu"
              size="large"
            >
              <MoreVert data-testid="icon-more-vert" />
            </IconButton>

            <Menu
              anchorEl={menuAnchorEl}
              open={!!menuAnchorEl}
              onClose={handleCloseMenu}
              transformOrigin={{
                vertical: 'bottom',
                horizontal: 'center',
              }}
              container={document.fullscreenElement}
            >
              {menuItems.map((option, i) => (
                <MenuItem
                  key={option.label}
                  data-itemindex={i}
                  onClick={handleCloseMenu}
                  style={{
                    display: 'flex',
                  }}
                  data-testid="menu-item"
                >
                  {option.label}
                </MenuItem>
              ))}
            </Menu>
          </>
        )}
      </div>
    </Slide>
  );
};

interface Props {
  visible: boolean;
  children: ReactNode;
  onMinimize?: MouseEventHandler<HTMLElement>;
  menuItems?: {
    label: string;
    onClick: (id?: string) => void;
  }[];
  bottomOffset?: number;
  focusedBoundingBoxId?: string;
}

export default OverlayToolBar;

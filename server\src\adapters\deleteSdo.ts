import {  DeleteSdoResponse } from '../model/responses';
import { callGQL } from '../api/callGraphql';
import { RequestHeader } from '../model/requests';
import { Logger } from '../logger';
import { deleteSdoQuery } from '../api/queries';
import Config from '../../apiConfig.json';
import { getSchemaIdAdapter } from '../adapters/getSchema';
import { Messages } from '../errors/messages';

export const deleteSdoAdapter = async (headers: RequestHeader, sdoId: string) => {
    const schemaId = await getSchemaIdAdapter(headers, Config.caseRegistryId);
    
    if (schemaId) {
        const query = deleteSdoQuery(sdoId, schemaId);
        const deletedSdoId = await callGQL<DeleteSdoResponse>(headers, query)
        .then((res) => res?.deleteStructuredData?.id)
        .catch((err) => {
          Logger.error(Messages.DeleteSdoFail + JSON.stringify(err));
        });
       if (deletedSdoId === sdoId) {
        return deletedSdoId
       }
    } else {
    Logger.error(Messages.getSchemaIdFail);
   }
   return undefined;
};

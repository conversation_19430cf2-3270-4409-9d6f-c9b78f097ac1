import { useSelector } from 'react-redux';
import AutoScaling from './AutoScaling';
import MediaPlayer from './MediaPlayer';
import Timeline from '@cbsa-components/Timeline';
import ResultTabsPlace from '../FoundObjectsPlace';
import { componentSelectors } from './reduxHelpers';
import { useStyles } from './styles';

const Editor = () => {
  const { tdo, hasAudio, hasVideo } = useSelector(componentSelectors);
  const classes = useStyles();

  return tdo ? (
    <>
      <div className={classes.editorGrid}>
        <div className={classes.container}>
          <div className={classes.mediaPlayer}>
            <MediaPlayer
              url={tdo.primaryAsset?.signedUri ?? ''}
              streams={tdo.streams}
              hasAudio={hasAudio}
              hasVideo={hasVideo}
            />
          </div>
          <div className={classes.clusterList}>
            <ResultTabsPlace /> {/* hasAudio hasVideo */}
          </div>
        </div>
        <div style={{ height: '100%', marginTop: '10px' }}>
          <div
            style={{
              position: 'relative',
              display: 'flex',
              height: '100%',
              padding: 0,
            }}
          >
            <div
              style={{ position: 'relative', display: 'flex', width: '100%' }}
            >
              <div className={classes.timeline}>
                <Timeline />
              </div>
            </div>
            <div className={classes.iconButtonGroup}>
              <AutoScaling showIcon />
              <AutoScaling showIcon={false} />
              <AutoScaling showIcon={false} />
              <AutoScaling showIcon={false} />
              <AutoScaling showIcon={false} />
            </div>
          </div>
        </div>
      </div>
    </>
  ) : null;
};

export default Editor;

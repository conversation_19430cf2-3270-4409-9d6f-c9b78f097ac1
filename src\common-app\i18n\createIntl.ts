import messages from './messages';
import { LOCALES } from './locales';
import { createIntl } from 'react-intl';
import { getAppSpecificLanguage } from '@common-pages/App/helper';

const defaultLocale = LOCALES.ENGLISH;
export const LOCALSTORAGE_APP_NAME_KEY = 'vtn_app_name';
export const LOCALSTORAGE_LANGUAGE_KEY = 'vtn_lang_preference';

const getAppName = () =>
  window.localStorage?.getItem(LOCALSTORAGE_APP_NAME_KEY) ?? '';

export const sagaIntl = () => {
  const storeLanguage =
    window.localStorage?.getItem(LOCALSTORAGE_LANGUAGE_KEY) ??
    window.navigator.language;

  const [_, locale] = Object.entries({
    en: LOCALES.ENGLISH,
    fr: LOCALES.FRENCH,
  }).find(([key]) => storeLanguage.includes(key)) || [undefined, defaultLocale];

  return createIntl({
    locale,
    messages: messages[getAppSpecificLanguage(getAppName(), locale)],
  });
};

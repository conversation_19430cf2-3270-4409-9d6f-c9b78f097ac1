import { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Grid2 as Grid } from '@mui/material';
import FacesWrapperTab from '../FacesWrapperTab';
import { componentSelectors, componentActions } from './reduxHelpers';
import PersonOfInterestViewer from './PersonOfInterestViewer';
import { useStyles } from './styles';
import { Head } from 'ts-essentials';

const ResultTabsPlace = () => {
  const { caseMedia, caseId } = useSelector(componentSelectors);
  const dispatch = useDispatch();
  const classes = useStyles();

  const uploadMedia = useCallback(
    (a: Head<Parameters<typeof componentActions.uploadMedia>>) => {
      dispatch(componentActions.uploadMedia(a));
    },
    [dispatch]
  );
  const { images } = caseMedia;
  return (
    <Grid
      container
      direction="column"
      className={classes.content}
      data-veritone-component="found-objects-place"
    >
      <div
        style={{ height: '240px', padding: '20px', backgroundColor: '#f2f2f2' }}
        data-testid="person-inderest"
      >
        <PersonOfInterestViewer
          caseId={caseId}
          images={images}
          uploadMedia={uploadMedia}
        />
      </div>
      <Grid
        data-test="video-result-tab"
        data-testid="video-result-tab"
        container
        direction="column"
        // TODO: use styled instead
        // classes={{ root: classes.videoResultsTab }}
      >
        <FacesWrapperTab />
      </Grid>
    </Grid>
  );
};

export default ResultTabsPlace;

import { Before, Given, Then } from '@badeball/cypress-cucumber-preprocessor';
import {
  DataTestSelector,
  deleteTdoByName,
} from '../../../support/helperFunction/landingPageHelper';
Before(() => {
  cy.LoginLandingPage();
});

Given('The user is in Landing Page', () => {
  cy.contains('Upload Media').should('be.visible');
  cy.contains('Sort by').should('be.visible');
  cy.getDataSetCy({ cyAlias: DataTestSelector.TopBar }).contains(
    'Last Modified Date'
  );
  deleteTdoByName(['lucy.mp4']);
});

Then('The Media List loaded successfully', () => {
  cy.getDataIdCy({ idAlias: DataTestSelector.Grid }).within(() => {
    cy.getDataIdCy({ idAlias: DataTestSelector.Checkbox }).should('be.visible');
    cy.getDataIdCy({ idAlias: DataTestSelector.FileName }).should('be.visible');
    cy.getDataSetCy({ cyAlias: DataTestSelector.DateTime }).should(
      'be.visible'
    );
    cy.getDataIdCy({ idAlias: DataTestSelector.Status }).should('be.visible');
  });
});

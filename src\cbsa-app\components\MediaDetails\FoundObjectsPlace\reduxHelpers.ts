import { createSelector } from 'reselect';
import { TreeObjectId } from '@cbsa-modules/universal';
import { addFile } from '@cbsa-modules/appWrapper';
import { selectCaseMedia } from '@common-modules/mediaDetails';
import { selectCurrentRoutePayload } from '@common-modules/routing';

export const componentSelectors = createSelector(
  selectCaseMedia,
  selectCurrentRoutePayload,
  (caseMedia, payload) => ({
    caseMedia,
    caseId: payload.case_id,
    tdoId: payload.tdo_id,
  })
);

export const componentActions = {
  uploadMedia: ({ file, caseId }: { file: File; caseId: TreeObjectId }) =>
    addFile({ file, caseId }),
};

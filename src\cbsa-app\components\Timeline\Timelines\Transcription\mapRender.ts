import Konva from 'konva';
import { map } from 'rxjs/operators';

import { MapRenderProps } from './interfaces';

const MAX_HEIGHT = 32;
const MAX_AUDIOWAVE_FRAMES = 600;

export const mapRender = (layer: Konva.Layer) =>
  map(
    ({
      audiowaves,
      startMs,
      stopMs,
      list,
      selected,
      width,
      timeRange,
    }: MapRenderProps) => {
      layer.destroyChildren();

      const multiplier = width / (stopMs - startMs);

      for (const group of list) {
        if (group) {
          const [startTimeMs, stopTimeMs] = group;
          const x = startTimeMs * multiplier - startMs * multiplier;
          const w = (stopTimeMs - startTimeMs) * multiplier;
          if ((x >= 0 && x <= width) || (w >= 0 && w <= width)) {
            const rect = new Konva.Rect({
              x,
              y: 0,
              width: w,
              height: MAX_HEIGHT,
              fill: 'rgb(255, 193, 7)',
            });
            layer.add(rect);
          }
        }
      }

      selected.forEach((slice) => {
        const [startTimeMs, stopTimeMs] = slice;
        const x = (startTimeMs - startMs) * multiplier;
        const w = (stopTimeMs - startTimeMs) * multiplier;
        if ((x >= 0 && x <= width) || (w >= 0 && w <= width)) {
          const rect = new Konva.Rect({
            x,
            y: 0,
            width: w,
            height: MAX_HEIGHT,
            fill: 'rgb(74, 144, 226)',
            opacity: 0.9,
          });
          layer.add(rect);
        }
      });

      // filter audiowaves to current visible range
      // and reduce # of audiowave frames drawn
      let reducedAudiowaves = audiowaves.filter((currentAudiowave) => {
        const { startTime, stopTime } = currentAudiowave;

        if (startTime > stopMs || stopTime < startMs) {
          return false;
        }
        return true;
      });

      while (reducedAudiowaves.length > MAX_AUDIOWAVE_FRAMES) {
        const evenFrames = reducedAudiowaves.filter((_item, i) => i % 2 === 0);
        const oddFrames = reducedAudiowaves.filter((_item, i) => i % 2 === 1);
        const newAudiowaves: MapRenderProps['audiowaves'] = [];
        for (let i = 0; i < evenFrames.length; i++) {
          // There will always be an even frame, may or may not be an odd frame
          const evenFrame = evenFrames[i]!; // Safe due to length check
          const oddFrame = oddFrames[i];
          if (oddFrame !== undefined) {
            const newMinValue =
              oddFrame.minValue < evenFrame.minValue
                ? oddFrame.minValue
                : evenFrame.minValue;

            const newMaxValue =
              oddFrame.maxValue > evenFrame.maxValue
                ? oddFrame.maxValue
                : evenFrame.maxValue;

            newAudiowaves.push({
              startTime: evenFrame.startTime,
              stopTime: oddFrame.stopTime,
              minValue: newMinValue,
              maxValue: newMaxValue,
            });
          } else {
            newAudiowaves.push(evenFrame);
          }
          reducedAudiowaves = newAudiowaves;
        }
      }
      reducedAudiowaves.forEach((currentAudioframes) => {
        const { startTime, stopTime, minValue, maxValue } = currentAudioframes;
        const x = (startTime - startMs) * multiplier;
        let w = (stopTime - startTime) * multiplier;
        if (w === 0) {
          w = 1;
        }
        // // Logic for symmetrical soundbars
        // let height = Math.abs(maxValue);
        // if (Math.abs(minValue) > height) {
        //   height = Math.abs(minValue);
        // }
        // height = height * MAX_HEIGHT;

        // logic for min-max soundbars
        const height = ((maxValue - minValue) * MAX_HEIGHT) / 2;

        const rect = new Konva.Rect({
          x,
          // // logic for symmetrical soundbars
          // y: (MAX_HEIGHT - height) / 2,
          // logic for min-max soundbars
          y: ((1 + minValue) * MAX_HEIGHT) / 2,
          width: w,
          height,
          opacity: 0.6,
          fill: 'rgb(255, 0 , 0)',
        });
        layer.add(rect);
      });

      // use for Transcription has not been run
      if (selected.length === 0) {
        const startTimeMs = timeRange[0];
        const stopTimeMs = timeRange[1];
        const x = startTimeMs * multiplier - startMs * multiplier;
        const w = (stopTimeMs - startTimeMs) * multiplier;
        if ((x >= 0 && x <= width) || (w >= 0 && w <= width)) {
          const rect = new Konva.Rect({
            x,
            y: 0,
            width: w,
            height: MAX_HEIGHT,
            fill: 'rgb(74, 144, 226)',
            opacity: 0.9,
          });
          layer.add(rect);
        }
      }

      return layer;
    }
  );

import { isEqual } from 'lodash';
import { useEffect } from 'react';
import { useStyles } from './styles';
import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import type { JobStatus } from 'veritone-types';
import { CircularProgress, ThemeProvider } from '@mui/material';
import { SummaryTDO } from '@cbsa-modules/universal';
import Dialog from '@common-components/ConfirmDialog';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const ExportCaseDialogContent = ({
  exportTdo,
  redactedTdos,
  createExportTdo,
  startExportJob,
  isOpen,
  onConfirm,
  onClose,
}: Props) => {
  const classes = useStyles();
  const intl = useIntl();
  const status = exportTdo?.jobs?.records?.[0]?.status;
  const downloadUrl = exportTdo?.assets?.records[0]?.signedUri ?? null;

  useEffect(() => {
    if (isOpen) {
      if (exportTdo === undefined) {
        createExportTdo();
      } else {
        const exportedTdos = Object.keys(
          exportTdo?.assets?.records?.[0]?.details?.tdos ?? {}
        );
        if (!isEqual(exportedTdos, redactedTdos)) {
          startExportJob();
        }
      }
    }
  }, [createExportTdo, exportTdo, isOpen, redactedTdos, startExportJob]);

  const renderStatus = (status: JobStatus | undefined) =>
    ({
      pending: (
        <div className={classes.export}>
          <CircularProgress thickness={2} size={30} />
          {I18nTranslate.TranslateMessage('exportPending')}
        </div>
      ),
      running: (
        <div className={classes.export}>
          <CircularProgress thickness={2} size={30} />
          {I18nTranslate.TranslateMessage('exportRunning')}
        </div>
      ),
      complete: I18nTranslate.TranslateMessage('exportComplete'),
      cancelled: I18nTranslate.TranslateMessage('exportFailed'),
      queued: I18nTranslate.TranslateMessage('exportFailed'),
      failed: I18nTranslate.TranslateMessage('exportFailed'),
      default: (
        <div className={classes.export}>
          <CircularProgress thickness={2} size={30} />
          {I18nTranslate.TranslateMessage('exportDefault')}
        </div>
      ),
    })[status || 'default'];

  return (
    <Dialog
      isOpen={isOpen}
      title={I18nTranslate.TranslateMessage('exportCaseTitle')}
      content={renderStatus(status)}
      confirmText={
        downloadUrl && status === 'complete'
          ? intl.formatMessage({ id: 'download' })
          : undefined
      }
      onConfirm={
        downloadUrl
          ? () => {
              window.location.href = downloadUrl;
              onConfirm();
            }
          : undefined
      }
      cancelText={intl.formatMessage({ id: 'cancel' })}
      onCancel={onClose}
    />
  );
};

interface Props {
  readonly exportTdo?: SummaryTDO;
  readonly redactedTdos: string[];
  readonly createExportTdo: () => void;
  readonly startExportJob: () => void;
  readonly isOpen: boolean;
  readonly onConfirm: () => void;
  readonly onClose: () => void;
}

const ExportCaseDialog = ({
  exportTdo,
  redactedTdos,
  createExportTdo,
  startExportJob,
  isOpen,
  onConfirm,
  onClose,
}: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <ExportCaseDialogContent
      exportTdo={exportTdo}
      redactedTdos={redactedTdos}
      createExportTdo={createExportTdo}
      startExportJob={startExportJob}
      isOpen={isOpen}
      onConfirm={onConfirm}
      onClose={onClose}
    />
  </ThemeProvider>
);

export default ExportCaseDialog;

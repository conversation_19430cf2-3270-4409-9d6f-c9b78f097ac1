import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import {
  FetchLatestTDOAuditLogAssetResponse,
  SendEventResponse,
} from '../services';
import { createAction } from '@reduxjs/toolkit';

export const LOG_AUDIT_EVENT = createAction<AuditLogEventPayload>(
  'vtn-app-redact/LOG_AUDIT_EVENT'
);
export const LOG_AUDIT_EVENT_INIT = 'vtn-app-redact/LOG_AUDIT_EVENT_INIT';
export const LOG_AUDIT_EVENT_SUCCESS =
  createGraphQLSuccessAction<SendEventResponse>(
    'vtn-app-redact/LOG_AUDIT_EVENT_SUCCESS'
  );
export const LOG_AUDIT_EVENT_FAILURE = createGraphQLFailureAction(
  'vtn-app-redact/LOG_AUDIT_EVENT_FAILURE'
);

export type AuditLogEventPayload = string;
export const actionLogAuditEvent = (payload: AuditLogEventPayload) =>
  LOG_AUDIT_EVENT(payload);

export const FETCH_LATEST_TDO_AUDIT_LOG_ASSET =
  'vtn-app-redact/FETCH_LATEST_TDO_AUDIT_LOG_ASSET';
export const FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS =
  createGraphQLSuccessAction<FetchLatestTDOAuditLogAssetResponse>(
    'vtn-app-redact/FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS'
  );
export const FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE =
  createGraphQLFailureAction(
    'vtn-app-redact/FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE'
  );

import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';
import Button from './components/ConfirmDialogButtons';
import makeStyles from '@mui/styles/makeStyles';
import CloseIcon from '@mui/icons-material/Close';
import React, { CSSProperties, useCallback, useEffect, useState } from 'react';
import {
  FormControl,
  FormControlLabel,
  FormLabel,
  Radio,
  RadioGroup,
  ThemeProvider,
  Tooltip,
} from '@mui/material';
import { I18nTranslate } from '@i18n';
import { defaultTheme } from '@cbsa/styles/materialThemes';
import { GroupShape } from '@redact/components/MediaDetailsPage/Editor/MediaPlayer/Dialogs/ConfirmWithMergeGroupDialog';
import useClosePopup from '@common/state/hooks/useClosePopup';

const useStyles = makeStyles((theme) => ({
  title: {
    background: theme.palette.primary.light,
    color: theme.palette.primary.main,
    fontSize: theme.spacing(2.2),
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  center: {
    textAlign: 'center',
  },
  contents: {
    display: 'flex',
    flexDirection: 'column',
    gap: '20px',
    fontSize: '14px',
    fontWeight: 'bold',
    padding: '20px 35px !important',
    minWidth: '450px',
  },
  actions: {
    background: theme.palette.grey[100],
    display: 'flex',
    flexDirection: 'row-reverse',
    padding: '25px',
    gap: '30px',
  },
  radioTitle: {
    fontSize: '14px',
    fontWeight: 'bold',
    color: theme.palette.text.primary,
  },
  warningTitle: {
    color: '#910D0D',
    fontSize: '16px',
    fontWeight: 'bold',
    margin: 0,
  },
}));

const ConfirmDialogContent = ({
  isOpen,
  title,
  content,
  confirmText,
  onConfirm,
  disableConfirmButton,
  cancelText,
  onCancel,
  warning,
  dialogClasses,
  showClose = false,
  titleCenter = false,
  containerStyle,
  extraOptions,
  shapeType,
}: Props) => {
  const classes = useStyles();

  const [groupShapeType, setGroupShapeType] = useState<GroupShape | undefined>(
    undefined
  );

  useClosePopup({ handleCloses: [onCancel] });

  useEffect(() => setGroupShapeType(shapeType), [shapeType]);

  const getTitleClasses = useCallback(() => {
    const titleClasses = [classes.title];
    if (titleCenter) {
      titleClasses.push(classes.center);
    }
    return titleClasses;
  }, [classes.center, classes.title, titleCenter]);

  const isGroupShape = (type: string): type is GroupShape =>
    ['ellipse', 'rectangle', 'default'].includes(type);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (isGroupShape(value)) {
      setGroupShapeType(value);
    }
  };

  return (
    <Dialog
      data-testid="confirm-dialog"
      open={isOpen}
      maxWidth={false}
      classes={dialogClasses}
      style={containerStyle}
      onClose={onCancel}
      container={document.fullscreenElement}
    >
      <DialogTitle
        data-testid="confirm-dialog-title"
        classes={{
          root: getTitleClasses().join(' '),
        }}
      >
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Tooltip title={title}>
            <Box
              flexGrow={1}
              sx={{
                maxWidth: '568px',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textAlign: 'left',
              }}
            >
              {title}
            </Box>
          </Tooltip>
          <Box>
            {showClose && (
              <IconButton onClick={onCancel} size="large">
                <CloseIcon fontSize="small" />
              </IconButton>
            )}
          </Box>
        </Box>
      </DialogTitle>
      <DialogContent
        data-testid="confirm-dialog-content"
        classes={{ root: classes.contents }}
      >
        {groupShapeType && (
          <>
            <p className={classes.warningTitle}>
              {I18nTranslate.TranslateMessage('warningAssignMergedGroup')}
            </p>
            <FormControl>
              <FormLabel
                id="demo-row-radio-buttons-group-label"
                className={classes.radioTitle}
              >
                Shape Type
              </FormLabel>
              <RadioGroup
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
                onChange={handleChange}
              >
                <FormControlLabel
                  value="ellipse"
                  control={<Radio checked={groupShapeType === 'ellipse'} />}
                  label="Ellipse"
                />
                <FormControlLabel
                  value="rectangle"
                  control={<Radio checked={groupShapeType === 'rectangle'} />}
                  label="Rectangle"
                />
                <FormControlLabel
                  value="default"
                  control={<Radio checked={groupShapeType === 'default'} />}
                  label="Object Type Default Shape"
                />
              </RadioGroup>
            </FormControl>
          </>
        )}
        {content}
      </DialogContent>
      <DialogActions
        data-testid="confirm-dialog-actions"
        classes={{ root: classes.actions }}
      >
        {confirmText && onConfirm && (
          <Button
            disabled={disableConfirmButton}
            variant={warning ? 'warning' : 'primary'}
            onClick={onConfirm}
          >
            {confirmText}
          </Button>
        )}
        {extraOptions &&
          extraOptions.map((option) => (
            <Button
              key={option.title}
              variant={option.variant || (warning ? 'warning' : 'primary')}
              onClick={() => option.handelClick(groupShapeType)}
            >
              {option.title}
            </Button>
          ))}
        <Button variant="secondary" onClick={onCancel}>
          {cancelText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

interface Props {
  readonly isOpen: boolean;
  readonly title: string | React.JSX.Element;
  readonly content: string | React.JSX.Element;
  readonly confirmText?: string | React.JSX.Element;
  readonly onConfirm?: () => void;
  readonly disableConfirmButton?: boolean;
  readonly cancelText: string | React.JSX.Element;
  readonly onCancel: () => void;
  readonly warning?: boolean;
  readonly dialogClasses?: DialogProps['classes'];
  readonly showClose?: boolean;
  readonly titleCenter?: boolean;
  readonly containerStyle?: CSSProperties;
  readonly extraOptions?: {
    title: string;
    variant?: 'primary' | 'secondary' | 'warning';
    handelClick: (shapeType?: GroupShape) => void;
  }[];
  readonly shapeType?: GroupShape;
}

const ConfirmDialog = ({
  isOpen,
  title,
  content,
  confirmText,
  onConfirm,
  disableConfirmButton,
  cancelText,
  onCancel,
  warning,
  dialogClasses,
  showClose = false,
  titleCenter = false,
  containerStyle,
  extraOptions,
  shapeType,
}: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <ConfirmDialogContent
      isOpen={isOpen}
      title={title}
      content={content}
      confirmText={confirmText}
      onConfirm={onConfirm}
      disableConfirmButton={disableConfirmButton}
      cancelText={cancelText}
      onCancel={onCancel}
      warning={warning}
      dialogClasses={dialogClasses}
      showClose={showClose}
      titleCenter={titleCenter}
      containerStyle={containerStyle}
      extraOptions={extraOptions}
      shapeType={shapeType}
    />
  </ThemeProvider>
);

export default ConfirmDialog;

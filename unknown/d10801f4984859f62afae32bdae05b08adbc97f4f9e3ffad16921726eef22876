.case {
  gap: 10px;
  display: flex;
  cursor: pointer;
  align-items: center;

  &Folder {
    font-size: 30px;
    margin: 5px 0;

    &.closed {
      filter: invert(24%) sepia(1%) saturate(4450%) hue-rotate(335deg)
        brightness(78%) contrast(61%);
    }
  }

  &Name {
    color: #4e4e4e;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;

    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;

    &.selected {
      color: #005c7e;
      font-weight: bold;
    }
  }

  &:hover {
    .case {
      &Folder {
        &.closed {
          filter: invert(15%) sepia(100%) saturate(3028%) hue-rotate(181deg)
            brightness(91%) contrast(101%);
        }
      }

      &Name {
        color: #005c7e;
        font-weight: bold;
      }
    }
  }
}

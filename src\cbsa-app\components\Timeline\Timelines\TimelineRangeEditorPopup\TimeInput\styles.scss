.timeInputTextHelper {
  font-size: 10px;
  margin-left: 0.15rem;
}

.groupInput {
  display: flex;
  width: 100%;
}

.groupInput > input {
  width: 25px;
  font-size: 14px;
  border-radius: 2px;
  border: 1px solid #eee;
  text-align: center;
  &:last-child {
    width: 35px;
  }
}

.groupInput > input[type='number']::-webkit-inner-spin-button,
.groupInput > input[type='number']::-webkit-outer-spin-button {
  appearance: none;
  margin: 0;
}

.groupInput > input:focus {
  outline: none;
  border: 1px solid #cce;
}

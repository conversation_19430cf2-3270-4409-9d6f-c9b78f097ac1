Feature: Redaction code page

  Background: Navigate to Redaction Code
    Given The user is on the Redaction Code screen

  @e2e @redaction-code @regression
  Scenario: Verify user can create a new redaction code
    Given The user deletes redaction code "Cypress Code Name 1" if exists
    When The user clicks on Add New Code button
    Then Redaction code sidebar should be displayed
    When The user inputs Redaction code with code name: "Cypress Code Name 1", description: "Cypress Test" and code number: "4MF93845"
    And User clicks on Add button
    Then The user sees the notification message "Saved Redaction Code successfully."
    Then Redaction code "Cypress Code Name 1" should be displayed

  @e2e @redaction-code @regression
  Scenario: Verify user can edit a redaction code
    Given The user deletes redaction code "Cypress Code Name Edited 1" if exists
    And The user deletes redaction code "Cypress Code Name Edited 2" if exists
    When The user clicks on Add New Code button
    And The user inputs Redaction code with code name: "Cypress Code Name Edited 1", description: "Cypress Test Edit" and code number: "4MF93846"
    And User clicks on Add button
    Then Redaction code "Cypress Code Name Edited 1" should be displayed
    When The user clicks the "Edit" button
    Then The Redaction code should be displayed with code name: "Cypress Code Name Edited 1", description: "Cypress Test Edit" and code number: "4MF93846"
    When The user inputs Redaction code with code name: "Cypress Code Name Edited 2", description: "Cypress Test Edit 2" and code number: "4MF93847"
    And User clicks on Add button
    Then The user sees the notification message "Saved Redaction Code successfully."
    And Redaction code "Cypress Code Name Edited 2" should be displayed
    When The user clicks the "Edit" button
    Then The Redaction code should be displayed with code name: "Cypress Code Name Edited 2", description: "Cypress Test Edit 2" and code number: "4MF93847"

  @e2e @redaction-code @regression
  Scenario: Verify user can delete a redaction code
    Given The user deletes redaction code "Cypress Code Name Delete 1" if exists
    When The user clicks on Add New Code button
    And The user inputs Redaction code with code name: "Cypress Code Name Delete 1", description: "Cypress Test Delete" and code number: "4MF93849"
    And User clicks on Add button
    Then The user sees the notification message "Saved Redaction Code successfully."
    Then Redaction code "Cypress Code Name Delete 1" should be displayed
    When The user clicks the "Delete" button
    Then Delete Redaction dialog is shown
    And The user clicks on "Delete" confirm button
    Then The user sees the notification message "Deleted redaction code successfully."

  @e2e @redaction-code @regression
  Scenario: Verify user can't create duplicate code
    Given The user deletes redaction code "Cypress Code Name Duplicate 1" if exists
    When The user clicks on Add New Code button
    And The user inputs Redaction code with code name: "Cypress Code Name Duplicate 1", description: "Cypress Test Duplicate" and code number: "4MF93850"
    And User clicks on Add button
    Then The user sees the notification message "Saved Redaction Code successfully."
    And Redaction code "Cypress Code Name Duplicate 1" should be displayed
    When The user clicks on Add New Code button
    And The user inputs Redaction code with code name: "Cypress Code Name Duplicate 1", description: "Cypress Test Duplicate" and code number: "4MF93850"
    And User clicks on Add button
    Then The user sees the notification message "Failed to save. Looks like Redaction Code or Code Name already exists. Please verify and use different Code or Code Name."

  @e2e @redaction-code @regression
  Scenario: Verify user can sort redaction code by <label> <sortedBy>
    Given The instructor presses the <label> sort button to sort by <sortedBy>
    Then <label> is sorted by <sortedBy>

    Examples:
      | label           | sortedBy |
      | 'Code Name'     | 'a-z'    |
      | 'Code Name'     | 'z-a'    |
      | 'Code'          | 'a-z'    |
      | 'Code'          | 'z-a'    |
      | 'Description'   | 'a-z'    |
      | 'Description'   | 'z-a'    |
      | 'Code Color'    | 'a-z'    |
      | 'Code Color'    | 'z-a'    |
      | 'Date Added'    | 'a-z'    |
      | 'Date Added'    | 'z-a'    |
      | 'Date Modified' | 'z-a'    |
      | 'Date Modified' | 'a-z'    |

  @e2e @redaction-code @regression
  Scenario: Verify user can interact with the scroll bar on the Redaction Code page
    Given The user selects Row per page to 20
    When The total number of rows displayed is up to 20
    When The user moves the navigation bar "down"
    Then The navigation bar should be scrolled "down"
    When The user moves the navigation bar "up"
    Then The navigation bar should be scrolled "up"

  @e2e @redaction-code @regression
  Scenario: Verify the "Rows per page" functionality on the Redaction Code page
    Given The user selects Row per page to 20
    When The total number of rows displayed is up to 20
    Given The user selects Row per page to 100
    When The total number of rows displayed is up to 100

  @e2e @redaction-code @regression
  Scenario: Verify pagination functionality on the Redaction Code page
    When The user clicks the ">" button in the pagination section
    Then The user should go to page: "11-20"
    When The user clicks the "<" button in the pagination section
    Then The user should go to page: "1-10"

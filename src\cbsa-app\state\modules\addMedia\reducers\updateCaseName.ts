import { Re } from '../reducers';

export const updateCaseName: Re = (state) => ({
  ...state,
  loaders: {
    ...state.loaders,
    isUpdatingCaseName: true,
  },
});

export const updateCaseNameSuccess: Re<{
  readonly createStructuredData: { data: { caseName: string } };
}> = (state, { payload }) => ({
  ...state,
  caseDetails: state.caseDetails
    ? {
        ...state.caseDetails,
        name: payload.createStructuredData.data.caseName,
      }
    : null,
  loaders: {
    ...state.loaders,
    isUpdatingCaseName: false,
  },
});

export const updateCaseNameFailure: Re = (state) => ({
  ...state,
  loaders: {
    ...state.loaders,
    isUpdatingCaseName: false,
  },
});

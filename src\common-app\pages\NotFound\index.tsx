import { useDispatch } from 'react-redux';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { ThemeProvider } from '@mui/material';

import AppContainer from '@common-components/AppContainer';
import errorLoading from '@resources/images/error-loading.svg';
import theme from '@redact/materialUITheme';
import { goToMainPage } from '@common-modules/mediaDetails';
import { I18nTranslate } from '@i18n';
import { useStyles } from './styles';

const NotFoundContent = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  return (
    <AppContainer>
      <div className={classes.container} data-testid="app-container">
        <img src={errorLoading} className={classes.bigImage} />
        <Typography
          variant="h5"
          className={classes.headline}
          data-testid="typography"
        >
          {I18nTranslate.TranslateMessage('pageNotFound')}
        </Typography>
        <Typography
          variant="subtitle1"
          color="textSecondary"
          className={classes.message}
          data-testid="typography"
        >
          {I18nTranslate.TranslateMessage('pageNotFoundDes')}
        </Typography>
        <Button
          className={classes.actionButton}
          variant="contained"
          color="primary"
          onClick={() => {
            dispatch(goToMainPage());
          }}
          data-veritone-element="not-found-home-button"
        >
          {I18nTranslate.TranslateMessage('home')}
        </Button>
      </div>
    </AppContainer>
  );
};

const NotFound = () => (
  <ThemeProvider theme={theme}>
    <NotFoundContent />
  </ThemeProvider>
);
export default NotFound;

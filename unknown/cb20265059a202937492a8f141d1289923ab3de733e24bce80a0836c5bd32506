import { combineReducers } from '@reduxjs/toolkit';
import { modules } from '@veritone/glc-redux';
import { operationReducer } from 'video-react';
import { extendedPlayerReducer } from '@common-state/modules/player';
const { auth, user, uiState, config } = modules;
const { namespace: userNamespace, reducer: userReducer } = user;
// TODO: Fix types
// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
const { namespace: authNamespace, reducer: authReducer } = auth;

/* Common Imports */
import appReducer, { namespace as appNamespace } from '@common-modules/app';
import ingestionReducer from '@common-modules/engines/ingestion/reducers';
import { namespace as ingestionNamespace } from '@common-modules/engines/ingestion/store';
import opticalTrackingReducer from '@common-modules/engines/optical-tracking/reducers';
import { namespace as opticalTrackingNamespace } from '@common-modules/engines/optical-tracking/store';
import transcriptionReducer from '@common-modules/engines/transcription/reducers';
import { namespace as transcriptionNamespace } from '@common-modules/engines/transcription/store';
import {
  mediaDetailsReducer,
  namespace as mediaDetailsNamespace,
} from '@common-modules/mediaDetails';
import filePickerReducer, {
  namespace as filePickerNamespace,
} from '@common-modules/filePicker';
import {
  notificationReducer,
  namespace as notificationNamespace,
} from '@common-modules/notification';
import trimReducer, { namespace as trimNamespace } from '@common-modules/trim';
import redactFileReducer, {
  namespace as redactFileNamespace,
} from '@common-modules/redactFile';
import {
  snackbarReducer,
  namespace as snackbarNamespace,
} from '@common-modules/snackbar';
import { enablePatches } from 'immer';
enablePatches();

// TODO: Can we type this better, Reducer<any> has some issues currently, needs further investigation
export default (extraReducers: Record<string, any>) =>
  // TODO: Fix types
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  combineReducers({
    // TODO: Fix
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    [uiState.namespace]: uiState.reducer,
    // TODO: Fix
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    [authNamespace]: authReducer,
    [userNamespace]: userReducer,
    player: extendedPlayerReducer,
    operation: operationReducer,

    /* Common Reducers */
    [appNamespace]: appReducer,
    [ingestionNamespace]: ingestionReducer,
    [opticalTrackingNamespace]: opticalTrackingReducer,
    [transcriptionNamespace]: transcriptionReducer,
    [mediaDetailsNamespace]: mediaDetailsReducer,
    [filePickerNamespace]: filePickerReducer,
    [notificationNamespace]: notificationReducer,
    [trimNamespace]: trimReducer,
    [redactFileNamespace]: redactFileReducer,
    [snackbarNamespace]: snackbarReducer,

    [config.namespace]: (state = window.config ?? {}) => state, // fixme?
    ...extraReducers,
  });

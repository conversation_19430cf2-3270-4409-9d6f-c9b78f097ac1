import { modules } from '@veritone/glc-redux';

import { userPermissionsSagas } from '@user-permissions';

import appSaga from '../modules/app/sagas';
import { saga as ingestionSaga } from '../modules/engines/ingestion/saga';
import { saga as opticalTrackingSaga } from '../modules/engines/optical-tracking/saga';
import { saga as transcriptionSaga } from '../modules/engines/transcription/saga';
import {
  watchCreateJobFacesData,
  watchCreateJobFacesSuccess,
  // watchLoadFacesStatus,
  // watchReLoadFacesStatus,
} from '../modules/facesTabModule/saga';
import filePickerRootSaga from '@common-modules/filePicker/saga';
import keyboardSagas from '@redact-modules/keyboard/saga';
// import {
//   watchCreateJobLicensePlatesData,
//   watchLoadLicensePlatesStatus,
// } from '@redact-modules/licensePlatesTabModule/saga';
import { initMediaDetails } from '../modules/mediaDetails/saga';
import { initNotification } from '@common-modules/notification/saga';
// import metricsSagas from '@redact-modules/metrics';
import overviewSaga from '@redact-modules/overview/saga';
import routeSaga from '@common-modules/routing/sagas';
import { uploadsSaga } from '@redact-modules/uploads/sagas';
import trimSaga from '@common-modules/trim/saga';
import { initRedactFile } from '@common-modules/redactFile/saga';
import { videoReactSaga } from '@common-modules/player/saga';
const {
  auth: { authRootSaga },
} = modules;

// these are sagas that should run on *all* routes, at app startup.
// define route-specific sagas on the routesMap
export const sagas = [
  userPermissionsSagas,
  routeSaga,
  appSaga,
  authRootSaga,
  watchCreateJobFacesData,
  watchCreateJobFacesSuccess,
  // watchLoadFacesStatus,
  // watchReLoadFacesStatus,
  // watchCreateJobLicensePlatesData,
  // watchLoadLicensePlatesStatus,
  overviewSaga,
  filePickerRootSaga,
  uploadsSaga,
  ingestionSaga,
  opticalTrackingSaga,
  transcriptionSaga,
  initMediaDetails,
  keyboardSagas,
  // metricsSagas,
  trimSaga,
  initRedactFile,
  initNotification,
  videoReactSaga,
];

import express from 'express';
import { isEmpty, pick } from 'lodash';
import { checkNameExistsAdapter } from '../adapters/checkNameExists';
import { getRootFolderAdapter } from '../adapters/getRootFolder';
import { checkFolderExistsAdapter } from '../adapters/checkFolderExists';
import { createCaseAdapter } from '../adapters/createCase';
import { Messages } from '../errors/messages';
import { StatusCodes } from '../errors/statusCodes';
import { getUserInfoByTokenAdapter } from '../adapters/getUserInfoByToken';
import { isAPITokenKey, isFolderId } from '../validations/helpers';
import { FolderId } from '../model/brands';

export const createCase = async (
  req: express.Request,
  res: express.Response
) => {
  const headers = pick(req.headers, ['authorization']);
  let userInfo = undefined;
  if(isAPITokenKey(headers)) {
    userInfo = await getUserInfoByTokenAdapter(headers);
    if (userInfo?.error) {
      return res.status(StatusCodes.BadRequest).json({
        error: userInfo.error,
      });
    }
    if (isEmpty(userInfo?.userId)) {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.FetchUserInfoFail
      });
    }
  }

  const { parentFolderId, name, description } = req.body;
  let isNameExist = false;
  let rootFolderId;

  if (isEmpty(name?.trim())) {
    return res.status(StatusCodes.BadRequest).json({
      error: Messages.NameIsRequired
    });
  }

  if (!isFolderId(parentFolderId)) {
    rootFolderId = (await getRootFolderAdapter(headers) || '') as FolderId; // Is it safe to set '' as FolderId?
    isNameExist = await checkNameExistsAdapter(headers, { folderId: rootFolderId, name: name});
  } else {
    const folder = await checkFolderExistsAdapter( headers, { folderId: parentFolderId} );
    if (folder) {
      const isCase = (folder.contentTemplates?.length || 0) > 0;
      const isFolderDepthInLimit = (folder?.folderPath?.length || 0) < 4;
      if (isCase) {
        return res.status(StatusCodes.BadRequest).json({
          error: Messages.InvalidParentFolderId,
        });
      }
      if (!isFolderDepthInLimit ) {
        return res.status(StatusCodes.BadRequest).json({
          error: Messages.FolderDepthLimitReached,
        });
      }
      isNameExist = await checkNameExistsAdapter(headers, { folderId: parentFolderId, name: name});
    }
    else {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.ParentFolderNotExists
      });
    }
  }
  if (isNameExist) {
    return res.status(StatusCodes.BadRequest).json({
      error: Messages.NameExistsAlready,
    });
  } else {
    const caseId = await createCaseAdapter(headers, {
      parentFolderId: parentFolderId || rootFolderId, name: name, description: description, userId: userInfo?.userId});
    if (caseId) {
      return res.status(StatusCodes.InsertedSuccess).json({
        name: name,
        caseId: caseId,
      });
    } else {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.createCaseFail,
      });
    }
  }
};


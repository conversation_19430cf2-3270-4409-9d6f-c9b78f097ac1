import { TDOId } from '@common-modules/universal/models/Brands';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { createAction } from '@reduxjs/toolkit';
import {
  CancelJobServiceResponse,
  QueryEngineResultsServiceResponse,
} from './services';
import type { JobStatus } from 'veritone-types';

export const NOOP = 'noop';

/**
 * Begins the process of starting an Optical Tracking engine job.
 * Includes polling and loading results.
 */
export const PROCESS_ENGINE_REQUEST = createAction<{
  tdoId: TDOId;
  tdoName: string;
  payload: {
    targetId: string;
    url: string;
  };
}>('vtn/redact/engine/transcription/PROCESS_ENGINE_REQUEST');
export const PROCESS_ENGINE_REQUEST_SUCCESS = createAction<{
  jobId: string;
  tdoId: TDOId;
  // jobStatus: string; // TODO: Validate this isn't used
}>('vtn/redact/engine/transcription/PROCESS_ENGINE_REQUEST_SUCCESS');
export const PROCESS_ENGINE_REQUEST_FAILURE = createAction(
  'vtn/redact/engine/transcription/PROCESS_ENGINE_REQUEST_FAILURE',
  (tdoId: TDOId, /* jobId: string,*/ errorMsg: string) => ({
    payload: {
      tdoId,
      // jobId, // TODO: Verify and fix
      errorMsg,
    },
    error: true,
  })
);

/**
 *
 * @param {{
 *   tdoId: TDOId;
 *   tdoName: string;
 *   targetId: string;
 *   url: string;
 * }} request
 */
export const processEngineRequestAction = ({
  tdoId,
  tdoName,
  targetId,
  url,
}: {
  tdoId: TDOId;
  tdoName: string;
  targetId: string;
  url: string;
}) => PROCESS_ENGINE_REQUEST({ tdoId, tdoName, payload: { targetId, url } });

export const processEngineRequestSuccessAction = ({
  tdoId,
  jobId,
}: {
  jobId: string;
  tdoId: TDOId;
}) => PROCESS_ENGINE_REQUEST_SUCCESS({ tdoId, jobId });

export const processEngineRequestFailureAction = ({
  tdoId,
  // jobId,
  errorMsg,
}: {
  tdoId: TDOId;
  // jobId: string;
  errorMsg: string;
}) => PROCESS_ENGINE_REQUEST_FAILURE(tdoId, errorMsg);

/**
 * Create engine job actions.
 */

export const CREATE_ENGINE_JOB = createAction<{
  tdoId: TDOId;
  tdoName: string;
  payload: { targetId: string; url: string };
}>('vtn/redact/engine/transcription/CREATE_ENGINE_JOB');
export const CREATE_ENGINE_JOB_SUCCESS = createGraphQLSuccessAction<{
  createJob: {
    id: string;
    targetId: TDOId;
    status: string;
  };
}>('vtn/redact/engine/transcription/CREATE_ENGINE_JOB_SUCCESS');
export const CREATE_ENGINE_JOB_FAILURE = createGraphQLFailureAction<
  any,
  { id: TDOId; tdoName: string },
  true
>('vtn/redact/engine/transcription/CREATE_ENGINE_JOB_FAILURE');

/**
 *
 * @param {{ tdoId: TDOId; targetId: string; url: string; }} payload
 */
export const createEngineJobAction = ({
  tdoId,
  tdoName,
  payload,
}: {
  tdoId: TDOId;
  tdoName: string;
  payload: { targetId: string; url: string };
}) => CREATE_ENGINE_JOB({ tdoId, tdoName, payload });

/**
 * Check job status actions.
 */

export const CHECK_JOB_STATUS = createAction<{
  jobId: string;
  tdoId: TDOId;
}>('vtn/redact/engine/transcription/CHECK_JOB_STATUS');
export const CHECK_JOB_STATUS_SUCCESS = createGraphQLSuccessAction<{
  job: {
    id: string;
    targetId: TDOId;
    status: JobStatus;
  };
}>('vtn/redact/engine/transcription/CHECK_JOB_STATUS_SUCCESS');
export const CHECK_JOB_STATUS_FAILURE = createGraphQLFailureAction(
  'vtn/redact/engine/transcription/CHECK_JOB_STATUS_FAILURE'
);

export const checkJobStatusAction = ({
  tdoId,
  jobId,
}: {
  jobId: string;
  tdoId: TDOId;
}) => CHECK_JOB_STATUS({ tdoId, jobId });

/**
 * Start and stop polling job status actions.
 */

export const START_POLLING_ENGINE_RESULTS = createAction<{
  jobId: string;
  tdoId: TDOId;
}>('vtn/redact/engine/transcription/START_POLLING_ENGINE_RESULTS');

export const STOP_POLLING_ENGINE_RESULTS = createAction<{
  tdoId: TDOId;
  jobId: string;
}>('vtn/redact/engine/transcription/STOP_POLLING_ENGINE_RESULTS');

export const startPollingEngineResultsAction = ({
  tdoId,
  jobId,
}: {
  jobId: string;
  tdoId: TDOId;
}) => START_POLLING_ENGINE_RESULTS({ tdoId, jobId });

export const stopPollingEngineResultsAction = ({
  tdoId,
  jobId,
}: {
  jobId: string;
  tdoId: TDOId;
}) => STOP_POLLING_ENGINE_RESULTS({ tdoId, jobId });

/**
 * Actions to get the metadata for engine results.
 */

export const QUERY_ENGINE_RESULTS = createAction<{
  tdoId: TDOId;
  engines: string[];
  mediaDuration: number;
}>('vtn/redact/engine/transcription/QUERY_ENGINE_RESULTS');
export const QUERY_ENGINE_RESULTS_SUCCESS =
  createGraphQLSuccessAction<QueryEngineResultsServiceResponse>(
    'vtn/redact/engine/transcription/QUERY_ENGINE_RESULTS'
  );
export const QUERY_ENGINE_RESULTS_FAILURE = createGraphQLFailureAction(
  'vtn/redact/engine/transcription/QUERY_ENGINE_RESULTS'
);

export const queryEngineResultsAction = ({
  tdoId,
  engineId,
  mediaDuration,
}: {
  tdoId: TDOId;
  engineId: string;
  mediaDuration: number;
}) => QUERY_ENGINE_RESULTS({ tdoId, engines: [engineId], mediaDuration });

/**
 * Actions to get the metadata for engine results.
 */

// export const QUERY_ENGINE_RESULTS_RECORD =
//   'vtn/redact/engine/transcription/QUERY_ENGINE_RESULTS_RECORD';
// export const QUERY_ENGINE_RESULTS_RECORD_SUCCESS =
//   'vtn/redact/engine/transcription/QUERY_ENGINE_RESULTS_RECORD';
// export const QUERY_ENGINE_RESULTS_RECORD_FAILURE =
//   'vtn/redact/engine/transcription/QUERY_ENGINE_RESULTS_RECORD';

// export const queryEngineResultsRecordAction = ({ tdoId }) => ({
//   type: QUERY_ENGINE_RESULTS_RECORD,
//   payload: { tdoId },
// });

/**
 * Actions to actually load the result data.
 */

// export const GET_ENGINE_RESULTS_FROM_URI =
//   'vtn/redact/engine/transcription/GET_ENGINE_RESULTS_FROM_URI';
// export const GET_ENGINE_RESULTS_FROM_URI_SUCCESS =
//   'vtn/redact/engine/transcription/GET_ENGINE_RESULTS_FROM_URI_SUCCESS';
// export const GET_ENGINE_RESULTS_FROM_URI_FAILURE =
//   'vtn/redact/engine/transcription/GET_ENGINE_RESULTS_FROM_URI_FAILURE';

// export const getEngineResultsFromURIAction = ({ signedUri }) => ({
//   type: GET_ENGINE_RESULTS_FROM_URI,
//   payload: { signedUri },
// });

/**
 * Cancel a job actions.
 */

export const CANCEL_JOB = createAction<{
  jobId: string;
  tdoId: TDOId;
}>('vtn/redact/engine/transcription/CANCEL_JOB');
export const CANCEL_JOB_SUCCESS =
  createGraphQLSuccessAction<CancelJobServiceResponse>(
    'vtn/redact/engine/transcription/CANCEL_JOB_SUCCESS'
  );
export const CANCEL_JOB_FAILURE = createGraphQLFailureAction(
  'vtn/redact/engine/transcription/CANCEL_JOB_FAILURE'
);

export const cancelJobAction = ({
  tdoId,
  jobId,
}: {
  jobId: string;
  tdoId: TDOId;
}) => CANCEL_JOB({ tdoId, jobId });

import {
  Button,
  DialogActions,
  DialogContent,
  DialogContentText,
  ThemeProvider,
} from '@mui/material';
import { Fragment } from 'react';

import { buttonTheme } from '@redact/materialUITheme';
import * as styles from './styles.scss';

const ConfirmView = ({ onClose }: ConfirmViewPropTypes) => (
  <Fragment>
    <DialogContent className={styles.closeDialogContent}>
      <DialogContentText>Your invitation has been sent</DialogContentText>
    </DialogContent>
    <DialogActions className={styles.closeDialogAction}>
      <ThemeProvider theme={buttonTheme}>
        <Button
          className={styles.closeButton}
          variant="contained"
          color="primary"
          onClick={onClose}
          data-veritone-element="invitation-sent-button"
        >
          Close
        </Button>
      </ThemeProvider>
    </DialogActions>
  </Fragment>
);

export default ConfirmView;

export interface ConfirmViewPropTypes {
  readonly onClose: () => void;
}

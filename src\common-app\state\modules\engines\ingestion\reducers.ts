import { Lens } from 'monocle-ts';
import { createReducer } from '@reduxjs/toolkit';

import * as Actions from './actions';
import {
  defaultState,
  defaultJobSlice,
  IngestionStore,
  JobState,
  IngestionTDOs,
} from './store';
import { TDOId } from '@cbsa/state/modules/universal';
import { ProcessEngineRequest } from './models/services';
import { GetActionCreatorPayloadT } from '@utils';

const ingestionTDOs = Lens.fromProp<IngestionStore>()('tdos');
const engineGroup = (tdoId: TDOId) =>
  Lens.fromNullableProp<IngestionTDOs>()(tdoId, {});
const engineJob = (jobId: string) =>
  Lens.fromNullableProp<NonNullable<IngestionTDOs['']>>()(
    jobId,
    defaultJobSlice()
  );
const getJob = (tdoId: TDOId, jobId: string) =>
  ingestionTDOs.compose(engineGroup(tdoId)).compose(engineJob(jobId)).get;
const modifyJob = (
  tdoId: TDOId,
  jobId: string,
  fn: (j: JobState) => JobState
) =>
  ingestionTDOs
    .compose(engineGroup(tdoId))
    .compose(engineJob(jobId))
    .modify(fn);

const processEngineRequest = (
  state: IngestionStore,
  { payload }: { payload: ProcessEngineRequest }
) => ingestionTDOs.compose(engineGroup(payload.tdoId)).modify((s) => s)(state);

const processEngineRequestSuccess = (
  state: IngestionStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
    endedOn: new Date(),
    error: undefined,
  }))(state);

const processEngineRequestFailure = (
  state: IngestionStore,
  {
    payload,
  }: {
    payload: GetActionCreatorPayloadT<
      typeof Actions.PROCESS_ENGINE_REQUEST_FAILURE
    >;
  }
) =>
  // TODO: Fix this 'any' type -- whether or not jobId exists is an unanswered question
  modifyJob(payload.tdoId, (payload as any).jobId, (job) => ({
    ...job,
    isRunning: false,
    status: 'failed',
    endedOn: new Date(),
    error: payload.errorMsg,
  }))(state);

const createJobSuccess = (
  state: IngestionStore,
  {
    payload,
  }: {
    payload: GetActionCreatorPayloadT<typeof Actions.CREATE_ENGINE_JOB_SUCCESS>;
  }
) =>
  modifyJob(payload.createJob.targetId, payload.createJob.id, (_job) => ({
    jobId: payload.createJob.id,
    isRunning: true,
    status: 'pending',
    startedOn: new Date(),
    endedOn: undefined,
    error: undefined,
  }))(state);

const startPollingEngineResults = (
  state: IngestionStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    jobId: payload.jobId,
    isRunning: true,
    status: 'pending',
    error: undefined,
  }))(state);

const stopPollingEngineResults = (
  state: IngestionStore,
  { payload }: { payload: { tdoId: TDOId; jobId: string } }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
  }))(state);

const checkJobStatusSuccess = (
  state: IngestionStore,
  {
    payload,
  }: {
    payload: GetActionCreatorPayloadT<typeof Actions.CHECK_JOB_STATUS_SUCCESS>;
  }
) =>
  payload.job.status ===
  getJob(payload.job.targetId, payload.job.id)(state).status
    ? state
    : modifyJob(payload.job.targetId, payload.job.id, (job) => ({
        ...job,
        status: payload.job.status,
      }))(state);

const cancelJob = (
  state: IngestionStore,
  { payload }: { payload: GetActionCreatorPayloadT<typeof Actions.CANCEL_JOB> }
) =>
  modifyJob(payload.tdoId, payload.jobId, (job) => ({
    ...job,
    isRunning: false,
    status: 'cancelled',
    endedOn: new Date(),
    error: undefined,
  }))(state);

const setIngestionOptions = (
  state: IngestionStore,
  {
    payload,
  }: {
    payload: {
      runHead?: boolean;
      runPerson?: boolean;
      runTranscription?: boolean;
    };
  }
) => ({
  ...state,
  ...payload,
});

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(Actions.PROCESS_ENGINE_REQUEST, processEngineRequest)
    .addCase(
      Actions.PROCESS_ENGINE_REQUEST_SUCCESS,
      processEngineRequestSuccess
    )
    .addCase(
      Actions.PROCESS_ENGINE_REQUEST_FAILURE,
      processEngineRequestFailure
    )
    .addCase(Actions.CREATE_ENGINE_JOB_SUCCESS, createJobSuccess)
    .addCase(Actions.START_POLLING_ENGINE_RESULTS, startPollingEngineResults)
    .addCase(Actions.STOP_POLLING_ENGINE_RESULTS, stopPollingEngineResults)
    .addCase(Actions.CHECK_JOB_STATUS_SUCCESS, checkJobStatusSuccess)
    .addCase(Actions.CANCEL_JOB, cancelJob)
    .addCase(Actions.SET_INGESTION_OPTIONS, setIngestionOptions);
});

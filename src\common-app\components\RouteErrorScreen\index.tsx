import { ThemeProvider } from '@mui/material';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import * as Sentry from '@sentry/browser';
import { useMemo, useState } from 'react';
import * as React from 'react';

import AppContainer from '../AppContainer';
import errorLoading from '@resources/images/error-loading.svg';

import { buttonTheme } from '@redact/materialUITheme';
import { useStyles } from '@common-pages/NotFound/styles';

import { I18nTranslate } from '@i18n';

export interface RouteErrorScreenPropTypes {
  readonly children?: React.ReactElement;
}

const RouteErrorScreenContent = ({ children }: RouteErrorScreenPropTypes) => {
  const [hasError, setHasError] = useState(false);
  const classes = useStyles();

  const renderContent = useMemo(
    () => (
      <AppContainer>
        <div
          className={classes.container}
          data-testid="route-loading-error-app-container"
        >
          <img src={errorLoading} className={classes.bigImage} />
          <Typography variant="h5" className={classes.headline}>
            {I18nTranslate.TranslateMessage('error')}
          </Typography>
          <Typography
            variant="subtitle1"
            color="textSecondary"
            className={classes.message}
          >
            {I18nTranslate.TranslateMessage('somethingWrong')}
          </Typography>
          <Button
            className={classes.actionButton}
            variant="contained"
            color="primary"
            onClick={() => {
              window.location.href = '/';
            }}
            data-testid="not-found-home-button"
          >
            {I18nTranslate.TranslateMessage('home')}
          </Button>
        </div>
      </AppContainer>
    ),
    [classes]
  );

  try {
    if (hasError) {
      // You can render any custom fallback UI
      return renderContent;
    }

    return children || renderContent;
  } catch (err) {
    Sentry.withScope((_scope) => {
      Sentry.captureException(err);
    });
    console.error(err);
    setHasError(true);
    return renderContent;
  }
};

const RouteErrorScreen = ({ children }: RouteErrorScreenPropTypes) => (
  <ThemeProvider theme={buttonTheme}>
    <RouteErrorScreenContent>{children}</RouteErrorScreenContent>
  </ThemeProvider>
);

export default RouteErrorScreen;

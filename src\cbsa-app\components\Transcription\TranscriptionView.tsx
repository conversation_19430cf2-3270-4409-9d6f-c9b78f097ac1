import { get, keys } from 'lodash';
import { memo, MouseEventHandler, useRef, useState } from 'react';

import { TranscriptionViewableWords } from '@common-modules/mediaDetails/models';

import ContextMenu from './ContextMenu';
import Display from './Display';
import Search from './Search';
import * as styles from './styles.scss';
import { TranscriptionPropTypes } from './TranscriptionPropTypes';
import { useDragSelectEffect as useDragSelect } from './useDragSelectEffect';
import { searchV2 } from './utils';

const TranscriptionView = ({
  currentWord,
  transcription,
  selectedWords,
  redactedWords,
  onRedactWords,
  onUnredactWords,
  onDeselectWords,
  onDeselectAll,
  onSelectWords,
  onSelectRangeWords,
  onViewNotes,
  onDeleteNotes,
  onSeekPlayVideo,
  onPauseVideo,
}: TranscriptionPropTypes) => {
  const ref = useRef<HTMLDivElement>(null);

  const [searchTerm, setSearchTerm] = useState('');
  const [searchFocus, setSearchFocus] = useState(0);
  const [searchResults, setSearchResults] = useState<
    ReadonlyArray<Record<string, TranscriptionViewableWords>> | undefined
  >(undefined);
  const [wordIdToLineNumberMap, setWordIdToLineNumberMap] = useState<{
    [id: string]: number;
  }>({});

  const [transcriptionLines, setTranscriptionLines] = useState<
    TranscriptionViewableWords[][]
  >([]);
  const [contextWord, setContextWord] = useState<
    TranscriptionViewableWords | undefined
  >(undefined);

  useDragSelect({
    ref,
    transcription,
    onSelectRangeWords,
    onSeekPlayVideo,
    onPauseVideo,
  });

  const onContextMenuOpen: MouseEventHandler = (evt) => {
    evt.stopPropagation();
    evt.preventDefault();
    const wordId: string | undefined = get(evt.target, 'id');
    setContextWord(transcription.find(({ id }) => id === wordId));
  };

  const onContextMenuClose = () => setContextWord(undefined);

  const onChange = (term: string) => {
    setSearchTerm(term);
    setSearchResults(undefined);
  };

  const onSearch = () => {
    const { results, wordIdToLineNumberMap } = searchV2(
      searchTerm,
      transcriptionLines,
      false
    );
    setSearchResults(results);
    setWordIdToLineNumberMap(wordIdToLineNumberMap);
    setSearchFocus(results.length > 0 ? 0 : -1);
  };

  const clearTerm = () => {
    setSearchTerm('');
    setSearchResults(undefined);
    setSearchFocus(0);
  };

  const onDisplayClick: MouseEventHandler = (evt) => {
    onContextMenuClose();
    if (evt.shiftKey) {
      const wordId = get(evt.target, 'id') as string | undefined;
      if (wordId?.startsWith('transcription_')) {
        const word = transcription.find(({ id }) => wordId === id);
        if (word) {
          if (selectedWords[wordId]) {
            onDeselectWords([word]);
          } else {
            onSelectWords([word]);
          }
        }
      } else {
        onDeselectAll();
      }
    }
  };

  // TODO: Justify final !
  const selectedRow =
    searchResults?.[searchFocus] &&
    wordIdToLineNumberMap?.[keys(searchResults[searchFocus])[0]!];

  return (
    <div
      ref={ref}
      className={styles.transcription}
      onContextMenu={onContextMenuOpen}
      onClick={onDisplayClick}
    >
      <Search
        term={searchTerm}
        focus={searchFocus}
        numResults={searchResults?.length ?? -1}
        onChange={onChange}
        onSearch={onSearch}
        onClear={clearTerm}
        onFocus={setSearchFocus}
      />
      <Display
        {...{
          transcription,
          currentWord,
          redactedWords,
          selectedWords,
          searchResults: searchResults || [],
          searchFocus,
          selectedRow,

          onTranscriptionLinesReady: setTranscriptionLines,
        }}
      />
      <ContextMenu
        {...{
          transcription,
          selectedWords,
          onDeselectAll,
          redactedWords,
          onRedactWords,
          onUnredactWords,
          searchResults: searchResults || [],
          word: contextWord,
          onViewNotes,
          onDeleteNotes,
          onClose: onContextMenuClose,
        }}
      />
    </div>
  );
};

export default memo(TranscriptionView);

import { MediaPlayerTime } from './../models/MediaPlayerTime';
import { clamp, get } from 'lodash';
import { all, fork, select, takeEvery, put } from 'typed-redux-saga/macro';

import {
  JUMP_MEDIA,
  PAUSE_MEDIA,
  PLAY_MEDIA,
  PLAY_MEDIA_BACKWARDS,
  PLAYBACK_RATE_MEDIA,
  SEEK_MEDIA,
  SEEK_PLAY_MEDIA,
  TOGGLE_PLAY_PAUSE_MEDIA,
  PLAYER_PAUSE,
  PLAYER_SEEKED,
  PlaybackRatePayload,
  JumpMediaPayload,
  SEEK_PREV_MARKER_TIME,
  actionSetPrevMarkerTime,
  setIsPlayerSeeked,
} from '../actions';
import { selectMediaDuration, selectFrameRate } from '../selectors';

import {
  findFrameIndex,
  convertFrameIndexToTimeMs,
  frameAlignStartTimeMs,
} from '@utils';

interface Action<P> {
  type: string;
  payload: P;
}

function* askPlayer<R = void>(
  fn: (mp: NonNullable<Window['mediaPlayer']>) => R
) {
  yield window.mediaPlayer && fn(window.mediaPlayer);
}

let isTemporaryPlayerPause = false; // set true when we need to pause player temporarily
let disableSeekedAlignment = false; // sometimes we want to disable this behavior (such as when using the backwards emulator)

export function computeSeekTime(timeMs: number) {
  // convert timeMs to a safe seek position

  // round up to whole millisecond position (avoids rounding down into previous frame)
  // time steps such as 66.6667 ms will round up to 67 ms so displays as .067 instead of 0.066 on the playhead

  // also add a small epsilon to ensure inside of frame (some time step have floating point issues
  // ex. seeking to 8.2 goes to 8.1999999 which then rounds down to previous frame during alignment)
  return Math.ceil(timeMs) / 1000.0 + 0.0001;
}

export function* videoSaga() {
  yield* all([
    fork(onActionSeekVideo),
    fork(onActionSeekPlayVideo),
    fork(onActionPlayVideo),
    fork(onActionPlayVideoBackwards),
    fork(onActionPauseVideo),
    fork(onActionTogglePlayPauseVideo),
    fork(onActionPlaybackRateVideo),
    fork(onActionJumpVideo),
    fork(onActionPlayerPause),
    fork(onPlayerSeeked),
    fork(onActionSeekPrevMarkerTime),
  ]);
}

export function* onActionSeekVideoHandle({
  payload,
}: {
  payload: MediaPlayerTime;
}) {
  const duration = yield* select(selectMediaDuration);
  const fps = yield* select(selectFrameRate);

  let startTimeMs = payload.startTimeMs;

  // align with frame start time
  if (fps) {
    startTimeMs = frameAlignStartTimeMs(startTimeMs, fps); // assume always seek to the start time of a frame
  }

  yield askPlayer((mediaPlayer) => {
    let seekTime = computeSeekTime(startTimeMs);

    // if seek to a time that is the same frame (using click and drag of playhead) then playhead will not re-render to aligned position
    // force re-render by adding epsilon to currentTime - will break the equality test in mediaPlayerView lastCurrentTimeReported == currentTime
    if (
      fps &&
      Math.abs(mediaPlayer.getState().player.currentTime - seekTime) < 0.001
    ) {
      seekTime += 0.0001; // add a tenth of ms
    }

    seekTime = clamp(seekTime, 0, duration - 0.000005); // hack for instability around end of video)

    mediaPlayer.seek(seekTime);
  });
}

export function* onActionSeekVideo() {
  // seek to new position clicking on playhead timeline(from pause or playing state), or seek to cluster/group start time
  yield* takeEvery(SEEK_MEDIA, onActionSeekVideoHandle);
}

export function* onActionSeekPlayVideoHandle({
  payload,
}: {
  payload: MediaPlayerTime;
}) {
  const duration = yield* select(selectMediaDuration);
  yield askPlayer((mediaPlayer) => {
    const destination = clamp(
      computeSeekTime(payload.startTimeMs),
      0,
      duration - 0.000005 // hack for instability around end of video
    );

    mediaPlayer.seek(destination);
    if (get(window, 'mediaPlayer.props.hasStarted')) {
      mediaPlayer.play();
    }
  });
}

export function* onActionSeekPlayVideo() {
  // how do we get here?
  yield* takeEvery(SEEK_PLAY_MEDIA, onActionSeekPlayVideoHandle);
}
export function* onActionPlayVideoHandle() {
  yield askPlayer((mediaPlayer) => {
    mediaPlayer.play();
  });
}

export function* onActionPlayVideo() {
  // right arrow held down
  yield* takeEvery(PLAY_MEDIA, onActionPlayVideoHandle);
}

let backwardsPlayEmulation: number;

export function* onActionPlayVideoBackwardsHandle() {
  const fps = yield* select(selectFrameRate);
  disableSeekedAlignment = true;
  yield askPlayer((mediaPlayer) => {
    try {
      // this probably won't work on most players
      mediaPlayer.playbackRate = -Math.abs(mediaPlayer.playbackRate);
      mediaPlayer.play();
    } catch (_error) {
      let windowIntervalMs = 20;
      if (fps) {
        windowIntervalMs = 1000 / fps;
      }
      backwardsPlayEmulation = window.setInterval(() => {
        if (!window.mediaPlayer) {
          return clearInterval(backwardsPlayEmulation);
        }
        window.mediaPlayer.forward(-windowIntervalMs / 1000);
      }, windowIntervalMs);
    }
  });
}

export function* onActionPlayVideoBackwards() {
  yield* takeEvery(PLAY_MEDIA_BACKWARDS, onActionPlayVideoBackwardsHandle);
}

export function* onActionPauseVideoHandle() {
  yield askPlayer((mediaPlayer) => {
    clearInterval(backwardsPlayEmulation);
    disableSeekedAlignment = false;
    mediaPlayer.playbackRate = Math.abs(mediaPlayer.playbackRate) || 1.0;
    mediaPlayer.pause();
  });
}

export function* onActionPauseVideo() {
  // pause mediaPlayer when release arrow key being held down
  yield* takeEvery(PAUSE_MEDIA, onActionPauseVideoHandle);
}

export function* onActionTogglePlayPauseVideoHandle() {
  yield askPlayer((mediaPlayer) => {
    if (get(mediaPlayer, 'video.video.paused')) {
      mediaPlayer.play();
    } else {
      mediaPlayer.pause();
    }
  });
}

export function* onActionTogglePlayPauseVideo() {
  // pause/play mediaPlayer using spacebar
  yield* takeEvery(TOGGLE_PLAY_PAUSE_MEDIA, onActionTogglePlayPauseVideoHandle);
}

export function* onActionPlayerPause() {
  // player has been paused
  yield* takeEvery<Action<void> & { videoProps: { currentTime: number } }>(
    PLAYER_PAUSE,
    function* (action) {
      const { currentTime } = action.videoProps;
      const fps = yield* select(selectFrameRate);
      const duration = yield* select(selectMediaDuration);

      // don't align to frame start at the end of video - this way play action will seek back to the beginning
      if (fps && duration - currentTime < 1 / fps) {
        return;
      }

      yield askPlayer((mediaPlayer) => {
        let seekTime = currentTime;

        // align with frame start time
        if (fps) {
          const frameIndex = findFrameIndex(currentTime * 1000, fps);

          // grabbing currentTime from action.videoProps appear to have fixed issue workaround below was addressing
          // -------------------
          // sometimes video is one frame ahead of mediaPlayer.props.currentTime - causes endtime alignment errors on UDRs
          // seek to next frame to ensure alignment of frame, cursor, udr end and avoid player "rewind"
          // if close to frame boundary move to next frame (within 25 ms)
          // const nextFrameTime =
          //   convertFrameIndexToTimeMs(frameIndex + 1, fps) / 1000.0;

          // if (nextFrameTime - currentTime < 0.025) {
          //   seekTime = nextFrameTime;
          // } else {
          //   seekTime = convertFrameIndexToTimeMs(frameIndex, fps) / 1000.0;
          // }
          // ----------------

          seekTime = convertFrameIndexToTimeMs(frameIndex, fps) / 1000.0;
          // currentTime = convertFrameIndexToTimeMs(frameIndex + 0.5, fps)/ 1000.0; // reproduce the lagging video bug GLC-4661 by pausing in between frames
          seekTime = computeSeekTime(seekTime * 1000);
          if (seekTime < duration) {
            mediaPlayer.seek(seekTime);
          }
        }
      });
    }
  );
}

export function* onActionPlaybackRateVideoHandle({
  payload,
}: {
  payload: PlaybackRatePayload;
}) {
  yield askPlayer((mediaPlayer) => {
    mediaPlayer.playbackRate = payload.playbackRate;
  });
}

export function* onActionPlaybackRateVideo() {
  yield* takeEvery(PLAYBACK_RATE_MEDIA, onActionPlaybackRateVideoHandle);
}

export function* onActionSeekPrevMarkerTime() {
  yield* takeEvery(SEEK_PREV_MARKER_TIME, function* ({ payload }) {
    const fps = yield* select(selectFrameRate);

    let prevMarkerMs = payload;

    // align with frame start time
    if (fps) {
      prevMarkerMs = frameAlignStartTimeMs(prevMarkerMs, fps); // assume always seek to the start time of a frame
    }

    yield* put(actionSetPrevMarkerTime(prevMarkerMs));
  });
}

export function* onPlayerSeekedHandle(
  action: Action<void> & {
    videoProps: {
      currentTime: number;
    };
  }
) {
  const { currentTime } = action.videoProps;
  if (!currentTime) {
    return;
  }
  if (disableSeekedAlignment) {
    return;
  }

  yield* put(setIsPlayerSeeked(true));
  const currentTimeMs = currentTime * 1000;
  const fps = yield* select(selectFrameRate);

  // if seeking was done on the player instead of timeline then we haven't aligned with the frame yet
  if (fps) {
    // find seek time that aligns with frame start time
    const startTimeMs = frameAlignStartTimeMs(currentTimeMs, fps);
    const seekTime = computeSeekTime(startTimeMs);

    // check if video is currently paused
    let isPaused = false;
    yield askPlayer((mediaPlayer) => {
      isPaused = !!get(mediaPlayer, 'video.video.paused');
    });
    // check if too far from desired seekTime (allow 1e-5 tol to ignore floating point issues)
    if (Math.abs(seekTime - currentTime) > 0.00001) {
      if (isPaused) {
        // safe to align with frame start when paused
        yield askPlayer((mediaPlayer) => {
          mediaPlayer.seek(seekTime);
        });
      } else {
        // if video is playing need to pause temporarily to avoid infinite loop
        isTemporaryPlayerPause = true;
        yield askPlayer((mediaPlayer) => {
          mediaPlayer.pause(); // inside onActionPlayerPause we seek (so return to this function a second time to resume playing)
        });
      }
    } else {
      // check if we need to release the player hold (resume playing)
      if (isPaused && isTemporaryPlayerPause) {
        isTemporaryPlayerPause = false; // release temporary pause
        yield askPlayer((mediaPlayer) => {
          mediaPlayer.play();
        });
      }
    }
  }
}

export function* onPlayerSeeked() {
  // seek via player control (also seek from timeline goes through this)
  yield* takeEvery<Action<void> & { videoProps: { currentTime: number } }>(
    PLAYER_SEEKED,
    onPlayerSeekedHandle
  );
}

export function* onActionJumpVideoHandle({
  payload,
}: {
  payload: JumpMediaPayload;
}) {
  const fps = yield* select(selectFrameRate);
  yield askPlayer((mediaPlayer) => {
    const currentTime = mediaPlayer.getState().player.currentTime;
    let jumpTimeMs;

    // jump by seconds
    if (payload.seconds) {
      jumpTimeMs = 1000 * (currentTime + payload.seconds);

      // if fps align with frame start
      if (fps) {
        jumpTimeMs = frameAlignStartTimeMs(jumpTimeMs, fps);
      }
    } else {
      if (payload.frames !== undefined) {
        // jump by frames
        if (fps) {
          const frameIndex = findFrameIndex(currentTime * 1000, fps);
          jumpTimeMs = convertFrameIndexToTimeMs(
            frameIndex + payload.frames,
            fps
          );
        } else {
          //  legacy videos / and audio only don't have frameRate - assume default 30 fps
          // todo: consider having a default audio only step size 200 ms?
          jumpTimeMs = 1000 * currentTime + (1000 * payload.frames) / 30;
        }
      } else {
        return;
      }
    }

    mediaPlayer.forward(computeSeekTime(jumpTimeMs) - currentTime); //  calculate precise shift that moves to desired seekTime
  });
}

export function* onActionJumpVideo() {
  // step via left/right arrow keys
  yield* takeEvery(JUMP_MEDIA, onActionJumpVideoHandle);
}

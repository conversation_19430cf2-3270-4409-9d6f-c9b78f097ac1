### How to manually create the ApplicationConfig - for environments where editing an application to add it is not available/functional

As instance admin

```
mutation CreateRedactConfig {
  applicationConfigDefinitionCreate(input: [
    {
      appId: "766e9916-9536-47e9-8dcb-dc225654bab3"
      configKey: "transcriptionEngineId"
      configType: String
      configLevel: Organization
      description: "Redact: Engine Id to use for Transcription"
      required: false
      secured: false
    },
    {
      appId: "766e9916-9536-47e9-8dcb-dc225654bab3"
      configKey: "transcriptionEngineOptions"
      configType: JSON
      configLevel: Organization
      description: "Redact: Transcription Engine Options"
      required: false
      secured: false
    },
    {
      appId: "766e9916-9536-47e9-8dcb-dc225654bab3"
      configKey: "featureFlags"
      configType: JSON
      configLevel: Organization
      description: "Redact: Feature Flags"
      required: false
      secured: false
    }
  ]){
    records {
      id
    }
  }
}
```


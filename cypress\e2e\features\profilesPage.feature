Feature: Verify Profiles

  Background: Navigate to Profiles page
    Given The user is on the "Profiles" screen
    Then The header should contain "Profiles" and "Add New Profile" button
    And The table contains the following columns:
      | Profile Name |
      | Is Default   |
      | Created By   |
      | Modified By  |
      | Created On   |
      | Modified On  |
      | Edit         |
      | Delete       |

  @e2e @profile @regression
  Scenario: Verify user can add a new profile
    Given The user deletes the profile "Cypress Profile - 01" if exists
    When The user clicks on Add new profile button
    Then The profile settings dialog should be displayed
    When The user enters name "Cypress Profile - 01" to Profile Name
    Then The user saves the "New" profile
    Then The profile "Cypress Profile - 01" should be visible in table
    And The format of "Cypress Profile - 01" should be DD:YYYY:hh:mm
    When The user is on the "Redactions" screen
    And The user selects "Upload Media" button
    And The user clicks on profiles dropdown
    Then The Profiles list should contain profile "Cypress Profile - 01"

  @e2e @profile @regression
  Scenario: Verify user can edit a profile
    Given The user deletes the profile "Updated Cypress Profile - 02" if exists
    And The user deletes the profile "Updated Cypress Profile - 01" if exists
    When The user clicks on Add new profile button
    Then The profile settings dialog should be displayed
    When The user enters name "Updated Cypress Profile - 01" to Profile Name
    Then The user saves the "New" profile
    When user clicks on Edit button on "Updated Cypress Profile - 01"
    Then The profile settings dialog should be displayed
    When The user enters name "Updated Cypress Profile - 02" to Profile Name
    When The user selects "CCTV" for Video Type
    And The user changes the Confident Threshold by "number" to 50
    Then The user saves the "Edited" profile
    Then The profile "Updated Cypress Profile - 02" should be visible in table
    And The format of "Updated Cypress Profile - 02" should be DD:YYYY:hh:mm
    When user clicks on Edit button on "Updated Cypress Profile - 02"
    Then Video Type should be "CCTV"
    And Confident Threshold by "number" should be 50

  @e2e @profile @regression
  Scenario: Verify user can set a profile as default
    Given The user deletes the profile "Set Defaut Cypress Profile - 01" if exists
    Given The user reset to default profile
    When The user clicks on Add new profile button
    Then The profile settings dialog should be displayed
    When The user enters name "Set Defaut Cypress Profile - 01" to Profile Name
    Then The user saves the "New" profile
    When user clicks on Is Default checkbox on profile "Set Defaut Cypress Profile - 01"
    Then The checkbox for "Set Defaut Cypress Profile - 01" is selected
    When The user is on the "Redactions" screen
    Then The user selects "Upload Media" button
    Then Profile "Set Defaut Cypress Profile - 01" is displayed in Profile list by default

  @e2e @profile @regression
  Scenario: Verify user can delete a profile
    Given The user deletes the profile "Delete Defaut Cypress Profile - 01" if exists
    When The user clicks on Add new profile button
    Then The profile settings dialog should be displayed
    When The user enters name "Delete Defaut Cypress Profile - 01" to Profile Name
    Then The user saves the "New" profile
    When The user clicks the Delete button on profile "Delete Defaut Cypress Profile - 01"
    Then The Delete Profile dialog should be displayed
    When user confirms the deletion
    Then Profile "Delete Defaut Cypress Profile - 01" should be deleted

  @e2e @profile @regression @small-screen
  Scenario: Verify user can use the scroll bar
    When The user moves the navigation bar "down"
    Then The navigation bar should be scrolled "down"
    And Show more files below
    When The user moves the navigation bar "up"
    Then The navigation bar should be scrolled "up"

  @e2e @profile @regression
  Scenario: Verify Row per page
    When user checks the Row per page default is "10"
    When user selects 20 from the dropdown
    Then The page is updated and the total number of rows displayed is up to "20"
    When user selects 100 from the dropdown
    Then The page is updated and the total number of rows displayed is up to "100"

  @e2e @profile @regression
  Scenario: Verify Pagination
    When The user clicks the ">" button in the pagination section
    Then The user should go to page: "11-20"
    When The user clicks the "<" button in the pagination section
    Then The user should go to page: "1-10"

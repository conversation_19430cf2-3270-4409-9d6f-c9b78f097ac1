import { createSelector } from 'reselect';
import {
  selectTDOs,
  deleteCase,
  changeCaseStatus,
  archiveCase,
  processCase,
  selectLoaders,
  startExportJob,
  createExportTdo,
  selectCaseDetails,
  selectAuditEvents,
  getCaseAuditLogs,
  selectAuditEventsLoading,
} from '@cbsa-modules/addMedia';
import { Case, SummaryTDO, TDOId, TreeObjectId } from '@cbsa-modules/universal';
import { connect, ConnectedProps } from 'react-redux';

export const mapStateToProps = createSelector(
  selectTDOs,
  selectLoaders,
  selectCaseDetails,
  selectAuditEvents,
  selectAuditEventsLoading,
  (tdos, loaders, caseDetails, auditEvents, auditLogsLoading) => ({
    tdos,
    loaders,
    caseDetails,
    auditEvents,
    auditLogsLoading,
  })
);

export const mapDispatchToProps = {
  approveCase: (caseDetails: Case) =>
    changeCaseStatus({ caseDetails, caseStatus: 'approved' }),
  readyForReviewCase: (caseDetails: Case) =>
    changeCaseStatus({ caseDetails, caseStatus: 'readyForReview' }),
  archiveCase: ({
    caseDetails,
    archive,
  }: {
    caseDetails: Case;
    archive: boolean;
  }) => archiveCase({ caseDetails, archive }),
  createExportTdo: (caseId: TreeObjectId) => createExportTdo(caseId),
  deleteCase: (caseDetails: Case) => deleteCase(caseDetails),
  processCase: ({
    images,
    media,
    batchId,
    caseDetails,
  }: {
    images: Array<string>;
    media: Array<SummaryTDO>;
    batchId: string;
    caseDetails: Case;
  }) => processCase({ images, media, batchId, caseDetails }),
  startExportJob: ({ caseId, tdoId }: { caseId: TreeObjectId; tdoId: TDOId }) =>
    startExportJob({ caseId, tdoId }),
  getCaseAuditLogs: () => getCaseAuditLogs(),
};

export const connector = connect(mapStateToProps, mapDispatchToProps);

export type Props = ConnectedProps<typeof connector>;

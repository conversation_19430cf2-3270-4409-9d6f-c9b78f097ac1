import { TDOId, Thunk } from '@cbsa-modules/universal';
import callGraphQLApi from '@helpers/callGraphQLApi';
import { DELETE_TDO_QUERY } from './queries/deleteTdo';
import { NOOP } from '@cbsa-modules/universal/actions';

export const deleteTdo: Thunk<{
  readonly tdoId: TDOId;
}> =
  ({ tdoId }) =>
  async (dispatch, getState) =>
    await callGraphQLApi({
      actionTypes: [NOOP, NOOP, NOOP],
      query: DELETE_TDO_QUERY,
      variables: { tdoId },
      dispatch,
      getState,
    });

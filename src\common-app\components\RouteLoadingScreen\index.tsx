import { CircularProgress, ThemeProvider } from '@mui/material';
import { useEffect, useState } from 'react';
import { buttonTheme } from '@redact/materialUITheme';
import * as styles from './styles.scss';

const RouteLoadingScreen = ({ delayMs = 0 }: RouteLoadingScreenPropTypes) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delayMs);
    return () => clearTimeout(timer);
  });

  return isVisible ? (
    <ThemeProvider theme={buttonTheme}>
      <div className={styles.container} data-testid="route-loading-screen">
        <CircularProgress
          data-testid="circular-progress"
          color={'primary'}
          size={125}
          thickness={1}
        />
      </div>
    </ThemeProvider>
  ) : (
    <div data-testid="route-loading-screen" />
  );
};

export default RouteLoadingScreen;

export interface RouteLoadingScreenPropTypes {
  readonly delayMs?: number;
}

import { IngestMediaFileRequest, RequestHeader } from '../model/requests';
import { createTdoAdapter } from './createTdo';
import { createIngestJob } from './createIngestJob';

export const ingestMediaAdapter = async (
  headers: RequestHeader,
  request: IngestMediaFileRequest
  ) => {
    const tdoId = await createTdoAdapter(headers,
      { url: request.url,
        caseId: request.caseId,
        externalIds: request.externalIds,
      });
    let response: Awaited<ReturnType<typeof createIngestJob>> | undefined;
    if (tdoId) {
      const  { url, fileType, runDetection, runHeadDetection, runPersonDetection, runTranscription, email} = request;
      response = await createIngestJob(headers, {
        tdoId, url, fileType, runDetection, runHeadDetection, runPersonDetection, runTranscription, email });
    }
    return response;
};

import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import * as Services from '../services';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { put, select, takeEvery, takeLatest } from 'typed-redux-saga/macro';
import { selectConfigEngines } from '@common-modules/engines/selectors';

export function* createExportTdo() {
  yield* takeLatest(Actions.CREATE_EXPORT_TDO, function* ({ payload }) {
    const { caseId } = payload;

    const intl = sagaIntl();

    try {
      if (caseId) {
        yield* put(Services.createExportTdo({ caseId: caseId }));
      }
    } catch {
      yield* put(
        enqueueSnackbar({
          message: intl.formatMessage({ id: 'fileFailedCreateTDO' }),
          variant: 'error',
        })
      );
    }
  });
}

export function* startExportJob() {
  yield* takeLatest(Actions.START_EXPORT_JOB, function* ({ payload }) {
    const intl = sagaIntl();
    const { caseId, tdoId } = payload;
    const { defaultClusterId, downloadEngineId } =
      yield* select(selectConfigEngines);

    try {
      if (caseId) {
        yield* put(
          Services.startExportJob({
            caseId,
            tdoId,
            clusterId: defaultClusterId,
            engineId: downloadEngineId,
          })
        );
      }
    } catch {
      yield* put(
        enqueueSnackbar({
          message: intl.formatMessage({ id: 'fileFailedCreateTDO' }),
          variant: 'error',
        })
      );
    }
  });
}

export function* createExportTdoFailure() {
  yield* takeEvery(Actions.CREATE_EXPORT_TDO_FAILURE, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'exportTdoNotCreated',
        }),
        variant: 'error',
      })
    );
  });
}

export function* startExportJobFailure() {
  yield* takeEvery(Actions.START_EXPORT_JOB_FAILURE, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({ id: 'caseCouldNotExported' }),
        variant: 'error',
      })
    );
  });
}

import { LockCheckResponse } from '../model/responses';
import { RequestHeader } from '../model/requests';
import { Messages } from '../errors/messages'
import { Logger } from '../logger';
import { getChildFoldersAdapter } from './getChildFolders';
import { checkCaseLockAdapter } from './checkCaseLock';
import { CaseId, FolderId } from '../model/brands';

export const checkFolderLockAdapter = async (
  headers: RequestHeader,
  folderId: FolderId,
) => {
  const defaultLimit = 1000;
  const defaultOffset = 0;
  const result: LockCheckResponse = { isFailed: false, isLocked: false, isJobRunning: false };

  const getFolders = async(offset: number): Promise<LockCheckResponse> => {
    try {
      const res = await  getChildFoldersAdapter(headers, { folderId, limit: defaultLimit, offset});
      if (res.isFailed) {
        result.isFailed = true;
        return result;
      }
      if (!(res.childFolders.length  > 0 )) return result;
      if (res.childFolders.length > 0) {
        // When there are contentTemplates then folders are cases - can we use a type guard?
        const caseIds = ((res.childFolders.filter((f) => f.contentTemplates?.length > 0) || []).map((f) => f.id) || []) as CaseId[] ;
        if (caseIds.length > 0) {
          for (const caseId of caseIds) {
            const caseLockResponse = await checkCaseLockAdapter(headers, caseId);
            if (caseLockResponse.isFailed || caseLockResponse.isLocked
              || caseLockResponse.isJobRunning) {
                return caseLockResponse;
              }
          }
        }
        const folderOnlyIds = ((res.childFolders.filter((f) => !(f.contentTemplates?.length > 0)) || []).map((f) => f.id) || []);
        if (folderOnlyIds.length > 0) {
          for (const folderId of folderOnlyIds) {
            const folderLockResponse = await checkFolderLockAdapter(headers,  folderId)
            if (folderLockResponse.isFailed || folderLockResponse.isLocked
              ||folderLockResponse.isJobRunning) {
                return folderLockResponse;
              }
          }
        }
        if(res.childFolders?.length === defaultLimit)    {
          return await getFolders(offset + defaultLimit);
        }
      }
    } catch (err) {
      result.isFailed = true;
      Logger.error(Messages.checkFolderLockFail  + JSON.stringify(err));
    }
    return result;
  }
  return await getFolders(defaultOffset);
};

import * as styles from './styles.scss';
import TextInput from './TextInput';
import Grid from '@mui/material/Grid2';
import SelectInput from './SelectInput';
import Button from '@mui/material/Button';
import { useForm } from 'react-hook-form';
import { FormViewProps } from '.';

const RequestQuoteFormView = (props: FormViewProps) => {
  const { onSubmit } = props;
  const { control, setValue, handleSubmit, formState, trigger } = useForm();

  return (
    <form className={styles.requestQuoteForm}>
      <Grid container spacing={3} direction="row">
        <Grid size={{ xs: 12 }} className={styles.enterpriseDescription}>
          To get started, we need some initial information from you
        </Grid>
        <Grid container spacing={2} size={{ xs: 12 }} justifyContent="center">
          <Grid container spacing={5} size={{ xs: 6 }}>
            <Grid size={{ xs: 12 }}>
              <TextInput
                controlName="firstName"
                control={control}
                setValue={setValue}
                label="First Name*"
                fullWidth
                slotProps={{
                  inputLabel: {
                    shrink: true,
                  },
                }}
                trigger={trigger}
              />
            </Grid>
            <Grid size={{ xs: 12 }}>
              <TextInput
                controlName="businessEmail"
                control={control}
                setValue={setValue}
                label="Business Email*"
                type="email"
                fullWidth
                slotProps={{
                  inputLabel: {
                    shrink: true,
                  },
                }}
                trigger={trigger}
              />
            </Grid>
            <Grid size={{ xs: 12 }}>
              <TextInput
                controlName="orgName"
                control={control}
                setValue={setValue}
                label="Business/Organization Name*"
                fullWidth
                slotProps={{
                  inputLabel: {
                    shrink: true,
                  },
                }}
                trigger={trigger}
              />
            </Grid>
          </Grid>
          <Grid container spacing={5} size={{ xs: 6 }}>
            <Grid size={{ xs: 12 }}>
              <TextInput
                controlName="lastName"
                label="Last Name*"
                control={control}
                setValue={setValue}
                fullWidth
                slotProps={{
                  inputLabel: {
                    shrink: true,
                  },
                }}
                trigger={trigger}
              />
            </Grid>
            <Grid size={{ xs: 12 }}>
              <TextInput
                controlName="phoneNumber"
                control={control}
                setValue={setValue}
                label="Phone*"
                fullWidth
                slotProps={{
                  inputLabel: {
                    shrink: true,
                  },
                }}
                trigger={trigger}
              />
            </Grid>
            <Grid size={{ xs: 12 }}>
              <SelectInput
                controlName="businessSize"
                control={control}
                setValue={setValue}
                trigger={trigger}
                label="Size of Business/Organization*"
                options={['1-100', '100-300', '300-500', '500+']}
              />
            </Grid>
          </Grid>
        </Grid>
        <Grid container size={{ xs: 12 }} justifyContent="center">
          <Button
            variant="contained"
            color="primary"
            type="submit"
            onClick={handleSubmit(onSubmit)}
            disabled={
              !formState.isDirty || formState.isSubmitting || !formState.isValid
            }
          >
            Submit
          </Button>
        </Grid>
      </Grid>
    </form>
  );
};

// interface FormData {
//   firstName: string;
//   lastName: string;
//   businessEmail: string;
//   orgName: string;
//   phoneNumber: string;
//   businessSize: string;
// }

export default RequestQuoteFormView;

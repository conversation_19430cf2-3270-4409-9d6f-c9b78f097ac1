Cypress.Commands.add(
  'DrawAnUdr',
  ({ orderNumber, x1, y1, x2, y2 }: coordinate) => {
    cy.get('[data-test="overlay-container"]').then(() => {
      cy.get('[data-test="overlay-draw"]').as('overlay');
      cy.get('@overlay').trigger('mousedown', {
        x: x1,
        y: y1,
        eventConstructor: 'MouseEvent',
        force: true,
      });
      // TO DO: Remove cy.wait()
      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(0); // Delay action before dragging
      cy.get('@overlay').trigger('mousemove', {
        x: x2,
        y: y2,
        eventConstructor: 'MouseEvent',
        force: true,
      });
      cy.get(
        `.react-draggable[data-testid="rnd-box"][type="udr"]:eq(${orderNumber})`
      ).should('be.exist');

      cy.get('@overlay').trigger('mouseup', {
        x: x2,
        y: y2,
        eventConstructor: 'MouseEvent',
        force: true,
      });
      return null;
    });
    cy.get(
      `.react-draggable[data-testid="rnd-box"][type="udr"]:eq(${orderNumber})`
    ).should('be.visible');
  }
);

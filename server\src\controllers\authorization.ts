import express from 'express';
import { StatusCodes } from '../errors/statusCodes';
import { isAPITokenKey } from '../validations/helpers';
import { isSessionToken } from '../validations/helpers';
import { pick } from 'lodash';

export const isAuthorized = (req: express.Request, res: express.Response) => {
  const headers = pick(req.headers, ['authorization']); 
  let tokenType = undefined;  
  if (isAPITokenKey(headers)) {
    tokenType = 'API Token Key'
  } else if (isSessionToken(headers)) {
    tokenType = 'Session Token'
  }
  return res.status(StatusCodes.Success).json({
    status: 'Authorized!',
    tokenType: tokenType,
  });
}
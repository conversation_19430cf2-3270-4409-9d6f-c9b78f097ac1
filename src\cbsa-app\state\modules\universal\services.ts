import { getLatestSchemaId } from '@common/state/modules/notification/services';
import { ThunkAction, UnknownAction } from '@reduxjs/toolkit';
import { modules } from '@veritone/glc-redux';

const {
  config: { getConfig },
} = modules;

export type Thunk<P, R = any> = (
  payload: P,
  ...args: any[]
) => ThunkAction<Promise<R | undefined>, any, unknown, UnknownAction>;

export const lookupLatestCaseSchemaId = (
  () => async (dispatch: any, getState: any) => {
    const { cbsaCaseRegistryId, caseRegistryId, apiRoot } =
      getConfig<Window['config']>(getState());
    const dataRegistryId = caseRegistryId || cbsaCaseRegistryId;

    return await getLatestSchemaId(
      dispatch,
      getState,
      dataRegistryId,
      caseRegistryId ? 'caseRegistryId' : 'cbsaCaseRegistryId',
      apiRoot
    );
  }
)();

export const lookupLatestCaseNotificationSchemaId = (
  () => async (dispatch: any, getState: any) => {
    const { caseNotificationRegistryId: dataRegistryId, apiRoot } =
      getConfig<Window['config']>(getState());

    return await getLatestSchemaId(
      dispatch,
      getState,
      dataRegistryId,
      'caseNotificationRegistryId',
      apiRoot
    );
  }
)();

export const lookupLatestMediaCommentsSchemaId = (
  () => async (dispatch: any, getState: any) => {
    const { redactMediaCommentRegistryId: dataRegistryId, apiRoot } =
      getConfig<Window['config']>(getState());

    return await getLatestSchemaId(
      dispatch,
      getState,
      dataRegistryId,
      'redactMediaCommentRegistryId',
      apiRoot
    );
  }
)();

export const lookupLatestRedactionCodesSchemaId = (
  () => async (dispatch: any, getState: any) => {
    const { redactionCodesRegistryId: dataRegistryId, apiRoot } =
      getConfig<Window['config']>(getState());

    return await getLatestSchemaId(
      dispatch,
      getState,
      dataRegistryId,
      'redactionCodesRegistryId',
      apiRoot
    );
  }
)();

export const lookupLatestTdoLockSchemaId = (
  () => async (dispatch: any, getState: any) => {
    const { tdoLockRegistryId: dataRegistryId, apiRoot } =
      getConfig<Window['config']>(getState());
    return await getLatestSchemaId(
      dispatch,
      getState,
      dataRegistryId,
      'tdoLockRegistryId',
      apiRoot
    );
  }
)();

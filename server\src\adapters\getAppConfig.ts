import { isPlainObject, isString, merge, omit } from 'lodash';
import { callGQL } from '../api/callGraphql';
import { getAppConfigsQuery } from '../api/queries';
import { Messages } from '../errors/messages'
import { RequestHeader } from '../model/requests';
import { Logger } from '../logger';
import { AppConfig, GetAppConfigsResponse } from '../model/responses';
import Config from '../../apiConfig.json';


export const getAppConfig = async (
  headers: RequestHeader,
) => {
  const appConfigs = await callGQL<GetAppConfigsResponse>(
    headers,
    getAppConfigsQuery,
    { applicationId: Config.veritoneAppId || "766e9916-9536-47e9-8dcb-dc225654bab3" }
  )
    .then((res) => res.applicationConfig?.records || [])
    .catch((err) => {
      Logger.error(Messages.getAppConfigsFail + JSON.stringify(err));
      return undefined;
    });
  const transcriptionEngineId = getAppConfigValue(appConfigs?.find((config) => config.configKey === 'transcriptionEngineId'));
  const applicationConfigOverrides = transcriptionEngineId
    ? { transcriptionEngineId }
    : {};
  const transcriptionEngineOptions = omit(
      getAppConfigValue(appConfigs?.find(
        (config) => config.configKey === 'transcriptionEngineOptions'
      )) || {},
      // remove keys that should not be overwritten
      'app',
      'user',
      'organizationId'
    );
  const featureFlags = getAppConfigValue(appConfigs?.find((config) => config.configKey === 'featureFlags')) || {};
  return Object.assign(
    {},
    Config,
    applicationConfigOverrides,
    { transcriptionEngineOptions },
    {
      featureFlags: merge({}, Config.featureFlags, featureFlags),
    },
  );
};

function getAppConfigValue(appConfig: AppConfig | undefined): any {
  if (appConfig?.configType == 'JSON') {
    return getBrokenJSONAppConfigValue(appConfig);
  }
  return appConfig?.value || undefined;
}

function getBrokenJSONAppConfigValue(
  appConfig: AppConfig
) {
  if (!appConfig.valueJSON && !appConfig.value) {
    return undefined;
  }
  // Default to valueJSON if it exists, otherwise try to parse value as JSON to handle platform UI deficiency
  if (appConfig.valueJSON) {
    return appConfig.valueJSON;
  } else if (isString(appConfig.value)) {
    try {
      const value = JSON.parse(appConfig.value);
      if (isPlainObject(value)) {
        // Relatively safe cast...
        return value as Record<string, any>;
      } else {
        throw new Error('Invalid JSON structure in app config');
      }
    } catch (_e) {
      Logger.error('Invalid JSON string in app config:', appConfig.value);
      return undefined;
    }
  }
}

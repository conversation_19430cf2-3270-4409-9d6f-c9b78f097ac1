.wrapper {
  margin-top: 20px;
  height: 153px;
  position: absolute;
  z-index: 10;
  transform: scale3d(1, 1, 1) translateX(32px);
}

.content {
  height: 100%;
  cursor: pointer;
  position: absolute;
}

.position {
  width: 2px;
  background: #e53935;
  height: 175px;
  margin: -1px 0 0 0;
}

.time {
  position: absolute;
  width: 75px;
  height: 14px;
  background: #000;
  color: white;
  font-size: 12px;
  text-align: center;
  top: -20px;
  line-height: 15px;
  left: -25px;
  border-radius: 7px;
  user-select: none;
  box-shadow: 0 0 3px 2px #242e34;

  > div {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #000;
    position: absolute;
    top: 13px;
    left: 19px;
  }
}

.handle {
  width: 12px;
  height: 24px;
  background: #e5393599;
  margin: 0 -5px;
  border: 1px solid #e53935;
  cursor: ew-resize;
}

.triangle {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #e53935;
  margin: 0 -5px;
}

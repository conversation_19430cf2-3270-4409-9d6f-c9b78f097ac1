import cn from 'classnames';
import { useState } from 'react';
import { I18nTranslate } from '@i18n';
import Details from './components/Details';
import { isImage } from '@helpers/tdoHelper';
import type { JobStatus } from 'veritone-types';
import { ErrorOutline, MoreVert } from '@mui/icons-material';
import { SummaryTDO, TDOId } from '@cbsa-modules/universal';
import { Checkbox, CircularProgress, ThemeProvider } from '@mui/material';
import { ViewImageDialog } from '@cbsa-components/Dialogs';
import { useStyles } from './styles';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const TdoWrapperContent = ({ tdo, selected, toggleTdo }: Props) => {
  const classes = useStyles();
  const [isDetailsOpen, setDetailsOpen] = useState(false);
  const [isImageModalOpen, setImageModalOpen] = useState(false);

  const { id: fileId, name, primaryAsset } = tdo;
  const status = tdo?.jobs?.records?.[0]?.status;
  const isPhoto = isImage(tdo);

  const renderThumbnail = (status: JobStatus | undefined) =>
    ({
      pending: <CircularProgress thickness={2} size={40} />,
      running: <CircularProgress thickness={2} size={40} />,
      complete: <img src={tdo.thumbnailUrl} />,
      cancelled: <ErrorOutline style={{ color: 'black', fontSize: '40px' }} />,
      queued: <CircularProgress thickness={2} size={40} />,
      failed: <ErrorOutline style={{ color: 'black', fontSize: '40px' }} />,
      default: primaryAsset?.signedUri ? (
        <img src={primaryAsset?.signedUri} />
      ) : (
        <ErrorOutline style={{ color: 'black', fontSize: '40px' }} />
      ),
    })[status || 'default'];

  const translateStatus = (status: JobStatus | undefined) =>
    status === 'complete'
      ? 'complete'
      : status
        ? status
        : isPhoto
          ? 'newImage'
          : 'error';

  const renderStatus = (status: JobStatus | undefined) =>
    I18nTranslate.TranslateMessage(translateStatus(status));

  return (
    <>
      <div className={classes.tdoWrapper}>
        <div
          className={cn(
            'Thumbnail',
            selected && {
              active: true,
              processed: status === 'complete',
              error: !isPhoto && status === undefined,
            }
          )}
        >
          {renderThumbnail(status)}
          <Checkbox
            className={'Checkbox'}
            onClick={() => toggleTdo(tdo?.id)}
            checked={selected}
          />
          <div className={'MoreVert'}>
            <MoreVert
              style={{ color: 'white', fontSize: '16px' }}
              onClick={() => setDetailsOpen(true)}
            />
          </div>
        </div>
        <div
          className={cn('Status', {
            processed: status === 'complete',
            error: !isPhoto && status === undefined,
          })}
        >
          {renderStatus(status)}
        </div>
        <Details
          isOpen={isDetailsOpen}
          onClose={() => setDetailsOpen(false)}
          onView={() =>
            isPhoto
              ? setImageModalOpen(true)
              : (window.location.href = `${window.location.href}/files/${fileId}`)
          }
        />
      </div>
      <ViewImageDialog
        title={name}
        signedUri={primaryAsset?.signedUri}
        isOpen={isImageModalOpen}
        onClose={() => setImageModalOpen(false)}
      />
    </>
  );
};

interface Props {
  tdo: SummaryTDO;
  selected: boolean;
  toggleTdo: (tdoId: TDOId) => void;
}

const TdoWrapper = ({ tdo, selected, toggleTdo }: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <TdoWrapperContent tdo={tdo} selected={selected} toggleTdo={toggleTdo} />
  </ThemeProvider>
);

export default TdoWrapper;

import { Component, MouseEventHandler } from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { I18nTranslate } from '@common/i18n';

interface Props {
  onSubmit: MouseEventHandler<HTMLElement>;
  onClose: () => void;
  open: boolean;
}

export default class DeleteRedactionModal extends Component<Props> {
  static defaultProps = {};

  render() {
    return (
      <Dialog
        open={this.props.open}
        onClose={(_evt, reason) => {
          if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
            this.props.onClose();
          }
        }}
        maxWidth="md"
        data-veritone-component="delete-redaction-modal"
      >
        <DialogTitle>
          {I18nTranslate.TranslateMessage('deleteReactionTitle')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {I18nTranslate.TranslateMessage('deleteReactionContent')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={this.props.onClose}
            color="primary"
            data-veritone-element="delete-redaction-modal-discard-button"
          >
            {I18nTranslate.TranslateMessage('discard')}
          </Button>
          <Button
            type="submit"
            color="primary"
            onClick={this.props.onSubmit}
            data-veritone-element="delete-redaction-modal-delete-button"
          >
            {I18nTranslate.TranslateMessage('delete')}
          </Button>
        </DialogActions>
      </Dialog>
    );
  }
}

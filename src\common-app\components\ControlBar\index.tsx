import {
  ProgressControl,
  PlayToggle,
  FullscreenToggle,
  CurrentTimeDisplay,
  DurationDisplay,
  TimeDivider,
  VolumeMenuButton,
  PlaybackRateMenuButton,
} from 'video-react';
import classNames from 'classnames';
import { Children, ReactNode } from 'react';
import { PLAYBACK_RATES } from '@helpers/constants';
import { mergeAndSortChildren } from '@helpers/utils';
import { ArrayOrSingle } from 'ts-essentials';

const ControlBar = (props: Props) => {
  const {
    autoHide = true,
    disableDefaultControls,
    disableCompletely = false,
    disablePlayback,
    className,
    children,
  } = props;

  const getDefaultChildren = () => {
    const defaultProps = { ...props };
    delete defaultProps.className;
    const children = [
      <PlayToggle
        {...defaultProps}
        key="play-toggle"
        order={1}
        data-veritone-element="control-bar-play-toggle"
        data-testid="control-bar-play-toggle"
      />,
      <VolumeMenuButton
        {...defaultProps}
        key="volume-menu-button"
        order={4}
        data-veritone-element="control-bar-volume-menu-button"
      />,
      <CurrentTimeDisplay
        {...defaultProps}
        key="current-time-display"
        order={5.1}
      />,
      <TimeDivider {...defaultProps} key="time-divider" order={5.2} />,
      <DurationDisplay {...defaultProps} key="duration-display" order={5.3} />,
      <ProgressControl
        {...defaultProps}
        key="progress-control"
        order={6}
        data-veritone-element="control-bar-progress-control"
      />,
      <FullscreenToggle
        {...defaultProps}
        key="fullscreen-toggle"
        order={8}
        data-veritone-element="control-bar-fullscreen-toggle"
      />,
    ];

    if (!disablePlayback) {
      children.push(
        <PlaybackRateMenuButton
          {...defaultProps}
          rates={[...PLAYBACK_RATES].reverse()}
          key="playback-rate"
          order={7}
          data-veritone-element="control-bar-playback-button"
        />
      );
    }
    return children;
  };

  const getChildren = () => {
    const childrenArray = Children.toArray(children);
    const defaultChildren = disableDefaultControls ? [] : getDefaultChildren();
    return mergeAndSortChildren(defaultChildren, childrenArray, props);
  };

  return disableCompletely ? null : (
    <div className={className}>
      <div
        className={classNames('video-react-control-bar', {
          'video-react-control-bar-auto-hide': autoHide,
        })}
      >
        {getChildren()}
      </div>
    </div>
  );
};

ControlBar.displayName = 'ControlBar';

interface Props {
  autoHide?: boolean;
  className?: string;
  children?: ArrayOrSingle<ReactNode>;
  disablePlayback?: boolean;
  disableCompletely?: boolean;
  disableDefaultControls?: boolean;
}

export default ControlBar;

import {
  UDRsPolyAssetGroup,
  UDRsPolyAssetGroupSeriesItem,
} from '@common-modules/mediaDetails/models';
import { TimelinesPropTypes } from '../TimelinesPropTypes';

export interface UDRGroupRow {
  UDRId: string;
  active: boolean;
  color: string;
  progress: number;
  udrGroup: UDRsPolyAssetGroup;
  selectedPolys: TimelinesPropTypes['selectedPolys'];
  startMs: number;
  stopMs: number;
  selectedUDRId: string | undefined;
  onUDRSelect: (
    groupId: string,
    id: string,
    udr: UDRsPolyAssetGroupSeriesItem
  ) => void;
  onChangeUDRGroupLabel: (groupId: string, label: string) => void;
  onChangeUDR: (
    groupId: string,
    udrId: string,
    changeType: 'start' | 'end',
    udr: UDRsPolyAssetGroupSeriesItem
  ) => void;
  onChangeUDRSubmit: (
    groupId: string,
    udrId: string,
    changeType: 'start' | 'end',
    udr: UDRsPolyAssetGroupSeriesItem
  ) => void;
  onGroupLabelClick: (groupId: string) => void;
  onUDRContainerClick: (groupId: string) => void;
}

import {
  DataTestSelector,
  DataTestSelectorMessages,
  DataTestSelectorRightPanelVideo,
  DataTestSelectorTimeline,
  DataTestSelectorVideoTab,
  DataVeritoneSelector,
} from '../../support/helperFunction/mediaDetailHelper';
import { footer, header } from './components';

export const mediaDetailPage = {
  acceptIcon: () => cy.getDataIdCy({ idAlias: DataTestSelector.AcceptIcon }),
  denyIcon: () => cy.getDataIdCy({ idAlias: DataTestSelector.DenyIcon }),
  fileName: () =>
    cy.get(`[data-veritone-element=${DataVeritoneSelector.FileName}]`),
  statusDropdown: () =>
    cy.get(`[data-veritone-element=${DataVeritoneSelector.Status}]`),
  videoTabBtn: () => cy.getDataIdCy({ idAlias: DataTestSelector.videoTabBtn }),
  detectObjectButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.DetectObjectButton }),
  detectOptionDialog: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.DetectOptionDialog }),
  checkBoxOutlineBlankIcon: () =>
    cy
      .getDataIdCy({ idAlias: DataTestSelector.CheckBoxOutlineBlankIcon })
      .first()
      .parent(),
  detectContinueButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.DetectCountineButton }),
  redactionEffectsDropdown: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.RedactionEffectsDropdown,
    }),
  clusterRow: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.ClusterRow }).first(),
  checkBox: () => cy.getDataIdCy({ idAlias: DataTestSelector.CheckBox }),
  firstObjectRedaction: () =>
    cy
      .getDataIdCy({ idAlias: DataTestSelectorVideoTab.ObjectRedaction })
      .first(),
  searchRedactionCodeField: () =>
    cy
      .getDataIdCy({
        idAlias: DataTestSelectorVideoTab.SearchRedactionCodeField,
      })
      .find('input'),
  measuringBarChildren: () =>
    cy
      .getDataIdCy({ idAlias: DataTestSelectorTimeline.MeasuringBar })
      .children()
      .eq(2),
  mediaDetailCloseBtn: () =>
    cy.get(
      `[data-veritone-element=${DataVeritoneSelector.MediaDetailCloseBtn}]`
    ),
  saveModalButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.SaveModalButton }),
  rightPanelFilterContainer: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.RightPanelFilterContainer,
    }),
  filterDetectionsButton: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.FilterDetectionsButton,
    }),
  objectType: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.RedactionDetectionType,
    }),
  redactionType: () =>
    cy.getDataIdCy({ idAlias: DataTestSelectorRightPanelVideo.RedactionType }),
  blurLevelInput: () =>
    cy
      .getDataIdCy({ idAlias: DataTestSelectorRightPanelVideo.BlurLevel })
      .find('input'),
  saveButtonRedaction: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.SaveButtonRedaction,
    }),
  confirmDialogButton: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.ConfirmDialogButton }),
  personFilterCheckbox: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.PersonFilterCheckbox,
    }),
  keyboardShortcutBtn: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelector.keyboardShortcutButton,
    }),
  selectFromDropdown: (item: string) =>
    cy.get('[role=menuitem]').contains(item),
  header: {
    ...header,
  },
  footer: {
    ...footer,
  },

  videoPlayScreen: () =>
    cy.getDataIdCy({ idAlias: DataTestSelectorVideoTab.VideoPlayScreen }),
  fileDetailTimeline: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.FileDetailTimeline }),
  resultTabVideo: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.ResultTabVideo }),
  audioTab: () =>
    cy.getDataIdCy({ idAlias: DataTestSelectorRightPanelVideo.AudioTab }),
  commentsTab: () =>
    cy.getDataIdCy({ idAlias: DataTestSelectorRightPanelVideo.CommentsTab }),
  headValue: () =>
    cy.getDataIdCy({ idAlias: DataTestSelectorTimeline.HeadValue }),
  personValue: () =>
    cy.getDataIdCy({ idAlias: DataTestSelectorTimeline.PersonValue }),
  restoreDefaultButton: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.RestoreDefaultButton),
    }),
  restoreDefaultColorButton: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorVideoTab.RestoreDefaultColorButton),
    }),
  videoMessages: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelector.VideoMessages),
    }),
  listOfObjects: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.ListOfObjects),
    }),
  detectFaceButton: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.DetectFaceButton),
    }),
  measuringBar: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorTimeline.MeasuringBar),
    }),
  mediaDetailPageContainer: () =>
    cy.get(`[data-veritone-element=${DataVeritoneSelector.MediaDetailPage}]`),
  homePage: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelector.HomePage),
    }),
  redactionEffectsDialog: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.RedactionEffectsDialog),
    }),
  objectRedaction: () =>
    cy.getDataIdCy({ idAlias: DataTestSelectorVideoTab.ObjectRedaction }),
  checkBoxContainer: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.CheckBoxContainer),
    }),
  sortByTimeButton: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.SortByTimeButton),
    }),
  cancelButton: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.CancelButton),
    }),
  engineProcessingTitle: () =>
    cy
      .getDataIdCy({
        idAlias: String(DataTestSelectorRightPanelVideo.EngineProcessingTitle),
      })
      .parent(),
  detectingObjects: () =>
    cy.contains(DataTestSelectorMessages.DetectingObjects),
  engineProcessingMessage: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.EngineProcessingMessage,
    }),
  homePageButton: () =>
    `[data-veritone-element=${DataVeritoneSelector.HomePageButton}]`,
  filterDetectionsDialog: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.FilterDetectionsDialog),
    }),
  UDRFilterCheckbox: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.UDRFilterCheckbox),
    }),
  headerFilterCheckbox: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.HeaderFilterCheckbox),
    }),
  allFilterCheckbox: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.AllFilterCheckbox),
    }),
  confirmDialogContent: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelector.ConfirmDialogContent),
    }),
  UDRFilterRemoveCheckBox: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.UDRFilterRemoveCheckBox),
    }),
  headFilterRemoveCheckBox: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorRightPanelVideo.HeadFilterRemoveCheckBox),
    }),
  personFilterRemoveCheckBox: () =>
    cy.getDataIdCy({
      idAlias: String(
        DataTestSelectorRightPanelVideo.PersonFilterRemoveCheckBox
      ),
    }),
  timelineRow: () =>
    cy.getDataIdCy({ idAlias: String(DataTestSelectorTimeline.TimelineRow) }),
  confirmDialogTitle: () =>
    cy.getDataIdCy({ idAlias: DataTestSelector.ConfirmDialogTitle }),
  nameGroup: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.NameGroup,
    }),
  nameGroupInput: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.NameGroupInput,
    }),
  groupItemMenu: () =>
    cy.getDataIdCy({ idAlias: DataTestSelectorRightPanelVideo.GroupItemMenu }),
  mergedWidthNamedGroup: () =>
    cy.getDataIdCy({
      idAlias: DataTestSelectorRightPanelVideo.MergedWidthNamedGroup,
    }),
  detectionName: () =>
    cy.get(`[data-test=${DataTestSelectorRightPanelVideo.DetectionName}]`),
  cancelButtonModal: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelector.CancelButtonModal),
    }),
  shapesMenu: () =>
    cy.getDataIdCy({
      idAlias: String(DataTestSelectorVideoTab.ShapesMenu),
    }),
};

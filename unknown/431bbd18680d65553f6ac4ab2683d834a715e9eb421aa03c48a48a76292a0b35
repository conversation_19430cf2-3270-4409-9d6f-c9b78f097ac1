import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { ServiceFetchUserOrgResponse } from '../services';

export const REQUEST_USER_ORGANIZATION =
  'vtn-component-user-onboarding/REQUEST_USER_ORGANIZATION';
export const REQUEST_USER_ORGANIZATION_SUCCESS =
  createGraphQLSuccessAction<ServiceFetchUserOrgResponse>(
    'vtn-component-user-onboarding/REQUEST_USER_ORGANIZATION_SUCCESS'
  );
export const REQUEST_USER_ORGANIZATION_FAILURE = createGraphQLFailureAction(
  'vtn-component-user-onboarding/REQUEST_USER_ORGANIZATION_FAILURE'
);

export const START_POLLING_USER_ORG =
  'vtn-component-user-onboarding/START_POLLING_USER_ORG';
export const STOP_POLLING_USER_ORG =
  'vtn-component-user-onboarding/STOP_POLLING_USER_ORG';

export interface StartPollingUserOrgRequest {
  readonly initWait: number;
}
/**
 * This action will either start polling or reset `initWait` on current polling.
 * Polling will start at `initWait` and increase wait over time.
 * @param initWait - ms to pause between polling.
 */
export const actionStartPollingUserOrg = ({
  initWait = 5000,
}: Partial<StartPollingUserOrgRequest>) => ({
  type: START_POLLING_USER_ORG,
  payload: { initWait },
});

export const actionStopPollingUserOrg = () => ({
  type: STOP_POLLING_USER_ORG,
});

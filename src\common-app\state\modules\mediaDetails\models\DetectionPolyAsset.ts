import { BoundingPoly } from './BoundingPoly';

export interface DetectionPolyAsset {
  readonly assetType?: 'vtn-standard';
  readonly taskId?: string;
  readonly jobId?: string;
  readonly contentType?: 'application/json';
  readonly language?: string;
  readonly sourceEngineName?: 'task-redact-detection-iron';
  readonly recordingId?: string;
  readonly sourceEngineId?: string;
  readonly series: ReadonlyArray<BoundingPoly>;
}

import { defaultState } from './store';
import { MainPageStore } from './MainPageStore';
import * as Actions from './actions';
import { CaseReducer, createReducer } from '@reduxjs/toolkit';

type Re<P = unknown> = CaseReducer<MainPageStore, { payload: P; type: string }>;

const setCasesSummary: Re<MainPageStore['casesSummary']> = (
  state,
  { payload }
) => ({
  ...state,
  casesSummary: payload,
});

const setSearchText: Re<MainPageStore['searchText']> = (
  state,
  { payload }
) => ({
  ...state,
  searchText: payload,
});

const setStatusFilter: Re<MainPageStore['statusFilter']> = (
  state,
  { payload }
) => ({
  ...state,
  statusFilter: payload,
});

const setShowArchived: Re<MainPageStore['showArchived']> = (
  state,
  { payload }
) => ({
  ...state,
  showArchived: payload,
});

const setCasesList: Re<MainPageStore['casesList']> = (state, { payload }) => ({
  ...state,
  casesList: payload,
});

const setCasesTotal: Re<MainPageStore['casesTotal']> = (
  state,
  { payload }
) => ({
  ...state,
  casesTotal: payload,
});

const setCasesSortColumn: Re<MainPageStore['casesSortColumn']> = (
  state,
  { payload }
) => ({
  ...state,
  casesSortColumn: payload,
});

const setCasesSortOrder: Re<MainPageStore['casesSortOrder']> = (
  state,
  { payload }
) => ({
  ...state,
  casesSortOrder: payload,
});

const setPaginationAmount: Re<MainPageStore['paginationAmount']> = (
  state,
  { payload }
) => ({
  ...state,
  paginationAmount: payload,
});

const setPaginationStart: Re<MainPageStore['paginationStart']> = (
  state,
  { payload }
) => ({
  ...state,
  paginationStart: payload,
});

export const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(Actions.SET_CASES_SUMMARY, setCasesSummary)
    .addCase(Actions.SET_SEARCH_TEXT, setSearchText)
    .addCase(Actions.SET_STATUS_FILTER, setStatusFilter)
    .addCase(Actions.SET_SHOW_ARCHIVED, setShowArchived)
    .addCase(Actions.SET_CASES_LIST, setCasesList)
    .addCase(Actions.SET_CASES_TOTAL, setCasesTotal)
    .addCase(Actions.SET_CASES_SORT_COLUMN, setCasesSortColumn)
    .addCase(Actions.SET_CASES_SORT_ORDER, setCasesSortOrder)
    .addCase(Actions.SET_PAGINATION_AMOUNT, setPaginationAmount)
    .addCase(Actions.SET_PAGINATION_START, setPaginationStart);
});

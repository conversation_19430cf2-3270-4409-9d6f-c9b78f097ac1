import { get } from 'lodash';
import { RefObject, useEffect } from 'react';
import { fromEvent, Subject } from 'rxjs';
import {
  distinctUntilChanged,
  filter,
  map,
  scan,
  startWith,
  switchMap,
  takeUntil,
} from 'rxjs/operators';

import { notNil } from '@utils';
import { TranscriptionPropTypes } from './TranscriptionPropTypes';

export const useDragSelectEffect = ({
  ref,
  transcription,
  onSeekPlayVideo,
  onSelectRangeWords,
  onPauseVideo,
}: UseDragSelectEffect) => {
  useEffect(() => {
    const div = ref.current;
    const onDestroy$ = new Subject<boolean>();

    if (div) {
      const mousedown$ = fromEvent<MouseEvent>(div, 'mousedown');
      const mousemove$ = fromEvent<MouseEvent>(div, 'mousemove');
      const mouseup$ = fromEvent<MouseEvent>(document, 'mouseup');

      mousedown$
        .pipe(
          filter((evt) => !evt.shiftKey && evt.button === 0),
          takeUntil(onDestroy$),
          switchMap((e) =>
            mousemove$.pipe(
              startWith(e),
              takeUntil(mouseup$),
              map((evt) => get(evt, 'target.id')),
              filter(notNil),
              distinctUntilChanged() as any, // TODO: fix type
              scan<string, { from: string; to: string }>(
                ({ from }, to) => ({
                  from: from || to,
                  to,
                }),
                { from: '', to: '' }
              )
            )
          )
        )
        .subscribe(({ from, to }) => {
          if (
            from.startsWith('transcription_') &&
            to.startsWith('transcription_')
          ) {
            const fromWord = transcription.find(({ id }) => from === id);
            const toWord = transcription.find(({ id }) => to === id);
            if (fromWord && toWord) {
              onSelectRangeWords([fromWord, toWord]);
              if (fromWord === toWord) {
                onSeekPlayVideo({ startTimeMs: fromWord.startTimeMs });
              } else {
                onPauseVideo();
              }
            }
          }
        });
    }

    return () => onDestroy$.next(true);
  }, [onPauseVideo, onSeekPlayVideo, onSelectRangeWords, ref, transcription]);
};

export type UseDragSelectEffect = Pick<
  TranscriptionPropTypes,
  'transcription' | 'onSelectRangeWords' | 'onSeekPlayVideo' | 'onPauseVideo'
> & { ref: RefObject<HTMLDivElement> };

import { callGQL } from '../api/callGraphql';
import { getUserInfoQuery } from '../api/queries';
import { Messages } from '../errors/messages'
import { RequestHeader } from '../model/requests';
import { Logger } from '../logger';
import { GetUserInfoResponse } from '../model/responses';

export const getUserInfoAdapter = async (
  headers: RequestHeader,
) => {
  const userInfo = await callGQL<GetUserInfoResponse>( headers, getUserInfoQuery)
    .then((res) => res.me)
    .catch((err) => {
      Logger.error(Messages.getUserInfoFail + JSON.stringify(err));
      return undefined;
    });
  return userInfo;
};
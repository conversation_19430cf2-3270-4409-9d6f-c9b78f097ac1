import {
  all,
  debounce,
  fork,
  put,
  takeLatest,
  select,
} from 'typed-redux-saga/macro';
import { sagaIntl } from '@common/i18n';
import * as Selectors from '../selectors';
import { Case, FolderId, TreeObjectId } from '@cbsa-modules/universal';
import { enqueueSnackbar } from '@common-modules/snackbar';
import {
  FETCH_CASES,
  FETCH_CASES_SUCCESS,
  FETCH_CASES_FAILURE,
  FETCH_SUMMARY,
  FETCH_SUMMARY_SUCCESS,
  FETCH_SUMMARY_FAILURE,
  PUT_CASE,
  PUT_CASE_SUCCESS,
  PUT_CASE_FAILURE,
  DELETE_CASE,
  DELETE_CASE_SUCCESS,
  DELETE_CASE_FAILURE,
  ARCHIVE_CASE,
  ARCHIVE_CASE_SUCCESS,
  ARCHIVE_CASE_FAILURE,
  setCasesList,
  setCasesSummary,
  setCasesTotal,
} from '../actions';
import {
  archiveCase,
  deleteCase,
  fetchCasesSearch,
  fetchCasesSummary,
  putNewCase,
} from '../services';

// interface Action<P> {
//   type: string;
//   payload: P;
// }

export function* initCasesList() {
  // This just fires up saga listeners. Triggering the actions happens
  // in the React component
  yield* all([
    fork(fetchCasesHandler),
    fork(fetchCasesSuccess),
    fork(fetchCasesFailure),
    fork(fetchSummaryHandler),
    fork(fetchSummarySuccess),
    fork(fetchSummaryFailure),
    fork(putCaseHandler),
    fork(putCaseSuccess),
    fork(putCaseFailure),
    fork(deleteCaseHandler),
    fork(deleteCaseFailure),
    fork(deleteCaseSuccess),
    fork(archiveCaseHandler),
    fork(archiveCaseSuccess),
    fork(archiveCaseFailure),
  ]);
}

function* fetchCasesHandler(): unknown {
  yield* debounce(500, FETCH_CASES, function* () {
    const searchText = yield* select(Selectors.selectSearchText);
    const statusFilter = yield* select(Selectors.selectStatusFilter);
    const showArchived = yield* select(Selectors.selectShowArchived);
    const casesSortColumn = yield* select(Selectors.selectCasesSortColumn);
    const casesSortOrder = yield* select(Selectors.selectCasesSortOrder);
    const paginationAmount = yield* select(Selectors.selectPaginationAmount);
    const paginationStart = yield* select(Selectors.selectPaginationStart);

    yield* put(
      fetchCasesSearch({
        searchText,
        statusFilter,
        showArchived,
        casesSortColumn,
        casesSortOrder,
        paginationAmount,
        paginationStart,
      })
    );
  });
}

function* fetchCasesSuccess() {
  yield* takeLatest(FETCH_CASES_SUCCESS, function* ({ payload }) {
    // Hide `Hide Once` from displayed results
    const cbsa_hideOnceFolders =
      (JSON.parse(
        window.localStorage.getItem('cbsa_hideOnceFolders') || '[]'
      ) as TreeObjectId[]) || [];
    const filteredFetchedCases = payload.results.filter(
      (current) =>
        current?.folderTreeObjectId &&
        !cbsa_hideOnceFolders.includes(current.folderTreeObjectId)
    );
    window.localStorage.setItem('cbsa_hideOnceFolders', JSON.stringify([]));

    // Show `Show Once` in displayed results
    const cbsa_showOnceFolders: Case[] =
      (JSON.parse(
        window.localStorage.getItem('cbsa_showOnceFolders') || '[]'
      ) as Case[]) || []; // TODO: should we change the localStorage key if this is Cases?

    const idMap = filteredFetchedCases.map(
      (currentMap) => currentMap.folderTreeObjectId
    );
    cbsa_showOnceFolders.forEach((currentFolder) => {
      if (!idMap.includes(currentFolder.folderTreeObjectId)) {
        filteredFetchedCases.unshift(currentFolder);
      }
    });

    window.localStorage.setItem('cbsa_showOnceFolders', JSON.stringify([]));

    filteredFetchedCases.forEach((fetchedCase) => {
      /* Reassign BE variable names */
      fetchedCase.treeObjectId = fetchedCase.folderTreeObjectId;
      fetchedCase.name = fetchedCase.caseName ?? '';
    });

    yield* put(setCasesList(filteredFetchedCases));
    yield* put(setCasesTotal(payload.totalResults));
  });
}

function* fetchCasesFailure() {
  yield* takeLatest(FETCH_CASES_FAILURE, function* ({ payload }) {
    yield* put(enqueueSnackbar(payload));
  });
}

function* fetchSummaryHandler() {
  yield* takeLatest(FETCH_SUMMARY, function* () {
    yield* put(setCasesSummary(null));
    yield* put(fetchCasesSummary({}));
  });
}

function* fetchSummarySuccess() {
  yield* takeLatest(FETCH_SUMMARY_SUCCESS, function* ({ payload }) {
    yield* put(
      setCasesSummary({
        ...payload,
      })
    );
  });
}

function* fetchSummaryFailure() {
  yield* takeLatest(FETCH_SUMMARY_FAILURE, function* ({ payload }) {
    yield* put(enqueueSnackbar(payload));
  });
}

function* putCaseHandler() {
  yield* takeLatest(PUT_CASE, function* ({ payload }) {
    yield* put(setCasesList([]));
    yield* put(setCasesSummary(null));

    yield* put(putNewCase({ newCaseName: payload }));
  });
}

function* putCaseSuccess() {
  yield* takeLatest(PUT_CASE_SUCCESS, function* ({ payload }) {
    const { caseId, caseData: _caseData } = payload;

    // // This adds the newly created case to the localStorage
    // // showOnce stack. Not required if redirecting on case
    // // creation.
    // const cbsa_showOnceFolders: Case[] =
    //   JSON.parse(window.localStorage.getItem('cbsa_showOnceFolders') || '[]') as Case[] || [];
    //
    // const showOnceIdMap = cbsa_showOnceFolders.map(current => current.folderTreeObjectId);
    //
    // if (!showOnceIdMap.includes(caseData.folderTreeObjectId)) {
    //   cbsa_showOnceFolders.push(caseData);
    // }
    // window.localStorage.setItem('cbsa_showOnceFolders', JSON.stringify(cbsa_showOnceFolders));

    yield* put(
      enqueueSnackbar({
        variant: 'success',
        message: sagaIntl().formatMessage({ id: 'newCaseCreated' }),
      })
    );
    window.location.href = `/case/${caseId}`;
  });
}

function* putCaseFailure() {
  yield* takeLatest(PUT_CASE_FAILURE, function* ({ payload }) {
    yield* put(
      enqueueSnackbar({
        variant: 'error',
        message: payload,
      })
    );

    yield* all([put(FETCH_CASES()), put(FETCH_SUMMARY())]);
  });
}

function* deleteCaseHandler() {
  yield* takeLatest(DELETE_CASE, function* ({ payload }) {
    yield* put(setCasesList([]));
    yield* put(setCasesSummary(null));
    yield* put(deleteCase({ deleteId: payload }));
  });
}

function* deleteCaseSuccess() {
  yield* takeLatest(DELETE_CASE_SUCCESS, function* ({ payload }) {
    const cbsa_hideOnceFolders =
      (JSON.parse(
        window.localStorage.getItem('cbsa_hideOnceFolders') || '[]'
      ) as FolderId[]) || [];
    if (!cbsa_hideOnceFolders.includes(payload)) {
      cbsa_hideOnceFolders.push(payload);
      window.localStorage.setItem(
        'cbsa_hideOnceFolders',
        JSON.stringify(cbsa_hideOnceFolders)
      );
    }

    yield* put(
      enqueueSnackbar({
        variant: 'success',
        message: sagaIntl().formatMessage({ id: 'caseDeleted' }),
      })
    );

    yield* all([put(FETCH_CASES()), put(FETCH_SUMMARY())]);
  });
}

function* deleteCaseFailure() {
  yield* takeLatest(DELETE_CASE_FAILURE, function* ({ payload }) {
    yield* put(
      enqueueSnackbar({
        variant: 'error',
        message: payload,
      })
    );

    yield* all([put(FETCH_CASES()), put(FETCH_SUMMARY())]);
  });
}

function* archiveCaseHandler() {
  yield* takeLatest(ARCHIVE_CASE, function* ({ payload }): unknown {
    yield* put(setCasesList([]));
    yield* put(setCasesSummary(null));
    yield* put(archiveCase({ archiveId: payload }));
  });
}

function* archiveCaseSuccess() {
  yield* takeLatest(ARCHIVE_CASE_SUCCESS, function* ({ payload }) {
    const cbsa_hideOnceFolders =
      (JSON.parse(
        window.localStorage.getItem('cbsa_hideOnceFolders') || '[]'
      ) as FolderId[]) || [];
    if (!cbsa_hideOnceFolders.includes(payload)) {
      cbsa_hideOnceFolders.push(payload);
      window.localStorage.setItem(
        'cbsa_hideOnceFolders',
        JSON.stringify(cbsa_hideOnceFolders)
      );
    }

    yield* put(
      enqueueSnackbar({
        variant: 'success',
        message: sagaIntl().formatMessage({ id: 'caseArchived' }),
      })
    );

    yield* all([put(FETCH_CASES()), put(FETCH_SUMMARY())]);
  });
}

function* archiveCaseFailure() {
  yield* takeLatest(ARCHIVE_CASE_FAILURE, function* ({ payload }) {
    yield* put(
      enqueueSnackbar({
        variant: 'error',
        message: payload,
      })
    );

    yield* all([put(FETCH_CASES()), put(FETCH_SUMMARY())]);
  });
}

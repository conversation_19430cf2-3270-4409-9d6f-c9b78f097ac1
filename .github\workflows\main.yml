name: Run Tests and Linter

on:
  pull_request:
    branches:
      - 'feature/**'
  push:
    branches:
      - 'master'
      - 'feature/**'

env:
  GITHUB_ACCESS_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '22.x'
      - run: yarn
      - run: yarn
        working-directory: server
      - run: yarn run check-licenses
      - run: yarn run lint
      - run: yarn test

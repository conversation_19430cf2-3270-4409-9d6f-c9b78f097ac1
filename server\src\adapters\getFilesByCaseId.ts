import { FileResponse, GetFilesResponse } from "../model/responses";
import { getFilesByCaseIdQuery } from "../api/queries";
import { RequestHeader, getFilesByCaseIdRequest } from "../model/requests";
import { callGQL } from "../api/callGraphql";
import { Logger } from "../logger";
import { Messages } from "../errors/messages";

export const getFilesByCaseIdAdapter = async (
    headers: RequestHeader,
    request: getFilesByCaseIdRequest,
  ) => {   
    const response: {isFailed: boolean; tdos: Array<FileResponse>} = {
      isFailed: false,
      tdos: [],
    }

    try {
      const { caseId, limit, offset } = request;
      const res = await callGQL<GetFilesResponse>(headers, getFilesByCaseIdQuery, { caseId, limit, offset});
      const newTdos = res.folder?.childTDOs?.records || [];
      response.tdos = [...response.tdos, ...newTdos] ; 

    } catch(err) {
      Logger.log(Messages.getFilesByCaseIdFail + JSON.stringify(err)); 
      response.isFailed = true;
    }  
    
    return response;
}
import { Re } from '@cbsa-modules/appWrapper/reducers';
import { CaseId, TreeObjectId, UploadMedia } from '@cbsa-modules/universal';

export const queueFile: Re<{
  readonly file: File;
  readonly caseId: TreeObjectId; // TODO: which type should it be? CaseId;
}> = (state, { payload }) => ({
  ...state,
  uploadingFiles: {
    ...state.uploadingFiles,
    queue: [
      ...state.uploadingFiles.queue,
      {
        key: new Date().getTime(),
        file: payload.file,
        caseId: payload.caseId as unknown as CaseId, // TODO: fix this case
      },
    ],
  },
});

export const processFile: Re<{
  uploadFile: UploadMedia;
}> = (state, { payload }) => ({
  ...state,
  uploadingFiles: {
    ...state.uploadingFiles,
    queue: state.uploadingFiles.queue.filter(
      (file) => file.key !== payload.uploadFile.key
    ),
    processing: [...state.uploadingFiles.processing, payload.uploadFile],
  },
});

export const uploadFileSuccess: Re<{
  uploadFile: UploadMedia;
}> = (state, { payload }) => ({
  ...state,
  uploadingFiles: {
    ...state.uploadingFiles,
    processing: state.uploadingFiles.processing.filter(
      (file) => file.key !== payload.uploadFile.key
    ),
    successful: [...state.uploadingFiles.successful, payload.uploadFile],
  },
});

export const uploadFileFailure: Re<{
  uploadFile: UploadMedia;
}> = (state, { payload }) => {
  const index = state.uploadingFiles.processing.indexOf(payload.uploadFile);
  return {
    ...state,
    uploadingFiles: {
      ...state.uploadingFiles,
      processing: [
        ...state.uploadingFiles.processing.slice(0, index),
        ...state.uploadingFiles.processing.slice(index + 1),
      ],
      failed: [...state.uploadingFiles.failed, payload.uploadFile],
    },
  };
};

export const removeSuccessfulFile: Re<{
  uploadFile: UploadMedia;
}> = (state, { payload }) => {
  const index = state.uploadingFiles.successful.indexOf(payload.uploadFile);
  return {
    ...state,
    uploadingFiles: {
      ...state.uploadingFiles,
      successful: [
        ...state.uploadingFiles.successful.slice(0, index),
        ...state.uploadingFiles.successful.slice(index + 1),
      ],
    },
  };
};

export const retryFailedFile: Re<{
  uploadFile: UploadMedia;
}> = (state, { payload }) => {
  const index = state.uploadingFiles.failed.indexOf(payload.uploadFile);
  return {
    ...state,
    uploadingFiles: {
      ...state.uploadingFiles,
      queue: [...state.uploadingFiles.queue, payload.uploadFile],
      failed: [
        ...state.uploadingFiles.failed.slice(0, index),
        ...state.uploadingFiles.failed.slice(index + 1),
      ],
    },
  };
};

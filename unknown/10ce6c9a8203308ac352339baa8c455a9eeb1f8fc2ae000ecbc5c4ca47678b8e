import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { fileStatus } from '../../src/controllers/fileStatus';
import { fetchIngestedFileStatusQuery } from '../../src/api/queries';
import { Messages } from '../../src/errors/messages';

describe('fileStatus', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('checks the status of ingested file w/ tdoId', async () => {
    const params = { tdoId: '123456789' };
    const req = getMockReq({
      params,
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.resolve({
      temporalDataObject: {
        id: '',
        name: '',
        jobs: {},
      },
    }));

    await fileStatus(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, fetchIngestedFileStatusQuery(params.tdoId));
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('fails to checks the status of ingested file w/o tdoId', async () => {
    const req = getMockReq({
      params: { tdoId: '' },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.reject());

    await fileStatus(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith( { 
      error: Messages.tdoIdRequired
    });
  });
});

import { CreateTdoResponse } from '../model/responses';
import { CreateTdoRequest, RequestHeader } from '../model/requests';
import { callGQL } from '../api/callGraphql';
import { createTdoQuery } from '../api/queries';
import { Messages } from '../errors/messages'
import { Logger } from '../logger';

export const createTdoAdapter = async (
  headers: RequestHeader,
  request: CreateTdoRequest
) => {
    const timestamp =  new Date().toISOString();
    const requestInput = {
        startDateTime: timestamp,
        stopDateTime: timestamp, 
        addToIndex: true,
        parentFolderId: request.caseId,
        details: {
          govQARequestId: request?.externalIds?.govQARequestId,
          foiaXpressRequestId: request?.externalIds?.foiaXpressRequestId,
          casepointRequestId: request?.externalIds?.casepointRequestId,
          nuixRequestId: request?.externalIds?.nuixRequestId,
          exterroRequestId: request?.externalIds?.exterroRequestId,
          tags: [
            {
              value: 'in redaction',
              redactionStatus: 'Draft',
            },
          ],
          veritoneProgram: {
            programLiveImage: '',
          },
        }    
      };   

    const tdoId = await callGQL<CreateTdoResponse>(
        headers,
        createTdoQuery,
        { input: requestInput, url: request.url }
      )
        .then((res) => res.createTDO?.id)
        .catch((err) => {
          Logger.error(Messages.createTdoFail + JSON.stringify(err));
        });

    return tdoId;
  }
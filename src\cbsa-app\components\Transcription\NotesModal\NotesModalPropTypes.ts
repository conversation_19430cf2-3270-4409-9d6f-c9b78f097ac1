import { createSelector } from 'reselect';

import {
  actionCloseNotes,
  actionDeleteNotes,
  actionUpdateNotes,
} from '@common-modules//mediaDetails';
import {
  selectRedactNotesEditing,
  selectRedactNotesIsEditorOpen,
} from '@common-modules/mediaDetails/selectors';

export const mapStateToProps = createSelector(
  selectRedactNotesIsEditorOpen,
  selectRedactNotesEditing,
  (isOpen, notes) => ({
    isOpen,
    notes,
  })
);

export const mapDispatchToProps = {
  onUpdateNotes: actionUpdateNotes,
  onCloseNotes: actionCloseNotes,
  onDeleteNotes: actionDeleteNotes,
};

export type NotesModalPropTypes = ReturnType<typeof mapStateToProps> &
  typeof mapDispatchToProps;

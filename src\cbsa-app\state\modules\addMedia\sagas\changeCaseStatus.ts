import * as Actions from '../actions';
import * as Services from '../services';
import { CaseStatus } from '@cbsa-modules/universal';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { put, takeEvery, takeLatest } from 'typed-redux-saga/macro';
import { sagaIntl } from '@common/i18n';

const successMessages = (status: CaseStatus) => {
  const intl = sagaIntl();
  return {
    new: intl.formatMessage({ id: 'caseIsNew' }),
    processing: intl.formatMessage({ id: 'caseIsProcessing' }),
    error: intl.formatMessage({ id: 'caseHasAnError' }),
    readyForReview: intl.formatMessage({
      id: 'caseIsReadyForReview',
    }),
    approved: intl.formatMessage({ id: 'caseHasBeenApproved' }),
    readyForExport: intl.formatMessage({
      id: 'caseIsReadyForExport',
    }),
    deleted: intl.formatMessage({ id: 'caseHasBeenDeleted' }),
  }[status];
};

export function* changeCaseStatus() {
  yield* takeLatest(Actions.CHANGE_CASE_STATUS, function* ({ payload }) {
    const { caseDetails, caseStatus } = payload;
    yield* put(Services.changeCaseStatus({ caseDetails, caseStatus }));
  });
}

export function* changeCaseStatusSuccess() {
  // yield* takeEvery(
  //   Actions.CHANGE_CASE_STATUS_SUCCESS,
  //   function* ({ meta }) {
  //     const {
  //       variables: {
  //         data: { status },
  //       },
  //     } = meta;

  //     yield* put(
  //       enqueueSnackbar({
  //         message: successMessages(status),
  //         variant: 'success',
  //       })
  //     );
  //   }
  // );
  // TODO: Validate this is correct
  yield* takeEvery(Actions.CHANGE_CASE_STATUS_SUCCESS, function* ({ meta }) {
    const {
      variables: {
        data: { status },
      },
    } = meta;

    yield* put(
      enqueueSnackbar({
        message: successMessages(status),
        variant: 'success',
      })
    );
  });
}

export function* changeCaseStatusFailure() {
  yield* takeEvery(Actions.CHANGE_CASE_STATUS_FAILURE, function* () {
    yield* put(
      enqueueSnackbar({
        message: sagaIntl().formatMessage({
          id: 'caseStatusWasNotUpdated',
        }),
        variant: 'error',
      })
    );
  });
}

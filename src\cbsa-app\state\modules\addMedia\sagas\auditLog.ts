import * as Actions from '../actions';
import * as Services from '../services';
import { selectTDOs } from '../selectors';
import { put, takeLatest, select } from 'typed-redux-saga/macro';

export function* getCaseAuditLogs() {
  yield* takeLatest(Actions.GET_CASE_AUDIT_LOGS, function* () {
    const tdos = yield* select(selectTDOs);
    for (const tdo of Object.values(tdos)) {
      yield* put(Services.getTdoAuditLogs({ tdoId: tdo.id }));
      yield* put(Actions.addAuditLogQueryPending());
    }
  });
  yield* takeLatest(Actions.GET_TDO_AUDIT_LOGS_SUCCESS, function* () {
    yield* put(Actions.subtractAuditLogQueryPending());
  });
}

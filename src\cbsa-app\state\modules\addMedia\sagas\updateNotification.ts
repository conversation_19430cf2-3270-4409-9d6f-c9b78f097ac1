import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import * as Services from '../services';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { put, takeEvery, takeLatest } from 'typed-redux-saga/macro';

export function* dismissNotification() {
  yield* takeLatest(Actions.DISMISS_NOTIFICATION, function* ({ payload }) {
    const { notification } = payload;
    yield* put(
      Services.updateNotification({
        notification,
        status: 'dismissed',
      })
    );
  });
}

export function* clearNotification() {
  yield* takeLatest(Actions.CLEAR_NOTIFICATION, function* ({ payload }) {
    const { notification } = payload;
    yield* put(
      Services.updateNotification({ notification, status: 'cleared' })
    );
  });
}

export function* dismissNotificationFailure() {
  yield* takeEvery(Actions.DISMISS_NOTIFICATION_FAILURE, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({ id: 'notiDismissFail' }),
        variant: 'error',
      })
    );
  });
}

export function* clearNotificationFailure() {
  yield* takeEvery(Actions.CLEAR_NOTIFICATION_FAILURE, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({ id: 'notiClearFail' }),
        variant: 'error',
      })
    );
  });
}

import Spinner from './Spinner';
import { useStyles } from './styles';
import Link from '@mui/material/Link';
import Button from '@mui/material/Button';
import { I18nTranslate } from '@common/i18n';
import { ThemeProvider } from '@mui/material';
import { selectUser } from '@helpers/tdoHelper';
import { LOCK_REFRESH } from '@helpers/constants';
import AppWrapper from '@cbsa-components/AppWrapper';
import { useDispatch, useSelector } from 'react-redux';
import Editor from '@cbsa-components/MediaDetails/Editor';
import { defaultTheme } from '@cbsa/styles/materialThemes';
import ArrowBackIos from '@mui/icons-material/ArrowBackIos';
import { useState, useEffect, useMemo, useRef } from 'react';
import CircularProgress from '@mui/material/CircularProgress';
import {
  checkTdoLock,
  removeTdoLock,
  selectTdoReadonly,
} from '@common-modules/mediaDetails';
import { componentSelectors, componentActions } from './reduxHelpers';
import RouteLoadingScreen from '@common-components/RouteLoadingScreen';
import SaveRedactionModal from '@cbsa-components/MediaDetails/Editor/SaveRedactionModal';
import { selectFeatureFlags } from '@common/user-permissions';

const onBeforeUnloadHandler = (event: BeforeUnloadEvent) => {
  event.preventDefault();
  // For browser compatibility - TODO: Potentially update to use visibility APIs rather than beforeunload
  // eslint-disable-next-line @typescript-eslint/no-deprecated
  event.returnValue = I18nTranslate.Intl().formatMessage({
    id: 'navigationDiscardWarning',
  });
};

const MediaDetailsContent = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const {
    isLoading,
    hasUnsavedChanges,
    faceDetectionInfo,
    caseId,
    tdoId,
    caseMedia,
    uploadingFiles,
    isRedactionAllowed,
    isSaveEnabled,
    gettingRedactionJobStatus,
  } = useSelector(componentSelectors);
  const { caseName, media } = caseMedia;
  const { isLocked } = useSelector(selectTdoReadonly);
  const { kvp, userId } = useSelector(selectUser);

  const currentFile = media.find((tdo) => tdo.id === tdoId);
  const interval = useRef<NodeJS.Timeout>();
  const { tdoLock } = useSelector(selectFeatureFlags);

  const [isPreRedactJobResult, setIsPreRedactJobResult] = useState(false);
  const [isSaveRedactionModalOpen, setIsSaveRedactionModalOpen] =
    useState(false);
  const [timer, runTimer] = useState<number | undefined>();

  useEffect(() => () => clearTimeout(timer), [timer]);

  useEffect(() => {
    if (tdoLock && tdoId && userId && !interval.current) {
      const lock = () =>
        dispatch(
          checkTdoLock({
            name: `${kvp?.firstName} ${kvp?.lastName}`,
            userId,
            tdoId: tdoId,
            lastAccessed: new Date().toISOString(),
          })
        );

      lock();
      const timer = setTimeout(() => {
        lock();
        interval.current = setInterval(lock, LOCK_REFRESH);
      }, LOCK_REFRESH / 6);

      return () => clearTimeout(timer);
    }
  }, [dispatch, kvp?.firstName, kvp?.lastName, tdoId, tdoLock, userId]);

  const handleLeavePage = () => {
    if (tdoLock && !isLocked && userId) {
      dispatch(removeTdoLock(tdoId, userId));
    }
  };

  useEffect(() => {
    window.addEventListener('unload', handleLeavePage);
    return () => {
      clearInterval(interval.current);
      window.removeEventListener('unload', handleLeavePage);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const isRedactionRunning = gettingRedactionJobStatus || isPreRedactJobResult;

  const onSaveChanges = useMemo(
    () => () => setIsSaveRedactionModalOpen(true),
    [setIsSaveRedactionModalOpen]
  );

  const handleSubmitSaveRedactionModal = useMemo(
    () => () => {
      dispatch(componentActions.onUpdateAsset());
      setIsSaveRedactionModalOpen(false);
    },
    [dispatch, setIsSaveRedactionModalOpen]
  );

  const onStartRedaction = useMemo(
    () => () => {
      dispatch(
        componentActions.onTryRedact({
          permissionKey: 'allowRunRedaction',
          onAllow: function* () {
            setIsPreRedactJobResult(true);
            runTimer(
              window.setTimeout(() => setIsPreRedactJobResult(false), 10000)
            );
            yield dispatch(componentActions.onRedact());
          },
        })
      );
    },
    [dispatch, setIsPreRedactJobResult]
  );

  useEffect(() => {
    dispatch(componentActions.fetchCaseMedia(caseId));
  }, [caseId, dispatch, uploadingFiles]);

  useEffect(() => {
    window.removeEventListener('beforeunload', onBeforeUnloadHandler);
    if (hasUnsavedChanges) {
      window.addEventListener('beforeunload', onBeforeUnloadHandler);
    }
    return () =>
      window.removeEventListener('beforeunload', onBeforeUnloadHandler);
  }, [hasUnsavedChanges]);

  const saveButtonStyle = isSaveEnabled
    ? [classes.saveButtonStyle, 'enabled']
    : [classes.saveButtonStyle, 'disabled'];

  const isRedactionDisabled =
    !isRedactionAllowed || isRedactionRunning || isLocked;

  const redactButtonStyle = isRedactionDisabled
    ? [classes.redactButtonStyle, 'disabled']
    : [classes.redactButtonStyle, 'enabled'];

  return isLoading ? (
    <RouteLoadingScreen delayMs={500} />
  ) : (
    <AppWrapper title={`${currentFile?.name} — ${caseName}`}>
      <div className={classes.topBar}>
        <div className="Back">
          <Link
            className="Link"
            href={`/case/${caseId}`}
            onClick={handleLeavePage}
            underline="none"
          >
            <ArrowBackIos classes={{ root: classes.primaryColor }} />
            <div className="Text">{I18nTranslate.TranslateMessage('back')}</div>
          </Link>
        </div>
        <div className="Buttons">
          <Button
            variant="contained"
            color="primary"
            classes={{ root: saveButtonStyle.join(' ') }}
            disabled={!isSaveEnabled || isLocked}
            onClick={onSaveChanges}
            data-veritone-element="save-button"
          >
            {I18nTranslate.TranslateMessage('save')}
          </Button>
          <Button
            variant="contained"
            color="primary"
            classes={{ root: redactButtonStyle.join(' ') }}
            disabled={isRedactionDisabled}
            onClick={onStartRedaction}
            data-veritone-element="redact-file-button"
          >
            {I18nTranslate.TranslateMessage('redactFile')}
            {isRedactionRunning && <CircularProgress size={24} thickness={2} />}
          </Button>
        </div>
      </div>
      <div className={classes.container}>
        <div className={classes.tabContent} id="media-detail-tab">
          <Editor />
        </div>
      </div>
      {isSaveRedactionModalOpen ? (
        <SaveRedactionModal
          open
          onClose={() => setIsSaveRedactionModalOpen(false)}
          onSubmit={handleSubmitSaveRedactionModal}
        />
      ) : (
        ''
      )}
      {!faceDetectionInfo.isReady && faceDetectionInfo.isFetching ? (
        <Spinner
          message={I18nTranslate.Intl().formatMessage({
            id: faceDetectionInfo.statusMessage || 'isBeingProcessed',
          })}
        />
      ) : (
        <></>
      )}
    </AppWrapper>
  );
};

const MediaDetails = () => (
  <ThemeProvider theme={defaultTheme}>
    <MediaDetailsContent />
  </ThemeProvider>
);

export default MediaDetails;

import {
  Checkbox,
  Dialog,
  Tooltip,
  Zoom,
  Paper,
  Box,
  IconButton,
  Button,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import { REDACT_APP_DEFAULT_PROFILE_SETTINGS } from '@helpers/constants';
import cn from 'classnames';
import { connect, ConnectedProps, useDispatch, useSelector } from 'react-redux';
import { ChangeEvent, useEffect, useCallback, useState } from 'react';
import { useIntl } from 'react-intl';
import { useStyles } from './styles';
import {
  selectIngestionOptions,
  setIngestionOptions,
} from '@common-modules/engines/ingestion';
import FaceIcon from '@mui/icons-material/Face';
import PersonIcon from '@mui/icons-material/Person';
import { actionCreateTDO } from '@redact-modules/uploads';
import { CaseId } from '@common/state/modules/universal/models/Brands';
import {
  IngestionStore,
  namespace,
} from '@common/state/modules/engines/ingestion/store';
import {
  selectDefaultProfile,
  selectProfileList,
} from '@redact/state/modules/settingsProfile/selectors';
import { actionFetchProfileList } from '@redact/state/modules/settingsProfile/actions';
import { Close } from '@mui/icons-material';

const UploadURL = ({
  open,
  signedUrl,
  govQARequestId,
  foiaXpressRequestId,
  casepointRequestId,
  nuixRequestId,
  exterroRequestId,
  onCancel,
  setIngestionOptions,
  runHead,
  runPerson,
  runTranscription,
  actionCreateTDO,
  caseId,
}: UploadURLPropTypes) => {
  const intl = useIntl();
  const classes = useStyles();

  const dispatch = useDispatch();

  const allProfiles = useSelector(selectProfileList);
  const defaultProfile = useSelector(selectDefaultProfile);
  const [selectedProfileId, setSelectedProfileId] = useState<string>(
    REDACT_APP_DEFAULT_PROFILE_SETTINGS.id
  );

  useEffect(() => {
    setSelectedProfileId(
      (defaultProfile || REDACT_APP_DEFAULT_PROFILE_SETTINGS).id
    );
  }, [defaultProfile]);

  useEffect(() => {
    dispatch(actionFetchProfileList({ limit: 1000, offset: 0 }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleProfileChange = useCallback(
    (event: SelectChangeEvent<string>) => {
      setSelectedProfileId(event.target.value);
    },
    []
  );

  const handleRunHead = (evt: ChangeEvent<HTMLInputElement>) =>
    setIngestionOptions({ runHead: evt.target.checked });

  const handleRunPerson = (evt: ChangeEvent<HTMLInputElement>) =>
    setIngestionOptions({ runPerson: evt.target.checked });

  const handleRunTranscription = (evt: ChangeEvent<HTMLInputElement>) =>
    setIngestionOptions({ runTranscription: evt.target.checked });

  const handleClickUpload = () => {
    if (signedUrl) {
      const file = {
        getUrl: signedUrl,
      };

      const callback = () => {
        onCancel();
      };

      actionCreateTDO(
        file,
        govQARequestId,
        foiaXpressRequestId,
        casepointRequestId,
        nuixRequestId,
        exterroRequestId,
        callback,
        caseId
      );
    }
  };

  return (
    <Dialog open={open} data-testid="upload-url-dialog">
      <Paper
        classes={{
          root: classes.filePickerPaperOverride,
        }}
      >
        <div className={classes.filePickerHeader}>
          <span className={classes.filePickerTitle}>
            {intl.formatMessage({
              id: 'ingestFromUrl',
              defaultMessage: 'Ingest from URL',
            })}
          </span>
          <IconButton
            className={classes.filePickerCloseButton}
            onClick={onCancel}
          >
            <Close />
          </IconButton>
        </div>
        <Box
          p="8px 16px 16px 0"
          display="flex"
          gap={1}
          justifyContent="space-between"
          alignItems="flex-end"
        >
          <Box>
            <div className={classes.filePickerOption} style={{ marginTop: 6 }}>
              <span className={classes.selectProfileLabel}>
                {intl.formatMessage({
                  id: 'settingsProfileTitle',
                  defaultMessage: 'Settings Profile: ',
                })}
              </span>
              <Select
                name="settings-profile"
                className={classes.selectInput}
                value={selectedProfileId}
                onChange={handleProfileChange}
              >
                <MenuItem
                  value={REDACT_APP_DEFAULT_PROFILE_SETTINGS.id}
                  className={classes.menuItem}
                >
                  {REDACT_APP_DEFAULT_PROFILE_SETTINGS.data.profileName}
                </MenuItem>
                {(allProfiles || []).map((p) => (
                  <MenuItem
                    key={p.id}
                    value={p.id}
                    className={classes.menuItem}
                  >
                    {p.data.profileName}
                  </MenuItem>
                ))}
              </Select>
            </div>
            <div className={classes.filePickerOption}>
              <Checkbox
                checked={runHead}
                color="primary"
                onChange={handleRunHead}
                // inputProps={
                //   { 'data-testid': 'upload-url-checkbox-run-head' } as any // terrible but necessary for now
                // }
                data-testid="upload-url-checkbox-run-head"
              />
              <FaceIcon data-testid="upload-url-face-icon" />
              <Tooltip
                slots={{
                  transition: Zoom,
                }}
                arrow
                title={<span>Detect heads, laptops, and license plates</span>}
                placement="top"
                classes={{
                  tooltip: classes.tooltip,
                }}
              >
                <span className={classes.filePickerContent}>
                  {intl.formatMessage({
                    id: 'filePickerRunHead',
                    defaultMessage: 'Run Head and Object Detection',
                  })}
                </span>
              </Tooltip>
            </div>
            <div className={classes.filePickerOption}>
              <Checkbox
                checked={runPerson}
                color="primary"
                onChange={handleRunPerson}
                data-testid="upload-url-checkbox-run-person"
              />
              <PersonIcon data-testid="person-icon" />
              <Tooltip
                slots={{
                  transition: Zoom,
                }}
                arrow
                title={<span>Detect person, laptops, and license plates</span>}
                placement="top"
                classes={{
                  tooltip: classes.tooltip,
                }}
              >
                <span className={classes.filePickerContent}>
                  {intl.formatMessage({
                    id: 'filePickerRunPerson',
                    defaultMessage: 'Run Person and Object Detection',
                  })}
                </span>
              </Tooltip>
            </div>
            <div className={classes.filePickerOption}>
              <Checkbox
                checked={runTranscription}
                color="primary"
                onChange={handleRunTranscription}
                data-testid="upload-url-checkbox-run-transcription"
              />
              <span
                className={cn(classes.transcriptionIcon, 'icon-transcription')}
              />
              <span className={classes.filePickerContent}>
                {intl.formatMessage({
                  id: 'runTranscription',
                  defaultMessage: 'Run Transcription',
                })}
              </span>
            </div>
            {govQARequestId && (
              <div className={classes.additionalID}>
                {intl.formatMessage(
                  {
                    id: 'govqaID',
                    defaultMessage: 'GovQA Request ID: {govQARequestId}',
                  },
                  { govQARequestId }
                )}
              </div>
            )}
            {foiaXpressRequestId && (
              <div className={classes.additionalID}>
                {intl.formatMessage(
                  {
                    id: 'foiaXpressID',
                    defaultMessage:
                      'FOIAXpress Request ID: {foiaXpressRequestId}',
                  },
                  { foiaXpressRequestId }
                )}
              </div>
            )}
            {casepointRequestId && (
              <div className={classes.additionalID}>
                {intl.formatMessage(
                  {
                    id: 'casepointID',
                    defaultMessage: 'Casepoint ID: {casepointRequestId}',
                  },
                  { casepointRequestId }
                )}
              </div>
            )}
            {nuixRequestId && (
              <div className={classes.additionalID}>
                {intl.formatMessage(
                  { id: 'nuixID', defaultMessage: 'NUIX ID: {nuixRequestId}' },
                  { nuixRequestId }
                )}
              </div>
            )}
            {exterroRequestId && (
              <div className={classes.additionalID}>
                {intl.formatMessage(
                  {
                    id: 'exterroID',
                    defaultMessage: 'Exterro ID: {exterroRequestId}',
                  },
                  { exterroRequestId }
                )}
              </div>
            )}
          </Box>
          <Box display={'flex'} gap={1}>
            <Button
              data-veritone-element={`picker-footer-cancel-button`}
              onClick={onCancel}
              className={classes.filePickerFooterCancelButton}
              data-testid="upload-url-button-cancel"
            >
              {intl.formatMessage({ id: 'cancel', defaultMessage: 'Cancel' })}
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleClickUpload}
              data-testid="upload-url-button-ingest"
            >
              {intl.formatMessage({ id: 'ingest', defaultMessage: 'Ingest' })}
            </Button>
          </Box>
        </Box>
      </Paper>
    </Dialog>
  );
};

const mapStateToDispatch = (state: { [namespace]: IngestionStore }) => ({
  ...selectIngestionOptions(state),
});

const mapPropsToDispatch = {
  setIngestionOptions,
  actionCreateTDO,
};

const connector = connect(mapStateToDispatch, mapPropsToDispatch);
type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(UploadURL);

export interface UploadURLPropTypes extends PropsFromRedux {
  readonly open: boolean;
  readonly signedUrl: string;
  readonly govQARequestId?: string;
  readonly foiaXpressRequestId?: string;
  readonly casepointRequestId?: string;
  readonly nuixRequestId?: string;
  readonly exterroRequestId?: string;
  readonly onCancel: () => void;
  readonly caseId?: CaseId;
}

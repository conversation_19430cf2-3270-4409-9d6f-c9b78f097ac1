import ArrowDownWard from '@mui/icons-material/ArrowDownward';
import ArrowUpward from '@mui/icons-material/ArrowUpward';

import { ViewSettings } from '@common-modules/mediaDetails/models';

const SortIcon = ({
  sortBy: { column, direction },
  type,
}: SortIconPropTypes) =>
  column === type ? (
    <div style={{ marginLeft: '0.25rem' }}>
      {direction === 'desc' ? (
        <ArrowDownWard fontSize="small" />
      ) : (
        <ArrowUpward fontSize="small" />
      )}
    </div>
  ) : null;

export default SortIcon;

export interface SortIconPropTypes {
  readonly type: string;
  readonly sortBy: ViewSettings['sortPolyGroupsBy'];
}

import { TDOId } from '@common-modules/universal/models/Brands';
import { NotificationInterface } from './models';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import {
  CreateMailboxAppResponse,
  DeleteJobSDOResponse,
  DeleteMultiJobSDOResponse,
  FetchEachJobResponse,
  FetchJobSDOsResponse,
  GetMailboxesResponse,
  RetryJobResponse,
  UpdateMultyJobSDOResponse,
  UpsertJobSDOResponse,
} from './services';
import { createAction } from '@reduxjs/toolkit';
export const NOOP_JOB_ACTION = 'notifications/noop';

export const SCHEMA_ID_NOT_FOUND = createAction<{
  registryName: string | undefined;
}>('notifications/schemaId_not_found');

export const UPSERT_JOB_SDO = createAction<{
  id?: string;
  data: { tdoId: TDOId; status: string };
}>('notifications/upsert-sdo');
export const UPSERT_JOB_SDO_SUCCESS =
  createGraphQLSuccessAction<UpsertJobSDOResponse>(
    'notifications/upsert-sdo-successfully'
  );
export const UPSERT_JOB_SDO_FAILURE = createGraphQLFailureAction(
  'notifications/upsert-sdo-failed'
);

export const DELETE_JOB_SDO = createAction<{
  id: string;
  data: NotificationInterface;
}>('notifications/delete-sdo');
export const DELETE_JOB_SDO_SUCCESS =
  createGraphQLSuccessAction<DeleteJobSDOResponse>(
    'notifications/delete-sdo-successfully'
  );
export const DELETE_JOB_SDO_FAILURE = createGraphQLFailureAction(
  'notifications/delete-sdo-failed'
);

export const DELETE_MULTY_JOB_SDO = 'notifications/DELETE_MULTY_JOB_SDO';
export const DELETE_MULTY_JOB_SDO_SUCCESS =
  createGraphQLSuccessAction<DeleteMultiJobSDOResponse>(
    'notifications/DELETE_MULTY_JOB_SDO_SUCCESS'
  );
export const DELETE_MULTY_JOB_SDO_FAILURE = createGraphQLFailureAction(
  'notifications/DELETE_MULTY_JOB_SDO_FAILURE'
);

export const UPDATE_MULTY_JOB_SDO = 'notifications/UPDATE_MULTY_JOB_SDO';
export const UPDATE_MULTY_JOB_SDO_SUCCESS =
  createGraphQLSuccessAction<UpdateMultyJobSDOResponse>(
    'notifications/UPDATE_MULTY_JOB_SDO_SUCCESS'
  );
export const UPDATE_MULTY_JOB_SDO_FAILURE = createGraphQLFailureAction(
  'notifications/UPDATE_MULTY_JOB_SDO_FAILURE'
);

export const FETCH_JOB_SDOS = createAction('notifications/fetch-sdos');
export const FETCH_JOB_SDOS_SUCCESS =
  createGraphQLSuccessAction<FetchJobSDOsResponse>(
    'notifications/fetch-sdos-successfully'
  );
export const FETCH_JOB_SDOS_FAILURE = createGraphQLFailureAction(
  'notifications/fetch-sdos-failed'
);

export const FETCH_JOB_STATUS_SUCCESS_REDUX = createAction<{
  [id: string]: {
    id: string;
    status: string;
  };
}>('notifications/FETCH_JOB_STATUS_SUCCESS_REDUX');

export const FETCH_JOB_STATUS_SUCCESS =
  createGraphQLSuccessAction<FetchEachJobResponse>(
    'notifications/fetch-job-successfully'
  );
export const FETCH_JOB_STATUS_FAILURE = createGraphQLFailureAction(
  'notifications/fetch-job-failed'
);

export const PRE_PROCESS_JOB = 'notifications/PRE_PROCESS_JOB';

export const RETRY_JOB = createAction<{ id: string }>(
  'notifications/RETRY_JOB'
);
export const RETRY_JOB_SUCCESS = createGraphQLSuccessAction<RetryJobResponse>(
  'notifications/RETRY_JOB_SUCCESS'
);
export const RETRY_JOB_FAILED = createGraphQLFailureAction(
  'notifications/RETRY_JOB_FAILED'
);

export const DELETE_JOB_SDO_REDUX = createAction<{
  ids: string[];
}>('notifications/delete-sdo-redux');

export const INSERT_NOTIFICATION_REDUX_WHEN_NETWORK_ERROR =
  createAction<NotificationInterface>(
    'notifications/INSERT_NOTIFICATION_REDUX_WHEN_NETWORK_ERROR'
  );

export const INIT_AIWARE = createAction<NonNullable<typeof window.aiware>>(
  'mailbox/INIT_AIWARE'
);

export const FETCH_MAILBOX = 'mailbox/FETCH_MAILBOX';
export const FETCH_MAILBOX_SUCCESS =
  createGraphQLSuccessAction<GetMailboxesResponse>(
    'mailbox/FETCH_MAILBOX_SUCCESS'
  );
export const FETCH_MAILBOX_FAILURE = createGraphQLFailureAction(
  'mailbox/FETCH_MAILBOX_FAILURE'
);

export const CREATE_MAILBOX_SYSTEM = 'mailbox/CREATE_MAILBOX_SYSTEM';
export const CREATE_MAILBOX_SYSTEM_SUCCESS =
  'mailbox/CREATE_MAILBOX_SYSTEM_SUCCESS';
export const CREATE_MAILBOX_SYSTEM_FAILURE =
  'mailbox/CREATE_MAILBOX_SYSTEM_FAILURE';

export const CREATE_MAILBOX_APP = 'mailbox/CREATE_MAILBOX_APP';
export const CREATE_MAILBOX_APP_SUCCESS =
  createGraphQLSuccessAction<CreateMailboxAppResponse>(
    'mailbox/CREATE_MAILBOX_APP_SUCCESS'
  );
export const CREATE_MAILBOX_APP_FAILURE = createGraphQLFailureAction(
  'mailbox/CREATE_MAILBOX_APP_FAILURE'
);

export const SUBSCRIBE_MAILBOX_APP = 'mailbox/SUBSCRIBE_MAILBOX_APP';
export const SUBSCRIBE_MAILBOX_APP_SUCCESS =
  'mailbox/SUBSCRIBE_MAILBOX_APP_SUCCESS';

export const CREATE_NOTI_APP = 'mailbox/CREATE_NOTI_APP';
export const CREATE_NOTI_APP_SUCCESS = 'mailbox/CREATE_NOTI_APP_SUCCESS';
export const CREATE_NOTI_APP_FAILURE = 'mailbox/CREATE_NOTI_APP_FAILURE';

/**
 * Insert or update an SDO. Leave id undefined for insert.
 */
export const actionUpsertSDO = (payload: {
  id?: string;
  data: { tdoId: TDOId; status: string };
}) => UPSERT_JOB_SDO(payload);

/**
 * Fetch a block of SDOs.
 */
export const actionFetchJobSDOs = () => FETCH_JOB_SDOS();

export const actionDeleteJobSDO = (payload: {
  id: string;
  data: NotificationInterface;
}) => DELETE_JOB_SDO(payload);

export const actionRetryJob = (payload: { id: string }) => RETRY_JOB(payload);

export const actionDeleteJobSdoRedux = (payload: { ids: string[] }) =>
  DELETE_JOB_SDO_REDUX(payload);

export const actionInsertNotificationReduxWhenNetWorkError = (
  payload: NotificationInterface
) => INSERT_NOTIFICATION_REDUX_WHEN_NETWORK_ERROR(payload);

export const actionInitAiware = INIT_AIWARE;

export const SHOW_OR_HIDE_CUSTOM_NOTIFICATION_LIST = createAction(
  'organization/showOrHideCustomNotification'
);
export const actionShowOrHideNotificationList = () =>
  SHOW_OR_HIDE_CUSTOM_NOTIFICATION_LIST();

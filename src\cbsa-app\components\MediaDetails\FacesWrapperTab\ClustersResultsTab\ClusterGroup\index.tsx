import { memo } from 'react';

import { ClusterItemGroup } from '@common-modules/mediaDetails/models';

import PropTypes, { SeekMediaTimePayload } from '../PropTypes';

import Collapsed from './Collapsed';
// import Expanded from './Expanded';

const ClusterGroup = ({
  segments,
  // isExpanded,
  // setSelectedGroups,
  // highlightedOverlay,
  // selected,
  onHighlightPoly,
}: ClusterGroupPropTypes) => (
  <div>
    <Collapsed segments={segments} onHighlightPoly={onHighlightPoly} />
    {/* <Expanded
      {...{
        isExpanded,
        highlightedOverlay,
        selected,
        setSelectedGroups,
        segments,
        onHighlightPoly,
      }}
    /> */}
  </div>
);

export default memo(ClusterGroup);

export type ClusterGroupPropTypes = Pick<
  PropTypes,
  'selected' | 'highlightedOverlay' | 'setSelectedGroups'
> & {
  readonly onHighlightPoly: (payload: SeekMediaTimePayload) => void;
  readonly segments: ClusterItemGroup['segments'];
};

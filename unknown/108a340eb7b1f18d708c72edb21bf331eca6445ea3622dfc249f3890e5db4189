import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import { deleteFileQuery } from '../../src/api/queries';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { deleteFile } from '../../src/controllers/deleteFile';
import * as isLocked from '../../src/adapters/checkTdoIsLocked';

describe('deleteFile', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  const checkFileIsLocked = jest.spyOn(isLocked, 'checkTdoIsLockedAdapter');
  checkFileIsLocked.mockImplementation(() => Promise.resolve(false));

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('deletes a file w/ tdoId', async () => {
    const params = { tdoId: '123456789' };
    const req = getMockReq({
      params,
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() => Promise.resolve({ deleteTDO: { id: '123456789' } }));

    await deleteFile(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, deleteFileQuery(params.tdoId));
    expect(isLocked.checkTdoIsLockedAdapter).toHaveBeenCalledTimes(1);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('fails to delete a file w/o tdoId', async () => {
    const params = { tdoId: '' };
    const req = getMockReq({
      params,
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() => Promise.reject({ response: { errors: [{ name: 'not_found' }] } }));

    await deleteFile(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
     expect(res.json).toHaveBeenCalledWith( { error: Messages.tdoIdRequired} );
  });
});

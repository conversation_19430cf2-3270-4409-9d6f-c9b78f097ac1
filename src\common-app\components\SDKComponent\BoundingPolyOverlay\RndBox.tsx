import {
  HEAD_COLOR,
  LAPTOP_COLOR,
  NOTEPAD_COLOR,
  CARD_COLOR,
  PLATE_COLOR,
  POIM_COLOR,
  REDACTION_CODE_FONT_SCALING_COEFF,
  REDACTION_CODE_FONT_SIZE,
  REDACTION_CODE_MIN_FONT_PX,
  UDR_COLOR,
  UNSELECTED_ICON_COLOR,
  PERSON_COLOR,
  OBJECT_TYPE,
} from '@helpers/constants';
import cn from 'classnames';
import { clamp } from 'lodash';
import * as style from './styles.scss';
import Menu from '@mui/material/Menu';
import { useSelector } from 'react-redux';
import { DeepReadonlyArray } from '@utils';
import * as styles from './overlay.styles.scss';
import { ShapeType } from '@common-modules/mediaDetails/models';
import MenuItem from '@mui/material/MenuItem';
import { Props as RndProps, Rnd } from 'react-rnd';
import { borderRadiusByPosition } from './helpers';
import { DraggableEventHandler } from 'react-draggable';
import { selectGlobalSettings } from '@common-modules/mediaDetails';
import { MouseEventHandler, useCallback, useState, useEffect } from 'react';
import { IndividualRedactionCode } from '@redact-modules/redactionCodes/models';
import { ReactComponent as IconUDR } from '@resources/images/object_type/UDR_24x24px.svg';
// eslint-disable-next-line import/no-duplicates
import { ReactComponent as IconHead } from '@resources/images/object_type/Head_24x24px.svg';
// eslint-disable-next-line import/no-duplicates
import { ReactComponent as IconPOIM } from '@resources/images/object_type/Head_24x24px.svg';
import { ReactComponent as IconPlate } from '@resources/images/object_type/Plate_24x24px.svg';
import { ReactComponent as IconLaptop } from '@resources/images/object_type/Laptop_24x24px.svg';
import { ReactComponent as IconNotepad } from '@resources/images/object_type/Notepad_24x24px.svg';
import { ReactComponent as IconCard } from '@resources/images/object_type/Card_24x24px.svg';
import { ReactComponent as IconPerson } from '@resources/images/object_type/Person_24x24px.svg';
import IconGearDefault from '@resources/images/rotating_gear.svg';
import IconEllipse from '@resources/images/EllipseShape.svg';
import IconRectangle from '@resources/images/RectangleShape.svg';
import { SvgIcon } from '@mui/material';
import useClosePopup from '@common/state/hooks/useClosePopup';

const RndBox = (props: Props) => {
  const {
    menuItems,
    'data-boxid': dataBoxId,
    type,
    size,
    shape,
    onChangeShape,
    position,
    currentTime,
    liveTrackingEnabled,
    unselectedBox,
    redactionCode,
    onFaceHighlight,
    groupId,
    videoDimensions,
    menuAnchorEl,
    setMenuAnchorEl,
    onDragStop,
  } = props;
  const resizeHandleSize = 6;
  const handleShift = resizeHandleSize / 2;
  const globalSettings = useSelector(selectGlobalSettings);
  const defaultShape = (
    {
      head: globalSettings.objectTypeEffects.head.shapeType,
      udr: globalSettings.objectTypeEffects.udr.shapeType,
      laptop: globalSettings.objectTypeEffects.laptop.shapeType,
      vehicle: globalSettings.objectTypeEffects.vehicle.shapeType,
      licensePlate: globalSettings.objectTypeEffects.plate.shapeType,
      notepad: globalSettings.objectTypeEffects.notepad.shapeType,
      card: globalSettings.objectTypeEffects.card.shapeType,
      poim: 'rectangle',
      person: globalSettings.objectTypeEffects.person.shapeType,
    } as const
  )[type];

  const [hover, toggleHover] = useState(false);
  const [isMenuOpen, setMenuOpen] = useState(false);

  useClosePopup({ handleCloses: [() => setMenuOpen(false)] });

  useEffect(() => {
    onMouseLeave();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shape]);

  const onMouseOver = useCallback(() => {
    if (!hover) {
      toggleHover(true);
    }
  }, [toggleHover, hover]);

  const onMouseLeave = useCallback(() => {
    if (hover && !isMenuOpen) {
      toggleHover(false);
    }
  }, [hover, isMenuOpen]);

  const handleOpenMenu: MouseEventHandler<HTMLElement> = useCallback(
    (event) => {
      event.preventDefault();
      event.stopPropagation();
      setMenuOpen(true);
      setMenuAnchorEl({
        top: event.clientY,
        left: event.clientX,
      });
      dataBoxId &&
        groupId &&
        onFaceHighlight?.({
          id: dataBoxId,
          timeMs: 1000 * currentTime,
          type,
          groupId,
        });
    },
    [dataBoxId, currentTime, onFaceHighlight, setMenuAnchorEl, type, groupId]
  );

  const handleCloseMenu: MouseEventHandler<HTMLElement> = useCallback(
    (e) => {
      if (e.target instanceof HTMLElement) {
        const itemIndex = e.target.getAttribute('data-itemindex');

        if (itemIndex) {
          menuItems?.[Number(itemIndex)]?.onClick(dataBoxId);
        }
      }
      setMenuOpen(false);
    },
    [menuItems, dataBoxId]
  );

  const handleDragStop: DraggableEventHandler = (e, d) => {
    if (videoDimensions?.width && videoDimensions?.height && onDragStop) {
      const { x, y } = d;
      const { width, height } = size;
      const { width: maxWidth, height: maxHeight } = videoDimensions;
      onDragStop(e, {
        ...d,
        x: clamp(x, 0, maxWidth - width),
        y: clamp(y, 0, maxHeight - height),
      });
    }
  };

  const renderIcon = useCallback(() => {
    let IconComponent;
    let color;
    let props = {};
    const { width } = size;

    const shapes = [
      undefined,
      defaultShape === 'rectangle' ? 'ellipse' : 'rectangle',
      defaultShape,
    ] as const;

    const toggleShape: MouseEventHandler<HTMLDivElement> = (e) => {
      e.stopPropagation();

      if (onChangeShape) {
        const index = shapes.findIndex((s) => s === shape);
        const newShape = shapes[(index + 1) % shapes.length];
        onChangeShape(newShape);
      }
    };

    switch (type) {
      case 'udr':
        IconComponent = IconUDR;
        color = UDR_COLOR;
        props = { stroke: 'black' };
        break;

      case 'head':
        IconComponent = IconHead;
        color = HEAD_COLOR;
        props = { fill: 'black' };
        break;

      case 'poim':
        IconComponent = IconPOIM;
        color = POIM_COLOR;
        props = { fill: 'black' };
        break;

      case 'licensePlate':
        IconComponent = IconPlate;
        color = PLATE_COLOR;
        props = { fill: 'black' };
        break;

      case 'vehicle':
        IconComponent = IconPlate;
        color = PLATE_COLOR;
        props = { fill: 'black' };
        break;

      case 'laptop':
        IconComponent = IconLaptop;
        color = LAPTOP_COLOR;
        props = { fill: 'black' };
        break;

      case 'notepad':
        IconComponent = IconNotepad;
        color = NOTEPAD_COLOR;
        props = { fill: 'black' };
        break;

      case 'card':
        IconComponent = IconCard;
        color = CARD_COLOR;
        props = { fill: 'black' };
        break;
      case 'person':
        IconComponent = IconPerson;
        color = PERSON_COLOR;
        props = { fill: 'black' };
        break;

      default:
        console.warn('Unknown type', type);
        return null;
    }

    return (
      <>
        {hover && !liveTrackingEnabled && (
          <div
            style={{
              left: 0,
              pointerEvents: 'auto',
              backgroundColor: !unselectedBox ? color : UNSELECTED_ICON_COLOR,
            }}
            className={cn(style.iconContainer, style.iconMoreVertContainer)}
            onMouseDown={handleOpenMenu}
            // Used as anchorEl for Popover on RedactionConfigMenu and TimeStampMenus
            id={`detection-icon-${dataBoxId}`}
            data-testid={`detection-icon-${dataBoxId}`}
          >
            <SvgIcon style={{ width: 16, height: 16 }}>
              {IconComponent && <IconComponent {...props} />}
            </SvgIcon>
          </div>
        )}
        {hover && !liveTrackingEnabled && !unselectedBox && (
          <div
            id="Shapes-Menu"
            data-testid="Shapes-Menu"
            style={{
              left: width < 50 ? 25 : 'auto',
              right: width < 50 ? 'auto' : 0,
              pointerEvents: 'auto',
              backgroundColor: color,
            }}
            className={cn(style.iconContainer, style.iconMoreVertContainer)}
            onClick={toggleShape}
          >
            <img
              className={style.shapeIcon}
              src={
                shape
                  ? shape === 'ellipse'
                    ? IconEllipse
                    : IconRectangle
                  : IconGearDefault
              }
              width={16}
              height={16}
              alt=""
            />
          </div>
        )}
      </>
    );
  }, [
    size,
    defaultShape,
    type,
    liveTrackingEnabled,
    unselectedBox,
    handleOpenMenu,
    dataBoxId,
    hover,
    shape,
    onChangeShape,
  ]);

  const redactionCodePosition = {
    center: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    top: {
      alignItems: 'flex-start',
      justifyContent: 'center',
    },
    bottom: {
      alignItems: 'flex-end',
      justifyContent: 'center',
    },
    above: {
      left: '50%',
      top: `-${clamp(props?.position?.y ?? 20, 0, 20)}px`,
      position: 'absolute' as const,
      transform: 'translateX(-50%)',
    },
    below: {
      left: '50%',
      top: `calc(100% - ${clamp(
        props.size.height +
          (props?.position?.y ?? 0) +
          20 -
          (videoDimensions?.height ?? 0),
        0,
        20
      )}px)`,
      position: 'absolute' as const,
      transform: 'translateX(-50%)',
    },
    left: {
      alignItems: 'center',
      justifyContent: 'flex-start',
    },
    right: {
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    default: {},
  }[redactionCode?.codeLocation ?? 'default'];

  return (
    <Rnd
      bounds="parent"
      data-testid={`rnd-box`}
      resizeHandleStyles={{
        topLeft: { left: -handleShift, top: -handleShift },
        topRight: { right: -handleShift, top: -handleShift },
        bottomLeft: { left: -handleShift, bottom: -handleShift },
        bottomRight: { right: -handleShift, bottom: -handleShift },
        right: { right: -handleShift },
        left: { left: -handleShift },
        top: { top: -handleShift },
        bottom: { bottom: -handleShift },
      }}
      resizeHandleClasses={{
        topLeft: styles.resizeHandle,
        topRight: styles.resizeHandle,
        bottomLeft: styles.resizeHandle,
        bottomRight: styles.resizeHandle,
        right: cn(styles.resizeHandle, styles.resizeHandleHorizontal),
        left: cn(styles.resizeHandle, styles.resizeHandleHorizontal),
        top: cn(styles.resizeHandle, styles.resizeHandleVertical),
        bottom: cn(styles.resizeHandle, styles.resizeHandleVertical),
      }}
      {...(({
        style,
        menuItems,
        videoDimensions,
        onChangeShape,
        redactionCode,
        liveTrackingEnabled,
        onFaceHighlight,
        currentTime,
        groupId,
        menuAnchorEl,
        setMenuAnchorEl,
        onDragStop,
        unselectedBox,
        ...other
      }) => other)(props)}
      onDragStop={handleDragStop}
      onMouseOver={onMouseOver}
      onMouseLeave={onMouseLeave}
      className={cn(
        unselectedBox
          ? styles.unselectedBox
          : props.enableResizing
            ? styles.activeSelectedBox
            : hover && !liveTrackingEnabled
              ? styles.hoveredSelectedBox
              : styles.selectedBox
      )}
      onContextMenu={handleOpenMenu}
    >
      <div
        className={style.rndParent}
        data-testid={`rnd-box-${dataBoxId}`}
        style={{
          ...props.style,
          width: '100%',
          height: '100%',
          overflow: 'hidden',
          position: 'absolute',
          pointerEvents: 'none',
          borderRadius: borderRadiusByPosition({
            defaultShape,
            position,
            shape,
            size,
            videoDimensions,
          }),
        }}
      >
        {!unselectedBox && isMenuOpen && (
          <Menu
            open={isMenuOpen}
            anchorReference="anchorPosition"
            anchorPosition={menuAnchorEl ?? { top: 0, left: 0 }}
            onClose={handleCloseMenu}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            container={document.fullscreenElement}
            style={{ zIndex: 99999 }}
          >
            {menuItems?.map((option, i) => (
              <MenuItem
                key={option.label}
                data-itemindex={i}
                onClick={handleCloseMenu}
                style={{
                  display: 'flex',
                }}
                className={style.menuItems}
              >
                {option.label}
              </MenuItem>
            ))}
          </Menu>
        )}
        {redactionCode && (
          <div
            className={style.code}
            data-testid={`redaction-code-${dataBoxId}`}
            style={{
              fontSize: `clamp(${REDACTION_CODE_MIN_FONT_PX}, ${
                REDACTION_CODE_FONT_SCALING_COEFF / redactionCode.code.length
              }cqw, ${
                REDACTION_CODE_FONT_SIZE *
                (document.getElementById('overlay-container')?.offsetHeight ?? // This calculation is using getElementById of the video container, videoDimensions is inaccurate when adjusting window sizes
                  1080)
              }px)`,
              color: redactionCode.codeColor,
              height: '100%',
              display: 'flex',
              overflow: 'hidden',
              ...redactionCodePosition,
              justifyContent:
                size?.width < 35
                  ? 'flex-start'
                  : redactionCodePosition.justifyContent,
            }}
          >
            {redactionCode.code}
          </div>
        )}
      </div>
      {renderIcon()}
    </Rnd>
  );
};

type Props = RndProps & {
  type: OBJECT_TYPE;
  size: {
    width: number;
    height: number;
  };
  shape?: ShapeType;
  menuItems?: DeepReadonlyArray<{
    label: string;
    onClick: (id?: string) => void;
  }>;
  'data-boxid'?: string;
  liveTrackingEnabled?: boolean;
  onChangeShape?: (shapeType?: ShapeType) => void;
  unselectedBox?: boolean;
  redactionCode?: IndividualRedactionCode;
  videoDimensions?: { height?: number; width?: number };
  currentTime: number;
  groupId?: string | undefined;
  onFaceHighlight?: (payload: {
    id: string;
    timeMs: number;
    type: OBJECT_TYPE;
    groupId: string;
  }) => void;
  menuAnchorEl?: { top: number; left: number };
  setMenuAnchorEl: (el: { top: number; left: number } | undefined) => void;
};

export default RndBox;

import { createSelector } from 'reselect';
import {
  selectTDOs,
  deleteTdo,
  selectLoaders,
  toggleTdoImage,
  toggleTdoMedia,
  selectCaseDetails,
  selectSelectedTdoMedia,
  selectSelectedTdoImages,
} from '@cbsa-modules/addMedia';
import { addFile } from '@cbsa-modules/appWrapper';
import { TDOId, TreeObjectId } from '@cbsa-modules/universal';

export const componentSelectors = createSelector(
  selectTDOs,
  selectLoaders,
  selectCaseDetails,
  selectSelectedTdoMedia,
  selectSelectedTdoImages,
  (tdos, loaders, caseDetails, selectedTdoMedia, selectedTdoImages) => ({
    tdos,
    loaders,
    caseDetails,
    selectedTdoMedia,
    selectedTdoImages,
  })
);

export const componentActions = {
  uploadMedia: ({ file, caseId }: { file: File; caseId: TreeObjectId }) =>
    addFile({ file, caseId }),
  deleteTdo: (tdoId: TDOId) => deleteTdo(tdoId),
  toggleTdoImage: (tdoId: TDOId) => toggleTdoImage(tdoId),
  toggleTdoMedia: (tdoId: TDOId) => toggleTdoMedia(tdoId),
};

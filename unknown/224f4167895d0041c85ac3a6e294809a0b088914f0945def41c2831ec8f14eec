import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles((theme) => ({
  notification: {
    background: 'white',
    border: `1px solid ${theme.palette.primary.light}`,
    borderRadius: '3px',
    marginBottom: '7px',
    padding: '5px 4px',
    position: 'relative',
    display: 'flex',
    flexDirection: 'row',

    '& .Indicator': {
      width: '3px',
      background: theme.palette.success.main,
      borderRadius: '2px',

      '&.failed': {
        background: theme.palette.error.main,
      },
    },

    '& .Status': {
      flex: 1,
      height: '40px',
      display: 'flex',
      alignItems: 'center',
      padding: '0 15px 0 12px',
      justifyContent: 'space-between',

      '& .Message': {
        color: 'black',
        fontSize: '14px',
        fontWeight: 'bold',
      },

      '& .TimeAgo': {
        color: theme.palette.grey[400],
        fontSize: '10px',
      },
    },

    '& .Close': {
      top: '7px',
      right: '8px',
      fontSize: '10px',
      cursor: 'pointer',
      position: 'absolute',
      color: theme.palette.grey['A200'],
    },

    '&:last-of-type': {
      marginBottom: 0,
    },
  },
}));

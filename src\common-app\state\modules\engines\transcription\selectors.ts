import { createSelector } from 'reselect';
import { TDOId } from '@common-modules/universal/models/Brands';
import { selectConfigEngines } from '../selectors';
import { selectCurrentTdoId } from '../../mediaDetails';
import { defaultJobSlice, namespace, TranscriptionStore } from './store';

export const selectTranscriptionEngineId = createSelector(
  selectConfigEngines,
  (engines) => engines.transcriptionEngineId
);

export const selectTranscriptionEngineOptions = createSelector(
  selectConfigEngines,
  (engines) => engines.transcriptionEngineOptions
);

export const selectStore = (state: { [namespace]: TranscriptionStore }) =>
  state[namespace];

export const selectAllEngineStatuses = (tdoId: TDOId) =>
  createSelector(selectStore, (s) => s[tdoId] || {});

export const selectAllEngineStatusesByCurrentTdoId = createSelector(
  selectStore,
  selectCurrentTdoId,
  (s, tdoId) => (tdoId ? s[tdoId] || {} : {})
);

export const selectEngineStatus = (tdoId: TDOId, jobId: string) =>
  createSelector(
    selectAllEngineStatuses(tdoId),
    (s) => s[jobId] || defaultJobSlice()
  );

export const selectEngineStatusByCurrentTdoId = (jobId: string) =>
  createSelector(
    selectAllEngineStatusesByCurrentTdoId,
    (s) => s[jobId] || defaultJobSlice()
  );

import { callGQL } from '../api/callGraphql';
import { RequestHeader } from '../model/requests';
import { Logger } from '../logger';
import { deleteFolderQuery} from '../api/queries';
import { DeleteFolderResponse } from '../model/responses';
import { TreeObjectId } from '../model/brands';

export const deleteFolderAdapter = async (headers: RequestHeader, folderId: TreeObjectId) => {
    const deleteQuery = deleteFolderQuery(folderId);
    try {
        const { deleteFolder /*, response */} = await callGQL<DeleteFolderResponse>(headers, deleteQuery);
        if(deleteFolder?.id === folderId) {
            return deleteFolder?.id;
        } else {
            // const errorName = response?.response?.errors?.[0]?.name
            // return errorName === 'not-found' ? errorName : undefined;
            return undefined;
        }
    } catch(err) {
        Logger.error(err);
        return undefined;
    }
};

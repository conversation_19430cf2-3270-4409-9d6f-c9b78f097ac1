import { useCallback } from 'react';
import {
  Button,
  Grid2 as Grid,
  ThemeProvider,
  Paper,
  Typography,
} from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';

import { CREATE_JOB_FACES_ACTION } from '@common-modules/facesTabModule';
import nullStateImage from '@resources/images/engine-null-state.svg';
import {
  selectTdo,
  selectDisableFacesButton,
} from '@common-modules/mediaDetails';
import { I18nTranslate } from '@common/i18n';

import { buttonTheme } from '@cbsa/styles/materialThemes';

import { useStyles } from './styles';

const style = {
  message: {
    color: '#CFD8DC',
    fontFamily: 'Roboto',
    fontSize: '16px',
    fontWeight: 'normal',
    marginTop: '40px',
    lineHeight: '19px',
  },
  gridItem: {
    '&.MuiGrid-item': {
      maxWidth: '50%',
    },
  },
};

const FacesNullStateTab = () => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const { tdo, buttonDetectFaceDisable } = useSelector(componentSelectors);

  const handleCreateJob = useCallback(() => {
    const tdoId = tdo?.id;
    const tdoUrl = tdo?.primaryAsset?.signedUri;
    if (tdoId && tdoUrl) {
      dispatch(componentActions.actionCreateJobFace(tdoId, tdoUrl));
    }
  }, [dispatch, tdo]);

  return (
    <Paper
      className={classes.container}
      data-veritone-component="faces-null-state-tab"
      data-testid="faces-null-state-tab"
    >
      <Grid container className={classes.tabNameWrapper}>
        <Grid size={{ xs: 12 }} className={classes.tabName}>
          {I18nTranslate.TranslateMessage('objectDetection')}
        </Grid>
      </Grid>
      <Grid
        container
        alignItems="center"
        direction="column"
        className={classes.content}
      >
        <Grid>
          <img src={nullStateImage} className={classes.nullStateIcon} />
        </Grid>
        <Grid size={{ xs: 6 }} sx={style.gridItem}>
          <Typography
            align="center"
            variant="body2"
            color={'secondary'}
            className={classes.message}
            data-testid="faces-null-state-tab-message"
            sx={style.message}
          >
            {I18nTranslate.TranslateMessage('detectDescription')}
          </Typography>
        </Grid>
        <Grid>
          <ThemeProvider theme={buttonTheme}>
            <Button
              variant="contained"
              color={'primary'}
              className={classes.buttonDerp}
              onClick={handleCreateJob}
              disabled={buttonDetectFaceDisable}
              data-veritone-element="faces-null-state-detect-faces-button"
            >
              {I18nTranslate.TranslateMessage('detectObjects')}
            </Button>
          </ThemeProvider>
        </Grid>
      </Grid>
    </Paper>
  );
};

const componentSelectors = (state: any) => ({
  tdo: selectTdo(state),
  buttonDetectFaceDisable: selectDisableFacesButton(state),
});

const componentActions = {
  actionCreateJobFace: (targetId: string, url: string) =>
    CREATE_JOB_FACES_ACTION({ targetId, url }),
};

export default FacesNullStateTab;

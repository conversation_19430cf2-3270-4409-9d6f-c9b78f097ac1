import { FilesByTreeObejectIdResponse } from "../model/responses";
import { getFilesByTreeObjectIdQuery } from "../api/queries";
import { RequestHeader } from "../model/requests";
import { callGQL } from "../api/callGraphql";
import { Logger } from "../logger";
import { Messages } from "../errors/messages";
import { uniq } from "lodash";
import { TDOId, TreeObjectId } from "../model/brands";

export const getFilesByTreeObjectIdAdapter = async (
    headers: RequestHeader,
    treeObjectId: TreeObjectId,
  ) => {
    const defaultLimit = 1000;
    const defaultOffset = 0;
    const response: {isFailed: boolean; tdoIds: Array<TDOId>} = {
      isFailed: false,
      tdoIds: [],
    }
    const getFiles = async (offset: number): Promise<{isFailed: boolean; tdoIds: Array<string>}> => {
      try {
        const query = getFilesByTreeObjectIdQuery;
        const variables = { treeObjectId, limit: defaultLimit , offset }
        const res = await callGQL<FilesByTreeObejectIdResponse>(headers, query, variables );
        const results = res.searchMedia?.jsondata.results;
        if(results?.length > 0) {
          const tdos =  results.map((r) => r.recording.recordingId );
          response.tdoIds = uniq([...response.tdoIds , ...(tdos || [])]);
          if (tdos.length === defaultLimit) {
            return await getFiles(offset + defaultLimit)
          }
        }
        return response;
      } catch(err) {
        Logger.log(Messages.getFilesByTreeObjectIdFail + JSON.stringify(err));
        response.isFailed = true;
        return response;
      }
    }
    return await getFiles(defaultOffset);
}
import {
  memo,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { renderer } from '../../renderer';
import { FaceDetectionPropTypes } from './FaceDetectionPropTypes';
import { mapRender } from './mapRender';

const FaceDetection = (props: FaceDetectionPropTypes) => {
  const ref = useRef<HTMLDivElement>(null);

  const [afterFirstRender, setAfterFirstRender] = useState<boolean>(false);

  const { render, destroy } = useMemo(
    () => renderer(ref, mapRender),
    [afterFirstRender] // eslint-disable-line react-hooks/exhaustive-deps
  );

  useEffect(() => {
    setAfterFirstRender(true);
    return () => {
      destroy();
    };
  }, [destroy]);

  useLayoutEffect(() => {
    render(props);
  });

  return <div ref={ref} data-testid="timeline-face-detection" />;
};

export default memo(FaceDetection);

import callGraphQL<PERSON>pi from '@helpers/callGraphQLApi';
import { TDOId, Thunk } from '@cbsa-modules/universal';
import { GET_AUDIT_LOGS, GetAuditLogsResponse } from './queries/auditLog';
import {
  GET_TDO_AUDIT_LOGS,
  GET_TDO_AUDIT_LOGS_SUCCESS,
  GET_TDO_AUDIT_LOGS_FAILURE,
} from '@cbsa-modules/addMedia';

export const getTdoAuditLogs: Thunk<{
  readonly tdoId: TDOId;
}> =
  ({ tdoId }) =>
  async (dispatch, getState) =>
    await callGraph<PERSON>Api<GetAuditLogsResponse>({
      actionTypes: [
        GET_TDO_AUDIT_LOGS,
        GET_TDO_AUDIT_LOGS_SUCCESS,
        GET_TDO_AUDIT_LOGS_FAILURE,
      ],
      query: GET_AUDIT_LOGS,
      variables: {
        terms: [{ tdoId }],
        application: 'redact',
      },
      dispatch,
      getState,
    });

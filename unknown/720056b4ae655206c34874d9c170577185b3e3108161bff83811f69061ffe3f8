import express from 'express';
import { Logger } from '../logger';
import { Messages } from '../errors/messages';
import { StatusCodes } from '../errors/statusCodes';
import { isAPITokenKey, isSessionToken } from './helpers';
import { pick } from 'lodash';
import { validateTokenQuery } from '../api/queries';
import { callGQL } from '../api/callGraphql';
import { ValidateTokenResponse } from '../model/responses';

export const validateToken = async (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => {
  const headers = pick(req.headers, ['authorization']);
  if (headers) {
    if (isAPITokenKey(headers)) {
      next();
    } else if(isSessionToken(headers)) {
      try {
        const token = headers.authorization.split(' ')[1]!; // Safe due to isSessionToken check
        const query = validateTokenQuery(token);
        const response = await callGQL<ValidateTokenResponse>(headers, query);
          if (response?.validateToken?.token) {
            next();
          }
        } catch(err) {
          Logger.error(err);
          return res.status(StatusCodes.Forbidden).send(err);
        }
    } else {
      Logger.error(Messages.InvalidToken);
      return res.status(StatusCodes.Unauthorized).send(Messages.InvalidToken);
    }
  } else {
    Logger.error(Messages.InvalidToken);
    return res.status(StatusCodes.Unauthorized).send(Messages.InvalidToken);
  }
};

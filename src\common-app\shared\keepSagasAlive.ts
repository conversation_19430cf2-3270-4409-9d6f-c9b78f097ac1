import * as Sentry from '@sentry/browser';
import { all, call, fork } from 'typed-redux-saga/macro';
// TODO: should we be using typed-redux-saga here?
// Why did just updating eslint packages cause this to be an issue?
// eslint-disable-next-line @jambit/typed-redux-saga/use-typed-effects
import {
  AllEffect,
  CancelEffect,
  ForkEffect,
  SelectEffect,
  PutEffect,
  TakeEffect,
} from 'redux-saga/effects';

type sagaType = () => Generator<
  | AllEffect<any>
  | CancelEffect
  | ForkEffect
  | SelectEffect
  | PutEffect<any>
  | TakeEffect,
  void
>;

export const keepAlive = (sagas: sagaType[]) =>
  function* () {
    yield* all(sagas.map((saga) => fork(keepAliveFn(saga))));
  };

const keepAliveFn = (sagaFn: sagaType) =>
  function* () {
    while (true) {
      try {
        return yield* call(sagaFn);
      } catch (e) {
        const stackStr =
          (e instanceof Error ? JSON.stringify(e.stack) : e?.toString()) ||
          'Unknown error';

        const stackMsgs = stackStr.match(/.{1,200}/g) || [];

        Sentry.captureException(
          // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
          new Error(`${sagaFn.name} aborted due to: ${e}`, { cause: e }),
          {
            tags: {
              saga: sagaFn.name,
              stack: stackMsgs[0] || '',
              stack2: stackMsgs[1] || '',
            },
          }
        );
        console.error(`${sagaFn.name} aborted due to:`, e);
      }
    }
  };

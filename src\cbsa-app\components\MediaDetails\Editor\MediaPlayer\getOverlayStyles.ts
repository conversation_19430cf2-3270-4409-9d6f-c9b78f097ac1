import { OverlayPreviewOptionsType } from '@common/state/modules/mediaDetails/models';
import {
  UDR_COLOR,
  HEAD_COLOR,
  PLATE_COLOR,
  LAPTOP_COLOR,
  POIM_COLOR,
  NOTEPAD_COLOR,
  CARD_COLOR,
} from '@helpers/constants';

const previewStyles = {
  black_fill: {
    backgroundColor: '#000000',
    borderWidth: '2px',
    borderStyle: 'solid',
    borderColor: '#000000',
  },
  none: {
    display: 'none',
  },
};

const stylesByObjectType = {
  head: {
    backgroundColor: `${HEAD_COLOR}26`,
    borderWidth: '2px',
    borderStyle: 'solid',
    borderColor: HEAD_COLOR,
  },
  udr: {
    backgroundColor: `${UDR_COLOR}26`,
    borderWidth: '2px',
    borderStyle: 'solid',
    borderColor: UDR_COLOR,
  },
  license: {
    backgroundColor: `${PLATE_COLOR}26`,
    borderWidth: '2px',
    borderStyle: 'solid',
    borderColor: PLATE_COLOR,
  },
  vehicle: {
    backgroundColor: `${PLATE_COLOR}26`,
    borderWidth: '2px',
    borderStyle: 'solid',
    borderColor: PLATE_COLOR,
  },
  laptop: {
    backgroundColor: `${LAPTOP_COLOR}26`,
    borderWidth: '2px',
    borderStyle: 'solid',
    borderColor: LAPTOP_COLOR,
  },
  notepad: {
    backgroundColor: `${NOTEPAD_COLOR}26`,
    borderWidth: '2px',
    borderStyle: 'solid',
    borderColor: NOTEPAD_COLOR,
  },
  card: {
    backgroundColor: `${CARD_COLOR}26`,
    borderWidth: '2px',
    borderStyle: 'solid',
    borderColor: CARD_COLOR,
  },
  poim: {
    backgroundColor: `${POIM_COLOR}26`,
    borderWidth: '2px',
    borderStyle: 'solid',
    borderColor: POIM_COLOR,
  },
  disabled: {
    backgroundColor: 'rgba(80, 80, 80, 0.15)',
  },
};

export const stagedBoundingBoxStyles = {
  ...stylesByObjectType.udr,
};

export const getOverlayStyles = (overlayPreview: OverlayPreviewOptionsType) => {
  switch (overlayPreview) {
    case 'black_fill':
      return {
        head: previewStyles.black_fill,
        poim: previewStyles.black_fill,
        udr: previewStyles.black_fill,
        licensePlate: previewStyles.black_fill,
        vehicle: previewStyles.black_fill,
        laptop: previewStyles.black_fill,
        notepad: previewStyles.black_fill,
        card: previewStyles.black_fill,
        disabled: stylesByObjectType.disabled,
      };
    case 'none':
      return {
        head: previewStyles.none,
        poim: previewStyles.none,
        udr: previewStyles.none,
        licensePlate: previewStyles.none,
        vehicle: previewStyles.none,
        laptop: previewStyles.none,
        notepad: previewStyles.none,
        card: previewStyles.none,
        disabled: stylesByObjectType.disabled,
      };
    default:
      return {
        head: stylesByObjectType.head,
        poim: stylesByObjectType.poim,
        udr: stylesByObjectType.udr,
        licensePlate: stylesByObjectType.license,
        vehicle: stylesByObjectType.vehicle,
        laptop: stylesByObjectType.laptop,
        notepad: stylesByObjectType.notepad,
        card: stylesByObjectType.card,
        disabled: stylesByObjectType.disabled,
      };
  }
};

import { TDOId } from '@common-modules/universal/models/Brands';
import { CaseStatus } from './Case';
import type { JobStatus, StructuredData } from 'veritone-types';

export interface StructuredDataObject extends Readonly<StructuredData> {
  readonly id: string;
  readonly createdDateTime: string;
  readonly modifiedDateTime: string;
  readonly message: string;
  readonly status: CaseStatus | JobStatus;
  readonly data: {
    readonly tdoId: TDOId;
    readonly status: 'Draft' | 'Pending Review' | 'Complete';
  };
}

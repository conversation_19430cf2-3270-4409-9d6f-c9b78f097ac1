import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles(() => ({
  noPoiDiv: {
    width: '100%',
    height: '100%',
    textAlign: 'center',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
    color: '#fff',
  },

  mainDiv: {
    height: '100%',
    width: '100%',
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },

  iconButtonLeft: {
    position: 'absolute',
    top: '50%',
    transform: 'translateY(-50%) scale(2.5)',
    left: '7px',
    opacity: '0.6',
    color: '#fff',
    padding: '0',
    filter: 'brightness(0.3)',
  },

  iconButtonRight: {
    position: 'absolute',
    top: '50%',
    transform: 'translateY(-50%) scale(2.5)',
    right: '7px',
    opacity: '0.6',
    color: '#fff',
    padding: '0',
    filter: 'brightness(0.3)',
  },

  buttonWrapDiv: {
    position: 'absolute',
    bottom: '0',
    height: '40px',
    width: '100%',
    paddingLeft: '10px',
    paddingRight: '10px',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    color: '#fff',
    fontSize: '12px',
    lineHeight: '40px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  addButton: {
    fontSize: '14px',
    height: '24px',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    color: '#fff',
    border: '2px solid #fff',
    borderRadius: '4px',
    padding: '2px 11px',
    cursor: 'pointer',
  },
}));

import { useState, useRef, useEffect, useLayoutEffect } from 'react';
import { values, isEmpty } from 'lodash';
import { List, ListRowRenderer } from 'react-virtualized';

import {
  UDR_COLOR,
  HEAD_COLOR,
  PLATE_COLOR,
  LAPTOP_COLOR,
  POIM_COLOR,
  TIMELINE_ICON_COLOR,
} from '@helpers/constants';
import { ReactComponent as IconUDR } from '@resources/images/object_type/UDR_24x24px.svg';
// eslint-disable-next-line import/no-duplicates
import { ReactComponent as IconHead } from '@resources/images/object_type/Head_24x24px.svg';
// eslint-disable-next-line import/no-duplicates
import { ReactComponent as IconPOIM } from '@resources/images/object_type/Head_24x24px.svg';
import { ReactComponent as IconPlate } from '@resources/images/object_type/Plate_24x24px.svg';
import { ReactComponent as IconLaptop } from '@resources/images/object_type/Laptop_24x24px.svg';
import FaceDetection from './FaceDetection';
import * as styles from './styles.scss';
import TimelineRow from './TimelineRow';
import UDRGroupRowComponent from './UDRGroupRows/UDRGroupRowComponent';
import { TimelinesPropTypes } from './TimelinesPropTypes';
import Transcription from './Transcription';
import UserDefinedRegions from './UserDefinedRegions';
import {
  UDRsPolyAssetGroupSeriesItem,
  UDRsPolyAssetStatus,
} from '@common-modules/mediaDetails/models';
import { useIntl } from 'react-intl';
import { SvgIcon } from '@mui/material';

const UDR_ROW_HEIGHT = 34;

const Timelines = ({
  audiowaves,
  mediaDuration,
  startWindowMs,
  stopWindowMs,
  progress,
  detectionCollections,
  udrCollection,
  selectedPolys,
  transcriptRedactions,
  selectedTimeSlices,
  udrAsset,
  globalSettings,

  highlightedOverlay,
  selectedUDRGroupId,

  transcriptionList,
  onUnredactSlice,
  onRedactSlice,
  onSelectSlice,
  onDeselectAll,
  onSetSelectedUDRGroup,
  onChangeUDRGroupLabel,
  onChangeUDR,
  onChangeUDRSubmit,
  onUDRSelect,
  onSetFaceHighlight,
}: TimelinesPropTypes) => {
  const udrRowsContainerRef = useRef<HTMLDivElement>(null);
  const [udrRowsContainerHeight, setUDRRowsContainerHeight] =
    useState<number>(0);
  const [udrRowsContainerWidth, setUDRRowsContainerWidth] = useState<number>(0);
  const intl = useIntl();

  const selectedUDRId =
    highlightedOverlay?.groupId &&
    udrAsset.boundingPolys[highlightedOverlay.groupId] &&
    highlightedOverlay.id
      ? highlightedOverlay.id
      : undefined;

  const [showUDRGroupRows, setShowUDRGroupRows] = useState(false);

  useEffect(() => {
    if (
      highlightedOverlay?.groupId &&
      udrAsset.boundingPolys[highlightedOverlay.groupId]
    ) {
      setShowUDRGroupRows(true);
    }

    if (!highlightedOverlay?.id) {
      setFocusUDRRowIdx(undefined);
    }
  }, [highlightedOverlay, setShowUDRGroupRows, udrAsset]);

  const toggleShowUDRGroupRows = () => {
    if (udrGroupsList.length === 0) {
      return;
    }

    const showUDRGroupRowsLocal = !showUDRGroupRows;

    setShowUDRGroupRows(showUDRGroupRowsLocal);
  };

  const [focusUDRRowIdx, setFocusUDRRowIdx] = useState<number | undefined>(0);

  // TODO: Move this logic to store
  // If UDR group gets deleted and it's selected need to
  // set selectedUDRGroupId to undefined
  // if state of selected changes need to ^^^
  useEffect(() => {
    if (selectedUDRGroupId) {
      const { boundingPolys = {} } = udrAsset;
      const series = boundingPolys[selectedUDRGroupId]?.series || [];
      const seriesSelected = series.find(
        (currentSeries) => selectedPolys[currentSeries.id]
      );

      if (!seriesSelected) {
        onSetSelectedUDRGroup(undefined);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onSetSelectedUDRGroup, selectedPolys, selectedUDRGroupId]);

  const onGroupLabelClick = (id: string) => {
    handleUDRGroupSelection(selectedUDRGroupId === id ? undefined : id);
  };

  function handleUDRGroupSelection(udrGroupId?: string) {
    if (selectedUDRGroupId !== udrGroupId) {
      onSetSelectedUDRGroup(udrGroupId);
    }
  }

  function onUDRContainerClick() {
    onSetFaceHighlight(null);
  }

  function udrSelectHandle(
    groupId: string,
    id: string,
    udr: UDRsPolyAssetGroupSeriesItem
  ) {
    onUDRSelect(groupId, id, udr);
  }

  const udrGroups =
    (udrAsset?.boundingPolys && values(udrAsset.boundingPolys)) || [];

  const udrGroupsList = udrCollection ? udrGroups.filter((item) => !!item) : [];

  const renderUDRGroupRow: ListRowRenderer = ({ index, style }) => {
    const udrGroup = udrGroupsList[index];

    if (!udrGroup) {
      return;
    }

    // const udrGroupWithFilteredSeries = {
    //   ...udrGroup,
    //   series: udrGroup.series.filter(
    //     (currSeries) => selectedPolys[currSeries.id]
    //   ),
    // };

    return (
      <div style={style} key={udrGroup.groupId}>
        <UDRGroupRowComponent
          key={udrGroup.groupId}
          UDRId={udrGroup.groupId}
          active={udrGroup.groupId === selectedUDRGroupId}
          onUDRContainerClick={onUDRContainerClick}
          color="rgb(255, 235, 59)"
          progress={udrGroup.status === UDRsPolyAssetStatus.PENDING ? 0 : 1}
          // udrGroup={udrGroupWithFilteredSeries}
          udrGroup={udrGroup}
          selectedPolys={selectedPolys}
          selectedUDRId={selectedUDRId}
          startMs={startWindowMs}
          stopMs={stopWindowMs}
          onUDRSelect={udrSelectHandle}
          onChangeUDRGroupLabel={onChangeUDRGroupLabel}
          onChangeUDR={onChangeUDR}
          onChangeUDRSubmit={onChangeUDRSubmit}
          onGroupLabelClick={onGroupLabelClick}
        />
      </div>
    );
  };

  useEffect(() => {
    if (
      !highlightedOverlay ||
      isEmpty(highlightedOverlay.groupId) ||
      !udrAsset.boundingPolys[highlightedOverlay.groupId] ||
      !udrRowsContainerRef.current
    ) {
      return;
    }

    const selectedGroupIdx = udrGroups.findIndex(
      (group) => group && highlightedOverlay.groupId === group.groupId
    );

    setFocusUDRRowIdx(selectedGroupIdx);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [highlightedOverlay, showUDRGroupRows]);

  useLayoutEffect(() => {
    if (!udrRowsContainerRef.current) {
      return;
    }
    setUDRRowsContainerHeight(
      udrRowsContainerRef.current.clientHeight - UDR_ROW_HEIGHT || 0
    );
    setUDRRowsContainerWidth(udrRowsContainerRef.current.clientWidth || 0);
  }, []);

  const isDetectingObjects =
    progress.faceDetection &&
    progress.faceDetection > 0 &&
    progress.faceDetection < 1;

  return (
    <div
      data-testid="file-detail-timeline"
      ref={udrRowsContainerRef}
      id="timeline-container"
      className={styles.timelineRow}
    >
      <TimelineRow
        onClick={toggleShowUDRGroupRows}
        color={UDR_COLOR}
        icon={
          <SvgIcon style={{ width: 15, height: 15 }}>
            {IconUDR && <IconUDR stroke={TIMELINE_ICON_COLOR} />}
          </SvgIcon>
        }
        label={intl.formatMessage({ id: 'timelineUdr' })}
        expansionArrowDirection={
          udrGroupsList.length ? (showUDRGroupRows ? 'DOWN' : 'UP') : undefined
        }
        progress={progress.udrs || 0}
      >
        <UserDefinedRegions
          startWindowMs={startWindowMs}
          stopWindowMs={stopWindowMs}
          collection={udrCollection?.collection}
          selected={selectedPolys}
          videoOffset={globalSettings?.videoOffset}
        />
      </TimelineRow>
      {udrGroupsList.length && showUDRGroupRows ? (
        <List
          width={udrRowsContainerWidth}
          height={udrRowsContainerHeight}
          rowCount={udrGroupsList.length}
          rowHeight={UDR_ROW_HEIGHT}
          rowRenderer={renderUDRGroupRow}
          scrollToIndex={focusUDRRowIdx}
          className={styles.virtualListContainer}
        />
      ) : (
        [
          isDetectingObjects || detectionCollections?.['head'] ? (
            <TimelineRow
              key={0}
              color={HEAD_COLOR}
              icon={
                <SvgIcon style={{ width: 15, height: 15 }}>
                  {IconHead && <IconHead fill={TIMELINE_ICON_COLOR} />}
                </SvgIcon>
              }
              label={intl.formatMessage({ id: 'timelineHead' })}
              expansionArrowDirection={undefined}
              progress={progress.faceDetection || 0}
            >
              <FaceDetection
                startWindowMs={startWindowMs}
                stopWindowMs={stopWindowMs}
                collection={detectionCollections?.['head']?.collection}
                selected={selectedPolys}
                videoOffset={globalSettings?.videoOffset}
                color={HEAD_COLOR}
              />
            </TimelineRow>
          ) : null,
          isDetectingObjects || detectionCollections?.['poim'] ? (
            <TimelineRow
              key={2}
              color={POIM_COLOR}
              icon={
                <SvgIcon style={{ width: 15, height: 15 }}>
                  {IconPOIM && <IconPOIM fill={TIMELINE_ICON_COLOR} />}
                </SvgIcon>
              }
              label={intl.formatMessage({ id: 'timelinePoi' })}
              expansionArrowDirection={undefined}
              progress={progress.faceDetection || 0}
            >
              <FaceDetection
                startWindowMs={startWindowMs}
                stopWindowMs={stopWindowMs}
                collection={detectionCollections?.['poim']?.collection}
                selected={selectedPolys}
                videoOffset={globalSettings?.videoOffset}
                color={POIM_COLOR}
              />
            </TimelineRow>
          ) : null,
          isDetectingObjects || detectionCollections?.['licensePlate'] ? (
            <TimelineRow
              key={1}
              color={PLATE_COLOR}
              icon={
                <SvgIcon style={{ width: 15, height: 15 }}>
                  {IconPlate && <IconPlate fill={TIMELINE_ICON_COLOR} />}
                </SvgIcon>
              }
              label={intl.formatMessage({ id: 'timelinePlateVehicle' })}
              expansionArrowDirection={undefined}
              progress={progress.faceDetection || 0}
            >
              <FaceDetection
                startWindowMs={startWindowMs}
                stopWindowMs={stopWindowMs}
                collection={detectionCollections?.['licensePlate']?.collection}
                selected={selectedPolys}
                videoOffset={globalSettings?.videoOffset}
                color={PLATE_COLOR}
              />
            </TimelineRow>
          ) : null,
          isDetectingObjects || detectionCollections?.['laptop'] ? (
            <TimelineRow
              key={2}
              color={LAPTOP_COLOR}
              icon={
                <SvgIcon style={{ width: 15, height: 15 }}>
                  {IconLaptop && <IconLaptop fill={TIMELINE_ICON_COLOR} />}
                </SvgIcon>
              }
              label={intl.formatMessage({ id: 'timelineLaptop' })}
              expansionArrowDirection={undefined}
              progress={progress.faceDetection || 0}
            >
              <FaceDetection
                startWindowMs={startWindowMs}
                stopWindowMs={stopWindowMs}
                collection={detectionCollections?.['laptop']?.collection}
                selected={selectedPolys}
                videoOffset={globalSettings?.videoOffset}
                color={LAPTOP_COLOR}
              />
            </TimelineRow>
          ) : null,
          <TimelineRow
            key={3}
            color="rgb(255, 193, 7)"
            icon={<i className="icon-transcription" />}
            label={intl.formatMessage({ id: 'timelineTranscription' })}
            expansionArrowDirection={undefined}
            progress={progress.transcription || 0}
          >
            <Transcription
              audiowaves={audiowaves}
              mediaDuration={mediaDuration}
              startWindowMs={startWindowMs}
              stopWindowMs={stopWindowMs}
              list={transcriptRedactions}
              selected={selectedTimeSlices}
              onUnredactSlice={onUnredactSlice}
              onRedactSlice={onRedactSlice}
              onSelectSlice={onSelectSlice}
              onDeselectAll={onDeselectAll}
              transcriptionList={transcriptionList}
            />
          </TimelineRow>,
        ]
      )}
    </div>
  );
};

export default Timelines;

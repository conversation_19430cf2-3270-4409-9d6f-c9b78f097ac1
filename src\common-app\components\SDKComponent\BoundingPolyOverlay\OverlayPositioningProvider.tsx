import cn from 'classnames';
import { useRefSize } from './useRefSize';
import { useStyles } from './overlayPositioningProvider.styles';
import { createContext, ReactNode, useMemo, useRef } from 'react';

const INITIAL_POSITION = { top: 0, left: 0, width: 0, height: 0 };

export const OverlayPositioningContext = createContext(INITIAL_POSITION);

const OverlayPositioningProvider = ({
  contentHeight,
  contentWidth,
  fixedWidth,
  children,
}: Props) => {
  const classes = useStyles();
  const ref = useRef<HTMLDivElement>(null);
  const refSize = useRefSize(ref);

  const position = useMemo(() => {
    if (ref.current && refSize) {
      const { height: screenHeight, width: screenWidth } =
        ref.current.getBoundingClientRect();

      const ratioScreen = screenWidth / screenHeight;
      const ratioContent = contentWidth / contentHeight;

      const [width, height] =
        ratioScreen > ratioContent
          ? [(contentWidth * screenHeight) / contentHeight, screenHeight]
          : [screenWidth, (contentHeight * screenWidth) / contentWidth];

      return {
        top: (screenHeight - height) / 2,
        left: (screenWidth - width) / 2,
        height,
        width,
      };
    }
    return INITIAL_POSITION;
  }, [contentHeight, contentWidth, refSize]);

  return (
    <OverlayPositioningContext.Provider value={position}>
      <div
        data-testid="media-player-component"
        className={cn({ [classes.clearfix]: fixedWidth })}
        style={{ height: 'calc(100% - 36px)' }}
      >
        <div
          style={{
            float: fixedWidth ? 'left' : 'none',
            position: 'relative',
            verticalAlign: 'bottom',
            height: '100%',
          }}
          ref={ref}
        >
          {children}
        </div>
      </div>
    </OverlayPositioningContext.Provider>
  );
};

interface Props {
  contentHeight: number;
  contentWidth: number;
  fixedWidth?: boolean;
  children?: ReactNode;
}

export default OverlayPositioningProvider;

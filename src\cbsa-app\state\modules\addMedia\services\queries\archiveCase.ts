import { CaseId, StructuredDataObject } from '@cbsa-modules/universal';

export const ARCHIVE_CASE_QUERY = `
  mutation updateRedactCase($id: ID!, $schemaId: ID!, $data: JSONData!) {
    createStructuredData(input: {
      id: $id
      schemaId: $schemaId
      data: $data
    }) {
      id
      data
    }
  }`;

export interface ARCHIVE_CASE_QUERY_RESPONSE {
  createStructuredData: {
    id: CaseId;
    data: StructuredDataObject;
  };
}

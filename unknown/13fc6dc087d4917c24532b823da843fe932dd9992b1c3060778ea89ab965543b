// import { NOT_FOUND } from 'redux-first-router'
import { createAction } from '@reduxjs/toolkit';
import { LocationState } from 'redux-first-router';
import { FolderId, TDOId } from '../universal/models/Brands';

export const ROUTE_AUTH = createAction<any>('route/ROUTE_AUTH');
export const ROUTE_LOGOUT = createAction('route/ROUTE_LOGOUT');
// TODO: Fix the type here
export const ROUTE_HOME = createAction(
  'route/ROUTE_HOME',
  (payload?: { folderId: FolderId; isCase: boolean }) => ({
    payload,
  })
);
export const ROUTE_REDACTION_CODES = createAction(
  'route/ROUTE_REDACTION_CODES'
);
export const ROUTE_INGEST = createAction('route/ROUTE_INGEST');
export const ROUTE_INGEST_GOVQA = createAction('route/ROUTE_INGEST_GOVQA');
export const ROUTE_EXAMPLE_TAKEOVER = createAction(
  'route/ROUTE_EXAMPLE_TAKEOVER'
);
export const ROUTE_EXAMPLE_TABS = createAction('route/ROUTE_EXAMPLE_TABS');
export const ROUTE_FORBIDDEN = createAction('route/ROUTE_FORBIDDEN');
export const ROUTE_MEDIA_DETAILS = createAction<{
  tdoId: TDOId;
  caseId?: string;
}>('route/ROUTE_MEDIA_DETAILS');
export const ROUTE_OVERVIEW = createAction('route/ROUTE_OVERVIEW');
export const ROUTE_LOAD_FAILED = createAction(
  'route/ROUTE_LOAD_FAILED',
  () => ({
    payload: undefined,
    query: {
      redirect: window.location.href,
    },
  })
);

export const ROUTE_ADD_MEDIA = createAction('route/ROUTE_ADD_MEDIA');
export const ROUTE_MEDIA_EDITOR = createAction('route/ROUTE_MEDIA_EDITOR');
export const ROUTE_REDACT_CASEDASHBOARD = createAction(
  'route/ROUTE_REDACT_CASEDASHBOARD'
);
export const ROUTE_SETTINGS_PROFILE = createAction(
  'route/ROUTE_SETTINGS_PROFILE'
);
// export const refreshRoute = () => (dispatch, getState) => {
//   const currentLocation = getState().location;
//
//   dispatch({
//     type: currentLocation.type,
//     payload: currentLocation.payload,
//     meta: {
//       query: currentLocation.query
//     }
//   });
// };

interface WithLocation {
  location: LocationState<{
    requiresAuth: boolean;
    component: any;
  }>;
}

export const selectCurrentRoutePayload = (state: WithLocation) =>
  state.location.payload;
export const selectRouteType = (state: WithLocation) => state.location.type;
export const selectRouteQuery = (state: WithLocation) => state.location.query;
export const selectRoutesMap = (state: WithLocation) =>
  state.location.routesMap;
export const selectPreviousRoute = (state: WithLocation) => state.location.prev;
export const selectPreviousRouteType = (state: WithLocation) =>
  state.location.prev.type;

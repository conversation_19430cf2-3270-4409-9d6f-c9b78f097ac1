import { renderCBSA, screen } from '@test/testUtils';
import FacesResultsTab from './index';

describe('FacesResultsTab', () => {
  const objects = [];
  for (let i = 0; i < 4; i++) {
    objects.push({
      startTimeMs: 0,
      object: {
        uri: 'https://dummyimage.com/600x400/000/fff',
        label: `label${i}`,
      },
    });
  }

  const engines = [
    {
      jsondata: {
        series: [objects[0], objects[1]],
      },
    },
    {
      jsondata: {
        series: [objects[2], objects[3]],
      },
    },
  ];

  it('should render top bar and faceItems', () => {
    const enginesResults = [engines[0]].map((item, index) => ({
      ...item,
      engineId: `${index + 1}`,
    }));
    const engineResultsWrapper = {
      records: enginesResults,
    };

    // @ts-expect-error
    renderCBSA(<FacesResultsTab enginesResults={engineResultsWrapper} />);

    expect(screen.getByTestId('cluster-results-tab-view')).toBeInTheDocument();
  });
});

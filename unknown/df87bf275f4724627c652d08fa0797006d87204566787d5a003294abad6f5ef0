import express from 'express';
import routes from './routes';
import cors from 'cors';
import bodyParser from 'body-parser';
import swaggerUi from 'swagger-ui-express';
import { swaggerDocument } from '../docs/swagger';

class App {
  public app;

  constructor() {
    this.app = express();
    this.middlewares();
    this.routes();
  }

  middlewares() {
    this.app.use(express.json());
    this.app.use(bodyParser.json());
    this.app.use(bodyParser.urlencoded({ extended: true }));
    this.app.use(cors());
    const customOptions = {
      customSiteTitle: 'Redact API',
      customCss: `.topbar { display: none ; };`,
    };
    this.app.use(
      '/api-docs',
      swaggerUi.serve,
      swaggerUi.setup(swaggerDocument, customOptions)
    );
  }

  routes() {
    this.app.use('/api/v1', routes);
  }
}

export default new App().app;

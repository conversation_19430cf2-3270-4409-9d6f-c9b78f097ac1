import { RequestHeader } from "../model/requests";
import { REGEX_API_TOKEN_KEY, REGEX_SESSION_TOKEN } from "../api/constants";
import { CaseId, FolderId, TDOId } from "../model/brands";

export const isAPITokenKey = (header: RequestHeader): header is RequestHeader & { authorization: string } => {
    const token = header.authorization?.split(' ')?.[1];
    return !!token && REGEX_API_TOKEN_KEY.test(token);
}

export const isSessionToken = (header: RequestHeader): header is RequestHeader & { authorization: string } => {
    const token = header.authorization?.split(' ')?.[1];
    return !!token && REGEX_SESSION_TOKEN.test(token);
}

export const isValidString = (value: unknown): value is string => {
    return (value !== null && value !== undefined && typeof value === "string"  && value.trim() !== '');
}

export const isTdoId = (value: unknown): value is TDOId => {
    return isValidString(value);
}

export const isCaseId = (value: unknown): value is CaseId => {
    return isValidString(value);
}

export const isFolderId = (value: unknown): value is FolderId => {
    return isValidString(value);
}
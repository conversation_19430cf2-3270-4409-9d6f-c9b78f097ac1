import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid2';
import Link from '@mui/material/Link';

import * as styles from './styles.scss';
import { UpgradeSelectionPropTypes } from './UpgradeSelectionPropTypes';

const UpgradeSelectionView = ({
  requestQuoteUrl,
  onUpgradeAccount,
}: UpgradeSelectionPropTypes) => (
  <div>
    <div className={styles.upgradeAccountDescription}>
      Increase the number of cases your team closes.
      <br />
      Upgrade today.
    </div>
    <Grid container spacing={2}>
      <Grid container size={{ xs: 6 }}>
        <Grid
          container
          direction="column"
          size="grow"
          className={styles.upgradeTypeBox}
        >
          <Grid className={styles.upgradeType}>PAY-AS-YOU-GO</Grid>
          <Grid container direction="column">
            <Grid
              container
              direction="row"
              justifyContent="center"
              alignItems="flex-end"
              className={styles.priceInfo}
            >
              <div className={styles.pricePerHr}>
                <div className={styles.price}>$100</div>
                <div className={styles.perHour}>/hour</div>
              </div>
            </Grid>
            <Grid className={styles.billingInfo}>
              <Link
                className={styles.upgradeTypeBoxLink}
                href="https://help.veritone.com/veritone-redact/subscription-options"
              >
                <u>Pay for what you use. No hourly minimum</u>
              </Link>
            </Grid>

            <Grid size="grow">
              <ul className={styles.perkList}>
                <li>Unlimited number of users per organization</li>
                <li>Video redaction (manual and automated)</li>
                <li>Audio redaction (keyword searchable)</li>
                <li>View and download redacted media files</li>
              </ul>
            </Grid>
          </Grid>
          <Grid
            className={`${styles.actionContainer} ${styles.getStartedActionButton} `}
          >
            <Button
              variant="contained"
              color="primary"
              size="large"
              classes={{ root: styles.upgradeActionButton }}
              onClick={onUpgradeAccount}
            >
              <div className={styles.btnContent}>
                GET STARTED
                <span className={styles.bottom}>2-minute sign-up</span>
              </div>
            </Button>
          </Grid>
        </Grid>
      </Grid>
      <Grid container size={{ xs: 6 }}>
        <Grid
          container
          direction="column"
          size="grow"
          className={styles.upgradeTypeBox}
        >
          <Grid className={styles.upgradeType}>Custom</Grid>
          <Grid
            container
            direction="column"
            justifyContent="center"
            alignItems="center"
            size="grow"
          >
            <div className={styles.letsTalk}>Let&apos;s Talk</div>
            <div className={styles.letsTalkDescription}>
              Talk to us about a solution tailored to your team&apos;s specific
              needs. Options include:
            </div>
          </Grid>
          <Grid size="grow">
            <ul className={styles.perkList}>
              <li>
                Bundle with{' '}
                <Link
                  className={styles.upgradeTypeBoxLink}
                  href="https://www.veritone.com/applications/identify/"
                >
                  <u>Veritone Identify</u>
                </Link>
              </li>
              <li>
                Deploy into a US GovCloud environment to support CJIS
                requirements
              </li>
              <li>Non US-based cloud deployment need</li>
              <li>Invoice-based billing</li>
            </ul>
          </Grid>
          <Grid className={styles.actionContainer}>
            <Button
              variant="contained"
              color="primary"
              size="large"
              classes={{ root: styles.upgradeActionButton }}
              href={requestQuoteUrl}
              target="_blank"
            >
              REQUEST A QUOTE
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  </div>
);

export default UpgradeSelectionView;

import CaseDetails from './components/CaseDetails';
import CaseActions from './components/CaseActions';
import Notifications from './components/Notifications';
import { useStyles } from './styles';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const SidebarContent = () => {
  const classes = useStyles();
  return (
    <div className={classes.sidebar}>
      <CaseDetails />
      <CaseActions />
      <Notifications />
    </div>
  );
};

const Sidebar = () => (
  <ThemeProvider theme={defaultTheme}>
    <SidebarContent />
  </ThemeProvider>
);

export default Sidebar;

import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import Dialog from '@common-components/ConfirmDialog';

const DeleteTdoDialog = ({ isOpen, onConfirm, onClose }: Props) => {
  const intl = useIntl();
  return (
    <Dialog
      isOpen={isOpen}
      title={I18nTranslate.TranslateMessage('deleteTdoTitle')}
      content={I18nTranslate.TranslateMessage('deleteTdoContent')}
      confirmText={intl.formatMessage({ id: 'delete' })}
      onConfirm={onConfirm}
      cancelText={intl.formatMessage({ id: 'cancel' })}
      onCancel={onClose}
      warning
    />
  );
};

interface Props {
  readonly isOpen: boolean;
  readonly onConfirm: () => void;
  readonly onClose: () => void;
}

export default DeleteTdoDialog;

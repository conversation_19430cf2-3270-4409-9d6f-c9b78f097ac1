.trimWrapper {
  flex: auto;
  display: flex;
  align-items: center;
  min-width: 4em;
  position: relative;
  // width: 4em;
  z-index: 9999;
  outline: none;
  text-align: center;
  margin: 0;
  padding: 0;
  height: 100%;

  .cutIconsWrapper {
    cursor: pointer;
    padding-top: 10px;
  }
  .checkCancelTrimWrapper {
    position: absolute;
    margin-top: -84px;
    z-index: 9999;
    margin-left: -12px;
    .icon {
      height: 36px;
      width: 36px;
      padding: 6px;
      background-color: #fff;
      border-radius: 18px;
      margin-bottom: 6px;
      cursor: pointer;
    }
  }

  .startTime::before,
  .startTime::after,
  .endTime::before,
  .endTime::after {
    content: '';
    height: 8px;
    width: 1px;
    border-right: 1px solid #fff;
    margin: 0 1px;
  }

  .startTimeInput {
    width: 100px;
    border-bottom: 1px solid #fff;
    margin-right: 20px;

    label {
      color: rgb(255, 255, 255, 0.54) !important;
    }

    textarea {
      color: white;
    }

    input {
      color: white !important;
    }
  }

  .startTime {
    width: 9px;
    cursor: ew-resize;
    height: 100%;
    background: #9b9b9b;
    border: 1px solid #fff;
    position: absolute;
    border-right: 1px solid #fff;
    border-radius: 3px 0 0 3px;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .endTime {
    width: 9px;
    cursor: ew-resize;
    height: 100%;
    background: #9b9b9b;
    position: absolute;
    border: 1px solid #fff;
    border-left: 1px solid #fff;
    border-radius: 0 3px 3px 0;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .trimRange {
    height: 100%;
    background: rgb(0, 0, 0, 0.5);
    padding: 2px 0;
    border-radius: 3px 0 0 3px;
  }

  .fakeProgressBar {
    height: 0.3em;
    width: 100%;
    background: rgb(115, 133, 159, 0.5);
    position: absolute;
  }
}

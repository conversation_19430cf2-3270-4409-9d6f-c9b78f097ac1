import { Organization } from '../models';

export const namespace = 'vtn-component-user-onboarding';

export interface OnboardingSlice {
  readonly [namespace]: OnboardingState;
}

/**
 * Web worker main store state.
 */
export type OnboardingState = UpgradeAccountState &
  InviteAppState &
  OrganizationState;

export interface UpgradeAccountState {
  readonly isUpgradeAccountOpen: boolean;
  readonly showRequestQuoteForm: boolean;
}

export interface InviteAppState {
  readonly isInviteAppOpen: boolean;
}

export interface OrganizationState {
  readonly organization: Organization;
}

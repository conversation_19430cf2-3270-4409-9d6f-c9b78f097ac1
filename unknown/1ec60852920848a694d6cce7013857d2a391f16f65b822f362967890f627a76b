import { Brand } from '@utils';
import { TDOId } from '@common-modules/universal/models/Brands';

export type NotificationSDOId = Brand<string, 'NotificationSDO'>;
export interface NotificationStore {
  readonly sdos: Array<NotificationInterface>;
  readonly showCustomNotificationList: boolean;
}

export interface NotificationInterface {
  readonly jobId: string;
  readonly engineName: string;
  readonly tdoId: TDOId;
  readonly tdoName: string;
  readonly userId: string;
  status: string;
  deleted: boolean;
  readonly sdoId?: NotificationSDOId;
}

export interface Mailbox {
  id: string;
  name: string;
  eventFilter?: {
    eventNames?: Array<string>;
    eventType?: string;
    applicationId?: string;
  };
}

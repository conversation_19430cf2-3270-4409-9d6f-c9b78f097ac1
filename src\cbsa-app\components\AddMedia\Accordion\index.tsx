import {
  AccordionSummary as MuiAccordionSummary,
  AccordionDetails as MuiAccordionDetails,
  AccordionSummaryProps,
  AccordionProps,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import MuiAccordion from '@mui/material/Accordion';
import { ArrowForwardIosSharp } from '@mui/icons-material';

export const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion elevation={0} square {...props}>
    {props.children}
  </MuiAccordion>
))(() => ({
  '&.Mui-expanded': {
    margin: '0',
  },
  '&:not(:last-child)': {
    borderBottom: 0,
  },
}));

export const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary expandIcon={<ArrowForwardIosSharp />} {...props} />
))(({ theme }) => ({
  background: '#70767B',
  boxShadow: '0 3px 8px #00000008',
  border: '1px solid #BAD0D9',
  fontSize: '18px',
  padding: '0 20px',
  width: '100%',
  minHeight: '60px',
  '&.MuiButtonBase-root.MuiAccordionSummary-root.Mui-expanded': {
    background: theme.palette.primary.main,
    minHeight: '70px',
  },
  '& .MuiAccordionSummary-expandIcon.Mui-expanded': {
    transform: 'rotate(90deg)',
  },
  '& .MuiAccordionSummary-content': {
    color: 'white',
    textTransform: 'uppercase',
  },
  '& .MuiSvgIcon-root': {
    fill: 'white',
  },
}));

export const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  padding: '15px 30px',
  background: theme.palette.grey[100],
  flexDirection: 'column',
}));

import { get } from 'lodash';
import { all, fork, select, take } from 'typed-redux-saga/macro';

import { PROCESS_ENGINE_REQUEST as RUN_TRANSCRIPTION } from '@common-modules/engines/transcription/actions';

import { CREATE_JOB_FACES_ACTION } from '@common-modules/facesTabModule';
import {
  NEW_OVERLAY,
  PLAY_REDACTED_ASSET,
  REDACT_SLICE,
  REDACT_WORDS,
  selectAudioRedactions,
  selectTdo,
} from '@common-modules/mediaDetails';
import { DetailTDO } from '@common-modules/mediaDetails/models';

export const sendEvent = <D extends Record<string, any>>(
  name: string,
  metadata?: D
) => window.Intercom?.('trackEvent', name, metadata);
export const isSampleMedia = (tdo: DetailTDO) => {
  const hasCreatedDateTime = get(tdo, 'details.createdDateTime', false);
  return !hasCreatedDateTime;
};

export function* intercomSagas() {
  yield* all([
    fork(watchFirstMediaRunDetection),
    fork(watchFirstMediaRunTracking),
    fork(watchFirstMediaPlayPreview),
    fork(watchFirstAudioTranscribe),
    fork(watchFirstMediaPlayPreviewWithAudioRedactions),
  ]);
}

// function* onActionFetchUserSuccess() {
//   const user: User = yield take(FETCH_USER_SUCCESS);
//   const scriptEl = document.createElement('script');
//   scriptEl.type = 'text/javascript';
//   scriptEl.text =
//     'https://d1f8f9xcsvx3ha.cloudfront.net/sbl/0.7.9/fastspring-builder.min.js';
//   document.head.appendChild(scriptEl);
// }

/**
 * User have clicked on the "Detect Faces" command button in the sample media for the first time.
 * Redact: Tutorial - Video - Auto
 */

export function* watchFirstMediaRunDetection() {
  yield* take(CREATE_JOB_FACES_ACTION);
  const tdo = yield* select(selectTdo);
  if (tdo && isSampleMedia(tdo)) {
    yield sendEvent('Redact: Tutorial - Video - Auto');
  }
}

/**
 * User have created a UDR in the sample media for the first time
 * Redact: Tutorial - Video - Manual
 */

export function* watchFirstMediaRunTracking() {
  yield* take(NEW_OVERLAY);
  const tdo = yield* select(selectTdo);
  if (tdo && isSampleMedia(tdo)) {
    yield sendEvent('Redact: Tutorial - Video - Manual');
  }
}

/**
 * User have viewed the *redacted* sample video for the first time
 * Redact: Tutorial - Video - View
 */

export function* watchFirstMediaPlayPreview() {
  yield* take(PLAY_REDACTED_ASSET);
  const tdo = yield* select(selectTdo);
  if (tdo && isSampleMedia(tdo)) {
    yield sendEvent('Redact: Tutorial - Video - View');
  }
}

/**
 * User have clicked on the "Transcribe Audio" command button for the first time in the sample madia
 * Redact: Tutorial - Audio - Transcribe
 */

export function* watchFirstAudioTranscribe() {
  yield* take(RUN_TRANSCRIPTION);
  const tdo = yield* select(selectTdo);
  if (tdo && isSampleMedia(tdo)) {
    yield sendEvent('Redact: Tutorial - Audio - Transcribe');
  }
}

// Redact: Tutorial - Audio - Redact

export const watchFirstMediaPlayPreviewWithAudioRedactions = function* (): any {
  yield* take([REDACT_WORDS, REDACT_SLICE]);
  const audioRedactions = yield* select(selectAudioRedactions);
  if (audioRedactions.length > 0) {
    yield sendEvent('Redact: Tutorial - Audio - Redact');
  } else {
    yield* watchFirstMediaPlayPreviewWithAudioRedactions();
  }
};

Feature: UDR

  @e2e @mdp @draw-udr @regression
  Scenario: Prepare test data
    Given The user uploads file "lucy.mp4" with transcription "off"   

  @e2e @mdp @draw-udr @regression
  Scenario: 1 UDR
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible

  @e2e @mdp @draw-udr @regression
  Scenario: Multiple UDR
    Given The user resets and goes to video "lucy.mp4"
    When The user draws 3 UDRs with increasing coordinates
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible

  @e2e @mdp @draw-udr @regression
  Scenario: Draw group UDR
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see the UDR in the timeline and cluster list
    When The user clicks on overlay group 1 in timeline
    And The user plays the video to 3 seconds
    And The user draws "1st" UDR at coordinates 200, 100, 250, 200
    Then The user should see the updated UDR "00:00 - 00:03" in the "1st" timeline and cluster list

  @e2e @mdp @draw-udr @regression
  Scenario: Add redaction effect
    Given The user resets and goes to video "lucy.mp4"
    When The user clicks on "Redacted" radio in Overlay Preview
    And The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see the UDR in the timeline and cluster list
    When The user opens UDR menu for UDR 1 with type "udr"
    And The user clicks on "<menuValue>" in UDR menu
    Then The pop-up "Custom Redaction Effect" should be shown
    When The user selects "<value>" redaction effect
    And The user presses the "Add" button in "Custom Redaction Effect" popup
    Then The user sees "<cssElement>" for UDR 1 should have "<colors>" color

    Examples:
      | value      | cssElement   | colors             | menuValue            |
      | black_fill | color        | rgb(34, 34, 34)    | Add Redaction Effect |
      | outline    | border-color | rgb(255, 255, 255) | Add Redaction Effect |

  @e2e @mdp @draw-udr @regression
  Scenario: Change blur level for UDR
    Given The user resets and goes to video "lucy.mp4"
    When The user clicks on "Redacted" radio in Overlay Preview
    And The user draws "1st" UDR at coordinates 100, 100, 200, 200
    # Then The user should see the UDR in the timeline and cluster list
    When The user opens UDR menu for UDR 1 with type "udr"
    And The user clicks on "Add Redaction Effect" in UDR menu
    And The user changes blur level to 5
    When The user presses the "Add" button in "Custom Redaction Effect" popup
    Then UDR 1 should have blur attribute

  @e2e @mdp @draw-udr @regression
  Scenario: Restore default of redaction effect for UDR
    Given The user resets and goes to video "lucy.mp4"
    When The user clicks on "Redacted" radio in Overlay Preview
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Add Redaction Effect" in UDR menu
    When The user selects "black_fill" redaction effect
    When The user presses the "Add" button in "Custom Redaction Effect" popup
    Then The user sees "color" for UDR 1 should have "rgb(34, 34, 34)" color
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Edit Redaction Effect" in UDR menu
    When The user presses the "Restore Default" button in "Custom Redaction Effect" popup
    Then The UDR 1 is not black-fill
    # flaky

  @e2e @mdp @draw-udr @regression
  Scenario: Remove redaction effect from UDR
    Given The user resets and goes to video "lucy.mp4"
    When The user clicks on "Redacted" radio in Overlay Preview
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Add Redaction Effect" in UDR menu
    When The user selects "black_fill" redaction effect
    When The user presses the "Add" button in "Custom Redaction Effect" popup
    Then The user sees "color" for UDR 1 should have "rgb(34, 34, 34)" color
    When The user opens UDR menu for UDR 1 with type "udr"
    And The user clicks on "Remove Redaction Effect" in UDR menu
    And Presses the "Delete" button
    Then The UDR 1 is not black-fill
    # flaky

  @e2e @mdp @draw-udr @regression
  Scenario: Stick UDR to video
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 150, 200, 200
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Stick to video" in UDR menu
    Then The timeline display a yellow line runs throughout the duration of the video

  @e2e @mdp @draw-udr @regression
  Scenario: Set time stamp for UDR
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 150, 200, 200
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Set Time Stamp" in UDR menu
    Then The timeline display a yellow line runs throughout the duration of the video
    When The user set "End" time "2" for UDR
    When The user set "Start" time "0" for UDR
    Then The user click button Set to set time for UDR
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Set Time Stamp" in UDR menu
    Then The user should see UDR set start time is "00" and end time is "02"

  @e2e @mdp @draw-udr @regression
  Scenario: Verify set time stamp for UDR with <warningText>
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 150, 200, 200
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Set Time Stamp" in UDR menu
    Then The timeline display a yellow line runs throughout the duration of the video
    When The user set "End" time "<endTime>" for UDR
    When The user set "Start" time "<startTime>" for UDR
    Then Screen display a red warning on "<warningText>" pop-up set time

    Examples:
      | startTime | endTime | warningText             |
      |         0 |      30 | Maximum value: exceeded |
      |        30 |       2 | Maximum value:          |

  @e2e @mdp @draw-udr @regression
  Scenario: Can't set Start and End time in negative for UDR
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 150, 200, 200
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Set Time Stamp" in UDR menu
    Then The timeline display a yellow line runs throughout the duration of the video
    When The user set "End" time "-" for UDR
    When The user set "Start" time "-" for UDR
    Then The user should see UDR set start time is "00" and end time is "00"

  @e2e @mdp @draw-udr @regression
  Scenario: Delete a UDR in Frame
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 150, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Delete UDR in Frame" in UDR menu
    Then The UDR 1 is deleted

  @e2e @mdp @draw-udr @regression
  Scenario: Delete UDR <menuValue>
    Given The user resets and goes to video "lucy.mp4"
    When The user draws 2 UDRs with increasing coordinates
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "<menuValue>" in UDR menu
    Then The UDR group is deleted from the timeline and right panel

    Examples:
      | menuValue          |
      | Delete UDR Group   |
      | Delete UDR Overlay |
      | Delete UDR Segment |

  @e2e @mdp @draw-udr @regression
  Scenario: Verify Redaction code
    Given The user resets and goes to video "lucy.mp4"
    When The user clicks on "Redacted" radio in Overlay Preview
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Add Redaction Code" in UDR menu
    Then The pop-up "Add Code" should be shown
    When The user searches and selects Redaction Code "Yumi code"
    And The user presses the "Add" button in "Add Code" popup
    Then The UDR 1 contains Redaction Code "ASFW123" and text color "rgb(253, 216, 53)"
    # Check change color
    Then The user reloads the page
    Then The user is on the video overlay
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Add Redaction Code" in UDR menu
    Then The pop-up "Add Code" should be shown
    When The user searches and selects Redaction Code "Yumi code"
    And The user change redact color to "B71C1C" in "Add Code" popup
    And The user presses the "Add" button in "Add Code" popup
    Then The UDR 1 contains Redaction Code "ASFW123" and text color "rgb(183, 28, 28)"
    # Check restore color
    When The user opens UDR menu for UDR 1 with type "udr"
    When The user clicks on "Edit Redaction Code" in UDR menu
    Then The pop-up "Edit Code" should be shown
    When The user presses the "Restore Default Color" button in "Edit Code" popup
    And The user presses the "Save" button in "Edit Code" popup
    Then The UDR 1 contains Redaction Code "ASFW123" and text color "rgb(253, 216, 53)"

  @e2e @mdp @draw-udr @regression
  Scenario: Verify user can change UDR shape
    Given The user resets and goes to video "lucy.mp4"
    When The user clicks on "Redacted" radio in Overlay Preview
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    And The user changes the UDR 1 shape
    Then The shape of the UDR 1 is "ellipse"

  @e2e @mdp @draw-udr @regression
  Scenario: Select an UDR from right panel
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user unchecks all checkbox in the right panel
    When The user selects checkbox for UDR 1 in the right panel
    Then The UDR 1 is displayed in preview video

  @e2e @mdp @draw-udr @regression
  Scenario: UDR will be displayed in Timeline and right panel
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 50, 50, 200, 200
    Then The user should see the UDR in the timeline and cluster list

  @e2e @mdp @draw-udr @regression
  Scenario: See start and end time of UDR in right panel
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 50, 50, 200, 200
    Then The user should see the start time and end time of UDR in the right panel

  @e2e @mdp @draw-udr @regression
  Scenario: Change name for UDR group
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 50, 50, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user clicks on "Overlay Group" in UDR group name at the right panel
    When The user types "Test" in name input
    Then The user should see "Test" in "1st" row of UDR group name at the right panel is visible
    When The user clicks on Pencil icon in UDR group name at the right panel
    When The user types "New Test" in name input
    Then The user should see "New Test" in "1st" row of UDR group name at the right panel is visible

  @e2e @mdp @draw-udr @regression
  Scenario: Select/Unselect UDR from right panel
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 150, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user unchecks all checkbox in the right panel
    Then The UDR 1 does not display in preview video
    When The user selects checkbox for UDR 1 in the right panel
    Then The UDR 1 is displayed in preview video

  @e2e @mdp @draw-udr @regression
  Scenario: Change Redaction Effects at right panel
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 150, 200, 200
    Then The user should see "Overlay Group 1" in "1st" row of UDR group name at the right panel is visible
    When The user clicks on "Redacted" radio in Overlay Preview
    When The user clicks on Redaction Effects menu in right panel
    When The user selects "black_fill" redaction effect in right panel
    Then The user save new redaction effect in right panel
    Then The user sees "color" for UDR 1 should have "rgb(34, 34, 34)" color

  @e2e @mdp @draw-udr @regression
  Scenario: Sort UDR by start Time
    Given The user resets and goes to video "lucy.mp4"
    When The user draws "1st" UDR at coordinates 100, 100, 200, 200
    Then The user should see the UDR in the timeline and cluster list
    When The user clicks on overlay group 1 in timeline
    And The user plays the video to 3 seconds
    When The user clicks on overlay group 1 in timeline
    When The user draws "1st" UDR at coordinates 50, 50, 200, 200
    Then The user should see "Overlay Group 2" in "2nd" row of UDR group name at the right panel is visible
    When The user selects sort by "start" time in right panel
    Then The user should see "Overlay Group 2" in "1st" row of UDR group name at the right panel is visible

  @e2e @mdp @draw-udr @regression @small-screen
  Scenario: Verify UDR Scroll bar
    Given The user resets and goes to video "lucy.mp4"
    When The user draws 6 UDRs with increasing coordinates
    When The user scrolls the navigation bar in right panel
    Then The user should see "Overlay Group 5" in "4th" row of UDR group name at the right panel is visible

  @e2e @mdp @draw-udr
  Scenario: Delete multiple test files
    Given The user deletes files
      | lucy.mp4 |
import { Re } from '../reducers';

export const processCase: Re = (state) => ({
  ...state,
  loaders: {
    ...state.loaders,
    isProcessingCase: true,
  },
});

export const processCaseSuccessful: Re = (state) => ({
  ...state,
  loaders: {
    ...state.loaders,
    isProcessingCase: false,
  },
});

export const processCaseFailure: Re = (state) => ({
  ...state,
  loaders: {
    ...state.loaders,
    isProcessingCase: false,
  },
});

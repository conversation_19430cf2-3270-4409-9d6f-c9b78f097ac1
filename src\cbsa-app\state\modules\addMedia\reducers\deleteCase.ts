import { Re } from '../reducers';
import { Case } from '@cbsa-modules/universal';

export const deleteCase: Re = (state) => ({
  ...state,
  loaders: {
    ...state.loaders,
    isDeletingCase: true,
  },
});

export const deleteCaseSuccessful: Re<Case> = (state, { payload }) => {
  const { status, modifiedDateTime } = payload;

  return {
    ...state,
    caseDetails: state.caseDetails
      ? {
          ...state.caseDetails,
          status,
          modifiedDateTime,
        }
      : null,
    loaders: {
      ...state.loaders,
      isDeletingCase: false,
    },
  };
};

export const deleteCaseFailure: Re = (state) => ({
  ...state,
  loaders: {
    ...state.loaders,
    isDeletingCase: false,
  },
});

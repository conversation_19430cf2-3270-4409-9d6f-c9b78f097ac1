import { clamp, Dictionary, keyBy } from 'lodash';
import { createSelector } from 'reselect';

import {
  AudioRedactionSlice,
  RedactedWords,
  TranscriptionViewableWords,
  ViewSettings,
} from '../models';
import { namespace } from '../store';
import { DetectionAssetState } from '../store.models';
import { ElementOf } from 'ts-essentials';

const selectStore = (s: { [namespace]: DetectionAssetState & ViewSettings }) =>
  s[namespace];

export const selectDisableUnredactAuditLog = createSelector(
  selectStore,
  (s) => s.disableUnredactAuditLog
);

export const selectTranscription = createSelector(
  selectStore,
  (s) => s.transcription
);

export const selectTranscriptionList = createSelector(
  selectTranscription,
  (t) => t?.transcription || []
);

export const selectTranscriptionView = createSelector(
  selectStore,
  (s) => s.transcriptionView
);

export const selectWordsSelected = createSelector(
  selectTranscriptionView,
  (s) => s.selected
);

export const selectSelectedTimeSlices = createSelector(
  selectTranscriptionView,
  ({ selected }) =>
    selected.reduce<AudioRedactionSlice[]>((acc, s) => {
      const lastSlice = acc.at(-1);
      if (!lastSlice || lastSlice[1] < s.startTimeMs) {
        acc.push([s.startTimeMs, s.stopTimeMs, undefined]);
        return acc;
      } else {
        const poppedSlice = acc.pop();
        if (poppedSlice) {
          acc.push([poppedSlice[0], s.stopTimeMs, lastSlice[2]]);
        }
        return acc;
      }
    }, [])
);

export const selectWordsSelectedMap = createSelector(
  selectTranscriptionView,
  (s) => keyBy(s.selected, 'id')
);

export const selectAudioRedactions = createSelector(
  selectTranscriptionView,
  (s) => s.redactions
);

export const selectWordsRedacted = createSelector(
  selectTranscription,
  selectTranscriptionView,
  (t, s) => {
    const redacted: RedactedWords[] = [];
    if (t) {
      for (const word of t.transcription) {
        for (const [start, end, redactionNotes] of s.redactions) {
          if (!(word.stopTimeMs <= start || word.startTimeMs >= end)) {
            redacted.push({
              ...word,
              startRedactionMs: Math.max(word.startTimeMs, start),
              stopRedactionMs: Math.min(word.stopTimeMs, end),
              redactionNotes,
            });
          }
        }
      }
    }
    return redacted as ReadonlyArray<ElementOf<typeof redacted>>;
  }
);

export const selectWordsRedactedMap = createSelector(
  selectWordsRedacted,
  (r) => keyBy(r, 'id') as Dictionary<RedactedWords | undefined>
);

export const selectRedactNotesEditing = createSelector(
  selectTranscriptionView,
  (v) => v.editNotes
);

export const selectRedactNotesIsEditorOpen = createSelector(
  selectRedactNotesEditing,
  (n) => !!n
);

export const selectCurrentEditingWord = createSelector(
  selectTranscriptionView,
  (v) => v.currentEditWords
);

export const selectCurrentPositionWord = createSelector(
  selectTranscription,
  (t) => {
    const transcription = t?.transcription || [];
    return (currentTime: number) => {
      let firstIndex = 0;
      let lastIndex = transcription.length - 1;
      let middleIndex = clamp(
        Math.floor((lastIndex + firstIndex) / 2),
        0,
        transcription.length - 1
      );

      if (lastIndex < 0) {
        return undefined;
      }

      const compare = (item: TranscriptionViewableWords, time: number) =>
        time >= item.startTimeMs && time <= item.stopTimeMs
          ? 0
          : time < item.startTimeMs
            ? -1
            : 1;

      while (
        compare(transcription[middleIndex]!, currentTime) !== 0 && // Safe due to index conditions
        firstIndex < lastIndex
      ) {
        const middleItem = transcription[middleIndex]!; // Safe due to index conditions
        if (compare(middleItem, currentTime) < 0) {
          lastIndex = middleIndex - 1;
        } else if (compare(middleItem, currentTime) > 0) {
          firstIndex = middleIndex + 1;
        }
        middleIndex = clamp(
          Math.floor((lastIndex + firstIndex) / 2),
          0,
          transcription.length - 1
        );
      }

      return compare(transcription[middleIndex]!, currentTime) === 0 // Safe due to index conditions
        ? transcription[middleIndex]
        : undefined;
    };
  }
);

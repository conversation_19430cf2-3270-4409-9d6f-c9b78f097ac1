import { I18nTranslate } from '@i18n';
import { useCallback, useRef, useState } from 'react';
import IconButton from '@mui/material/IconButton';
import ChevronLeft from '@mui/icons-material/ChevronLeft';
import ChevronRight from '@mui/icons-material/ChevronRight';
import { SummaryTDO, TreeObjectId } from '@cbsa-modules/universal';
import { useStyles } from './styles';

const PersonOfInterestViewer = ({ caseId, images, uploadMedia }: Props) => {
  const classes = useStyles();
  const [index, setIndex] = useState(0);
  const inputFile = useRef<HTMLInputElement>(null);

  const handleSelectImage = () => inputFile.current?.click();
  const handleUploadImage = () => {
    const file = inputFile.current?.files?.[0];
    if (file && caseId) {
      uploadMedia({ file, caseId });
    }
  };

  const renderPoiImage = useCallback(() => {
    if (images.length === 0) {
      return (
        <div className={classes.noPoiDiv}>
          <span>
            {I18nTranslate.TranslateMessage('noPersonOfInterestImages')}
          </span>
        </div>
      );
    } else {
      return (
        <img
          src={images[index]?.primaryAsset?.signedUri}
          alt="person of interest"
          style={{ maxWidth: '100%', maxHeight: '100%' }}
        />
      );
    }
  }, [images, index, classes]);

  return (
    <div className={classes.mainDiv}>
      {renderPoiImage()}
      {images.length > 1 && (
        <>
          <IconButton
            onClick={() => {
              let newIndex = index - 1;
              if (newIndex < 0) {
                newIndex = images.length - 1;
              }
              setIndex(newIndex);
            }}
            size="medium"
            classes={{ root: classes.iconButtonLeft }}
          >
            <ChevronLeft />
          </IconButton>
          <IconButton
            onClick={() => {
              let newIndex = index + 1;
              if (newIndex > images.length - 1) {
                newIndex = 0;
              }
              setIndex(newIndex);
            }}
            size="medium"
            classes={{ root: classes.iconButtonRight }}
          >
            <ChevronRight />
          </IconButton>
        </>
      )}
      <div className={classes.buttonWrapDiv}>
        <span>
          <span style={{ fontWeight: 'bold' }}>
            {images.length > 0 ? index + 1 : '0'}/{images.length}
          </span>{' '}
          {I18nTranslate.TranslateMessage('POIReferenceImages')}
        </span>
        <button
          type="button"
          className={classes.addButton}
          onClick={handleSelectImage}
        >
          ADD
        </button>
        <input
          type="file"
          accept="image/*"
          ref={inputFile}
          style={{ display: 'none' }}
          onChange={handleUploadImage}
        />
      </div>
    </div>
  );
};

interface Props {
  caseId: TreeObjectId;
  images: Array<SummaryTDO>;
  uploadMedia: ({ file, caseId }: { file: File; caseId: TreeObjectId }) => void;
}

export default PersonOfInterestViewer;

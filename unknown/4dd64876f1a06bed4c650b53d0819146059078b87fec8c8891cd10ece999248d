import express from 'express';
import { Logger } from '../logger';
import { pick } from 'lodash';
import { Messages } from '../errors/messages';
import { StatusCodes } from '../errors/statusCodes';
import { getSdoIdAdapter } from '../adapters/getSdoId';
import { deleteFolderAdapter } from '../adapters/deleteFolder';
import { getFilesByTreeObjectIdAdapter } from '../adapters/getFilesByTreeObjectIdAdapter';
import { deleteAllFilesAdapter } from '../adapters/deleteAllFiles';
import { checkFolderLockAdapter } from '../adapters/checkFolderLock';
import { isFolderId } from '../validations/helpers';

export const deleteFolder = async (
    req: express.Request,
    res: express.Response
  ) => {
    const folderId = req.params?.folderId?.trim();
    const headers = pick(req.headers, ['authorization']);
    if (!isFolderId(folderId)) {
      return res.status(StatusCodes.BadRequest).json({ error: Messages.folderIdRequired});
    }

    const folder = await getSdoIdAdapter(headers, folderId);
    const isCase = folder && folder.contentTemplates?.length > 0;
    if (isCase) {
      return res.status(StatusCodes.BadRequest).json({ error: Messages.notCaseDeleteEndpoint });
    }
    if (folder) {
      const caseLockResponse = await checkFolderLockAdapter(headers, folderId);
      if (caseLockResponse.isFailed) {
        return res.status(StatusCodes.BadRequest).json({ error: Messages.checkFolderLockFail});
      }
      else if (caseLockResponse.isLocked ) {
        return res.status(StatusCodes.BadRequest).json({ error: Messages.folderIsLocked });
      }
      else if (caseLockResponse.isJobRunning ) {
        return res.status(StatusCodes.BadRequest).json({ error: Messages.FolderCaseFilesInProcessingState});
      }

      const getFilesResponse = await getFilesByTreeObjectIdAdapter(headers, folder.treeObjectId);
      if(getFilesResponse.isFailed) {
        return res.status(StatusCodes.Success).json({ message: Messages.getFilesByTreeObjectIdFail });
      }
      if (getFilesResponse.tdoIds?.length > 0) {
        const deleteFilesResponse = await deleteAllFilesAdapter(headers, getFilesResponse.tdoIds);
          if (deleteFilesResponse.isFailed) {
            return res.status(StatusCodes.Success).json({message: Messages.deleteAllFilesFail });
          }
      }

      const deletedId = await deleteFolderAdapter(headers, folder.treeObjectId); 
      if (deletedId === folder.treeObjectId) {
        Logger.log('deleted folderId: ' + folderId);
        return res.status(StatusCodes.Success).json({
            folderId: folderId,
            message: Messages.deleteFolderSuccess
        });
      } else {
        if (deletedId === 'not-found') {
          return res.status(StatusCodes.BadRequest).json({ error: Messages.InvalidFolderId });
        } else {
            return res.status(StatusCodes.BadRequest).json( {error : Messages.DeleteFolderFail });
        }
      }
    } else {
        return res.status(StatusCodes.BadRequest).json({error: Messages.InvalidFolderId });
    }
  };

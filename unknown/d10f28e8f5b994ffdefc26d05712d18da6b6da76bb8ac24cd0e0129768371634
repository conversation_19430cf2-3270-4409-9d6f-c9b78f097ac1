import makeStyles from '@mui/styles/makeStyles';

import {
  UDR_COLOR,
  CARD_COLOR,
  NOTEPAD_COLOR,
  HEAD_COLOR,
  PLATE_COLOR,
  LAPTOP_COLOR,
  POIM_COLOR,
} from '@helpers/constants';

const previewStyles = {
  black_fill: {
    backgroundColor: '#000000',
    border: '2px solid #000000',
  },
  none: {
    display: 'none',
  },
};

const stylesByObjectType = {
  head: {
    backgroundColor: `${HEAD_COLOR}26`,
    border: `2px solid ${HEAD_COLOR}`,
  },
  udr: {
    backgroundColor: `${UDR_COLOR}26`,
    border: `2px solid ${UDR_COLOR}`,
  },
  license: {
    backgroundColor: `${PLATE_COLOR}26`,
    border: `2px solid ${PLATE_COLOR}`,
  },
  vehicle: {
    backgroundColor: `${PLATE_COLOR}26`,
    border: `2px solid ${PLATE_COLOR}`,
  },
  laptop: {
    backgroundColor: `${LAPTOP_COLOR}26`,
    border: `2px solid ${LAPTOP_COLOR}`,
  },
  notepad: {
    backgroundColor: `${NOTEPAD_COLOR}26`,
    border: `2px solid ${NOTEPAD_COLOR}`,
  },
  card: {
    backgroundColor: `${CARD_COLOR}26`,
    border: `2px solid ${CARD_COLOR}`,
  },
  poim: {
    backgroundColor: `${POIM_COLOR}26`,
    border: `2px solid ${POIM_COLOR}`,
  },
  disabled: {
    backgroundColor: 'rgba(80, 80, 80, 0.15)',
  },
};

export const useStyles = makeStyles((_theme) => ({
  player: {
    height: '46vh',
  },

  // @import '~video-react/styles/scss/video-react';

  // videoReactBigPlayButton: {
  //   top: '150px',
  // },

  previewStyles,
  ...stylesByObjectType,

  overlay_black_fill: {
    head: previewStyles.black_fill,
    poim: previewStyles.black_fill,
    udr: previewStyles.black_fill,
    licensePlate: previewStyles.black_fill,
    vehicle: previewStyles.black_fill,
    laptop: previewStyles.black_fill,
    notepad: previewStyles.black_fill,
    card: previewStyles.black_fill,
    disabled: stylesByObjectType.disabled,
  },
}));

import { ClusterItemGroup } from '@common-modules/mediaDetails/models';

import ClusterSegmentView from '../ClusterSegment';
import PropTypes, { SeekMediaTimePayload } from '../PropTypes';

const Expanded = ({
  isExpanded,
  segments,
  setSelectedGroups,
  highlightedOverlay,
  selected,
  onHighlightPoly,
}: ClusterGroupExpandedPropTypes) => (
  <>
    {isExpanded
      ? segments.map((segment) => (
          <ClusterSegmentView
            key={segment.id}
            segment={segment}
            setSelectedGroups={setSelectedGroups}
            selected={selected}
            highlightedOverlay={highlightedOverlay}
            onHighlightPoly={onHighlightPoly}
          />
        ))
      : null}
  </>
);

export default Expanded;

export type ClusterGroupExpandedPropTypes = Pick<
  PropTypes,
  'selected' | 'highlightedOverlay' | 'setSelectedGroups'
> & {
  readonly onHighlightPoly: (payload: SeekMediaTimePayload) => void;
  readonly isExpanded: boolean;
  readonly segments: ClusterItemGroup['segments'];
};

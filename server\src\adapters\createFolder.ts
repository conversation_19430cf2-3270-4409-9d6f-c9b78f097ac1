import { CreateFolderResponse } from '../model/responses';
import { CreateFolderRequest, RequestHeader } from '../model/requests';
import { callGQL } from '../api/callGraphql';
import { createFolderQuery, createFolderUsingUserIdQuery } from '../api/queries';
import { Messages } from '../errors/messages'
import { Logger } from '../logger';
import { isEmpty } from 'lodash';

export const createFolderAdapter = async (
  headers: RequestHeader,
  request: CreateFolderRequest
) => {
  const query = isEmpty(request.userId) ? createFolderQuery : createFolderUsingUserIdQuery;
  const createFolder = await callGQL<CreateFolderResponse>(
    headers,
    query,
    { ...request }
  )
    .then((res) => res.createFolder)
    .catch((err) => {
      Logger.error(Messages.createFolder, JSON.stringify(err));
    });    
  return createFolder;
};

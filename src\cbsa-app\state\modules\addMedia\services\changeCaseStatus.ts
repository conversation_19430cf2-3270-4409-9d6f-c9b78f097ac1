import moment from 'moment';
import callGraph<PERSON><PERSON><PERSON> from '@helpers/callGraph<PERSON>A<PERSON>';
import { Case, Thunk, lookupLatestCaseSchemaId } from '@cbsa-modules/universal';
import {
  CHANGE_CASE_STATUS_QUERY,
  ChangeCaseStatusQueryResponse,
} from './queries/changeCaseStatus';
import {
  CHANGE_CASE_STATUS_FAILURE,
  CHANGE_CASE_STATUS_SUCCESS,
} from '@cbsa-modules/addMedia';
import { NOOP } from '@cbsa-modules/universal/actions';

export const changeCaseStatus: Thunk<{
  readonly caseDetails: Case;
  readonly caseStatus: string;
}> =
  ({ caseDetails, caseStatus }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    return await callGraph<PERSON>Api<ChangeCaseStatusQueryResponse>({
      actionTypes: [
        NOOP,
        <PERSON>AN<PERSON>_CASE_STATUS_SUCCESS,
        CHANGE_CASE_STATUS_FAILURE,
      ],
      query: CHANGE_CASE_STATUS_QUERY,
      variables: {
        id: caseDetails.id,
        schemaId,
        data: {
          caseName: caseDetails.name,
          status: caseStatus,
          archive: caseDetails.archive,
          folderTreeObjectId: caseDetails.treeObjectId,
          createdDateTime: caseDetails.createdDateTime,
          modifiedDateTime: moment(new Date()).toISOString(),
          priority: 1,
          processingTime: 3600,
        },
      },
      dispatch,
      getState,
    });
  };

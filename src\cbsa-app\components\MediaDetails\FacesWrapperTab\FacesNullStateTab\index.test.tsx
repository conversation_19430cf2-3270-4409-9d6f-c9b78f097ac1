import { renderCBSA, screen } from '@test/testUtils';
import FacesNullStateTab from './index';

describe('FacesNullStateTab', () => {
  it('should render detect faces button and text description', () => {
    renderCBSA(<FacesNullStateTab />);

    expect(
      screen.getByTestId('faces-null-state-tab-message')
    ).toHaveTextContent(
      'Automatically detect and place bounding boxes on objects within this video, making redaction faster and easier for you.'
    );
    expect(screen.getByRole('button')).toHaveTextContent(
      'DETECT HEADS AND OBJECTS'
    );
    expect(screen.getByRole('img')).toBeInTheDocument();
  });
});

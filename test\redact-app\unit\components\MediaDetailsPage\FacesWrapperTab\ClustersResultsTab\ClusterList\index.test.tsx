import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { I18nProvider, LOCALES } from '@i18n';
import * as redux from 'react-redux';
import ClusterList, {
  ClusterListViewPropTypes,
} from '@redact-components/MediaDetailsPage/FacesWrapperTab/ClustersResultsTab/ClusterList';
import ClusterListFilter from '@redact-components/MediaDetailsPage/FacesWrapperTab/ClustersResultsTab/ClusterListFilter';
import ClusterListSort from '@redact-components/MediaDetailsPage/FacesWrapperTab/ClustersResultsTab/ClusterListSort';
import { DEFAULT_FILTER_PARAMETERS } from '@helpers/constants';
import { vars as sortProps } from '../ClusterListSort/index.test';
import { vars as filterProps } from '../ClusterListFilter/index.test';
import configureStore from 'redux-mock-store';
import {
  namespace,
  SetMergeSelectClusterIdsRequest,
} from '@common-modules/mediaDetails';
import { VirtuosoMockContext } from 'react-virtuoso';

jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: jest.fn(),
}));

const vars: ClusterListViewPropTypes = {
  clusterList: [
    {
      id: 'string1',
      type: 'head',
      picUri: 'string',
      userLabel: 'string',
      pinnedDate: undefined,
      segmentSortType: 'time',
      startTimeMs: 0,
      stopTimeMs: 10,
      groups: [
        {
          id: 'string1',
          type: 'head',
          picUri: 'string',
          clusterId: 'string',
          segments: [
            {
              id: 'string2',
              groupId: 'group1',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
            {
              id: 'string3',
              groupId: 'group1',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
          ],
        },
      ],
      segments: [
        {
          id: 'string2',
          groupId: 'group1',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
        {
          id: 'string3',
          groupId: 'group1',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
      ],
    },
    {
      id: 'string4',
      type: 'head',
      picUri: 'string',
      userLabel: 'string',
      pinnedDate: undefined,
      segmentSortType: 'time',
      startTimeMs: 0,
      stopTimeMs: 10,
      groups: [
        {
          id: 'string4',
          type: 'head',
          picUri: 'string',
          clusterId: 'string',
          segments: [
            {
              id: 'string5',
              groupId: 'group2',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
            {
              id: 'string6',
              groupId: 'group2',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
          ],
        },
      ],
      segments: [
        {
          id: 'string5',
          groupId: 'group2',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
        {
          id: 'string6',
          groupId: 'group2',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
      ],
    },
  ],
  changeClusterLabel: jest.fn(),
  changeClusterPinned: jest.fn(),
  highlightedOverlay: undefined,
  selected: {},
  clusterMergeGroupIds: {},
  clusterMergeSegments: {},
  setSelectedGroups: jest.fn(),
  setClusterMergeGroupIds: jest.fn(),
  setClusterMergeSegments: jest.fn(),
  onHighlightPoly: jest.fn(),
  updateClusterAssignment: jest.fn(),
  changeSegmentSortType: jest.fn(),
  children: [],
  virtuosoRef: { current: null },
  handleScrollToIndex: jest.fn(),
};

const pinnedClusterVars: ClusterListViewPropTypes = {
  clusterList: [
    {
      id: 'string4',
      type: 'head',
      picUri: 'string',
      userLabel: 'cluster1',
      pinnedDate: 1732520511526,
      segmentSortType: 'time',
      startTimeMs: 0,
      stopTimeMs: 10,
      groups: [
        {
          id: 'string4',
          type: 'head',
          picUri: 'string',
          clusterId: 'string',
          segments: [
            {
              id: 'string5',
              groupId: 'group2',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
            {
              id: 'string6',
              groupId: 'group2',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
          ],
        },
      ],
      segments: [
        {
          id: 'string5',
          groupId: 'group2',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
        {
          id: 'string6',
          groupId: 'group2',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
      ],
    },
    {
      id: 'string1',
      type: 'head',
      picUri: 'string',
      userLabel: undefined,
      pinnedDate: 1732520488006,
      segmentSortType: 'time',
      startTimeMs: 0,
      stopTimeMs: 10,
      groups: [
        {
          id: 'string1',
          type: 'head',
          picUri: 'string',
          clusterId: 'string',
          segments: [
            {
              id: 'string2',
              groupId: 'group1',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
            {
              id: 'string3',
              groupId: 'group1',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
          ],
        },
      ],
      segments: [
        {
          id: 'string2',
          groupId: 'group1',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
        {
          id: 'string3',
          groupId: 'group1',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
      ],
    },
    {
      id: 'string8',
      type: 'head',
      picUri: 'string',
      userLabel: undefined,
      pinnedDate: undefined,
      segmentSortType: 'time',
      startTimeMs: 0,
      stopTimeMs: 10,
      groups: [
        {
          id: 'string9',
          type: 'head',
          picUri: 'string',
          clusterId: 'string',
          segments: [
            {
              id: 'string10',
              groupId: 'group2',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
            {
              id: 'string6',
              groupId: 'group2',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
          ],
        },
      ],
      segments: [
        {
          id: 'string10',
          groupId: 'group2',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
        {
          id: 'string6',
          groupId: 'group2',
          type: 'head',
          startTimeMs: 0,
          stopTimeMs: 10,
          numAutoInterpolations: 1,
          isManualInterpolation: true,
          subsegmentIds: [],
          objectIds: [],
        },
      ],
    },
  ],
  changeClusterLabel: jest.fn(),
  changeClusterPinned: jest.fn(),
  changeSegmentSortType: jest.fn(),
  highlightedOverlay: undefined,
  selected: {},
  clusterMergeGroupIds: {},
  clusterMergeSegments: {},
  setSelectedGroups: jest.fn(),
  setClusterMergeGroupIds: jest.fn(),
  setClusterMergeSegments: jest.fn(),
  onHighlightPoly: jest.fn(),
  updateClusterAssignment: jest.fn(),
  children: [],
  virtuosoRef: { current: null },
  handleScrollToIndex: jest.fn(),
};

describe('React testing library test', () => {
  const useDispatchMock = jest.mocked(redux.useDispatch);
  const mockDispatchFn = jest.fn();
  useDispatchMock.mockReturnValue(mockDispatchFn);

  const initState = {
    [namespace]: {
      filterParameters: DEFAULT_FILTER_PARAMETERS,
      config: {
        featureFlags: {
          feather: true,
          detectNotepads: true,
          detectCards: true,
          analyzeInterpolation: true,
        },
      },
    },
  };
  const mockStore = configureStore();
  const store = mockStore(initState);

  beforeAll(() => {
    global.IntersectionObserver = class {
      readonly root: Element | null = null;
      readonly rootMargin: string = '';
      readonly thresholds: ReadonlyArray<number> = [];
      readonly takeRecords: () => IntersectionObserverEntry[] = jest.fn();
      unobserve() {}
      disconnect() {}
      _callback: IntersectionObserverCallback;

      constructor(callback: IntersectionObserverCallback) {
        this._callback = callback;
      }

      observe = (element: Element) => {
        const entry: IntersectionObserverEntry = {
          target: element,
          isIntersecting: true,
          intersectionRatio: 1,
          time: 0,
          boundingClientRect: {} as DOMRectReadOnly,
          intersectionRect: {} as DOMRectReadOnly,
          rootBounds: null,
        };
        this._callback([entry], this);
      };
    };
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly ClusterList', () => {
    const vars: ClusterListViewPropTypes = {
      clusterList: [
        {
          id: 'string',
          type: 'head',
          picUri: 'string',
          userLabel: 'string',
          pinnedDate: undefined,
          segmentSortType: 'time',
          startTimeMs: 0,
          stopTimeMs: 10,
          groups: [
            {
              id: 'string',
              type: 'head',
              picUri: 'string',
              clusterId: 'string',
              segments: [
                {
                  id: 'string',
                  groupId: 'string',
                  type: 'head',
                  startTimeMs: 0,
                  stopTimeMs: 10,
                  numAutoInterpolations: 1,
                  isManualInterpolation: true,
                  subsegmentIds: [],
                  objectIds: [],
                },
              ],
            },
          ],
          segments: [
            {
              id: 'string',
              groupId: 'string',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
          ],
        },
      ],
      changeClusterLabel: jest.fn(),
      changeClusterPinned: jest.fn(),
      changeSegmentSortType: jest.fn(),
      highlightedOverlay: undefined,
      selected: {},
      clusterMergeGroupIds: {},
      clusterMergeSegments: {},
      setSelectedGroups: jest.fn(),
      setClusterMergeGroupIds: jest.fn(),
      setClusterMergeSegments: jest.fn(),
      onHighlightPoly: jest.fn(),
      updateClusterAssignment: jest.fn(),
      children: [],
      virtuosoRef: { current: null },
      handleScrollToIndex: jest.fn(),
    };

    const { asFragment } = render(
      <redux.Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <ClusterList
            virtuosoRef={vars.virtuosoRef}
            clusterList={vars.clusterList}
            highlightedOverlay={vars.highlightedOverlay}
            selected={vars.selected}
            clusterMergeGroupIds={vars.clusterMergeGroupIds}
            clusterMergeSegments={vars.clusterMergeSegments}
            setSelectedGroups={vars.setSelectedGroups}
            setClusterMergeGroupIds={vars.setClusterMergeGroupIds}
            setClusterMergeSegments={vars.setClusterMergeSegments}
            onHighlightPoly={vars.onHighlightPoly}
            changeClusterLabel={vars.changeClusterLabel}
            changeClusterPinned={vars.changeClusterPinned}
            updateClusterAssignment={vars.updateClusterAssignment}
            changeSegmentSortType={vars.changeSegmentSortType}
            handleScrollToIndex={vars.handleScrollToIndex}
          >
            <ClusterListFilter
              filterParameters={filterProps.filterParameters as any}
              dataFetchedForDetectionType={
                filterProps.dataFetchedForDetectionType
              }
              settings={filterProps.globalSettings as any}
              clusterMergeGroupIds={vars.clusterMergeGroupIds}
              clusterMergeSegments={vars.clusterMergeSegments}
              updateClusterAssignment={vars.updateClusterAssignment}
              clearClusterMerge={jest.fn()}
              clusterList={vars.clusterList}
              handleScrollToIndex={vars.handleScrollToIndex}
            />
            <ClusterListSort
              clusterList={sortProps.clusterList as any}
              selected={sortProps.selected}
              setSelectedGroups={sortProps.setSelectedGroups}
              setSortBy={sortProps.setSortBy}
              sortBy={sortProps.sortBy as any}
              settings={filterProps.globalSettings as any}
            />
          </ClusterList>
        </I18nProvider>
      </redux.Provider>
    );
    expect(asFragment()).toMatchSnapshot();
    expect(
      screen.queryByTestId('face-item-expand-button')
    ).not.toBeInTheDocument();
    expect(screen.queryByTestId('expand-more')).not.toBeInTheDocument();
    expect(
      screen.getByTestId('cluster-list-filter-container')
    ).toBeInTheDocument();
    expect(
      screen.getByTestId('cluster-list-sort-container')
    ).toBeInTheDocument();
  });

  it('renders correctly ClusterList has IconButton', async () => {
    const vars: ClusterListViewPropTypes = {
      clusterList: [
        {
          id: 'string',
          type: 'head',
          picUri: 'string',
          userLabel: 'string',
          pinnedDate: undefined,
          segmentSortType: 'time',
          startTimeMs: 0,
          stopTimeMs: 10,
          groups: [
            {
              id: 'string',
              type: 'head',
              picUri: 'string',
              clusterId: 'string',
              segments: [
                {
                  id: 'string',
                  groupId: 'string',
                  type: 'head',
                  startTimeMs: 0,
                  stopTimeMs: 10,
                  numAutoInterpolations: 1,
                  isManualInterpolation: true,
                  subsegmentIds: [],
                  objectIds: [],
                },
                {
                  id: 'string',
                  groupId: 'string',
                  type: 'head',
                  startTimeMs: 0,
                  stopTimeMs: 10,
                  numAutoInterpolations: 1,
                  isManualInterpolation: true,
                  subsegmentIds: [],
                  objectIds: [],
                },
              ],
            },
          ],
          segments: [
            {
              id: 'string',
              groupId: 'string',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
            {
              id: 'string',
              groupId: 'string',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
          ],
        },
      ],
      changeClusterLabel: jest.fn(),
      changeClusterPinned: jest.fn(),
      changeSegmentSortType: jest.fn(),
      highlightedOverlay: undefined,
      selected: {},
      clusterMergeGroupIds: {},
      clusterMergeSegments: {},
      setSelectedGroups: jest.fn(),
      setClusterMergeGroupIds: jest.fn(),
      setClusterMergeSegments: jest.fn(),
      onHighlightPoly: jest.fn(),
      updateClusterAssignment: jest.fn(),
      children: [],
      virtuosoRef: { current: null },
      handleScrollToIndex: jest.fn(),
    };

    const { asFragment } = render(
      <redux.Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <ClusterList
            virtuosoRef={vars.virtuosoRef}
            clusterList={vars.clusterList}
            highlightedOverlay={vars.highlightedOverlay}
            selected={vars.selected}
            clusterMergeGroupIds={vars.clusterMergeGroupIds}
            clusterMergeSegments={vars.clusterMergeSegments}
            setSelectedGroups={vars.setSelectedGroups}
            setClusterMergeGroupIds={vars.setClusterMergeGroupIds}
            setClusterMergeSegments={vars.setClusterMergeSegments}
            onHighlightPoly={vars.onHighlightPoly}
            changeClusterLabel={vars.changeClusterLabel}
            changeClusterPinned={vars.changeClusterPinned}
            updateClusterAssignment={vars.updateClusterAssignment}
            changeSegmentSortType={vars.changeSegmentSortType}
            handleScrollToIndex={vars.handleScrollToIndex}
          >
            <ClusterListFilter
              filterParameters={filterProps.filterParameters as any}
              dataFetchedForDetectionType={
                filterProps.dataFetchedForDetectionType
              }
              settings={filterProps.globalSettings as any}
              clusterMergeGroupIds={vars.clusterMergeGroupIds}
              clusterMergeSegments={vars.clusterMergeSegments}
              updateClusterAssignment={vars.updateClusterAssignment}
              clearClusterMerge={jest.fn()}
              clusterList={vars.clusterList}
              handleScrollToIndex={vars.handleScrollToIndex}
            />
            <ClusterListSort
              clusterList={sortProps.clusterList as any}
              selected={sortProps.selected}
              setSelectedGroups={sortProps.setSelectedGroups}
              setSortBy={sortProps.setSortBy}
              sortBy={sortProps.sortBy as any}
              settings={filterProps.globalSettings as any}
            />
          </ClusterList>
        </I18nProvider>
      </redux.Provider>,
      {
        wrapper: ({ children }) => (
          <VirtuosoMockContext.Provider
            value={{ viewportHeight: 300, itemHeight: 100 }}
          >
            {children}
          </VirtuosoMockContext.Provider>
        ),
      }
    );

    expect(asFragment()).toMatchSnapshot();
    await waitFor(() => {
      expect(screen.getByTestId('clusterList-row')).toBeInTheDocument();
    });
    expect(screen.getByTestId('face-item-expand-button')).toBeInTheDocument();
    expect(screen.getByTestId('expand-more')).toBeInTheDocument();
    expect(
      screen.getByTestId('cluster-list-filter-container')
    ).toBeInTheDocument();
    expect(
      screen.getByTestId('cluster-list-sort-container')
    ).toBeInTheDocument();
  });

  it('renders correctly ClusterList has all selected IconButton', () => {
    const vars: ClusterListViewPropTypes = {
      clusterList: [
        {
          id: 'string1',
          type: 'head',
          picUri: 'string',
          userLabel: 'string',
          pinnedDate: undefined,
          segmentSortType: 'time',
          startTimeMs: 0,
          stopTimeMs: 10,
          groups: [
            {
              id: 'string1',
              type: 'head',
              picUri: 'string',
              clusterId: 'string',
              segments: [
                {
                  id: 'string2',
                  groupId: 'group1',
                  type: 'head',
                  startTimeMs: 0,
                  stopTimeMs: 10,
                  numAutoInterpolations: 1,
                  isManualInterpolation: true,
                  subsegmentIds: [],
                  objectIds: [],
                },
                {
                  id: 'string3',
                  groupId: 'group1',
                  type: 'head',
                  startTimeMs: 0,
                  stopTimeMs: 10,
                  numAutoInterpolations: 1,
                  isManualInterpolation: true,
                  subsegmentIds: [],
                  objectIds: [],
                },
              ],
            },
          ],
          segments: [
            {
              id: 'string2',
              groupId: 'group1',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
            {
              id: 'string3',
              groupId: 'group1',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
          ],
        },
        {
          id: 'string4',
          type: 'head',
          picUri: 'string',
          userLabel: 'string',
          pinnedDate: undefined,
          segmentSortType: 'time',
          startTimeMs: 0,
          stopTimeMs: 10,
          groups: [
            {
              id: 'string4',
              type: 'head',
              picUri: 'string',
              clusterId: 'string',
              segments: [
                {
                  id: 'string5',
                  groupId: 'group2',
                  type: 'head',
                  startTimeMs: 0,
                  stopTimeMs: 10,
                  numAutoInterpolations: 1,
                  isManualInterpolation: true,
                  subsegmentIds: [],
                  objectIds: [],
                },
                {
                  id: 'string6',
                  groupId: 'group2',
                  type: 'head',
                  startTimeMs: 0,
                  stopTimeMs: 10,
                  numAutoInterpolations: 1,
                  isManualInterpolation: true,
                  subsegmentIds: [],
                  objectIds: [],
                },
              ],
            },
          ],
          segments: [
            {
              id: 'string5',
              groupId: 'group2',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
            {
              id: 'string6',
              groupId: 'group2',
              type: 'head',
              startTimeMs: 0,
              stopTimeMs: 10,
              numAutoInterpolations: 1,
              isManualInterpolation: true,
              subsegmentIds: [],
              objectIds: [],
            },
          ],
        },
      ],
      changeClusterLabel: jest.fn(),
      changeClusterPinned: jest.fn(),
      changeSegmentSortType: jest.fn(),
      highlightedOverlay: undefined,
      selected: {
        string1: true,
        string2: true,
        string3: true,
        string4: true,
        string5: true,
        string6: true,
      },
      clusterMergeGroupIds: {},
      clusterMergeSegments: {},
      setSelectedGroups: jest.fn(),
      setClusterMergeGroupIds: jest.fn(),
      setClusterMergeSegments: jest.fn(),
      onHighlightPoly: jest.fn(),
      updateClusterAssignment: jest.fn(),
      children: [],
      virtuosoRef: { current: null },
      handleScrollToIndex: jest.fn(),
    };

    const { asFragment } = render(
      <redux.Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <ClusterList
            virtuosoRef={vars.virtuosoRef}
            clusterList={vars.clusterList}
            highlightedOverlay={vars.highlightedOverlay}
            selected={vars.selected}
            clusterMergeGroupIds={vars.clusterMergeGroupIds}
            clusterMergeSegments={vars.clusterMergeSegments}
            setSelectedGroups={vars.setSelectedGroups}
            setClusterMergeGroupIds={vars.setClusterMergeGroupIds}
            setClusterMergeSegments={vars.setClusterMergeSegments}
            onHighlightPoly={vars.onHighlightPoly}
            changeClusterLabel={vars.changeClusterLabel}
            changeClusterPinned={vars.changeClusterPinned}
            updateClusterAssignment={vars.updateClusterAssignment}
            changeSegmentSortType={vars.changeSegmentSortType}
            handleScrollToIndex={vars.handleScrollToIndex}
          >
            <ClusterListFilter
              filterParameters={filterProps.filterParameters as any}
              dataFetchedForDetectionType={
                filterProps.dataFetchedForDetectionType
              }
              settings={filterProps.globalSettings as any}
              clusterMergeGroupIds={vars.clusterMergeGroupIds}
              clusterMergeSegments={vars.clusterMergeSegments}
              updateClusterAssignment={vars.updateClusterAssignment}
              clearClusterMerge={jest.fn()}
              clusterList={vars.clusterList}
              handleScrollToIndex={vars.handleScrollToIndex}
            />
            <ClusterListSort
              clusterList={sortProps.clusterList as any}
              selected={sortProps.selected}
              setSelectedGroups={sortProps.setSelectedGroups}
              setSortBy={sortProps.setSortBy}
              sortBy={sortProps.sortBy as any}
              settings={filterProps.globalSettings as any}
            />
          </ClusterList>
        </I18nProvider>
      </redux.Provider>,
      {
        wrapper: ({ children }) => (
          <VirtuosoMockContext.Provider
            value={{ viewportHeight: 300, itemHeight: 100 }}
          >
            {children}
          </VirtuosoMockContext.Provider>
        ),
      }
    );
    expect(asFragment()).toMatchSnapshot();
    expect(screen.queryAllByTestId('cluster-group-container')).toHaveLength(2);
    expect(screen.queryAllByTestId('face-item-expand-button')).toHaveLength(2);
    expect(screen.queryAllByTestId('expand-more')).toHaveLength(2);
    expect(
      screen.getByTestId('cluster-list-filter-container')
    ).toBeInTheDocument();
    expect(
      screen.getByTestId('cluster-list-sort-container')
    ).toBeInTheDocument();
  });

  it("renders correctly cluster panel groups menu and disabled 'Merge with pinned group' option", () => {
    render(
      <redux.Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <ClusterList
            virtuosoRef={vars.virtuosoRef}
            clusterList={vars.clusterList}
            highlightedOverlay={vars.highlightedOverlay}
            selected={vars.selected}
            clusterMergeGroupIds={vars.clusterMergeGroupIds}
            clusterMergeSegments={vars.clusterMergeSegments}
            setSelectedGroups={vars.setSelectedGroups}
            setClusterMergeGroupIds={vars.setClusterMergeGroupIds}
            setClusterMergeSegments={vars.setClusterMergeSegments}
            onHighlightPoly={vars.onHighlightPoly}
            changeClusterLabel={vars.changeClusterLabel}
            changeClusterPinned={vars.changeClusterPinned}
            updateClusterAssignment={vars.updateClusterAssignment}
            changeSegmentSortType={vars.changeSegmentSortType}
            handleScrollToIndex={vars.handleScrollToIndex}
          >
            <ClusterListFilter
              filterParameters={filterProps.filterParameters as any}
              dataFetchedForDetectionType={
                filterProps.dataFetchedForDetectionType
              }
              settings={filterProps.globalSettings as any}
              clusterMergeGroupIds={vars.clusterMergeGroupIds}
              clusterMergeSegments={vars.clusterMergeSegments}
              updateClusterAssignment={vars.updateClusterAssignment}
              clearClusterMerge={jest.fn()}
              clusterList={vars.clusterList}
              handleScrollToIndex={vars.handleScrollToIndex}
            />
            <ClusterListSort
              clusterList={sortProps.clusterList as any}
              selected={sortProps.selected}
              setSelectedGroups={sortProps.setSelectedGroups}
              setSortBy={sortProps.setSortBy}
              sortBy={sortProps.sortBy as any}
              settings={filterProps.globalSettings as any}
            />
          </ClusterList>
        </I18nProvider>
      </redux.Provider>,
      {
        wrapper: ({ children }) => (
          <VirtuosoMockContext.Provider
            value={{ viewportHeight: 300, itemHeight: 100 }}
          >
            {children}
          </VirtuosoMockContext.Provider>
        ),
      }
    );
    const cardMenus = screen.getAllByTestId('cluster-card-menu');
    expect(cardMenus.length).toBe(2);
    if (cardMenus[0]) {
      fireEvent.click(cardMenus[0]);
      expect(screen.getByRole('menu')).toBeInTheDocument();
      expect(
        screen.getByRole('menuitem', {
          name: /pin to top/i,
        })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('menuitem', {
          name: /merge with pinned group/i,
        })
      ).toBeInTheDocument();
      const mergeWithPinnedGroup = screen.getByRole('menuitem', {
        name: /merge with pinned group/i,
      });
      expect(mergeWithPinnedGroup).toBeInTheDocument();
      // Show the disabled option if no other cluster panel groups are pinned
      expect(mergeWithPinnedGroup).toHaveAttribute('aria-disabled');
      expect(
        screen.getByRole('menuitem', {
          name: /delete group/i,
        })
      ).toBeInTheDocument();
    }
  });

  it("renders correctly 'Merge with pinned group' option", () => {
    render(
      <redux.Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <ClusterList
            virtuosoRef={vars.virtuosoRef}
            clusterList={pinnedClusterVars.clusterList}
            highlightedOverlay={pinnedClusterVars.highlightedOverlay}
            selected={pinnedClusterVars.selected}
            clusterMergeGroupIds={vars.clusterMergeGroupIds}
            clusterMergeSegments={vars.clusterMergeSegments}
            setSelectedGroups={vars.setSelectedGroups}
            setClusterMergeGroupIds={vars.setClusterMergeGroupIds}
            setClusterMergeSegments={vars.setClusterMergeSegments}
            onHighlightPoly={pinnedClusterVars.onHighlightPoly}
            changeClusterLabel={pinnedClusterVars.changeClusterLabel}
            changeClusterPinned={pinnedClusterVars.changeClusterPinned}
            updateClusterAssignment={pinnedClusterVars.updateClusterAssignment}
            changeSegmentSortType={vars.changeSegmentSortType}
            handleScrollToIndex={vars.handleScrollToIndex}
          >
            <ClusterListFilter
              filterParameters={filterProps.filterParameters as any}
              dataFetchedForDetectionType={
                filterProps.dataFetchedForDetectionType
              }
              settings={filterProps.globalSettings as any}
              clusterMergeGroupIds={vars.clusterMergeGroupIds}
              clusterMergeSegments={vars.clusterMergeSegments}
              updateClusterAssignment={vars.updateClusterAssignment}
              clearClusterMerge={jest.fn()}
              clusterList={vars.clusterList}
              handleScrollToIndex={vars.handleScrollToIndex}
            />
            <ClusterListSort
              clusterList={sortProps.clusterList as any}
              selected={sortProps.selected}
              setSelectedGroups={sortProps.setSelectedGroups}
              setSortBy={sortProps.setSortBy}
              sortBy={sortProps.sortBy as any}
              settings={filterProps.globalSettings as any}
            />
          </ClusterList>
        </I18nProvider>
      </redux.Provider>,
      {
        wrapper: ({ children }) => (
          <VirtuosoMockContext.Provider
            value={{ viewportHeight: 300, itemHeight: 100 }}
          >
            {children}
          </VirtuosoMockContext.Provider>
        ),
      }
    );
    const cardMenus = screen.getAllByTestId('cluster-card-menu');
    expect(cardMenus.length).toBe(3);
    if (cardMenus[0]) {
      fireEvent.click(cardMenus[0]);
      expect(screen.getByRole('menu')).toBeInTheDocument();
      const mergeWithPinnedGroup = screen.getByRole('menuitem', {
        name: /merge with pinned group/i,
      });
      expect(mergeWithPinnedGroup).toBeInTheDocument();
      // expect the option to be enabled
      expect(mergeWithPinnedGroup).not.toHaveAttribute('aria-disabled');
      // Do not show the expand icon if there is only one other cluster panel group pinned
      expect(
        screen.queryByTestId('merge-with-pinned-group-expand-icon')
      ).not.toBeVisible();
      fireEvent.click(mergeWithPinnedGroup);
      // dispatch action to merge with pinned group
      expect(pinnedClusterVars.updateClusterAssignment).toHaveBeenCalled();
    }

    if (cardMenus[2]) {
      fireEvent.click(cardMenus[2]);
      expect(screen.getByRole('menu')).toBeInTheDocument();
      const mergeWithPinnedGroup = screen.getByRole('menuitem', {
        name: /merge with pinned group/i,
      });
      expect(mergeWithPinnedGroup).toBeInTheDocument();
      // expect the option to be enabled
      expect(mergeWithPinnedGroup).not.toHaveAttribute('aria-disabled');
      // Show the expand icon only if there is more than one other cluster panel group pinned
      expect(
        screen.getByTestId('merge-with-pinned-group-expand-icon')
      ).toBeInTheDocument();
      // open the sub-menu
      fireEvent.click(mergeWithPinnedGroup);
      const cluster1Texts = screen.getAllByText('cluster1');
      expect(cluster1Texts.length).toBe(2); // 1 in the option and 1 in the cluster list
      if (cluster1Texts[1]) {
        fireEvent.click(cluster1Texts[1]);
        expect(pinnedClusterVars.updateClusterAssignment).toHaveBeenCalled();
      }
    }
  });

  it('Can open group menu by right click', () => {
    render(
      <redux.Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <ClusterList
            virtuosoRef={vars.virtuosoRef}
            clusterList={pinnedClusterVars.clusterList}
            highlightedOverlay={pinnedClusterVars.highlightedOverlay}
            selected={pinnedClusterVars.selected}
            clusterMergeGroupIds={vars.clusterMergeGroupIds}
            clusterMergeSegments={vars.clusterMergeSegments}
            setSelectedGroups={vars.setSelectedGroups}
            setClusterMergeGroupIds={vars.setClusterMergeGroupIds}
            setClusterMergeSegments={vars.setClusterMergeSegments}
            onHighlightPoly={pinnedClusterVars.onHighlightPoly}
            changeClusterLabel={pinnedClusterVars.changeClusterLabel}
            changeClusterPinned={pinnedClusterVars.changeClusterPinned}
            updateClusterAssignment={pinnedClusterVars.updateClusterAssignment}
            changeSegmentSortType={vars.changeSegmentSortType}
            handleScrollToIndex={vars.handleScrollToIndex}
          >
            <ClusterListFilter
              filterParameters={filterProps.filterParameters as any}
              dataFetchedForDetectionType={
                filterProps.dataFetchedForDetectionType
              }
              settings={filterProps.globalSettings as any}
              clusterMergeGroupIds={vars.clusterMergeGroupIds}
              clusterMergeSegments={vars.clusterMergeSegments}
              updateClusterAssignment={vars.updateClusterAssignment}
              clearClusterMerge={jest.fn()}
              clusterList={vars.clusterList}
              handleScrollToIndex={vars.handleScrollToIndex}
            />
            <ClusterListSort
              clusterList={sortProps.clusterList as any}
              selected={sortProps.selected}
              setSelectedGroups={sortProps.setSelectedGroups}
              setSortBy={sortProps.setSortBy}
              sortBy={sortProps.sortBy as any}
              settings={filterProps.globalSettings as any}
            />
          </ClusterList>
        </I18nProvider>
      </redux.Provider>,
      {
        wrapper: ({ children }) => (
          <VirtuosoMockContext.Provider
            value={{ viewportHeight: 300, itemHeight: 100 }}
          >
            {children}
          </VirtuosoMockContext.Provider>
        ),
      }
    );

    const clusters = screen.getAllByTestId('clusterList-row');
    expect(clusters.length).toBe(3);

    if (clusters[0]) {
      fireEvent.contextMenu(clusters[0]);
    }

    expect(screen.getByRole('menu')).toBeInTheDocument();
  });

  it('Bulk select clusters', () => {
    const clusterMergeGroupIds: { [key: string]: boolean } = {};

    const setClusterMergeGroupIds = jest.fn(
      (payload: SetMergeSelectClusterIdsRequest): any => {
        payload.clusterIds.forEach((id) => {
          clusterMergeGroupIds[id] = payload.selected;
        });
      }
    );

    render(
      <redux.Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <ClusterList
            virtuosoRef={vars.virtuosoRef}
            clusterList={pinnedClusterVars.clusterList}
            highlightedOverlay={pinnedClusterVars.highlightedOverlay}
            selected={pinnedClusterVars.selected}
            clusterMergeGroupIds={clusterMergeGroupIds}
            clusterMergeSegments={vars.clusterMergeSegments}
            setSelectedGroups={vars.setSelectedGroups}
            setClusterMergeGroupIds={setClusterMergeGroupIds}
            setClusterMergeSegments={vars.setClusterMergeSegments}
            onHighlightPoly={pinnedClusterVars.onHighlightPoly}
            changeClusterLabel={pinnedClusterVars.changeClusterLabel}
            changeClusterPinned={pinnedClusterVars.changeClusterPinned}
            updateClusterAssignment={pinnedClusterVars.updateClusterAssignment}
            changeSegmentSortType={vars.changeSegmentSortType}
            handleScrollToIndex={vars.handleScrollToIndex}
          >
            <ClusterListFilter
              filterParameters={filterProps.filterParameters as any}
              dataFetchedForDetectionType={
                filterProps.dataFetchedForDetectionType
              }
              settings={filterProps.globalSettings as any}
              clusterMergeGroupIds={vars.clusterMergeGroupIds}
              clusterMergeSegments={vars.clusterMergeSegments}
              updateClusterAssignment={vars.updateClusterAssignment}
              clearClusterMerge={jest.fn()}
              clusterList={vars.clusterList}
              handleScrollToIndex={vars.handleScrollToIndex}
            />
            <ClusterListSort
              clusterList={sortProps.clusterList as any}
              selected={sortProps.selected}
              setSelectedGroups={sortProps.setSelectedGroups}
              setSortBy={sortProps.setSortBy}
              sortBy={sortProps.sortBy as any}
              settings={filterProps.globalSettings as any}
            />
          </ClusterList>
        </I18nProvider>
      </redux.Provider>,
      {
        wrapper: ({ children }) => (
          <VirtuosoMockContext.Provider
            value={{ viewportHeight: 300, itemHeight: 100 }}
          >
            {children}
          </VirtuosoMockContext.Provider>
        ),
      }
    );

    const clusters = screen.getAllByTestId('clusterList-row');
    expect(clusters.length).toBe(3);

    // select first cluster by ctrl + click
    if (clusters[0]) {
      fireEvent.click(clusters[0], { ctrlKey: true });
    }
    expect(setClusterMergeGroupIds).toHaveBeenCalledWith({
      clusterIds: ['string4'],
      selected: true,
    });

    // Bulk select second and third cluster by shift + click
    if (clusters[2]) {
      fireEvent.click(clusters[2], { shiftKey: true });
    }
    expect(setClusterMergeGroupIds).toHaveBeenCalledWith({
      clusterIds: ['string4', 'string1', 'string8'],
      selected: true,
    });

    // Bulk unselect second and third cluster by shift + click
    if (clusters[1]) {
      fireEvent.click(clusters[1], { shiftKey: true });
    }
    expect(setClusterMergeGroupIds).toHaveBeenCalledWith({
      clusterIds: ['string1', 'string8'],
      selected: false,
    });
  });
});

import { createSelector } from 'reselect';

import {
  actionDeleteNotes,
  actionDeselectAll,
  actionDeselectWords,
  actionPauseVideo,
  actionRangeSelectWords,
  actionRedactWords,
  actionSeekPlayVideo,
  actionSelectWords,
  actionUnredactWords,
  actionViewNotes,
} from '@common-modules/mediaDetails';
import {
  selectCurrentPositionWord,
  selectTranscription,
  selectWordsRedactedMap,
  selectWordsSelectedMap,
} from '@common-modules/mediaDetails/selectors';

export const mapStateToProps = createSelector(
  selectTranscription,
  selectWordsSelectedMap,
  selectWordsRedactedMap,
  selectCurrentPositionWord,
  (transcription, selectedWords, redactedWords, currentWord) => ({
    transcription: transcription?.transcription || [],
    selectedWords,
    redactedWords,
    currentWord,
  })
);

export const mapDispatchToProps = {
  onSelectWords: actionSelectWords,
  onSelectRangeWords: actionRangeSelectWords,
  onDeselectWords: actionDeselectWords,
  onDeselectAll: actionDeselectAll,
  onRedactWords: actionRedactWords,
  onSeekPlayVideo: actionSeekPlayVideo,
  onPauseVideo: actionPauseVideo,
  onUnredactWords: actionUnredactWords,
  onViewNotes: actionViewNotes,
  onDeleteNotes: actionDeleteNotes,
};

export type TranscriptionPropTypes = ReturnType<typeof mapStateToProps> &
  typeof mapDispatchToProps;

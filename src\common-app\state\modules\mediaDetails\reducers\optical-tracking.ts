import { produce } from 'immer';
import { defaultState } from '../store';
import { GetActionCreatorPayloadT } from '@utils';
import { CaseReducer, createReducer } from '@reduxjs/toolkit';
import * as actions from '@common-modules/engines/optical-tracking/actions';

type Re<P = unknown> = CaseReducer<
  typeof defaultState,
  { payload: P; type: string }
>;

const onCreateEngineJobSuccess: Re<
  GetActionCreatorPayloadT<typeof actions.CREATE_ENGINE_JOB_SUCCESS>
> = (state, { payload }) =>
  produce(state, (draft) => {
    const { id, createJob, createJobPayload } = payload;
    const undoAction = draft.history.past.find(
      (history) =>
        history &&
        'action' in history &&
        'id' in history.action.payload &&
        id === history.action.payload.id
    );

    if (!undoAction) {
      draft.history.past.push({
        action: {
          creator: actions.PROCESS_ENGINE_REQUEST_OVERLAY,
          payload: createJobPayload,
        },
        inverseAction: {
          creator: actions.CANCEL_JOB,
          payload: {
            id,
            tdoId: createJob.targetId,
            jobId: createJob.id,
            isUndo: true,
          },
        },
      });
      draft.history.future = [];
    } else if (
      'inverseAction' in undoAction &&
      'jobId' in undoAction.inverseAction.payload
    ) {
      undoAction.inverseAction.payload.jobId = createJob.id;
    }
  });

export default createReducer(defaultState, (builder) => {
  builder.addCase(actions.CREATE_ENGINE_JOB_SUCCESS, onCreateEngineJobSuccess);
});

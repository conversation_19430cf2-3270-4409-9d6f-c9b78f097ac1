import { createSelector } from 'reselect';

import {
  selectDetectionCollections,
  selectUDRCollection,
  selectGlobalSettings,
  selectFrameRate,
  selectOverlayPreview,
  selectAudioRedactions,
  selectSelected,
  selectUDRBoundingPolyBeingUpdated,
  selectHighlightedOverlay,
  selectDeletedOverlaysCache,
  selectMediaDuration,
} from '@common-modules/mediaDetails';

import { getOverlayStyles } from '../getOverlayStyles';

export const componentSelectors = createSelector(
  selectDetectionCollections,
  selectUDRCollection,
  selectSelected,
  selectAudioRedactions,
  (state: { player: any }) => state.player,
  selectOverlayPreview,
  selectGlobalSettings,
  selectFrameRate,
  selectUDRBoundingPolyBeingUpdated,
  selectHighlightedOverlay,
  selectDeletedOverlaysCache,
  selectMediaDuration,
  (
    detectionCollections,
    udrCollection,
    selected,
    redactedWords,
    player,
    overlayPreview,
    globalSettings,
    fps,
    udrBeingUpdated,
    highlightedOverlay,
    deletedOverlaysCache,
    mediaDuration
  ) => ({
    detectionCollections,
    udrCollection,
    selected,
    redactedWords,
    player,
    overlayStyles: getOverlayStyles(overlayPreview),
    globalSettings,
    fps,
    udrBeingUpdated,
    highlightedOverlay,
    deletedOverlaysCache,
    mediaDuration,
  })
);

import { all, fork, takeLatest, put, select } from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';
const {
  user: { userIsAuthenticated, FETCH_USER_SUCCESS },
} = modules;

import {
  selectRouteType,
  selectCurrentRoutePayload,
  ROUTE_AUTH,
  ROUTE_HOME,
} from '@common-state/modules/routing';

export function* redirectAwayIfAlreadyAuthenticated() {
  if (yield* select(userIsAuthenticated)) {
    yield* put(ROUTE_HOME());
  }
}

function* redirectAwayAfterUserLogin() {
  yield* takeLatest(FETCH_USER_SUCCESS, redirectAwayAfterUserLoginHandle);
}

export function* redirectAwayAfterUserLoginHandle() {
  const routeType = yield* select(selectRouteType);
  const currentRoutePayload = yield* select(selectCurrentRoutePayload);
  const { nextType, nextPayload } = currentRoutePayload.query || {};

  if (routeType === ROUTE_AUTH.type) {
    // look for redirect information in the query string, and send the user
    // to their original destination if it exists.
    // TODO: types
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const redirectType = nextType || ROUTE_HOME.type;
    let parsedNextPayload;
    try {
      // TODO: type and validate
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      parsedNextPayload = JSON.parse(nextPayload);
    } catch (_e) {
      /* */
    }

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    yield* put({ type: redirectType, payload: parsedNextPayload });
  }
}

export function* loadAuthPage() {
  yield* all([
    fork(redirectAwayIfAlreadyAuthenticated),
    fork(redirectAwayAfterUserLogin),
  ]);
}

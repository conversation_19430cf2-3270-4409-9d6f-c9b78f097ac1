export const envData = {
  dev: {
    baseUrl: 'https://redact.dev.us-1.veritone.com',
    apiRoot: 'https://api.dev.us-1.veritone.com',
    glcApiRoot: 'https://glc-backend.dev.us-1.veritone.com',
  },
  stage: {
    baseUrl: 'https://redact.stage.veritone.com',
    apiRoot: 'https://api.stage.veritone.com',
    glcApiRoot: 'https://glc-backend.stage.veritone.com',
  },
  prod: {
    baseUrl: 'https://redact.veritone.com',
    apiRoot: 'https://api.veritone.com',
    glcApiRoot: 'https://glc-backend.veritone.com',
  },
  azure_prod: {
    baseUrl: 'https://edact.us-gov-2.veritone.com',
    apiRoot: 'https://api.us-gov-2.veritone.com',
    glcApiRoot: 'https://glc-backend.us-gov-2.veritone.com',
  },
  azure_stage: {
    baseUrl: 'https://redact.stage.us-gov-2.veritone.com',
    apiRoot: 'https://api.stage.us-gov-2.veritone.com',
    glcApiRoot: 'https://glc-backend.stage.us-gov-2.veritone.com',
    featureFlags: {
      tdoLock: true,
      feather: true,
      redactionCodes: true,
      detectCards: true,
      detectNotepads: true,
      analyzeInterpolation: true,
    },
  },
};

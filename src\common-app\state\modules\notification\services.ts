import { ThunkAction, UnknownAction } from '@reduxjs/toolkit';
import { modules } from '@veritone/glc-redux';

import callGraphQLApi from '@helpers/callGraphQLApi';

import * as Actions from './actions';
import { NotificationInterface, NotificationSDOId } from './models';
import { SCHEMA_ID_NOT_FOUND, NOOP_JOB_ACTION } from './actions';
import { get } from 'lodash';
import { AsyncOrSync } from 'ts-essentials';
export type Thunk<P, R = any> = (
  payload: P,
  ...args: any[]
) => ThunkAction<Promise<R | undefined>, any, unknown, UnknownAction>;

const {
  config: { getConfig },
} = modules;

export interface GetLatestSchemaIdResponse {
  dataRegistry: { publishedSchema: { id: string } };
}
export const getLatestSchemaId = async (
  dispatch: (a: UnknownAction) => AsyncOrSync<void>,
  getState: () => any,
  dataRegistryId?: string,
  registryName?: string,
  scope?: string
) => {
  if (!dataRegistryId) {
    dispatch(SCHEMA_ID_NOT_FOUND({ registryName }));
    return;
  }
  const sessionStorageKey = `${scope || ''}-${dataRegistryId}`;
  let schemaId = sessionStorage.getItem(sessionStorageKey) || undefined;

  if (!schemaId) {
    try {
      const resp = await callGraphQLApi<GetLatestSchemaIdResponse>({
        actionTypes: [NOOP_JOB_ACTION, NOOP_JOB_ACTION, NOOP_JOB_ACTION],
        query: `
          query fetchLatestSchema($dataRegistryId: ID!) {
            dataRegistry(id: $dataRegistryId) {
              id
              name
              publishedSchema {
                id
              }
            }
          }`,
        variables: { dataRegistryId },
        dispatch,
        getState,
      });

      schemaId = get(resp, 'dataRegistry.publishedSchema.id');
      if (!schemaId) {
        dispatch(SCHEMA_ID_NOT_FOUND({ registryName }));
      } else {
        sessionStorage.setItem(sessionStorageKey, schemaId);
      }
    } catch (err) {
      console.log(err);
      dispatch(SCHEMA_ID_NOT_FOUND({ registryName }));
    }
  }
  return schemaId;
};

/**
 * Upsert an SDO. Leave id undefined for insert.
 */
export interface UpsertJobSDOResponse {
  createStructuredData: {
    id: NotificationSDOId;
    data: NotificationInterface;
    createdDateTime: string;
    modifiedDateTime: string;
  };
}
export const upsertJobSDO: Thunk<{
  readonly id?: string;
  readonly data: NotificationInterface;
}> =
  ({ id, data }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const query = `
      mutation upsertSDOs($id: ID, $schemaId: ID!, $data: JSONData!) {
        createStructuredData(input: {
          id: $id,
          schemaId: $schemaId,
          data: $data
        }) {
          id
          data
          createdDateTime
          modifiedDateTime
        }
      }
    `;
    return await callGraphQLApi<UpsertJobSDOResponse>({
      actionTypes: [
        Actions.NOOP_JOB_ACTION,
        Actions.UPSERT_JOB_SDO_SUCCESS,
        Actions.UPSERT_JOB_SDO_FAILURE,
      ],
      query,
      variables: { schemaId, id, data },
      dispatch,
      getState,
    });
  };

export type UpdateMultyJobSDOResponse = Record<
  string,
  {
    id: string;
    data: any;
    createdDateTime: string;
    modifiedDateTime: string;
  }
>;
export const updateMultyJobSDO: Thunk<{
  readonly sdos: ReadonlyArray<{
    sdoId: NotificationSDOId;
    data: NotificationInterface;
  }>;
}> =
  ({ sdos }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const query = `
    mutation updateSDOs {
      ${sdos
        .map(
          (sdo, index) => `sdoId${index}: createStructuredData(input: {
            id: "${sdo.sdoId}",
            schemaId: "${schemaId}",
            data: {
              status: "${sdo.data.status}",
              jobId: "${sdo.data.jobId}",
              tdoId: "${sdo.data.tdoId}",
              userId: "${sdo.data.userId}",
              tdoName: "${sdo.data.tdoName}",
              engineName: "${sdo.data.engineName}",
              deleted: ${sdo.data.deleted},
            }
          }
          ) {
            id
            data
            createdDateTime
            modifiedDateTime
          }`
        )
        .join(',')}
    }
  `;
    return await callGraphQLApi<UpdateMultyJobSDOResponse>({
      actionTypes: [
        Actions.UPDATE_MULTY_JOB_SDO,
        Actions.UPDATE_MULTY_JOB_SDO_SUCCESS,
        Actions.UPDATE_MULTY_JOB_SDO_FAILURE,
      ],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

// /**
//  * Delete an SDO by ID.
//  * @param request - SDO Id and TDO Id required.
//  */
export interface DeleteJobSDOResponse {
  deleteStructuredData: {
    id: string;
  };
}
export const deleteJobSDO: Thunk<{
  readonly id: string;
}> =
  ({ id }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const query = `
      mutation deleteSDO($id: ID!, $schemaId: ID!) {
        deleteStructuredData(input: {
          id: $id,
          schemaId: $schemaId,
        }) {
          id
        }
      }
    `;

    return await callGraphQLApi<DeleteJobSDOResponse>({
      actionTypes: [
        Actions.NOOP_JOB_ACTION,
        Actions.DELETE_JOB_SDO_SUCCESS,
        Actions.DELETE_JOB_SDO_FAILURE,
      ],
      query,
      variables: { schemaId, id },
      dispatch,
      getState,
    });
  };

export type DeleteMultiJobSDOResponse = Record<
  string,
  {
    id: string;
  }
>;
export const deleteMultiJobSDO: Thunk<{
  readonly ids: ReadonlyArray<string>;
}> =
  ({ ids }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const query = `
      mutation deleteSDOs {
        ${ids
          .map(
            (
              id,
              index
            ) => `sdoId${index}: deleteStructuredData(input: {id: "${id}", schemaId: "${schemaId}"}) {
              id
            }`
          )
          .join(',')}
      }
    `;

    return await callGraphQLApi<DeleteMultiJobSDOResponse>({
      actionTypes: [
        Actions.DELETE_MULTY_JOB_SDO,
        Actions.DELETE_MULTY_JOB_SDO_SUCCESS,
        Actions.DELETE_MULTY_JOB_SDO_FAILURE,
      ],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

/**
 * Fetch a list of SDOs.
 */
export interface FetchJobSDOsResponse {
  structuredDataObjects: {
    records: {
      id: string;
      data: any;
      createdDateTime: string;
      modifiedDateTime: string;
    }[];
  };
}
export const fetchJobSDOs: Thunk<{
  readonly limit: number;
  readonly offset: number;
  readonly userId: string;
}> =
  ({ limit, offset, userId }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const query = `
      query listSDOs($schemaId: ID!, $limit: Int!, $offset: Int, $filter: JSONData) {
        structuredDataObjects(
          schemaId: $schemaId,
          limit: $limit,
          offset: $offset,
          orderBy: [{field: modifiedDateTime, direction: desc}],
          filter: $filter
        ) {
          records {
            id
            data
            createdDateTime
            modifiedDateTime
          }
        }
      }
    `;

    return await callGraphQLApi<FetchJobSDOsResponse>({
      actionTypes: [
        Actions.NOOP_JOB_ACTION,
        Actions.FETCH_JOB_SDOS_SUCCESS,
        Actions.FETCH_JOB_SDOS_FAILURE,
      ],
      query,
      variables: { schemaId, limit, offset, filter: { userId } },
      dispatch,
      getState,
    });
  };

// fetch status of jobs in sdo
export type FetchEachJobResponse = Record<
  string,
  {
    id: string;
    status: string;
  }
>;
export const fetchEachJob: Thunk<{
  sdos: ReadonlyArray<NotificationInterface>;
}> =
  ({ sdos }) =>
  async (dispatch, getState) => {
    const query = `
    query mapJobs {
      ${sdos
        .map(
          ({ jobId }) => `jobId${jobId}: job(id: "${jobId}") {
        id
        status
      }`
        )
        .join(',')}
    }
    `;

    return await callGraphQLApi<FetchEachJobResponse>({
      actionTypes: [
        Actions.NOOP_JOB_ACTION,
        Actions.FETCH_JOB_STATUS_SUCCESS,
        Actions.FETCH_JOB_STATUS_FAILURE,
      ],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

// /**
//  * Use DataRegistry to lookup the latest published SDO Schema ID.
//  * @returns {string} Schema Id
//  */
const lookupLatestSchemaId = (
  () => async (dispatch: (a: UnknownAction) => void, getState: () => any) => {
    const { redactNotificationSchemaId: dataRegistryId, apiRoot } =
      getConfig<Window['config']>(getState());

    return await getLatestSchemaId(
      dispatch,
      getState,
      dataRegistryId,
      'redactNotificationSchemaId',
      apiRoot
    );
  }
)();

export interface RetryJobResponse {
  retryJob: {
    id: string;
    status: string;
  };
}
export const retryJob: Thunk<{
  readonly id: string;
}> =
  ({ id }) =>
  async (dispatch, getState) => {
    const query = `
      mutation retryJob($id: ID!) {
        retryJob(id: $id) {
          id
          status
        }
      }
    `;

    return await callGraphQLApi<RetryJobResponse>({
      actionTypes: [
        Actions.NOOP_JOB_ACTION,
        Actions.RETRY_JOB_SUCCESS,
        Actions.RETRY_JOB_FAILED,
      ],
      query,
      variables: { id },
      dispatch,
      getState,
    });
  };

export interface GetMailboxesResponse {
  notificationMailboxes: {
    name: string;
    id: string;
    eventFilter: {
      eventNames: string[];
      eventType: string;
      applicationId: string;
    };
  }[];
}
export const getMailboxes: Thunk<void> = () => async (dispatch, getState) => {
  const query = `
    query notificationMailboxes {
      notificationMailboxes{
        name
        id
        eventFilter{
          eventNames
          eventType
          applicationId
        }
      }
    }
    `;

  return await callGraphQLApi<GetMailboxesResponse>({
    actionTypes: [
      Actions.FETCH_MAILBOX,
      Actions.FETCH_MAILBOX_SUCCESS,
      Actions.FETCH_MAILBOX_FAILURE,
    ],
    query,
    variables: {},
    dispatch,
    getState,
  });
};

export interface CreateMailboxAppResponse {
  notificationMailboxCreate: {
    id: string;
    name: string;
  };
}
export const createMailboxApp: Thunk<{ applicationId: string }> =
  ({ applicationId }) =>
  async (dispatch, getState) => {
    const query = `
      mutation createMailbox {
        notificationMailboxCreate(input:{
          name: "Mailbox Redact",
          eventFilter: {
            eventNames: ["redactNotificationEvent"],
            eventType: "redact",
            applicationId:"${applicationId}",
            delivery:{
              deliveryType:NotificationMailbox
            }
          },
          notificationTemplate:"{{message}}",
        }) {
          id
          name
        }
      }`;

    return await callGraphQLApi<CreateMailboxAppResponse>({
      actionTypes: [
        Actions.CREATE_MAILBOX_APP,
        Actions.CREATE_MAILBOX_APP_SUCCESS,
        Actions.CREATE_MAILBOX_APP_FAILURE,
      ],
      query,
      variables: {},
      dispatch,
      getState,
    });
  };

export interface CreateNotiAppResponse {
  notificationPost: {
    id: string;
  };
}
export const createNotiApp: Thunk<{
  message: string;
  title: string;
  mailboxId: string;
}> =
  ({ message, mailboxId, title }) =>
  async (dispatch, getState) => {
    const query = `
      mutation {
        notificationPost(
          input: {
            mailboxIds: ["${mailboxId}"],
            body: "${message}"
            contentType: "string"
            title: "${title}"
            flags: [unread, unseen],
          }
        ) {
          id
        }
      }`;

    return await callGraphQLApi<CreateNotiAppResponse>({
      actionTypes: [
        Actions.NOOP_JOB_ACTION,
        Actions.NOOP_JOB_ACTION,
        Actions.NOOP_JOB_ACTION,
      ],
      query,
      variables: {},
      getState,
      dispatch,
    });
  };

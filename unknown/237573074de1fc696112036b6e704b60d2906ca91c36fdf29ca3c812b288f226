import { memo } from 'react';
import { useIntl } from 'react-intl';
import Dialog from '@common-components/ConfirmDialog';
import * as styles from './index.scss';

const ViewImageDialog = ({ title, signedUri, isOpen, onClose }: Props) => {
  const intl = useIntl();
  return (
    <Dialog
      isOpen={isOpen}
      title={title}
      content={
        <img
          className={styles.image}
          src={signedUri}
          data-testid="view-image-dialog-img"
        />
      }
      cancelText={intl.formatMessage({ id: 'close' })}
      onCancel={onClose}
    />
  );
};

interface Props {
  readonly title: string;
  readonly signedUri: string;
  readonly isOpen: boolean;
  readonly onClose: () => void;
}

export default memo(ViewImageDialog);

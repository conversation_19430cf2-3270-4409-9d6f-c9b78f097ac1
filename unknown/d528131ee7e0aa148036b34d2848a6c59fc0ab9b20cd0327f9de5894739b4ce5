import { memo } from 'react';

import * as styles from './styles.scss';

const CheckBox = ({
  title,
  checked,
  indeterminate,
  onChange,
}: CheckboxPropTypes) => {
  const handleChange = () => {
    onChange?.(!checked);
  };

  return (
    <div data-veritone-component="check-box" data-testid="checkbox">
      <div
        data-testid="checkbox-content"
        onClick={handleChange}
        className={styles.checkBox}
      >
        <div className={styles.whiteBox} />
        {checked ? (
          <div className={styles.checkedBox}>
            {indeterminate ? (
              <svg
                data-test="svg"
                data-testid="svg-checked"
                aria-hidden="true"
                width="18"
                height="25"
              >
                <g transform="matrix(1,0,0,1,-3,-3)">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z" />
                </g>
              </svg>
            ) : (
              <svg
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="25"
                data-test="svg"
                data-testid="svg-unchecked"
              >
                <g transform="matrix(1,0,0,1,-3,-3)">
                  <path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                </g>
              </svg>
            )}
          </div>
        ) : null}
      </div>
      {title ? (
        <div data-testid="checkbox-title" className={styles.title}>
          {title}
        </div>
      ) : null}
    </div>
  );
};

export default memo(CheckBox);

export interface CheckboxPropTypes {
  readonly title?: string;
  readonly checked?: boolean;
  readonly indeterminate?: boolean;
  readonly onChange?: (checked: boolean) => void;
}

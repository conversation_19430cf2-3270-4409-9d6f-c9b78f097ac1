import { ChildFolderResponse, GetChildFoldersResponse } from "../model/responses";
import { getChildFoldersQuery } from "../api/queries";
import { RequestHeader, getChildFoldersRequest} from "../model/requests";
import { callGQL } from "../api/callGraphql";
import { Logger } from "../logger";
import { Messages } from "../errors/messages";

export const getChildFoldersAdapter = async (
    headers: RequestHeader,
    request: getChildFoldersRequest,
  ) => {   
    const response: {isFailed: boolean; childFolders: Array<ChildFolderResponse> } = {
      isFailed: false,
      childFolders: [],
    }
    
    try {
      const { folderId, limit, offset } = request;
      const res = await callGQL<GetChildFoldersResponse>(headers, getChildFoldersQuery,  { folderId, limit, offset });
      const newFolders = res.folder?.childFolders.records || [];
      response.childFolders = [...response.childFolders, ...newFolders ] ;
    } catch(err) {
      Logger.log(Messages.getChildFoldersFail + JSON.stringify(err)); 
      response.isFailed = true;
    }  
    
    return response;
}
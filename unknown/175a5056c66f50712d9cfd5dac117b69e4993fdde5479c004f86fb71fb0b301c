import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { mediaFileDetailsQuery } from '../../src/api/queries';
import { mediaFileDetails } from '../../src/controllers/mediaFileDetails';

describe('mediaFileDetails', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('retrieve all assets of ingested media w/ tdoId', async () => {
    const req = getMockReq({
      params: { tdoId: '123456789' },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.resolve({
      temporalDataObject: {
        thumbnailUrl: '',
        redactedMediaAssets: {},
        auditLogAssets: {},
        details:{}
      },
    }));

    await mediaFileDetails(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, mediaFileDetailsQuery(req.params.tdoId!));
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('fails to retrieve redacted media and audit log files w/o tdoId', async () => {
    const req = getMockReq({
      params: { tdoId: '' },
      headers: {
        authorization: 'Bearer token',
      },
    });

    await mediaFileDetails(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.caseIdRequired });
  });

  it('fails to retrieve redacted media and audit log files w/ error', async () => {
    const req = getMockReq({
      params: { tdoId: '123456789' },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.reject());

    await mediaFileDetails(req, res);
    expect(error).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
  });

  it('does not fail if there is no redacted transcript', async () => {
    const req = getMockReq({
      params: { tdoId: '123456789' },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.resolve({
      temporalDataObject: {
        thumbnailUrl: '',
        redactedMediaAssets: {},
        auditLogAssets: {},
        redactedTranscriptAssets: {
          records: []
        },
        details:{}
      },
    }));

    await mediaFileDetails(req, res);
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, mediaFileDetailsQuery(req.params.tdoId!));
    expect(error).not.toHaveBeenCalled();
  });
});

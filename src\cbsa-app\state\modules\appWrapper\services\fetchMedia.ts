import callGraph<PERSON><PERSON><PERSON> from '@helpers/callGraphQLApi';
import { NOOP } from '@cbsa-modules/universal/actions';
import { TreeObjectId, Thunk } from '@cbsa-modules/universal';
import { FETCH_MEDIA_SUCCESS, FETCH_MEDIA_FAILURE } from '../actions';
import { FETCH_MEDIA_QUERY, FETCH_MEDIA_RESPONSE } from './queries/fetchMedia';

export const fetchMedia: Thunk<
  {
    readonly offset: number;
    readonly limit: number;
    readonly caseId: TreeObjectId;
  },
  FETCH_MEDIA_RESPONSE
> =
  ({ offset, limit, caseId }) =>
  async (dispatch, getState) =>
    await call<PERSON>rap<PERSON><PERSON><PERSON><PERSON><FETCH_MEDIA_RESPONSE>({
      actionTypes: [NOOP, FETCH_MEDIA_SUCCESS, FETCH_MEDIA_FAILURE],
      query: FETCH_MEDIA_QUERY,
      variables: { offset, limit, caseId },
      dispatch,
      getState,
    });

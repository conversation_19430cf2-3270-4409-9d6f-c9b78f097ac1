import { Case, TreeObjectId } from '@cbsa-modules/universal';

export interface MainPageStore {
  casesSummary: CasesSummaryInfo | null;
  searchText: string;
  statusFilter: Case['status'] | 'all';
  showArchived: boolean;
  casesList: Case[];
  casesTotal: number;
  casesSortColumn: 'caseName' | 'createdDateTime' | 'modifiedDateTime';
  casesSortOrder: 'asc' | 'desc';
  paginationAmount: number;
  paginationStart: number;
}

export interface CaseRowPropTypes {
  caseInfo: Case;
  onCaseClick: (caseId: TreeObjectId) => void;
}

export interface CaseManagementProps {
  searchText: string;
  setSearchText: (value: string) => void;
  statusFilter: string;
  setStatusFilter: (value: unknown) => void;
  showArchived: boolean;
  setShowArchived: (value: unknown) => void;
}

export interface CasesSummaryInfo {
  casesCreated: number;
  casesApproved: number;
  casesReopened: number;
  casesReadyForExport: number;
}

export interface NewCaseDialogProps {
  showNewCaseDialog: boolean;
  setShowNewCaseDialog: (value: boolean) => void;
}

export interface FolderSummary {
  id: string;
  treeObjectId: TreeObjectId;
  contentTemplates: {
    id: string;
    sdo: {
      id: string;
      data: Case;
    };
  }[];
  childTDOs: {
    count: number;
  };
}

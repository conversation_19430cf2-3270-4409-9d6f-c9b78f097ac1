import moment from 'moment';
import 'moment/locale/fr';
import { useState } from 'react';
// import { I18nTranslate } from '@common/i18n';
import { useIntl } from 'react-intl';
import { startCase } from 'lodash';
import { onEnter } from '@helpers/keypressHelpers';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { AccessTime, PlaylistAddCheck } from '@mui/icons-material';
import { CircularProgress, InputAdornment, ThemeProvider } from '@mui/material';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
} from '@cbsa-components/AddMedia/Accordion';
import { Props, connector } from './props';
import { useStyles, useStatusStyles } from './styles';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const CaseDetailsContent = ({
  caseDetails,
  updateCaseName,
  loaders,
}: Props) => {
  const intl = useIntl();
  const { name, createdDateTime, status } = caseDetails || {};
  const { isUpdatingCaseName } = loaders;

  const [changedName, setChangedName] = useState(name);
  const [expanded, setExpanded] = useState(true);

  const classes = useStyles();
  const statusColors = useStatusStyles();
  const statusColorKey =
    caseDetails?.status?.toLowerCase() as keyof typeof statusColors;
  const statusColor = statusColors[statusColorKey];

  return (
    <Accordion
      expanded={expanded}
      onChange={() => setExpanded(!expanded)}
      classes={{ root: classes.caseDetails }}
    >
      <AccordionSummary aria-controls="case-details">
        <Typography>{intl.formatMessage({ id: 'caseDetails' })}</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <div className={'CaseName'}>
          {intl.formatMessage({ id: 'caseName' })}
        </div>
        <TextField
          variant="outlined"
          value={changedName}
          fullWidth
          onKeyDown={onEnter(() => {
            if (caseDetails && changedName) {
              updateCaseName({ caseDetails, name: changedName });
            }
          })}
          onChange={(e) => setChangedName(e.target.value)}
          error={name !== changedName}
          label={name !== changedName && 'Press Enter to Submit'}
          slotProps={{
            htmlInput: {
              style: { color: '#005C7E', fontSize: '14px', fontWeight: 'bold' },
            },
            input: {
              endAdornment: isUpdatingCaseName && (
                <InputAdornment position="start">
                  <CircularProgress
                    thickness={2}
                    size={20}
                    style={{
                      color: '#005C7E',
                    }}
                  />
                </InputAdornment>
              ),
            },
          }}
        />
        <div className={'Age'}>
          <div className={'Title'}>
            <AccessTime />
            {intl.formatMessage({ id: 'age' })}
          </div>
          <div className={'Value'}>
            {startCase(moment.utc(createdDateTime).fromNow(true))}
          </div>
        </div>
        <div className={'Status'}>
          <div className={'Title'}>
            <PlaylistAddCheck />
            {intl.formatMessage({ id: 'status' })}
          </div>
          <div className={'Value'}>
            <Typography
              classes={{ root: statusColor }}
              style={{ fontWeight: 'bold' }}
            >
              {intl.formatMessage({ id: status })}
            </Typography>
          </div>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

const CaseDetails = ({ caseDetails, updateCaseName, loaders }: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <CaseDetailsContent
      caseDetails={caseDetails}
      updateCaseName={updateCaseName}
      loaders={loaders}
    />
  </ThemeProvider>
);

export default connector(CaseDetails);

#!/usr/bin/env python3

# Purpose:
#   Test create and run jobs.
# Prerequisites:
#   1) Python3: brew install python
#   2) requests package: pip3 install requests
# Usage:
#   ./01-jobs.py

import datetime
import traceback
from test_suite import *
import threading
import concurrent.futures

speechmatics_task = {
    # Speechmatics English (Global) V2F
    "engineId": "54525249-da68-4dbf-b6fe-aea9a1aefd4d"
}
head_detection_task = {
    # Head Detection (CPU) V2F
    "engineId": "cc34d1cd-1369-4141-a60c-e51cda00d4ec"
}
object_tracker_task = {
    # Object Tracker V2F
    "engineId": "3f03e804-cab6-413f-805c-ec36b6e33f5b",
    "payload": {
        "backTrackFrameLimit": 0,
        "boundingPolys": [
            {
                "boundingPoly": [
                    {
                        "x": 0.5836922671968212,
                        "y": 0.6770646314528928
                    },
                    {
                        "x": 0.7803261869971688,
                        "y": 0.6770646314528928
                    },
                    {
                        "x": 0.7803261869971688,
                        "y": 0.9199247709957782
                    },
                    {
                        "x": 0.5836922671968212,
                        "y": 0.9199247709957782
                    }
                ],
                "timeMs": 0,
                "parentUdrId": "ee23-d2aa-8a0b"
            }
        ],
        "app": "redact"
    }
}
blur_task = {
    # Redact Blur V2F
    "engineId": "e924437d-e9c1-401c-bc3f-d0fccad945ff"
}
download_task = {
    # Redact Download V2F
    "engineId": "01bd9b24-7d09-4fb1-abd5-7db28c1a4d89"
}

def check_job_status(job_id, job_name, check_frequency, max_time):
    '''Periodically check job status until completed or timed out'''

    start_time = datetime.datetime.now()
    elapsed_time = datetime.timedelta(seconds=0)
    job_desc = f"(id:{job_id} name:{job_name})"

    while elapsed_time.total_seconds() <= max_time:
        status = ts.job_status(job_id)
        now = datetime.datetime.now()
        elapsed_time = now - start_time
        ts.print_msg(f"Job {job_desc} status: {status}")
        if status == "failed" or status == "complete":
            break
        time.sleep(check_frequency)

    if status != "complete":
        ts.print_msg(f"Job {job_desc} failed with status: {status}", True)
        exit(1)

    if elapsed_time.total_seconds() > max_time:
        ts.print_msg(f"Job {job_desc} took too long: {elapsed_time.seconds()} seconds. Limit is {max_time} seconds.", True)
        exit(1)

    ts.print_msg(f"Job {job_desc} completed successfully in {elapsed_time.total_seconds()} seconds.")

try:
    # Create a new test suite
    ts = TestSuite("01-jobs")
    ts.print_msg(f"GQL: {ts.gql_uri}")

    # Get a session token to use in rest of tests
    ts.get_token()
    ts.print_msg(f"Token: {ts.token}")

    # Upload test file
    put_url, get_url = ts.get_signed_writable_url(ts.test_file_name)
    #ts.print_msg(f"Put URL: {put_url}")
    #ts.print_msg(f"Get URL: {get_url}")
    upload_file(ts.test_file_uri, put_url)

    webstream_adapter_task = {
        # webstream adapter V2F
        "engineId": "9e611ad7-2d3b-48f6-a51b-0a1ba40feab4",
        "payload": {
            "url": get_url
        }
    }

    # Create a new empty TDO
    tdo_id = ts.create_tdo()
    ts.print_msg(f"TDO: {tdo_id}")

    # Run 4 jobs in parallel:
    #   1 job with 3 tasks (Head Detection + Transcription + Optical Tracking), and
    #   3 jobs each with a different single task
    jobs = []
    job_id = ts.create_job_v2f(tdo_id, [webstream_adapter_task, speechmatics_task, head_detection_task, object_tracker_task])
    ts.print_msg(f"Job with 3 tasks: {job_id}")
    jobs.append((job_id, "3 tasks"))

    job_id = ts.create_job_v2f(tdo_id, [webstream_adapter_task, speechmatics_task])
    ts.print_msg(f"Speechmatics job: {job_id}")
    jobs.append((job_id, "speechmatics"))

    job_id = ts.create_job_v2f(tdo_id, [webstream_adapter_task, head_detection_task])
    ts.print_msg(f"Head detection job: {job_id}")
    jobs.append((job_id, "head detection"))

    job_id = ts.create_job_v2f(tdo_id, [webstream_adapter_task, object_tracker_task])
    ts.print_msg(f"Object tracker job: {job_id}")
    jobs.append((job_id, "object tracker"))

    # Periodically query job statuses until jobs completed or timed out
    threads = []
    for job_id, job_name in jobs:
        threads.append(threading.Thread(target=check_job_status, args=(job_id, job_name, ts.job_status_check_interval_secs, ts.max_job_secs)))

    for thread in threads:
        thread.start()

    for thread in threads:
        thread.join(timeout=float(ts.max_job_secs))

    # Run Redact Blur job
#     job_id = ts.create_job_v2f(tdo_id, [webstream_adapter_task, blur_task])
#     ts.print_msg(f"Blur job: {job_id}")
#     check_job_status(job_id, "blur", ts.job_status_check_interval_secs, ts.max_job_secs)

    # Clean up
    deleted_id = ts.delete_tdo(tdo_id)
    ts.print_msg(f"Deleted TDO {deleted_id} successfully.")

    exit(0)

except Exception as e:
    # capture any unhandled exception in log files
    ts.print_msg(traceback.format_exc(), True)
    exit(1)

finally:
    ts.out_log.close()
    ts.err_log.close()

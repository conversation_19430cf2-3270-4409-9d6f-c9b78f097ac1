export const UPDATE_CASE_NAME_FOLDER_QUERY = `
  mutation updateCaseNameFolder($caseId: ID!, $name: String!) {
    updateFolder(input: {
      id: $caseId
      name: $name
     }) {
      name
    }
  }`;

export const UPDATE_CASE_NAME_SDO_QUERY = `
  mutation updateCaseNameSdo($sdoId: ID!, $schemaId: ID!, $data: JSONData) {
    createStructuredData(input: {
      id: $sdoId
      schemaId: $schemaId
      data: $data
     }) {
      id
      data
    }
  }`;
export interface UpdateCaseNameSDOQueryResponse {
  createStructuredData: {
    id: string;
    data: any;
  };
}

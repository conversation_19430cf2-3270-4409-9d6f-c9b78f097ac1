import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { ServiceMakePurchaseResponse } from '../services';
import { createAction } from '@reduxjs/toolkit';

export const MAKE_PURCHASE = createAction(
  'vtn-component-user-onboarding/MAKE_PURCHASE',
  (payload: MakePurchasePayload) => ({
    payload,
  })
);
export const MAKE_PURCHASE_SUCCESS =
  createGraphQLSuccessAction<ServiceMakePurchaseResponse>(
    'vtn-component-user-onboarding/MAKE_PURCHASE_SUCCESS'
  );
export const MAKE_PURCHASE_FAILURE = createGraphQLFailureAction(
  'vtn-component-user-onboarding/MAKE_PURCHASE_FAILURE'
);

export interface MakePurchasePayload {
  foo: boolean;
}

export const actionMakePurchase = (payload: MakePurchasePayload) =>
  MAKE_PURCHASE(payload);

export const OPEN_UPGRADE_ACCOUNT = createAction(
  'vtn-component-user-onboarding/OPEN_UPGRADE_ACCOUNT'
);
export const CLOSE_UPGRADE_ACCOUNT = createAction(
  'vtn-component-user-onboarding/CLOSE_UPGRADE_ACCOUNT'
);

export const actionOpenUpgradeAccount = () => OPEN_UPGRADE_ACCOUNT();

export const actionCloseUpgradeAccount = () => CLOSE_UPGRADE_ACCOUNT();

export const OPEN_FASTSPRING = 'vtn-component-user-onboarding/OPEN_FASTSPRING';

export const actionOpenFastSpring = () => ({
  type: OPEN_FASTSPRING,
});

export const SHOW_REQUEST_QUOTE_FORM = createAction(
  'vtn-component-user-onboarding/SHOW_REQUEST_QUOTE_FORM'
);
export const HIDE_REQUEST_QUOTE_FORM = createAction<void>(
  'vtn-component-user-onboarding/HIDE_REQUEST_QUOTE_FORM'
);

export const actionShowRequestQuoteForm = () => SHOW_REQUEST_QUOTE_FORM();

export const actionHideRequestQuoteForm = () => HIDE_REQUEST_QUOTE_FORM();

import moment from 'moment';
import callGraph<PERSON><PERSON><PERSON> from '@helpers/callGraph<PERSON>Api';
import { ARCHIVE_CASE_QUERY } from './queries/archiveCase';
import { Case, Thunk, lookupLatestCaseSchemaId } from '@cbsa-modules/universal';
import {
  ARCHIVE_CASE_FAILURE,
  ARCHIVE_CASE_SUCCESS,
} from '@cbsa-modules/addMedia';
import { NOOP } from '@cbsa-modules/universal/actions';

export const archiveCase: Thunk<{
  readonly caseDetails: Case;
  readonly archive: boolean;
}> =
  ({ caseDetails, archive }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    return await callGraph<PERSON>Api({
      actionTypes: [NOOP, ARCHIVE_CASE_SUCCESS, ARCHIVE_CASE_FAILURE],
      query: ARCHIVE_CASE_QUERY,
      variables: {
        id: caseDetails.id,
        schemaId,
        data: {
          caseName: caseDetails.name,
          status: caseDetails.status,
          archive: archive ? 'archived' : 'reopened',
          folderTreeObjectId: caseDetails.treeObjectId,
          createdDateTime: caseDetails.createdDateTime,
          modifiedDateTime: moment(new Date()).toISOString(),
          priority: 1,
          processingTime: 3600,
        },
      },
      dispatch,
      getState,
    });
  };

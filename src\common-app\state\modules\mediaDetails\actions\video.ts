import { createAction } from '@reduxjs/toolkit';
import { MediaPlayerTime } from '../models';
import { markWorkerAction } from '@utils';

export const SET_SELECTED_UDR_GROUP = createAction<{
  groupId: string | undefined;
}>('vtn-redact-state-mediaDetails-view/SET_SELECTED_UDR_GROUP');

export const setSelectedUdrGroup = (groupId: string | undefined) =>
  markWorkerAction(SET_SELECTED_UDR_GROUP({ groupId }));

export const SET_SELECTED_UDR_GROUP_ON_WEB_WORKER_ONLY = createAction<{
  groupId: string | undefined;
}>(
  'vtn-redact-state-mediaDetails-view/SET_SELECTED_UDR_GROUP_ON_WEB_WORKER_ONLY'
);

export const setSelectedUdrGroupOnWebWorkerOnly = (groupId?: string) =>
  markWorkerAction(SET_SELECTED_UDR_GROUP_ON_WEB_WORKER_ONLY({ groupId }));

export const SEEK_MEDIA = createAction<MediaPlayerTime>(
  'vtn-redact-state-mediaDetails-view/SEEK_VIDEO'
);
export const actionSeekVideo = (payload: MediaPlayerTime) =>
  SEEK_MEDIA(payload);

export const SEEK_PLAY_MEDIA = createAction<MediaPlayerTime>(
  'vtn-redact-state-mediaDetails-view/SEEK_PLAY_VIDEO'
);
export const actionSeekPlayVideo = (payload: MediaPlayerTime) =>
  SEEK_PLAY_MEDIA(payload);

export const PLAY_MEDIA = createAction(
  'vtn-redact-state-mediaDetails-view/PLAY_MEDIA'
);
export const actionPlayVideo = () => PLAY_MEDIA();

export const PLAY_MEDIA_BACKWARDS = createAction(
  'vtn-redact-state-mediaDetails-view/PLAY_MEDIA_BACKWARDS'
);
export const actionPlayVideoBackwards = () => PLAY_MEDIA_BACKWARDS();

export const TOGGLE_PLAY_PAUSE_MEDIA = createAction(
  'vtn-redact-state-mediaDetails-view/TOGGLE_PLAY_PAUSE_MEDIA'
);
export const actionTogglePlayPauseVideo = () => TOGGLE_PLAY_PAUSE_MEDIA();

export const PAUSE_MEDIA = createAction(
  'vtn-redact-state-mediaDetails-view/PAUSE_VIDEO'
);
export const actionPauseVideo = () => PAUSE_MEDIA();

export const PLAYBACK_RATE_MEDIA = createAction<PlaybackRatePayload>(
  'vtn-redact-state-mediaDetails-view/PLAYBACK_RATE_MEDIA'
);
export interface PlaybackRatePayload {
  readonly playbackRate: number;
}
export const actionPlaybackRateVideo = (payload: PlaybackRatePayload) =>
  PLAYBACK_RATE_MEDIA(payload);

export const JUMP_MEDIA = createAction<JumpMediaPayload>(
  'vtn-redact-state-mediaDetails-view/JUMP_MEDIA'
);
export interface JumpMediaPayload {
  readonly seconds?: number;
  readonly frames?: number;
}
export const actionJumpVideo = (payload: JumpMediaPayload) =>
  JUMP_MEDIA(payload);

export const SET_REGULAR_MODE_PLAYBACK_SPEED = createAction<number>(
  'vtn-redact-state-mediaDetails-view/SET_REGULAR_MODE_PLAYBACK_SPEED'
);
export const setRegularModePlaybackSpeedAction = (payload: number) =>
  SET_REGULAR_MODE_PLAYBACK_SPEED(payload);

export const PLAYER_SEEKED = 'video-react/SEEKED';
export const PLAYER_PAUSE = 'video-react/PAUSE';

export const SET_IS_PLAYER_SEEKED = createAction<boolean>(
  'vtn-redact-state-mediaDetails-view/SET_IS_PLAYER_SEEKED'
);

export const setIsPlayerSeeked = (payload: boolean) =>
  SET_IS_PLAYER_SEEKED(payload);

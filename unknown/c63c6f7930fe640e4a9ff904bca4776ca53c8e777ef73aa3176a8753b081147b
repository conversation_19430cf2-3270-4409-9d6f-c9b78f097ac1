import { createSelector } from 'reselect';

import {
  selectGlobalSettings,
  SET_GLOBAL_SETTINGS,
} from '@common-modules/mediaDetails';
import { GlobalSettings } from '@common/state/modules/mediaDetails/models';

export const componentSelectors = createSelector(
  selectGlobalSettings,
  (initialValues) => ({
    initialValues,
  })
);

export const sliderActions = {
  onSaveGlobalSetting: (payload: GlobalSettings) =>
    SET_GLOBAL_SETTINGS(payload),
};

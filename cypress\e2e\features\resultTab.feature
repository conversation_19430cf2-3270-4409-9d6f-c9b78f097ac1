Feature: Results tab

  Scenario: Prepare test data
    Given The user uploads file "upload_test.mp4" with transcription "on"

  Scenario Outline: Video tab, Filter by <type>
    Given each detection name should not be empty
    And I have unselected all filters
    Then no detection should be visible
    When I filter the video tab by "<type>"
    Then every result detection should contain "<type>"

    Examples:
      | type |
      | Head |
      # | Person | // To do: Flaky, Video generated NOT have person

  Sc<PERSON><PERSON>: Audio tab
    When I click the audio tab button
    Then I should see "online" in file details transcript

  @e2e
  Scenario: Delete multiple test files
    Given The user deletes files
      | upload_test.mp4 |

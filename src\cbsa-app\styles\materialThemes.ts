import {
  createTheme,
  Theme,
  Palette,
  PaletteOptions,
} from '@mui/material/styles';

export const defaultTheme = createTheme({
  palette: {
    primary: {
      main: '#005C7E',
      light: '#005C7E1A',
    },
    success: {
      main: '#219653',
      light: '#2196531A',
    },
    warning: {
      main: '#D57200',
    },
    error: {
      main: '#D5000E',
      light: '#D5000E1A',
    },
    info: {
      main: '#5D00D8',
    },
    background: {
      paper: '#FFFFFF',
      default: '#FBFBFB',
    },
    processed: {
      main: '#D57200',
    },
  } as PaletteOptions,
  typography: {
    fontSize: 12,
  },
});

export type ExtendedTheme = Theme & {
  palette: Palette & {
    processed: {
      main: string;
      light: string;
    };
  };
};

export const buttonTheme = createTheme({
  palette: {
    primary: {
      main: '#2196F3',
      light: '#1bc1f3',
      dark: '#1976d2',
      contrastText: '#fff',
    },
    secondary: {
      main: '#B0BEC5',
      light: '#1bc1f3',
      dark: '#1976d2',
      contrastText: '#CFD8DC',
    },
    text: {
      secondary: '#48555C',
    },
  },
});

export const tabsTheme = createTheme({
  palette: {
    primary: {
      main: '#F9A02C',
      light: '#f9d45f',
      dark: '#f99516',
      contrastText: '#fff',
    },
  },
});

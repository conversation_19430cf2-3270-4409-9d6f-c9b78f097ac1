// import Button from '@mui/material/Button';
import { memo, useCallback, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Box from '@mui/material/Box';

import { componentSelectors, componentActions } from './reduxHelpers';
import ClusterList from './ClusterList';
import ClusterListFilter from './ClusterListFilter';
import ClusterListSort from './ClusterListSort';
import ConfirmDialog from './ConfirmDialog';
import { SeekMediaTimePayload } from './PropTypes';
import { useFilter } from './useFilter';
import { useSort } from './useSort';
import {
  IChangeClusterLabelRequest,
  SetSelectedGroupsRequest,
  SetSortByRequest,
} from '@common/state/modules/mediaDetails';
// import * as styles from './styles.scss';
// import { I18nTranslate } from '@common/i18n';

const ClusterResultsTabView = () => {
  const {
    clusterList,
    filterParameters,
    sortBy,
    highlightedOverlay,
    selected,
    // detectionRunning,
    // settings,
    // buttonDetectFaceDisable,
    // dataFetchedForDetectionType,
  } = useSelector(componentSelectors);
  const dispatch = useDispatch();
  const [showModal, setShowModal] = useState(false);
  const filteredList = useFilter(clusterList, filterParameters);
  const sortedList = useSort(filteredList, sortBy);

  // TODO Need to keep button disabled if detection ran and
  // settings (faceDetectionThreshold, videoType) have not changed.
  // const disableDetectionButton = () =>
  //  Boolean((!!settings && detectionRunning.length) || buttonDetectFaceDisable);

  const handleCreateJob = useCallback(
    () => dispatch(componentActions.detectFaces()),
    [dispatch]
  );

  const onHighlightPoly = useCallback(
    (payload: SeekMediaTimePayload) => {
      dispatch(componentActions.seekMediaTime(payload));
      dispatch(
        componentActions.onFaceHighlight({
          ...payload,
          timeMs: payload.startTimeMs,
        })
      );
    },
    [dispatch]
  );

  const setSelectedGroups = useCallback(
    (input: SetSelectedGroupsRequest) =>
      dispatch(componentActions.setSelectedGroups(input)),
    [dispatch]
  );

  const changeClusterLabel = useCallback(
    (input: IChangeClusterLabelRequest) =>
      dispatch(componentActions.changeClusterLabel(input)),
    [dispatch]
  );

  const setSortBy = useCallback(
    (input: SetSortByRequest) => dispatch(componentActions.setSortBy(input)),
    [dispatch]
  );

  // const openModal = () => {
  //   setShowModal(true);
  // };

  const closeModal = useCallback(() => {
    setShowModal(false);
  }, [setShowModal]);

  const onClickContinue = useCallback(() => {
    setShowModal(false);
    handleCreateJob();
  }, [setShowModal, handleCreateJob]);

  return (
    <Box
      display="flex"
      flexDirection="column"
      style={{ width: '100%', height: '100%' }}
      data-testid="cluster-results-tab-view"
    >
      {/* <Box
        display="flex"
        flexDirection="row"
        className={styles.buttonDetectContainer}
      >
        <Button
          variant="outlined"
          size="small"
          color="secondary"
          onClick={openModal}
          disabled={disableDetectionButton()}
          data-veritone-element="faces-list-detect-faces-button"
        >
          {I18nTranslate.TranslateMessage('detectHead')}
        </Button>
      </Box> */}
      <ClusterList
        clusterList={sortedList}
        setSelectedGroups={setSelectedGroups}
        changeClusterLabel={changeClusterLabel}
        highlightedOverlay={highlightedOverlay}
        selected={selected}
        onHighlightPoly={onHighlightPoly}
      >
        <ClusterListFilter
          // dataFetchedForDetectionType={dataFetchedForDetectionType}
          filterParameters={filterParameters}
        />
        <ClusterListSort
          clusterList={filteredList}
          selected={selected}
          setSelectedGroups={setSelectedGroups}
          setSortBy={setSortBy}
          sortBy={sortBy}
        />
      </ClusterList>
      <ConfirmDialog
        isOpen={showModal}
        onClose={closeModal}
        onContinue={onClickContinue}
      />
    </Box>
  );
};

export default memo(ClusterResultsTabView);

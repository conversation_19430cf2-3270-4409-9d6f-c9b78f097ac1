import {
  memo,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { renderer } from '../../renderer';
import { mapRender } from './mapRender';
import { UserDefinedRegionsPropTypes } from './UserDefinedRegionsPropTypes';

const UserDefinedRegions = (props: UserDefinedRegionsPropTypes) => {
  const ref = useRef<HTMLDivElement>(null);
  const [afterFirstRender, setAfterFirstRender] = useState<boolean>(false);
  const { render, destroy } = useMemo(
    () => renderer(ref, mapRender),
    [afterFirstRender] // eslint-disable-line react-hooks/exhaustive-deps
  );

  useLayoutEffect(() => render(props));
  useEffect(() => {
    setAfterFirstRender(true);
    return () => destroy();
  }, [destroy]);

  return <div ref={ref} />;
};

export default memo(UserDefinedRegions);

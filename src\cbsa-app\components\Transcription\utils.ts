import { clamp, keyBy, sortBy } from 'lodash';

import {
  AudioRedactionSlice,
  TranscriptionViewableWords,
  TranscriptionViewState,
} from '@common-modules/mediaDetails/models';

export const searchV2 = (
  term: string,
  transcriptionLines: TranscriptionViewableWords[][],
  startsWithMatch = true
): {
  results: Record<string, TranscriptionViewableWords>[];
  wordIdToLineNumberMap: { [id: string]: number };
} => {
  const FREE_WORDS = ['.', '...'];
  const results: Record<string, TranscriptionViewableWords>[] = [];

  const wordIdToLineNumberMap: { [id: string]: number } = {};

  const terms = term
    .trim()
    .split(' ')
    .filter((t) => t !== '')
    .map((t) => t.toLowerCase());

  if (terms.length === 0) {
    return { results: [], wordIdToLineNumberMap };
  }

  for (let lineNum = 0; lineNum < transcriptionLines.length; lineNum++) {
    // ! justified by loop index
    const line = transcriptionLines[lineNum]!;
    for (let i = 0; i < line.length; i++) {
      const result: TranscriptionViewableWords[] = [];
      let addResult = true;
      for (let j = 0; j < terms.length; j++) {
        const word = line[i];
        const t = terms[j]!; // Justified by loop
        if (
          word?.words
            .split(/\s*(?:[ .,;])\s*/) // Support editing of transcripts in Illuminate
            .some((w) =>
              startsWithMatch
                ? w.toLowerCase().startsWith(t)
                : w.toLowerCase() === t
            )
        ) {
          result.push(word);
          wordIdToLineNumberMap[word.id] = lineNum;
          i++;
        } else if (j > 0 && word && FREE_WORDS.includes(word.words)) {
          result.push(word);
          wordIdToLineNumberMap[word.id] = lineNum;
          j--;
          i++;
        } else {
          addResult = false;
          break;
        }
      }
      if (addResult) {
        results.push(keyBy(result, 'id'));
      }
    }
  }

  return { results, wordIdToLineNumberMap };
};

export const search = (
  term: string,
  transcription: ReadonlyArray<TranscriptionViewableWords>,
  startsWithMatch = true
) => {
  const FREE_WORDS = ['.', '...'];
  const results: Record<string, TranscriptionViewableWords>[] = [];
  const terms = term
    .trim()
    .split(' ')
    .filter((t) => t !== '');
  if (terms.length === 0) {
    return [];
  }

  for (let i = 0; i < transcription.length; i++) {
    const result: TranscriptionViewableWords[] = [];
    let addResult = true;
    for (let j = 0; j < terms.length; j++) {
      const word = transcription[i];
      const t = terms[j]!.toLowerCase(); // Justified by loop
      if (
        word &&
        (startsWithMatch
          ? word.words.toLowerCase().startsWith(t)
          : word.words.toLowerCase() === t)
      ) {
        result.push(word);
        i++;
      } else if (j > 0 && word && FREE_WORDS.includes(word.words)) {
        result.push(word);
        j--;
        i++;
      } else {
        addResult = false;
        break;
      }
    }
    if (addResult) {
      results.push(keyBy(result, 'id'));
    }
  }

  return results as Readonly<typeof results>;
};

export const findContiguousWords = (
  transcription: ReadonlyArray<TranscriptionViewableWords>,
  word: TranscriptionViewableWords,
  pred: (word: TranscriptionViewableWords | undefined) => boolean
): ReadonlyArray<TranscriptionViewableWords> => {
  const oi = transcription.indexOf(word);
  const words: TranscriptionViewableWords[] = [word];
  if (oi !== -1) {
    let li = oi - 1;
    let ri = oi + 1;
    while (pred(transcription[li]) || pred(transcription[ri])) {
      if (pred(transcription[li])) {
        words.push(transcription[li]!);
        li--;
      } else {
        li = -1;
      }
      if (pred(transcription[ri])) {
        words.push(transcription[ri]!);
        ri++;
      } else {
        ri = transcription.length;
      }
    }
  }
  return sortBy(words, 'startTimeMs');
};

/**
 * Find if currentTime falls within redacted audio using binary search.
 */
export const isRedactedAudio = (
  redaction: TranscriptionViewState['redactions'],
  currentTime: number
) => {
  let firstIndex = 0;
  let lastIndex = redaction.length - 1;
  let middleIndex = clamp(
    Math.floor((lastIndex + firstIndex) / 2),
    0,
    redaction.length - 1
  );

  if (lastIndex < 0) {
    return false;
  }

  const compare = (item: AudioRedactionSlice, time: number) =>
    time >= item[0] && time <= item[1] ? 0 : time < item[0] ? -1 : 1;

  while (
    compare(redaction[middleIndex]!, currentTime) !== 0 &&
    firstIndex < lastIndex
  ) {
    if (compare(redaction[middleIndex]!, currentTime) < 0) {
      lastIndex = middleIndex - 1;
    } else if (compare(redaction[middleIndex]!, currentTime) > 0) {
      firstIndex = middleIndex + 1;
    }
    middleIndex = clamp(
      Math.floor((lastIndex + firstIndex) / 2),
      0,
      redaction.length - 1
    );
  }

  const AudioRedactionSlice = redaction[middleIndex];
  if (!AudioRedactionSlice) {
    return false;
  }

  return compare(AudioRedactionSlice, currentTime) === 0;
};

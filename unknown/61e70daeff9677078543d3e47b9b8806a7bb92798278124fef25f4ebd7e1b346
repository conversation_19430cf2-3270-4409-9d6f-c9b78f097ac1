import { Dialog, DialogTitle, IconButton, Typography } from '@mui/material';
import RemoveIcon from '@mui/icons-material/Close';

import ConfirmView from './ConfirmView';
import FormView from './FormView';
import * as styles from './styles.scss';

const InviteAppModalView = ({
  isFormOpen,
  isConfirmOpen,
  onSendInvite,
  onClose,
}: InviteAppModalViewPropTypes) => (
  <Dialog
    open
    onClose={(_evt, reason) => {
      if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
        onClose();
      }
    }}
    maxWidth="md"
    fullWidth
    aria-labelledby="form-dialog-title"
    data-veritone-component="invite-redaction-modal"
    className={styles.inviteModal}
    classes={{
      paper: styles.dialogPaper,
      root: styles.dialogRoot,
    }}
  >
    <DialogTitle>
      <Typography id="form-dialog-title" className={styles.dialogTitle}>
        Invite Others to Use Redact
      </Typography>
      <IconButton className={styles.closeIcon} onClick={onClose} size="large">
        <RemoveIcon color="secondary" fontSize="small" />
      </IconButton>
    </DialogTitle>
    {isFormOpen ? (
      <FormView onSendInvite={onSendInvite} onClose={onClose} />
    ) : null}
    {isConfirmOpen ? <ConfirmView onClose={onClose} /> : null}
  </Dialog>
);

export default InviteAppModalView;

export interface InviteAppModalViewPropTypes {
  readonly isFormOpen: boolean;
  readonly isConfirmOpen: boolean;
  readonly onSendInvite: (emails: ReadonlyArray<string>) => void;
  readonly onClose: () => void;
}

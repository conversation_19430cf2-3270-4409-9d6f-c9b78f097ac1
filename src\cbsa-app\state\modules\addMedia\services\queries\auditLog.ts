import { TDOId } from '@common/state/modules/universal/models/Brands';

export const GET_AUDIT_LOGS = `
  query($application: String, $terms: [JSONData!]) {
    auditEvents(application: $application, terms: $terms) {
      records {
        id
        payload
        application
      }
    }
  }`;
export interface GetAuditLogsResponse {
  auditEvents: {
    records: {
      id: string;
      payload: {
        timestamp?: string;
        userName?: string;
        action?: string;
        tdoId?: TDOId;
        md5sum?: string | null;
      };
      application: string;
    }[];
  };
}

import makeStyles from '@mui/styles/makeStyles';

const topOffset = '115px';

export const useStyles = makeStyles((theme) => ({
  topBar: {
    backgroundColor: '#e5eef2',
    boxShadow: '0 3px 8px #00000008',
    border: '1px solid #cfcfcf80',
    height: '60px',
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',

    '& .Back': {
      alignItems: 'center',
      display: 'flex',
      height: '100%',
      marginLeft: '30px',

      '& .Link': {
        display: 'flex',
        alignItems: 'center',

        '& .Text': {
          color: '#005c7e',
          fontSize: '20px',
          fontWeight: 'bold',
        },

        '&:hover': {
          textDecoration: 'none',
        },
      },
    },

    '& .MuiCircularProgress': {
      color: theme.palette.primary.main,
      position: 'absolute',
    },
  },

  container: {
    background: 'white',
    width: '100vw',
    maxWidth: '100vw',
    height: `calc(100vh - ${topOffset})`,
    maxHeight: `calc(100vh - ${topOffset})`,
    overflow: 'hidden',
  },

  tabContent: {
    overflow: 'hidden',
    height: '100%',
    maxHeight: '100%',
  },

  spinnerContainer: {
    position: 'absolute',
    top: '50%',
    height: '100px',
    marginTop: '-50px',
    left: '50%',
    width: '300px',
    marginLeft: '-150px',
    color: 'white',
    outline: 'none',
  },

  primaryColor: {
    color: theme.palette.primary.main,
  },

  saveButtonStyle: {
    marginRight: '12px',
    height: '37px',
    backgroundColor: `${theme.palette.primary.dark} !important`, // isSaveButtonHovered ? '#384852' : '#242E34',
    fontWeight: 500,
    position: 'relative',
    borderRadius: '2px',
    textTransform: 'uppercase',

    '&.enabled': {
      border: '1px solid #6C7C84',
      color: '#CFD8DC',
    },

    '&.disabled': {
      border: '1px solid rgb(91, 111, 126)',
      color: 'rgb(91, 111, 126)',
    },
  },

  redactButtonStyle: {
    height: '37px',
    position: 'relative',
    marginRight: 20,
    borderRadius: '2px',
    textTransform: 'uppercase',

    '&.enabled': {
      background: '#23557C',
      color: 'white',
    },

    '&.disabled': {
      color: '#5B6F7E',
    },

    '& .MuiCircularProgress-root': {
      position: 'absolute',
    },
  },
}));

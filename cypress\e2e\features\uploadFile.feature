Feature: Upload

  Background: Upload File

  @e2e @mdp @regression
  Scenario: User Upload without running any engines
    Given The user uploads file "upload_test.mp4" with transcription "off"
    Then The user selects and deletes the uploaded file "upload_test.mp4"

  @e2e @mdp @regression
  Scenario: User Upload with engines
    Given The user uploads file "upload_test.mp4" with transcription "head, person, transcription"
    Then The user selects and deletes the uploaded file "upload_test.mp4"

  @e2e @mdp @regression
  Scenario: Delete multiple test files
    Given The user deletes files
      | upload_test.mp4   |
      | lucy.mp4          |
      | fullObject.mp4    |
      | Changed File Name |

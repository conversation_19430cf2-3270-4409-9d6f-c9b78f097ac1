import Konva from 'konva';

import { HEIGHT_PX, OFFSET_LEFT_X } from './constants';
import { Render } from './interfaces';

import { formatTime, aToB } from './utils';

// Pixels to the left of playhead. Used for icons by timelines.

// use subdivisions corresponding to following interval sizes
// 5hr, 1hr, 30min, 15min, 5min, 1min, 30sec, 15sec, 5sec, 1sec, 0.5 sec, 0.1 sec, 0.05sec,  0.01sec
const MAX_INTERVAL_DURATION = 3600 * 5;
const INTERVAL_SUBDIVISIONS = [5, 2, 2, 3, 5, 2, 2, 3, 5, 2, 5, 2, 5];

/* The following values refer to an offset from the top of the canvas to begin
drawing the tick marks (in a downward direction), thus smaller values
result in taller ticks. */
const TICK_SIZE_CROP = [0, 6, 10, 14];

const MIN_PIXELS_BETWEEN_TICKS = 12;
const MIN_PIXELS_BETWEEN_LABELS = 60;

const drawTicksRecursive = (
  layer: Konva.Layer,
  konvaStageWidth: number,
  startMs: number,
  x0: number,
  baseIntervalIndex: number,
  depth: number,
  pixelsBetweenIntervals: number,
  pixelsToMs: (pixels: number) => number
): undefined => {
  // check if reached maximum tick or subdivision depth
  if (
    baseIntervalIndex + depth >= INTERVAL_SUBDIVISIONS.length ||
    depth >= TICK_SIZE_CROP.length
  ) {
    return;
  }

  // for initial interval only draw first tick
  const numTicks = depth
    ? INTERVAL_SUBDIVISIONS[baseIntervalIndex + depth - 1]! // Safe due to previous length check
    : 1;

  const pixelsBetweenTickmarks = pixelsBetweenIntervals / numTicks;

  // return if under min tick spacing
  if (pixelsBetweenTickmarks < MIN_PIXELS_BETWEEN_TICKS) {
    return;
  }

  for (let k = 0; k < numTicks; k++) {
    const x = x0 + k * pixelsBetweenTickmarks;

    // past right edge
    if (x > konvaStageWidth) {
      return;
    }

    // for sub-intervals don't draw first tick (already has been drawn)
    if (k > 0 || depth === 0) {
      const crop = TICK_SIZE_CROP[depth]!; // Safe due to previous length check
      // check whether to draw tick
      if (x >= OFFSET_LEFT_X) {
        const tick = new Konva.Rect({
          x,
          y: 20 + crop,
          width: crop ? 1 : 2,
          height: 26 - crop - 6,
          fill: '#999',
        });
        layer.add(tick);

        // check whether label
        if (pixelsBetweenTickmarks > MIN_PIXELS_BETWEEN_LABELS) {
          const label = new Konva.Text({
            x: x - 29,
            y: 2,
            width: 60,
            align: 'center',
            text: formatTime(pixelsToMs(x - OFFSET_LEFT_X), 2, true),
            fontSize: depth ? 11 : 12,
            fontFamily: `Roboto, sans-serif`,
            fill: '#ccc',
          });
          layer.add(label);
        }
      }
    }

    // create sub-intervals
    drawTicksRecursive(
      layer,
      konvaStageWidth,
      startMs,
      x,
      baseIntervalIndex,
      depth + 1,
      pixelsBetweenTickmarks,
      pixelsToMs
    );
  }
};

export const ticksRenderer = (
  stage: Konva.Stage,
  _minWindowMs: number,
  _maxWindowMs: number,
  layer: Konva.Layer
): Render => {
  const container = stage.container();
  const ticksTrack = new Konva.Rect({
    x: 0,
    y: 0,
    fill: '#ffffff',
  });

  return (startMs, stopMs) => {
    const { clientWidth: konvaStageWidth } = container;
    const windowDuration = (stopMs - startMs) / 1000;

    // determine starting interval set size and pixels
    let baseIntervalIndex = 0;
    let baseIntervalDuration = MAX_INTERVAL_DURATION;
    let pixelsBetweenIntervals =
      (konvaStageWidth - OFFSET_LEFT_X) /
      (windowDuration / baseIntervalDuration);
    for (
      ;
      baseIntervalIndex < INTERVAL_SUBDIVISIONS.length;
      baseIntervalIndex++
    ) {
      // break if at least 3 full intervals / major tickmarks
      if (3 * baseIntervalDuration < windowDuration) {
        break;
      }
      const divs = INTERVAL_SUBDIVISIONS[baseIntervalIndex]!; // Safe due to for loop condition
      // break if pixelsBetweenIntervals will drop below MIN_PIXELS_BETWEEN_LABELS
      if (pixelsBetweenIntervals / divs < MIN_PIXELS_BETWEEN_LABELS) {
        break;
      }
      // reduce base interval size
      baseIntervalDuration /= divs;
      pixelsBetweenIntervals /= divs;
    }

    // use negative xOffset to ensure first interval sub-intervals will display
    let xOffset = 0;
    if (startMs > 0) {
      // find next interval index
      const startingInterval = startMs / 1000 / baseIntervalDuration;
      xOffset =
        (Math.floor(startingInterval) - startingInterval) *
        pixelsBetweenIntervals;
    }

    layer.destroyChildren();
    ticksTrack.setSize({ width: konvaStageWidth - xOffset, height: HEIGHT_PX });
    layer.add(ticksTrack);

    const pixelsToMs = aToB(OFFSET_LEFT_X, konvaStageWidth, startMs, stopMs);

    for (
      let i = 0;
      xOffset + i * pixelsBetweenIntervals <= konvaStageWidth;
      i++
    ) {
      const x = i * pixelsBetweenIntervals + OFFSET_LEFT_X + xOffset;
      drawTicksRecursive(
        layer,
        konvaStageWidth,
        startMs,
        x,
        baseIntervalIndex,
        0,
        pixelsBetweenIntervals,
        pixelsToMs
      );
    }
    return layer;
  };
};

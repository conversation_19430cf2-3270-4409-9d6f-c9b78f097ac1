import makeStyles from '@mui/styles/makeStyles';

const useStyles = makeStyles((theme) => ({
  'audit-event-dialog-content': {
    // --primary-color: #005c7e;
    // --event-background-color: #f2f7f8;

    width: '450px',
    fontWeight: 200,
  },

  heading: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  headingDownload: {
    display: 'inline-flex',
    backgroundColor: theme.palette.primary.main,
    textTransform: 'uppercase',
    color: 'white',
    width: '150px',
    height: '35px',
    borderRadius: '4px',

    '& svg': {
      margin: '0 5px 3px 0',
    },
  },

  auditEventContainer: {},

  headingResults: {},

  auditEventItem: {
    backgroundColor: theme.palette.primary.light,
    marginTop: '30px',
    padding: '15px',
  },

  auditEventTitle: {
    color: theme.palette.primary.main,
    textTransform: 'uppercase',
    fontWeight: 800,
  },
  auditEventContent: {
    marginTop: '3px',
    marginBottom: '20px',

    '&:last-child': {
      marginBottom: 0,
    },
  },

  dialogPaper: {
    maxHeight: 'calc(100% - 115px)',
  },
}));

export default useStyles;

import { styled } from '@mui/material/styles';
import { <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';

const style = {
  background: '#9E0000',
  boxShadow: '0px 3px 6px #0000004D',
  borderRadius: '3px',
  color: 'white',
  fontSize: '11px',
  width: '160px',
  height: '30px',
  '&:hover': {
    background: '#750000',
  },
};

const DeleteButton = ({ children, onClick }: Props) => {
  const CustomButton = styled(MuiButton)(style);
  return <CustomButton onClick={onClick}>{children}</CustomButton>;
};

interface Props {
  children: string;
  onClick: () => void;
}

export default DeleteButton;

### Steps to manually update Redact Editor role permission

------

1.  Get the current permission value of Redact Editor role from DB.
    ```
    Example: Current Redact Editor permission value in DB is "{-67100672,63,0,1275068416}"
    ```

2.  Using [aiware-core/lib/functional-permissions-lib](https://github.com/veritone/aiware-core/tree/master/lib/functional-permissions-lib) utility to unmask the value.
    ```
    Example:
    functional-permissions-lib ((HEAD detached at 4ea955feb)) $ ./map2id {-67100672,63,0,1275068416}
    13,26,27,28,29,30,31,32,33,34,35,36,37,122,123,126
    ```

3.   Add/Update changed to unmasked permission list.
     ```
     Example: 
     Updated permisssion list:  13,14,15,16,17,26,27,28,29,30,31,32,33,34,35,36,37,122,123,126
     ```

4.   Using [aiware-core/lib/functional-permissions-lib](https://github.com/veritone/aiware-core/tree/master/lib/functional-permissions-lib) utility to mask the updated permission list.
    ```
    Example: 
    functional-permissions-lib ((HEAD detached at 4ea955feb)) $ ./id2map 13,14,15,16,17,26,27,28,29,30,31,32,33,34,35,36,37,122,123,126
    -66854912,63,0,1275068416    
    ```

5.  Update Database with updated masked value ("{-66854912,63,0,1275068416}") for role_id = "d0136963-c244-452d-9539-d27447a60f47"
    ```
    Example: Query
    INSERT INTO public.role (
        role_id, role_name, role_description, app_name, permissions, organization_id, is_private, is_app_event_role, application_id)
        VALUES (
                'd0136963-c244-452d-9539-d27447a60f47',
                'Redact Editor',
                'Access to all features for this application',
                'redaction_2_0',
                '{-66854912,63,0,1275068416}',
                NULL,
                false,
                false,
                '766e9916-9536-47e9-8dcb-dc225654bab3'
                )
    ON CONFLICT (role_id) DO UPDATE
    SET permissions='{-66854912,63,0,1275068416}';
    ```

6.  Once changes are confirmed after local testing using stage env, create a PR to update DB scripts in aiware-core repo.
    ```
    Example PR: https://github.com/veritone/aiware-core/pull/1204
    ```









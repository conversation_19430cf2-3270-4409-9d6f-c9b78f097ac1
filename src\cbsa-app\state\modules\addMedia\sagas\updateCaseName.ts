import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import * as Services from '../services';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { put, takeEvery } from 'typed-redux-saga/macro';

export function* updateCaseName() {
  yield* takeEvery(Actions.UPDATE_CASE_NAME, function* ({ payload }) {
    const { caseDetails, name } = payload;

    yield* put(Services.updateCaseName({ caseDetails, name }));
  });
}

export function* updateCaseNameSuccess() {
  yield* takeEvery(Actions.UPDATE_CASE_NAME_SUCCESS, function* ({ payload }) {
    const {
      createStructuredData: {
        data: { caseName },
      },
    } = payload;

    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({ id: 'caseNameChange' }, { caseName }),
        variant: 'success',
      })
    );
  });
}

export function* updateCaseNameFailure() {
  yield* takeEvery(Actions.UPDATE_CASE_NAME_FAILURE, function* ({ payload }) {
    const { error } = payload;

    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({ id: 'caseNameChangeFail' }, { error }),
        variant: 'error',
      })
    );
  });
}

import { useMemo, useState } from 'react';
import * as React from 'react';
import { useSelector } from 'react-redux';

import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';

import { useStyles } from './styles';
import { componentSelectors } from './reduxHelpers';

import autoDetectHeadScaling from '@resources/images/auto-detect-head-scaling.svg';

import autoDetectHeadScalingHighlight from '@resources/images/auto-detect-head-scaling-highlight.svg';

import AutoScalingSlider from './AutoScalingSlider';
import { FormProvider, useForm } from 'react-hook-form';

const AutoScaling = ({ showIcon }: { showIcon: boolean }) => {
  const { initialValues } = useSelector(componentSelectors);
  const classes = useStyles();
  const [anchorEl, setAnchorEl] = useState<(EventTarget & Element) | null>(
    null
  );
  const [focus, setFocus] = useState(false);
  const formMethods = useForm({
    values: initialValues,
  });

  const handlePopper = useMemo(
    () => (event: React.MouseEvent) => {
      const { currentTarget } = event;
      setAnchorEl(anchorEl ? null : currentTarget);
      setFocus(!anchorEl);
    },
    [anchorEl]
  );

  const handleFocus = useMemo(
    () => (focus: boolean) => {
      if (!anchorEl) {
        setFocus(focus);
      }
    },
    [anchorEl]
  );

  return (
    <div
      className={classes.icon}
      onMouseEnter={() => {
        handleFocus(true);
      }}
      onMouseLeave={() => {
        handleFocus(false);
      }}
    >
      {showIcon && (
        <>
          <IconButton onClick={handlePopper} size="large">
            <img
              src={
                focus ? autoDetectHeadScalingHighlight : autoDetectHeadScaling
              }
              width={16}
              height={16}
            />
          </IconButton>
          <Popper
            id={'timeline-auto-scaling-btn'}
            open={Boolean(anchorEl)}
            anchorEl={anchorEl}
            placement="top"
            disablePortal
          >
            <Paper className={classes.container}>
              <form>
                <FormProvider {...formMethods}>
                  <form style={{ position: 'relative', top: -30 }}>
                    <AutoScalingSlider
                      controlName="head"
                      initValue={initialValues.objectTypeEffects.head.scaling}
                    />
                  </form>
                </FormProvider>
              </form>
            </Paper>
          </Popper>
        </>
      )}
    </div>
  );
};

export default AutoScaling;

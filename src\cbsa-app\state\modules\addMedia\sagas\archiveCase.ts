import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import * as Services from '../services';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { put, takeEvery, takeLatest } from 'typed-redux-saga/macro';

export function* archiveCase() {
  yield* takeLatest(Actions.ARCHIVE_CASE, function* ({ payload }) {
    const { caseDetails, archive } = payload;
    yield* put(Services.archiveCase({ caseDetails, archive }));
  });
}

export function* archiveCaseSuccess() {
  yield* takeEvery(Actions.ARCHIVE_CASE_SUCCESS, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({ id: 'caseStatusChanged' }),
        variant: 'success',
      })
    );
  });
}

export function* archiveCaseFailure() {
  yield* takeEvery(Actions.ARCHIVE_CASE_FAILURE, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({ id: 'caseStatusNotChanged' }),
        variant: 'error',
      })
    );
  });
}

import { createSelector } from 'reselect';
import {
  addFile,
  selectCases,
  selectMedia,
  selectLoaders,
  fetchCases,
  fetchMedia,
} from '@cbsa-modules/appWrapper';
import { TreeObjectId } from '@cbsa-modules/universal';
import { selectCurrentRoutePayload } from '@common-modules/routing';
import { connect, ConnectedProps } from 'react-redux';

const mapStateToProps = createSelector(
  selectCases,
  selectMedia,
  selectLoaders,
  selectCurrentRoutePayload,
  (cases, media, loaders, payload) => ({
    cases,
    media,
    loaders,
    caseId: payload?.case_id ?? '',
  })
);

const mapDispatchToProps = {
  fetchCases: ({
    query,
    offset,
    limit,
  }: {
    query: string;
    offset: number;
    limit: number;
  }) => fetchCases({ query, offset, limit }),
  fetchMedia: ({
    caseId,
    offset,
    limit,
  }: {
    caseId: TreeObjectId;
    offset: number;
    limit: number;
  }) => fetchMedia({ caseId, offset, limit }),
  uploadMedia: ({ file, caseId }: { file: File; caseId: TreeObjectId }) => {
    if (caseId) {
      addFile({ file, caseId });
    }
  },
};

export const connector = connect(mapStateToProps, mapDispatchToProps);
export type Props = ConnectedProps<typeof connector> & {
  readonly isOpen: boolean;
};

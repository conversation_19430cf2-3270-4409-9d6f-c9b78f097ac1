import { connect } from 'react-redux';
import { createSelector } from 'reselect';

import {
  actionCloseUpgradeAccount,
  actionHideRequestQuoteForm,
} from '../../actions';
import {
  selectShowRequestQuoteForm,
  selectUpgradeAccountIsOpen,
} from '../../selectors';
import UpgradeAccountModalView from './UpgradeAccountModalView';

export default connect(
  createSelector(
    selectUpgradeAccountIsOpen,
    selectShowRequestQuoteForm,
    (isOpen, showRequestQuoteForm) => ({ isOpen, showRequestQuoteForm })
  ),
  {
    onClose: actionCloseUpgradeAccount,
    onExited: actionHideRequestQuoteForm,
  }
)(UpgradeAccountModalView);

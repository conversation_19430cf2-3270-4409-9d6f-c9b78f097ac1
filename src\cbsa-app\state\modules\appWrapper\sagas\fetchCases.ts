import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import * as Services from '../services';
import { put, takeLatest } from 'typed-redux-saga/macro';
import { enqueueSnackbar } from '@common-modules/snackbar';

export function* fetchCasesHandler() {
  yield* takeLatest(Actions.FETCH_CASES, function* ({ payload }) {
    const { query, offset, limit } = payload;
    const conditions = query
      ? query
          .trim()
          .split(' ')
          .map((value) => ({
            field: 'caseName',
            operator: 'query_string',
            value: `*${value}*`,
          }))
      : [];

    yield* put(
      Services.fetchCases({
        offset,
        limit,
        query: conditions,
      })
    );
  });
}

export function* fetchCasesFailureHandler() {
  yield* takeLatest(Actions.FETCH_CASES_FAILURE, function* () {
    const intl = sagaIntl();
    yield enqueueSnackbar({
      message: intl.formatMessage({ id: 'failedToFetchCase' }),
      variant: 'error',
    });
  });
}

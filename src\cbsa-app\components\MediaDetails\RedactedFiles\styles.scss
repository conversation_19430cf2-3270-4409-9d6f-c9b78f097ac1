.lastRedactedFiles {
  height: 19px;
  width: 150px;
  color: rgb(207, 216, 220, 0.87);
  font-family: Roboto, Arial, Helvetica, sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
}

.filename {
  width: 350px;
  color: rgb(207, 216, 220, 0.87);
  font-family: Roboto, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin-top: 10px;
  text-overflow: ellipsis;
  overflow: hidden;
}

.modifiedDateTime {
  height: 16px;
  width: 203px;
  color: rgb(207, 216, 220, 0.87);
  font-family: Roboto, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin-top: 12px;
}

.deleteButton {
  box-sizing: border-box;
  height: 37px;
  width: 105px;
  border: 1px solid #6c7c84;
  border-radius: 2px;
}

.downloadButton {
  height: 36px;
  width: 114px;
  border-radius: 2px;
  background-color: #2196f3;
  color: #cfd8dc;
  box-shadow: 0 1px 5px 0 rgb(0, 0, 0, 0.2), 0 2px 2px 0 rgb(0, 0, 0, 0.14),
    0 3px 1px -2px rgb(0, 0, 0, 0.2);
  margin-left: 11.5px;
}

.buttonWrapper {
  margin-top: 33px;
}

.wrapper {
  margin-top: 55px;
  display: inline;
  width: 400px;
}

.metricWrapper {
  float: left;
  text-align: center;
}

.metricValueLabel {
  float: left;
  color: rgb(207, 216, 220, 0.87);
  font-family: Roboto, Arial, Helvetica, sans-serif;
  font-size: 14px;
  display: inline-block;
  margin-left: 12px;
}

.separator {
  float: left;
  margin-left: 21px;
  margin-right: 21px;
  margin-top: -2px;
  height: 24px;
  width: 2px;
  border: 1px solid #4e5c6e;
  background-color: #4e5c6e;
}

.icon {
  color: #cfd8dc;
  height: 20px !important;
  width: 20px !important;
}

.udrIcon {
  margin-top: 4px;
  color: #cfd8dc;
  width: 16px;
}

.downloadSpinner {
  position: absolute;
  margin: auto;
}

.mediaPlayButton {
  border-radius: 1em !important;
  border: none !important;
}

.tooltip {
  font-size: 12px !important;
  word-break: break-all;
  max-width: 500px;
}

.controlBar {
  position: absolute !important;
  bottom: 0 !important;
  width: 100%;
}

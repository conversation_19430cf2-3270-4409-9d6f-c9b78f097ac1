import {
  DataTestSelector,
  DataVeritoneSelector,
} from '../../../support/helperFunction/mediaDetailHelper';

export const header = {
  fileNameHeader: () =>
    cy.get(`[data-veritone-element=${DataVeritoneSelector.FileName}]`),
  editorTab: () => cy.getDataIdCy({ idAlias: DataTestSelector.EditorTab }),
  redactedFilesTab: () =>
    cy.getDataIdCy({ idAlias: DataVeritoneSelector.RedactFileTabBtn }),
  undoButton: () =>
    cy.get(`[data-veritone-element=${DataVeritoneSelector.UndoButton}]`),
  redoButton: () =>
    cy.get(`[data-veritone-element=${DataVeritoneSelector.RedoButton}]`),
  notificationButton: () =>
    cy.get(
      `[data-veritone-element=${DataVeritoneSelector.NotificationButton}]`
    ),
  shortcutsButton: () =>
    cy.get(`[data-veritone-element=${DataVeritoneSelector.ShortcutsButton}]`),
  mediaSettingsBtn: () =>
    cy.get(`[data-veritone-element=${DataVeritoneSelector.MediaSettingsBtn}]`),
  helpCenterButton: () =>
    cy.get(`[data-veritone-element=${DataVeritoneSelector.HelpCenterButton}]`),
  mediaDetailCloseBtnHeader: () =>
    cy.get(
      `[data-veritone-element=${DataVeritoneSelector.MediaDetailCloseBtn}]`
    ),
};

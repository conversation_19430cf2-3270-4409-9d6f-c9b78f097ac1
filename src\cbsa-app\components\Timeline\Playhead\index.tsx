import { clamp } from 'lodash';
import { memo, useCallback } from 'react';

import { PlayheadPropTypes } from './PlayheadPropTypes';
import PlayheadView from './PlayheadView';

const MIN_GAP = 3000;

const Playhead = ({
  minWindowMs,
  maxWindowMs,
  startWindowMs,
  stopWindowMs,
  onSetStart,
  onSetStop,
  onSeekMedia,
}: PlayheadPropTypes) => {
  const onStart = useCallback(
    (ms: number) => onSetStart(clamp(ms, minWindowMs, stopWindowMs - MIN_GAP)),
    [minWindowMs, onSetStart, stopWindowMs]
  );
  const onStop = useCallback(
    (ms: number) => onSetStop(clamp(ms, startWindowMs + MIN_GAP, maxWindowMs)),
    [maxWindowMs, onSetStop, startWindowMs]
  );

  return (
    <PlayheadView
      {...{
        minWindowMs,
        maxWindowMs,
        startWindowMs,
        stopWindowMs,
        onSetStart: onStart,
        onSetStop: onStop,
        onSeekMedia,
      }}
    />
  );
};

export default memo(Playhead);

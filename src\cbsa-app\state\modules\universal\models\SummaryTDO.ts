import { TDOId } from '@common-modules/universal/models/Brands';
import type { JobStatus, TaskStatus } from 'veritone-types';

export interface SummaryTDOTask {
  readonly id?: string;
  readonly targetId?: TDOId;
  readonly status: TaskStatus;
  readonly createdDateTime: string;
  readonly startedDateTime: string | null;
  readonly engine: {
    readonly id: string;
    readonly categoryId: string;
  };
  readonly job: {
    readonly status: JobStatus;
  };
  readonly jobId: string;
}

export interface SummaryTDOJob {
  readonly targetId?: TDOId;
  readonly status: JobStatus;
  readonly createdDateTime: string;
}

export interface VeritoneFile {
  readonly audioSampleRate?: number;
  readonly duration?: number;
  readonly fileName?: string;
  readonly fileType?: string;
  readonly filename: string;
  readonly hasAudio?: boolean;
  readonly hasVideo?: boolean;
  readonly mimetype: string;
  readonly segmented?: boolean;
  readonly height?: number;
  readonly width?: number;
  readonly videoFrameRate?: number;
  readonly size?: number;
}

export interface SummaryTDO {
  readonly id: TDOId;
  readonly name: string;
  readonly status: 'downloaded' | 'recording' | 'recorded';
  readonly thumbnailUrl: string;
  readonly createdDateTime?: string; // ISO
  readonly modifiedDateTime?: string; // ISO
  readonly startDateTime?: string; // ISO
  readonly stopDateTime?: string; // ISO
  readonly details: {
    readonly tags?: ReadonlyArray<{
      redactionStatus: 'Draft' | 'Pending Review' | 'Complete';
      value: 'in redaction';
      toBeDeletedTime?: string;
    }>;
    readonly govQARequestId?: string;
    readonly foiaXpressRequestId?: string;
    readonly casepointRequestId?: string;
    readonly nuixRequestId?: string;
    readonly exterroRequestId?: string;
    readonly veritoneFile?: VeritoneFile;
    readonly isExport?: boolean;
  };
  readonly assets: {
    readonly count: number;
    readonly records: ReadonlyArray<{
      readonly assetType: string;
      readonly signedUri: string;
      readonly details: {
        readonly audioRedactions?: number;
        readonly boundingPolysCount?: number;
        readonly selectedPolyGroupsCount?: number;
        readonly tdos?: Record<
          TDOId,
          {
            readonly auditLogAssetId: string;
            readonly auditLogName: string;
            readonly redactedMediaAssetId: string;
            readonly redactedMediaName: string;
          }
        >;
      };
    }>;
  };
  readonly jobs: {
    readonly records: ReadonlyArray<SummaryTDOJob>;
  };
  readonly tasks?: {
    readonly records: ReadonlyArray<SummaryTDOTask>;
  };
  readonly primaryAsset: {
    readonly id: string;
    readonly signedUri: string;
    readonly contentType: string;
  };
  readonly streams?: ReadonlyArray<{
    readonly protocol: string;
    readonly uri: string;
  }>;
}

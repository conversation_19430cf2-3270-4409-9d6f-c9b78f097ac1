// This was implemented using enzyme and should be converted to react-testing-library
// however it doesn't actually look like any of it is currently used so just commenting
// for now, in case it is actually needed somewhere
//
// import * as React from 'react';
// import { I18nProvider } from './';
// import { mount, MountRendererProps } from 'enzyme';

// export default function mountWithIntl(
//   node: React.ReactElement,
//   options?: MountRendererProps | undefined
// ) {
//   const wrapProps: MountRendererProps = {
//     wrappingComponent: I18nProvider,
//   };
//   return mount(node, Object.assign(wrapProps, options));
// }

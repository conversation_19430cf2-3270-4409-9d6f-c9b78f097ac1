import { createSelector } from 'reselect';

import {
  selectAudioRedactions,
  selectSelectedTimeSlices,
} from './transcription';
import { selectClusterList, selectMediaDuration, selectSelected } from './view';

const CANVAS_WIDTH = 2000;
const CANVAS_HEIGHT = 32;

// IS THIS USED ANYMORE???

const buildTimelineDataUrl =
  (type: string, fillStyle: string | CanvasGradient | CanvasPattern) =>
  (
    list: ReturnType<typeof selectClusterList>,
    selected: ReturnType<typeof selectSelected>,
    mediaDuration: ReturnType<typeof selectMediaDuration>
  ) => {
    const multiplier = CANVAS_WIDTH / (mediaDuration * 1000);
    const canvas = document.createElement('canvas');
    canvas.width = CANVAS_WIDTH;
    canvas.height = CANVAS_HEIGHT;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = fillStyle;
      for (const cluster of list) {
        for (const group of cluster.groups) {
          if (group.type === type) {
            for (const { id, startTimeMs, stopTimeMs } of group.segments) {
              if (selected[id]) {
                ctx.fillRect(
                  startTimeMs * multiplier,
                  0,
                  (stopTimeMs - startTimeMs) * multiplier,
                  CANVAS_HEIGHT
                );
              }
            }
          }
        }
      }
    }
    return canvas.toDataURL();
  };

export const selectUDRsImgData = createSelector(
  selectClusterList,
  selectSelected,
  selectMediaDuration,
  buildTimelineDataUrl('udr', 'rgb(255, 235, 59)')
);

export const selectFaceDetectionImgData = createSelector(
  selectClusterList,
  selectSelected,
  selectMediaDuration,
  buildTimelineDataUrl('head', 'rgb(129, 199, 132)')
);

export const selectTranscriptionImgData = createSelector(
  selectAudioRedactions,
  selectSelectedTimeSlices,
  selectMediaDuration,
  (redactions, selectedTimeSlices, mediaDuration) => {
    const multiplier = CANVAS_WIDTH / (mediaDuration * 1000);
    const canvas = document.createElement('canvas');
    canvas.width = CANVAS_WIDTH;
    canvas.height = CANVAS_HEIGHT;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = 'rgb(255, 193, 7)';
      for (const group of redactions) {
        if (group) {
          const [startTimeMs, stopTimeMs] = group;
          ctx.fillRect(
            startTimeMs * multiplier,
            0,
            (stopTimeMs - startTimeMs) * multiplier,
            CANVAS_HEIGHT
          );
        }
      }
      ctx.fillStyle = 'rgb(74, 144, 226)';
      ctx.globalAlpha = 0.8;
      selectedTimeSlices.forEach((slice) => {
        const [startTimeMs, stopTimeMs] = slice;
        ctx.fillRect(
          startTimeMs * multiplier,
          0,
          (stopTimeMs - startTimeMs) * multiplier,
          CANVAS_HEIGHT
        );
      });
      ctx.globalAlpha = 1;
    }
    return canvas.toDataURL();
  }
);

import { TDOId } from '../../universal/models/Brands';
export const namespace = 'vtn-redact-engine-transcription';

/**
 * OpticalTracting Store
 *
 * interface OpticalTrackingStore {
 *  [tdoId: TDOId]: {
 *    [jobId: string]: {
 *      jobId: string;
 *      isRunning: boolean;
 *      status: 'pending' | 'queued' | 'running' | 'complete' | 'failed' | 'cancelled' | 'aborted'
 *      startedOn: Date;
 *      endedOn?: Date;
 *      error?: string;
 *    }
 *  }
 * }
 */

export interface JobState {
  jobId?: string;
  isRunning: boolean;
  status:
    | 'pending'
    | 'queued'
    | 'running'
    | 'complete'
    | 'failed'
    | 'cancelled'
    | 'aborted';
  startedOn: Date;
  endedOn?: Date;
  error?: string;
}

interface TranscriptionTDO {
  [tdoId: TDOId]: {
    [jobId: string]: JobState;
  };
}

export type TranscriptionStore = Partial<TranscriptionTDO>;

export const defaultState: TranscriptionStore = {};

export const defaultJobSlice: () => JobState = () => ({
  jobId: undefined,
  isRunning: true,
  status: 'pending',
  startedOn: new Date(),
  endedOn: undefined,
  error: undefined,
});

import { ConnectedProps, connect } from 'react-redux';

import { processEngineRequestAction } from '@common-modules/engines/transcription';
import {
  selectTdo,
  selectDisableTranscriptionButton,
  LocalState,
} from '@common-modules/mediaDetails';

import { TranscriptFailedStateTabPropTypes } from './TranscriptFailedStateTabPropTypes';
import TranscriptFailedStateTabView from './TranscriptFailedStateTabView';

const FailedStateTab = ({
  tdo,
  onStartJob,
  videoLoaded,
  buttonTranscriptionDisable,
}: TranscriptFailedStateTabPropTypes) => {
  const handleOnStartJob = () => {
    if (!buttonTranscriptionDisable && tdo?.primaryAsset) {
      onStartJob({
        tdoId: tdo.id,
        tdoName: tdo.name,
        targetId: tdo.id,
        url: tdo.primaryAsset.signedUri,
      });
    }
  };

  return (
    <TranscriptFailedStateTabView
      onStartJob={handleOnStartJob}
      isDisable={buttonTranscriptionDisable}
      videoLoaded={videoLoaded}
    />
  );
};

// export default connect(
//   (state) => ({
//     tdo: selectTdo(state),
//     buttonTranscriptionDisable: selectDisableTranscriptionButton(state),
//   }),
//   {
//     onStartJob: processEngineRequestAction,
//   }
// )(FailedStateTab);

const mapState = (state: LocalState) => ({
  tdo: selectTdo(state),
  buttonTranscriptionDisable: selectDisableTranscriptionButton(state),
});
const mapDispatch = {
  onStartJob: processEngineRequestAction,
};
const connector = connect(mapState, mapDispatch);
export type TranscriptFailedStateTabPropsFromRedux = ConnectedProps<
  typeof connector
>;
export default connector(FailedStateTab);

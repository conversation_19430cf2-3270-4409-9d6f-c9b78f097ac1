import { ReactNode } from 'react';
import * as sideBarStyles from '../SideBar/styles.scss';
import { AppContainer as LibAppContainer } from '@veritone/glc-react';

const AppContainer = ({
  sideBarOffset,
  ...props
}: {
  readonly sideBarOffset?: boolean;
  readonly appBarOffset?: boolean;
  readonly topBarOffset?: boolean;
  readonly children?: ReactNode;
}) => (
  <LibAppContainer
    {...props}
    leftOffset={sideBarOffset ? parseInt(sideBarStyles.sidebarwidth || '0') : 0}
  />
);

export default AppContainer;

import { memo } from 'react';

import { useStyles } from '../styles';

const pad = (num: number) => ('0' + num).slice(-2);

const hhmmss = (ms: number) => {
  const secs = (ms / 1000) | 0;
  const hours = Math.floor(secs / 60 / 60);
  const minutes = Math.floor(secs / 60) % 60;
  const seconds = secs % 60;
  return hours
    ? `${hours}:${pad(minutes)}:${pad(seconds)}`
    : `${pad(minutes)}:${pad(seconds)}`;
};

const TimePeriod = ({
  startTimeMs,
  stopTimeMs,
  onClick,
}: TimePeriodPropTypes) => {
  const classes = useStyles();
  return (
    <span className={classes.timePeriod} onClick={onClick}>
      {hhmmss(startTimeMs)} - {hhmmss(stopTimeMs)}
    </span>
  );
};

export default memo(TimePeriod);

export interface TimePeriodPropTypes {
  readonly onClick: () => void;
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
}

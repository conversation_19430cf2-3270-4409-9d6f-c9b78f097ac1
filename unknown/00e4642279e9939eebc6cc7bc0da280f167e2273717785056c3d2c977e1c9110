import { createSelector } from 'reselect';
import { NotificationStore } from './models';
import { namespace } from './store';
import { notificationType } from '@veritone/glc-react';

export const selectStore = (store: { [namespace]: NotificationStore }) =>
  store[namespace];

export const selectSDOs = createSelector(selectStore, (s) => s.sdos);

export const selectShowOrHideNotificationList = createSelector(
  selectStore,
  (s) => s.showCustomNotificationList
);

export const TYPE_PREPARING = 'preparing';
export const TYPE_PROCESSING = 'processing';
export const TYPE_FAILED = 'failed';
export const TYPE_COMPLETE = 'complete';

export const listPreparing = ['pending', 'queued', 'standby_pending'];
export const listProcessing = [
  'running',
  'waiting',
  'paused',
  'resuming',
  'accepted',
];
export const listFailed = ['failed', 'cancelled', 'aborted'];
export const listComplete = ['complete'];

export const convertRealStatusToNotiStatus = (
  status: string
): notificationType['type'] => {
  if (listPreparing.includes(status)) {
    return TYPE_PREPARING;
  }
  if (listProcessing.includes(status)) {
    return TYPE_PROCESSING;
  }
  if (listFailed.includes(status)) {
    return TYPE_FAILED;
  }
  if (listComplete.includes(status)) {
    return TYPE_COMPLETE;
  }
  return TYPE_PROCESSING;
};

import { memo } from 'react';
import TdoWrapper from './components/TdoWrapper';
import { SummaryTDO, TDOId } from '@cbsa-modules/universal';
import { useStyles } from './styles';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const TdoGridContent = ({ tdos, toggleTdo, selectedTdos }: Props) => {
  const classes = useStyles();

  return (
    <div className={classes.tdoGrid}>
      {tdos?.map(
        (tdo, key) =>
          !tdo?.details?.isExport && (
            <TdoWrapper
              key={tdo?.id ?? key}
              tdo={tdo}
              toggleTdo={toggleTdo}
              selected={tdo?.id in (selectedTdos ?? {})}
            />
          )
      )}
    </div>
  );
};

interface Props {
  tdos: SummaryTDO[];
  toggleTdo: (tdoId: TDOId) => void;
  selectedTdos: Record<TDOId, any>;
}

const TdoGrid = ({ tdos, toggleTdo, selectedTdos }: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <TdoGridContent
      tdos={tdos}
      toggleTdo={toggleTdo}
      selectedTdos={selectedTdos}
    />
  </ThemeProvider>
);

export default memo(TdoGrid);

import {
  all,
  call,
  cancel,
  fork,
  put,
  race,
  select,
  takeEvery,
  takeLatest,
  take,
  delay,
} from 'typed-redux-saga/macro';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { OUT_CREATE_JOB_FAIL, OUT_CREATE_JOB_SUCCESS } from '@worker';

import { STOP_ALL_ENGINE_POLLING } from '../actions';
import * as Actions from './actions';
import { selectEngineStatus } from './selectors';
import {
  cancelJobService,
  checkJobStatusService,
  createEngineJobService,
  queryEngineResultsService,
} from './services';
import { TDOId } from '../../universal/models/Brands';
import { ActionCreatorWithPayload } from '@reduxjs/toolkit';
import { selectTranscriptionRunning } from '../../mediaDetails';
import { TdoTask } from '../../mediaDetails/models';
import { sagaIntl } from '@common/i18n';

export function* saga() {
  yield* all([
    fork(onProcessEngineRequestAction),
    fork(onProcessEngineRequestSuccessAction),
    fork(onProcessEngineRequestFailureAction),
    fork(onCreateEngineJobAction),
    fork(onStartPollingEngineResultsAction),
    fork(onCheckJobStatusAction),
    fork(onQueryEngineResultsRecordAction),
    fork(onCancelJobAction),
    fork(onCancelJobSuccessAction),
    fork(onCancelJobFailureAction),
  ]);
}

/**
 * Action watchers.
 */

function* onProcessEngineRequestAction() {
  yield* takeEvery(Actions.PROCESS_ENGINE_REQUEST, function* (action) {
    yield* startEngineJob(action.payload);
  });
}

export function* onProcessEngineRequestSuccessAction() {
  yield* takeLatest(
    Actions.START_POLLING_ENGINE_RESULTS,
    onHandleEngineRequestSuccessAction
  );
}

export function* onHandleEngineRequestSuccessAction() {
  const runningTask: TdoTask[] = yield* select(selectTranscriptionRunning);
  yield* all(
    runningTask.map(() => take(Actions.PROCESS_ENGINE_REQUEST_SUCCESS))
  );

  const intl = sagaIntl();
  yield* put(
    enqueueSnackbar({
      message: intl.formatMessage({
        id: 'processTranscriptionSuccess',
        defaultMessage: 'A transcription process has completed.',
      }),
    })
  );
}

function* onProcessEngineRequestFailureAction() {
  yield* takeEvery(Actions.PROCESS_ENGINE_REQUEST_FAILURE, function* (action) {
    yield* put(
      enqueueSnackbar({
        message: action.payload.errorMsg,
      })
    );
  });
}

function* onCreateEngineJobAction() {
  yield* takeEvery(Actions.CREATE_ENGINE_JOB, function* (action) {
    yield* put(createEngineJobService(action.payload));
  });
}

function* onStartPollingEngineResultsAction() {
  yield* takeEvery(Actions.START_POLLING_ENGINE_RESULTS, function* (action) {
    yield* call(startPollingJobStatus, action.payload);
  });
}

function* onCheckJobStatusAction() {
  yield* takeEvery(Actions.CHECK_JOB_STATUS, function* (action) {
    yield* put(checkJobStatusService(action.payload));
  });
}

function* onQueryEngineResultsRecordAction() {
  yield* takeEvery(Actions.QUERY_ENGINE_RESULTS, function* (action) {
    yield* put(queryEngineResultsService(action.payload));
  });
}

// function* onGetEngineResultsFromURIAction() {
//   yield takeEvery(Actions.GET_ENGINE_RESULTS_FROM_URI, function*(action) {
//     yield put(getEngineResultsFromURIService(action.payload));
//   });
// }

function* onCancelJobAction() {
  yield* takeEvery(Actions.CANCEL_JOB, function* (action) {
    yield* put(cancelJobService(action.payload));
  });
}

function* onCancelJobSuccessAction() {
  yield* takeEvery(Actions.CANCEL_JOB_SUCCESS, function* () {
    yield* put(
      enqueueSnackbar({
        message: 'The transcription process has been canceled.',
      })
    );
  });
}

function* onCancelJobFailureAction() {
  yield* takeEvery(Actions.CANCEL_JOB_FAILURE, function* () {
    yield* put(
      enqueueSnackbar({
        message: "I'm sorry. The cancel request failed.",
      })
    );
  });
}

/**
 * Start engine job process.
 */

export const startEngineJob = function* ({
  tdoId,
  tdoName,
  payload,
  retryCount = 3,
}: {
  tdoId: TDOId;
  tdoName: string;
  payload: {
    targetId: string;
    url: string;
  };
  retryCount?: number;
}): any {
  yield* put(Actions.createEngineJobAction({ tdoId, tdoName, payload }));
  const { ok, err } = yield* race({
    ok: take(Actions.CREATE_ENGINE_JOB_SUCCESS),
    err: take(Actions.CREATE_ENGINE_JOB_FAILURE),
  });

  if (err && retryCount <= 0) {
    yield* put(OUT_CREATE_JOB_FAIL(err));
    yield* cancel();
  }

  if (err && retryCount > 0) {
    yield* delay(3000);
    yield* startEngineJob({
      tdoId,
      tdoName,
      payload,
      retryCount: retryCount - 1,
    });
  }

  if (ok) {
    yield* put(
      Actions.startPollingEngineResultsAction({
        tdoId,
        jobId: ok.payload.createJob.id,
      })
    );
    yield* put(OUT_CREATE_JOB_SUCCESS(ok));
  }
};

export function* startPollingJobStatus({
  tdoId,
  jobId,
}: {
  tdoId: TDOId;
  jobId: string;
}) {
  while (true) {
    const { isRunning, status, error } = yield* select(
      selectEngineStatus(tdoId, jobId)
    );

    if (status === 'complete') {
      break;
    }
    if (!isRunning || error) {
      yield* put(Actions.stopPollingEngineResultsAction({ tdoId, jobId }));
      yield* cancel();
    }
    yield* put(Actions.checkJobStatusAction({ tdoId, jobId }));
    const { stop } = yield* race({
      ok: delay(10_000),
      stop: take([
        isActionOfTDO(Actions.STOP_POLLING_ENGINE_RESULTS, jobId),
        STOP_ALL_ENGINE_POLLING,
      ]),
    });
    if (stop) {
      yield* put(Actions.stopPollingEngineResultsAction({ tdoId, jobId }));
      yield* cancel();
      return;
    }
  }

  yield* put(Actions.processEngineRequestSuccessAction({ tdoId, jobId }));
}

export const isActionOfTDO =
  <P>(
    action: ActionCreatorWithPayload<P extends { jobId: string } ? P : never>,
    jobId: string
  ) =>
  ({ type, payload }: { type: string; payload?: { jobId?: string } }) =>
    type === action.type && payload?.jobId === jobId;

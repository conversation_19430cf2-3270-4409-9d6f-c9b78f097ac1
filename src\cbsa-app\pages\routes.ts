// import { Action } from '@reduxjs/toolkit';
// import { NOT_FOUND } from 'redux-first-router';
import { initCasesList } from '@cbsa-modules/mainPage/sagas';
import { initAddMedia } from '@cbsa-modules/addMedia/sagas';
// import NotFound from '@common-pages/NotFound';
// import { initMainPage } from '@redact-modules/mainPage/sagas';
// import { selectTdoIsChanged } from '@common-modules/mediaDetails';
// import { loadMediaDetailsPage } from '@common-modules/mediaDetails/saga';
import {
  // ROUTE_FORBIDDEN,
  ROUTE_HOME,
  ROUTE_ADD_MEDIA,
  ROUTE_MEDIA_EDITOR,
  // ROUTE_LOAD_FAILED,
  // ROUTE_LOGOUT,
  // ROUTE_MEDIA_DETAILS,
  // ROUTE_INGEST,
  // ROUTE_INGEST_GOVQA,
} from '@common-modules/routing';
import { loadMediaDetailsPage } from '@common-modules/mediaDetails/saga';
import {
  Action,
  ReceivedAction,
  RoutesMap,
  StateGetter,
} from 'redux-first-router';

const routes: RoutesMap<{
  component: () => Promise<any>;
  requiresAuth?: boolean;
  redirects?: {
    test: (
      getState: StateGetter<any>,
      action: Action | ReceivedAction
    ) => boolean;
    to: Action;
  }[];
  saga?: () => void;
}> = {
  // [ROUTE_LOGOUT]: {
  //   path: '/logout',
  //   component: () => import(`@cbsa-pages/Logout`),
  //   requiresAuth: false,
  // },
  [ROUTE_HOME.type]: {
    path: '/',
    component: () => import(`@cbsa-pages/MainPage`),
    requiresAuth: true,
    saga: initCasesList,
  },
  [ROUTE_ADD_MEDIA.type]: {
    path: '/case/:case_id',
    component: () => import(`@cbsa-pages/AddMedia`),
    requiresAuth: true,
    saga: initAddMedia,
  },
  [ROUTE_MEDIA_EDITOR.type]: {
    // path: '/case/:case_id/editor', // <- original
    // path: '/files/:tdoId?', // <- for use with copy of Redact's Media Details page
    path: '/case/:case_id/files/:tdoId?',
    component: () => import(`@cbsa-pages/MediaDetails`),
    // component: () => import(`@redact-pages/MediaDetails`),
    requiresAuth: true,
    saga: loadMediaDetailsPage,
  },
  // [ROUTE_INGEST]: {
  //   path: '/ingest',
  //   component: () => import(`@redact-pages/MainPage`),
  //   requiresAuth: true,
  //   saga: initMainPage,
  // },
  // [ROUTE_INGEST_GOVQA]: {
  //   path: '/ingest/GovQA/:govQARequestId',
  //   component: () => import(`@redact-pages/MainPage`),
  //   requiresAuth: true,
  //   saga: initMainPage,
  // },
  // [ROUTE_MEDIA_DETAILS]: {
  //   path: '/files/:tdoId?',
  //   component: () => import(`@redact-pages/MediaDetails`),
  //   requiresAuth: true,
  //   // TODO Move into actual sagas
  //   saga: loadMediaDetailsPage,
  //   confirmLeave: (state: unknown, action: Action) => {
  //     if (action.type === ROUTE_HOME && selectTdoIsChanged(state)) {
  //       return 'Would you like to save your changes before closing?';
  //     }
  //   },
  // },
  // [NOT_FOUND]: {
  //   path: '/not-found',
  //   component: NotFound,
  //   requiresAuth: true,
  // },
  // [ROUTE_LOAD_FAILED]: {
  //   path: '/not-found',
  //   component: () => import(`@redact-pages/NotFound`),
  //   requiresAuth: true,
  // },
  // [ROUTE_FORBIDDEN]: {
  //   path: '/forbidden',
  //   component: () => import(`@redact-pages/Forbidden`),
  //   requiresAuth: true,
  // },
};

export default routes;

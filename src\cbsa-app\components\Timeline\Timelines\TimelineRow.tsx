import * as React from 'react';

import * as styles from './styles.scss';
const getArrow = (direction?: string) => {
  const upArrow = '▲';
  const downArrow = '▼';
  if (!direction) {
    return null;
  }
  if (direction === 'UP') {
    return downArrow;
  } else if (direction === 'DOWN') {
    return upArrow;
  }
  return null;
};
const TimelineRow = ({
  color,
  icon,
  label,
  expansionArrowDirection,
  progress,
  children,
  height,
  onClick,
}: TimelineRowPropTypes) => (
  <div
    data-testid="timeline-row"
    onClick={onClick}
    className={styles.timelineContainer}
    style={{ height }}
  >
    <div
      className={styles.icon}
      style={{ borderColor: color, minWidth: '190px' }}
    >
      <div className={styles.iconContainer}>
        <span>{icon}</span>
        <label> {label} </label>
      </div>
      <div className={styles.arrowContainer}>
        <label>{getArrow(expansionArrowDirection)}</label>
      </div>
    </div>
    <div className={['timeline-row', styles.timeline].join(' ')}>
      {children}
    </div>
    {progress < 1 ? (
      <div
        className={styles.progress}
        style={{ width: `${100 - progress * 100}%` }}
      />
    ) : null}
  </div>
);

export default TimelineRow;

export interface TimelineRowPropTypes {
  readonly color: string;
  readonly icon: React.ReactNode;
  readonly label: string;
  readonly expansionArrowDirection?: string;
  readonly progress: number;
  readonly children: React.ReactElement;
  readonly height?: number;
  readonly onClick?: () => any;
}

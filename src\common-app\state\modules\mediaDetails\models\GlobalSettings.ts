import { IntRange } from '@utils';
import { RedactionConfig } from './RedactionPolys';

export type RedactionType = 'blur' | 'black_fill' | 'outline' | 'inverse_blur';

export enum ClusterSimThresholdType {
  VERY_LOW = 'verylow',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  VERY_HIGH = 'veryhigh',
}

export const BlurEngineRedactionType = {
  blur: 'blur',
  black_fill: 'blackFill',
  outline: 'outline',
  inverse_blur: 'inverseBlur',
};

export type DetectionType =
  | 'head'
  | 'licensePlate'
  | 'laptop'
  | 'vehicle'
  | 'udr'
  | 'id card'
  | 'notepad';

export type ShapeType = 'rectangle' | 'ellipse';

export enum FeatherType {
  NONE = 'none',
  LIGHT = 'light',
  MEDIUM = 'medium',
  HEAVY = 'heavy',
}

export type BlurLevel = IntRange<1, 10>;

export interface LegacyGlobalSettings {
  readonly faceDetectionScaling: number; // 0 - 100
  readonly blurLevel?: BlurLevel; // integer 1-10 Legacy setting and won't be used in GlobalSettings. Will be removed when loading from these settings
  readonly blurLevelVehicle?: BlurLevel; // integer 1-10 Legacy setting and won't be used in GlobalSettings. Will be removed when loading from these settings
  readonly redactionType?: RedactionType; // Legacy setting and won't be used in GlobalSettings. Will be removed when loading from these settings
  // In legacyGlobalSettings, ObjectTypeEffects could contain blurLevel and blackFill if so, these will be moved to redactionConfig in saga.ts
  readonly objectTypeEffects: {
    readonly head: ObjectTypeEffects & LegacyObjectTypeEffects;
    readonly udr: ObjectTypeEffects & LegacyObjectTypeEffects;
    readonly laptop: ObjectTypeEffects & LegacyObjectTypeEffects;
    readonly vehicle: ObjectTypeEffects & LegacyObjectTypeEffects;
    readonly plate: ObjectTypeEffects & LegacyObjectTypeEffects;
    readonly notepad?: ObjectTypeEffects;
    readonly card?: ObjectTypeEffects;
    readonly person?: ObjectTypeEffects;
  };
}

export interface LegacyObjectTypeEffects {
  readonly blurLevel?: BlurLevel; // Moved to redactionConfig.blurLevel, contains the same value
  readonly blackFill?: boolean; // Moved to redactionConfig.redactionType, uses string of redactionType instead of true/false boolean blackFill
}

export interface ObjectTypeEffects {
  readonly shapeType: ShapeType;
  readonly redactionConfig: RedactionConfig;
  readonly scaling?: number; // 0 - 50
  // readonly featherType: FeatherType;
}

export interface GlobalSettings {
  readonly faceDetectionThreshold: number; // 0 - 100
  readonly videoType: 'Bodycam' | 'Broadcast' | 'CCTV';
  readonly detectionRate: number; // >= 1
  readonly autoInterpolationMax: number; // Default: 1000 (milliseconds)
  readonly manualInterpolationMax: number; // Default: 10_000? (milliseconds)
  readonly interpolationMaxStretch?: number; // max size ratio of interpolation gap relative to connected objects
  readonly trackingBackwardLimitSec: number; //  Max optical tracking backward (seconds)
  readonly trackingForwardLimitSec: number; //  Max optical tracking forward (seconds)
  readonly patchPreviousVersion: boolean;
  readonly audioType: number;
  readonly videoOffset: number;
  readonly objectTypeEffects: {
    readonly head: ObjectTypeEffects & { scaling: number }; // 0 - 50
    readonly udr: Omit<ObjectTypeEffects, 'scaling'>;
    readonly laptop: ObjectTypeEffects & { scaling: number }; // 0 - 50
    readonly vehicle: ObjectTypeEffects & { scaling: number }; // 0 - 50
    readonly plate: ObjectTypeEffects & { scaling: number }; // 0 - 50
    readonly notepad: ObjectTypeEffects & { scaling: number }; // 0 - 50
    readonly card: ObjectTypeEffects & { scaling: number }; // 0 - 50
    readonly person: ObjectTypeEffects & { scaling: number }; // 0 - 50
  };
  readonly isSaved?: boolean; // whether has been saved to TDO
  // Developer settings
  readonly legacyClustering?: boolean;
  readonly clusterSimThreshold?: ClusterSimThresholdType;
}

export interface BaseGlobalSettings
  extends LegacyGlobalSettings,
    // Note: Use LegacyGlobalSettings which contain LegacyObjectTypeEffects or ObjectTypeEffects
    Omit<GlobalSettings, 'objectTypeEffects'> {}

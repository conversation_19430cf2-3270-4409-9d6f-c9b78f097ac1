import { TDOId } from '@cbsa-modules/universal';

export interface MediaComment {
  readonly tdoId: TDOId;
  readonly done: boolean;
  readonly read: boolean;
  readonly comment: string;
  readonly deleted: boolean;
  readonly archived: boolean;
  readonly commentId: string;
  readonly createdBy: string;
  readonly searchName: string;
  readonly mediaTimestamp: number;
  readonly createdDateTime: string;
  readonly modifiedDateTime: string;
  readonly id: string;
}

export enum CommentSortBy {
  Timestamp = 'mediaTimestamp',
  DateCreated = 'modifiedDateTime',
}

$handle-size: 6px;

.resizeHandle {
  background-color: white;
  width: $handle-size !important;
  height: $handle-size !important;
}

.resizeHandleHorizontal {
  top: calc(50% - #{calc(#{$handle-size} / 2)}) !important;
}

.resizeHandleVertical {
  left: calc(50% - #{calc(#{$handle-size} / 2)}) !important;
}

.activeSelectedBox {
  z-index: 102;
}

.hoveredSelectedBox {
  z-index: 101;
}

.selectedBox {
  z-index: 100;
}

.unselectedBox {
  z-index: 99;
}

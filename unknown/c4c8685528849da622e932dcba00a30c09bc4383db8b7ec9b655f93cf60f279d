import {
  Button,
  ThemeProvider,
  // Theme,
  // StyledEngineProvider,
} from '@mui/material';

import { buttonTheme } from '@redact/materialUITheme';
import * as styles from './styles.scss';

const InviteAppButtonView = ({ onOpen }: InviteAppButtonViewPropTypes) => (
  <ThemeProvider theme={buttonTheme}>
    <div className={styles.buttonWrapper}>
      <Button
        className={styles.downloadButton}
        variant="contained"
        color="primary"
        onClick={onOpen}
        data-veritone-element="redacted-files-send-invite-email-button"
      >
        INVITE
      </Button>
    </div>
  </ThemeProvider>
);

export default InviteAppButtonView;

export interface InviteAppButtonViewPropTypes {
  readonly onOpen: () => void;
}

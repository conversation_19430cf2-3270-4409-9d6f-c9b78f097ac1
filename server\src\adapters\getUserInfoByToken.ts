import { Messages } from '../errors/messages'
import { Logger } from '../logger';
import Config from '../../apiConfig.json';
import { TOKEN_API } from '../api/constants';

export const getUserInfoByTokenAdapter = async (
  headers: { authorization: string },
)=> {
    const token = headers.authorization.split(' ')[1];
    const tokenApiUrl = Config.apiRoot + TOKEN_API + token;

    try {
      const response = await fetch(tokenApiUrl);

      if (response.ok) {
        const json = await response.json();

        if (!json?.data?.user) {
          return { error: Messages.userNotFound };
        }

        return json.data.user;
      } else {
        const errorMessage = await response.text();
        return { error: errorMessage };
      }
    } catch (err: any) {
      const errorMessage = Messages.FetchUserInfoFail + err?.message;
      Logger.error(errorMessage);
      return { error: errorMessage };
    }
};

import {
  createRef,
  DetailedHTMLProps,
  Dispatch,
  ImgHTMLAttributes,
  memo,
  RefObject,
  SetStateAction,
  useEffect,
  useState,
} from 'react';

const emptyImg =
  'data:image/gif;base64,R0lGODlhAQABAIAAAAUEBAAAACwAAAAAAQABAAACAkQBADs=';
import loadingBalls from '@resources/images/loading-balls.svg';

import * as Q from './queue';

const LOOKUP = new WeakMap<
  RefObject<HTMLImageElement>,
  {
    readonly src: string;
    readonly image: HTMLImageElement;
    readonly onLoadHandler: (this: HTMLImageElement) => void;
    readonly onErrorHandler: (this: HTMLImageElement) => void;
  }
>();

const onInit = (
  ref: RefObject<HTMLImageElement>,
  setSrc: Dispatch<SetStateAction<string>>,
  priority = 10,
  src = ''
) => {
  if (!src) {
    setSrc(emptyImg);
    return;
  }
  if (<PERSON>.has<PERSON>ey(src)) {
    setSrc(src);
    return;
  }
  const image = new Image();
  const onLoadHandler = onLoad(ref, setSrc);
  const onErrorHandler = onError(ref, setSrc);
  image.addEventListener('load', onLoadHandler);
  image.addEventListener('error', onErrorHandler);
  LOOKUP.set(ref, { src, image, onLoadHandler, onErrorHandler });
  Q.add({ priority, key: src, payload: image });
  if (Q.size() === 1) {
    image.src = src;
  }
};

const onComplete = (ref: RefObject<HTMLImageElement>) => {
  const item = LOOKUP.get(ref); // Q.findByKey(image.src);
  if (item) {
    item.image.removeEventListener('load', item.onLoadHandler);
    item.image.removeEventListener('error', item.onErrorHandler);
  }
  const nextItem = Q.next<HTMLImageElement>();
  if (nextItem) {
    if (nextItem.key === item?.image.src) {
      onComplete(ref);
    } else {
      nextItem.payload.src = nextItem.key;
    }
  }
};

const onLoad = (
  ref: RefObject<HTMLImageElement>,
  setSrc: Dispatch<SetStateAction<string>>
) =>
  function (this: HTMLImageElement) {
    if (ref.current) {
      setSrc(this.src || emptyImg);
    }
    onComplete(ref);
  };

const onError = (
  ref: RefObject<HTMLImageElement>,
  setSrc: Dispatch<SetStateAction<string>>
) =>
  function (this: HTMLImageElement) {
    if (ref.current) {
      setSrc(emptyImg);
    }
    onComplete(ref);
  };

const Qimg = ({ src, priority, ...imgProps }: QimgPropTypes) => {
  const ref = createRef<HTMLImageElement>();
  const [imgSrc, setSrc] = useState(loadingBalls);
  useEffect(() => {
    onInit(ref, setSrc, priority, src);
    return () => {
      const item = LOOKUP.get(ref);
      if (item) {
        Q.remove(item.image);
      }
    };
  }, [priority, ref, src]);
  return <img data-testid="q-image" ref={ref} src={imgSrc} {...imgProps} />;
};

export default memo(Qimg);

export interface QimgPropTypes
  extends DetailedHTMLProps<
    ImgHTMLAttributes<HTMLImageElement>,
    HTMLImageElement
  > {
  readonly priority?: number;
}

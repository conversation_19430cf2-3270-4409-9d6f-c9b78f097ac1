import {
  DEFAULT_BLUR,
  DEFAULT_BLUR_VEHICLE,
  DEFAULT_OBJECT_TYPE_EFFECTS,
  OBJECT_EFFECT_TYPES,
} from '@helpers/constants';
import {
  GlobalSettings,
  BaseGlobalSettings,
  LegacyGlobalSettings,
} from '../models';
import { omit } from 'lodash';

export function getGlobalSettings(settings: BaseGlobalSettings) {
  if (
    settings.blurLevel !== undefined ||
    settings.blurLevelVehicle !== undefined ||
    settings.redactionType !== undefined
  ) {
    /* Update settings from legacy values for tdo and global settings then remove from the settings object */
    return loadFromLegacySettings(settings);
  } else if (checkObjectTypeEffectsLegacy(settings?.objectTypeEffects)) {
    /* legacy settings v2 which includes objectTypeEffects: { blurLevel, blackFill } exist */
    return loadFromLegacySettingsV2(settings);
  }

  const objectTypeEffectsFromSettings = { ...DEFAULT_OBJECT_TYPE_EFFECTS };

  // Iterate over ObjectEffectTypes and set default values for any missing keys
  OBJECT_EFFECT_TYPES.forEach((objectType) => {
    if (objectType === 'udr') {
      objectTypeEffectsFromSettings[objectType] = {
        ...DEFAULT_OBJECT_TYPE_EFFECTS[objectType],
        ...(settings?.objectTypeEffects?.[objectType] || {}),
      };
    } else {
      objectTypeEffectsFromSettings[objectType] = {
        ...DEFAULT_OBJECT_TYPE_EFFECTS[objectType],
        ...(objectType === 'head'
          ? { scaling: settings?.faceDetectionScaling ?? 20 }
          : {}),
        ...(settings?.objectTypeEffects?.[objectType] || {}),
      };
    }
  });

  return {
    ...omit(settings, 'faceDetectionScaling'),
    objectTypeEffects: objectTypeEffectsFromSettings,
  };
}

export function checkObjectTypeEffectsLegacy(
  objectTypeEffects: LegacyGlobalSettings['objectTypeEffects']
) {
  return (
    objectTypeEffects &&
    // Check if old values exist
    (objectTypeEffects.head?.blurLevel !== undefined ||
      objectTypeEffects.head?.blackFill !== undefined ||
      objectTypeEffects.udr?.blurLevel !== undefined ||
      objectTypeEffects.udr?.blackFill !== undefined ||
      objectTypeEffects.laptop?.blurLevel !== undefined ||
      objectTypeEffects.laptop?.blackFill !== undefined ||
      objectTypeEffects.vehicle?.blurLevel !== undefined ||
      objectTypeEffects.vehicle?.blackFill !== undefined ||
      objectTypeEffects.plate?.blurLevel !== undefined ||
      objectTypeEffects.plate?.blackFill !== undefined)
  );
}

export function loadFromLegacySettings(
  legacyGlobalSettings: BaseGlobalSettings
): GlobalSettings {
  const {
    faceDetectionThreshold,
    faceDetectionScaling,
    videoType,
    detectionRate,
    autoInterpolationMax,
    manualInterpolationMax,
    trackingBackwardLimitSec,
    trackingForwardLimitSec,
    patchPreviousVersion,
    audioType,
    videoOffset,
    blurLevel,
    blurLevelVehicle,
    redactionType,
  } = legacyGlobalSettings;

  const TYPE_EFFECTS = {
    shapeType: 'rectangle' as const,
    redactionConfig: {
      blurLevel: blurLevel || DEFAULT_BLUR,
      fillType: redactionType || 'blur',
    },
  };

  const TYPE_EFFECTS_VEHICLE = {
    shapeType: 'rectangle' as const,
    redactionConfig: {
      blurLevel: blurLevelVehicle || DEFAULT_BLUR_VEHICLE,
      fillType: redactionType || 'blur',
    },
  };

  const objectTypeEffectsByType = { ...DEFAULT_OBJECT_TYPE_EFFECTS };

  OBJECT_EFFECT_TYPES.forEach((objectType) => {
    if (objectType === 'udr') {
      objectTypeEffectsByType[objectType] = {
        ...TYPE_EFFECTS,
      };
    } else {
      objectTypeEffectsByType[objectType] = {
        ...(objectType === 'vehicle'
          ? {
              ...TYPE_EFFECTS_VEHICLE,
              scaling: 0,
            }
          : {
              ...TYPE_EFFECTS,
              scaling:
                objectType === 'head'
                  ? (faceDetectionScaling ?? 20)
                  : objectType === 'plate'
                    ? 20
                    : 0,
            }),
      };
    }
  });

  return {
    faceDetectionThreshold,
    videoType,
    detectionRate,
    autoInterpolationMax,
    manualInterpolationMax,
    trackingBackwardLimitSec,
    trackingForwardLimitSec,
    patchPreviousVersion,
    audioType,
    videoOffset,
    // Spread objects to guarantee each detection type gets its own object
    objectTypeEffects: objectTypeEffectsByType,
  };
}

export function loadFromLegacySettingsV2(
  legacyGlobalSettings: BaseGlobalSettings
): GlobalSettings {
  // TODO: The incoming type needs to be updated to correctly reflect that objectTypeEffects
  // and it's keys are optional.  Also needs test cases added to ensure that the conversion works

  const objectTypeEffectsByType = { ...DEFAULT_OBJECT_TYPE_EFFECTS };

  OBJECT_EFFECT_TYPES.forEach((objectType) => {
    switch (objectType) {
      case 'udr':
        objectTypeEffectsByType[objectType] = {
          shapeType:
            legacyGlobalSettings.objectTypeEffects?.udr?.shapeType ||
            'rectangle',
          redactionConfig: {
            blurLevel:
              legacyGlobalSettings.objectTypeEffects?.udr?.blurLevel ||
              DEFAULT_BLUR,
            fillType: legacyGlobalSettings.objectTypeEffects?.udr?.blackFill
              ? 'black_fill'
              : 'blur',
          },
          // featherType: FeatherType.MEDIUM,
        };
        break;
      // Original Keys before adding notepad, card, and person
      case 'head':
      case 'laptop':
      case 'vehicle':
      case 'plate':
        objectTypeEffectsByType[objectType] = {
          shapeType:
            legacyGlobalSettings.objectTypeEffects?.[objectType]?.shapeType ||
            'rectangle',
          redactionConfig: {
            blurLevel:
              legacyGlobalSettings.objectTypeEffects?.[objectType]?.blurLevel ||
              DEFAULT_BLUR,
            fillType: legacyGlobalSettings.objectTypeEffects?.[objectType]
              ?.blackFill
              ? 'black_fill'
              : 'blur',
          },
          scaling:
            (legacyGlobalSettings.objectTypeEffects?.[objectType]?.scaling ??
            objectType === 'head')
              ? (legacyGlobalSettings.faceDetectionScaling ??
                legacyGlobalSettings.objectTypeEffects?.head?.scaling ??
                20)
              : objectType === 'plate'
                ? 20
                : 0,
          // featherType: FeatherType.MEDIUM,
        };
        break;
      default:
        objectTypeEffectsByType[objectType] = {
          shapeType:
            legacyGlobalSettings.objectTypeEffects?.[objectType]?.shapeType ||
            'rectangle',
          redactionConfig: {
            blurLevel:
              legacyGlobalSettings.objectTypeEffects?.[objectType]
                ?.redactionConfig?.blurLevel || DEFAULT_BLUR,
            fillType:
              legacyGlobalSettings.objectTypeEffects?.[objectType]
                ?.redactionConfig?.fillType || 'blur',
          },
          // featherType: FeatherType.MEDIUM,
          scaling:
            legacyGlobalSettings.objectTypeEffects?.[objectType]?.scaling ?? 0,
        };
    }
  });

  const {
    faceDetectionThreshold,
    videoType,
    detectionRate,
    autoInterpolationMax,
    manualInterpolationMax,
    trackingBackwardLimitSec,
    trackingForwardLimitSec,
    patchPreviousVersion,
    audioType,
    videoOffset,
  } = legacyGlobalSettings;

  // Remove the following legacy settings blurLevel, blurLevelVehicle, redactionType
  return {
    faceDetectionThreshold,
    videoType,
    detectionRate,
    autoInterpolationMax,
    manualInterpolationMax,
    trackingBackwardLimitSec,
    trackingForwardLimitSec,
    patchPreviousVersion,
    audioType,
    videoOffset,
    objectTypeEffects: objectTypeEffectsByType,
  };
}

import Konva from 'konva';
import { RefObject } from 'react';
import {
  animationFrameScheduler,
  combineLatest,
  fromEvent,
  interval,
  Subject,
} from 'rxjs';
import {
  distinctUntilChanged,
  map,
  sample,
  startWith,
  takeUntil,
  tap,
} from 'rxjs/operators';

import { selectSelected } from '@common-modules/mediaDetails';
import { BoundingPolyTree } from '@worker';
import { MapRenderOperator, RenderRequest } from './interfaces';

export const renderer = (
  { current: container }: RefObject<HTMLDivElement>,
  mapRender: MapRenderOperator
) => {
  if (!container) {
    return {
      render: () => {},
      destroy: () => {},
    };
  }

  // Setup Konva
  // const canvasWidth = container.clientWidth;
  const stage = new Konva.Stage({
    container,
    width: container.clientWidth,
    height: 32,
  });
  const layer = new Konva.Layer({ listening: false });
  stage.add(layer);

  // Setup Subjects
  const onStartWindowMs$ = new Subject<number>();
  const onStopWindowMs$ = new Subject<number>();
  const onCollection$ = new Subject<BoundingPolyTree | undefined>();
  const onVideoOffset$ = new Subject<number | undefined>();
  const onColor$ = new Subject<string | undefined>();
  const onSelected$ = new Subject<ReturnType<typeof selectSelected>>();
  const onResize$ = fromEvent(window, 'resize').pipe(
    map(() => container.clientWidth),
    distinctUntilChanged(),
    startWith(container.clientWidth)
  );
  const raf$ = interval(undefined, animationFrameScheduler);
  const onDestroy$ = new Subject<boolean>();

  // Setup Observables
  const startWindowMs$ = onStartWindowMs$.pipe(distinctUntilChanged());
  const stopWindowMs$ = onStopWindowMs$.pipe(distinctUntilChanged());
  const collection$ = onCollection$.pipe(distinctUntilChanged());
  const selected$ = onSelected$.pipe(distinctUntilChanged());
  const videoOffset$ = onVideoOffset$.pipe(distinctUntilChanged());
  const color$ = onColor$.pipe(distinctUntilChanged());

  /**
   * Redraw when Observables change.
   */
  combineLatest(
    [
      startWindowMs$,
      stopWindowMs$,
      collection$,
      selected$,
      onResize$.pipe(tap((width) => stage.width(width))),
      videoOffset$,
      color$,
    ],
    (startMs, stopMs, collection, selected, width, videoOffset, color) => ({
      startMs,
      stopMs,
      collection,
      selected,
      width,
      videoOffset: videoOffset || 0,
      color,
    })
  )
    .pipe(takeUntil(onDestroy$), sample(raf$), mapRender(layer))
    .subscribe((l) => l.batchDraw());

  return {
    render: ({
      startWindowMs,
      stopWindowMs,
      collection,
      selected,
      videoOffset,
      color,
    }: RenderRequest) => {
      onStartWindowMs$.next(startWindowMs);
      onStopWindowMs$.next(stopWindowMs);
      onSelected$.next(selected);
      onCollection$.next(collection);
      onVideoOffset$.next(videoOffset);
      onColor$.next(color);
    },
    destroy: () => {
      onDestroy$.next(true);
      stage.destroy();
    },
  };
};

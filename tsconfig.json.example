{"compileOnSave": false, "compilerOptions": {"target": "es5", "module": "ESNext", "lib": ["es2021", "es2019", "dom", "dom.iterable", "scripthost", "webworker"], "skipLibCheck": true, "allowJs": true, "jsx": "react", "declaration": false, "sourceMap": true, "outDir": "./build", "removeComments": true, "importHelpers": true, "downlevelIteration": true, "resolveJsonModule": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "baseUrl": "./", "rootDir": "..", "typeRoots": ["node_modules/@types", "node_modules/veritone-types/@types", "./types"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "preserveConstEnums": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "paths": {"@engine/*": ["./src/engine/*"], "@ducks/*": ["./src/engine/ducks/*"], "@ducks": ["src/engine/ducks/index.ts"], "@pages/*": ["./src/pages/*"], "@utility/*": ["./src/utility/*"], "@components/*": ["./src/components/*"], "@src/*": ["./src/*"], "@theme/*": ["./src/theme/*"]}}, "files": ["./src/index.tsx", "./src/types.d.ts"], "exclude": ["node_modules", "build"], "include": ["./src/**/*"]}
import { TDOId } from '../../universal/models/Brands';
export const namespace = 'vtn-redact-engine-optical-tracking';

/**
 * OpticalTracking Store
 *
 * interface OpticalTrackingStore {
 *  [tdoId: TDOId]: {
 *    [jobId: string]: {
 *      jobId: string;
 *      isRunning: boolean;
 *      status: 'pending' | 'queued' | 'running' | 'complete' | 'failed' | 'cancelled' | 'aborted'
 *      startedOn: Date;
 *      endedOn?: Date;
 *      error?: string;
 *    }
 *  }
 * }
 */

export interface JobState {
  jobId?: string;
  isRunning: boolean;
  status:
    | 'pending'
    | 'queued'
    | 'running'
    | 'complete'
    | 'failed'
    | 'cancelled'
    | 'aborted';
  startedOn: Date;
  endedOn?: Date;
  error?: string;
}

interface OpticalTrackingTDO {
  [tdoId: TDOId]: {
    [jobId: string]: JobState;
  };
}

export type OpticalTrackingStore = Partial<OpticalTrackingTDO>;

export const defaultState: OpticalTrackingStore = {};

export const defaultJobSlice: () => JobState = () => ({
  jobId: undefined,
  isRunning: true,
  status: 'pending',
  startedOn: new Date(),
  endedOn: undefined,
  error: undefined,
});

import { useSelector, useDispatch } from 'react-redux';
import { Link, ThemeProvider } from '@mui/material';
import { isImage } from '@helpers/tdoHelper';
import { I18nTranslate } from '@common/i18n';
import Dropzone from '@cbsa-components/Dropzone';
import AppWrapper from '@cbsa-components/AppWrapper';
import Sidebar from '@cbsa-components/AddMedia/Sidebar';
import ArrowBackIos from '@mui/icons-material/ArrowBackIos';
import MediaLibrary from '@cbsa-components/AddMedia/MediaLibrary';
import { componentSelectors, componentActions } from './reduxHelpers';
import RouteLoadingScreen from '@common-components/RouteLoadingScreen';
import { useStyles } from './styles';
import { defaultTheme } from '@cbsa/styles/materialThemes';
import { ValueOf } from 'ts-essentials';

const AddMediaContent = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const { tdos, loaders, caseDetails, selectedTdoMedia, selectedTdoImages } =
    useSelector(componentSelectors);
  const { treeObjectId: caseId, name } = caseDetails || {};
  const { isLoadingCaseDetails, isLoadingCaseTdosInitial } = loaders;
  const [tdoImages, tdoMedia] = Object.values(tdos).reduce<
    [ValueOf<typeof tdos>[], ValueOf<typeof tdos>[]]
  >(
    ([i, m], tdo) => (isImage(tdo) ? [[...i, tdo], m] : [i, [...m, tdo]]),
    [[], []]
  );

  return isLoadingCaseDetails ? (
    <RouteLoadingScreen delayMs={500} />
  ) : (
    <AppWrapper title={name ?? ''}>
      <div className={classes.topBar}>
        <div className={'Back'}>
          <Link className={'Link'} href="/" underline="none">
            <ArrowBackIos className={classes.primaryColor} />
            <div className={'Text'}>
              {I18nTranslate.TranslateMessage('back')}
            </div>
          </Link>
        </div>
      </div>
      <div className={classes.content}>
        <div className={'Grid'}>
          <div className={'Dropzone'}>
            <Dropzone
              acceptedTypes={{
                'image/*': ['.png', '.gif', '.jpeg', '.jpg'],
                'video/*': ['.mp4', '.avi', '.wmv', '.ts', '.mov'],
              }}
              onUpload={(file: File) => {
                if (file && caseId) {
                  dispatch(
                    componentActions.uploadMedia({
                      file,
                      caseId,
                    })
                  );
                }
              }}
            />
          </div>
          <div className={'MediaLibraries'}>
            <MediaLibrary
              type="images"
              tdos={tdoImages}
              deleteTdo={(tdoId) => {
                dispatch(componentActions.deleteTdo(tdoId));
              }}
              toggleTdo={(tdoId) => {
                dispatch(componentActions.toggleTdoImage(tdoId));
              }}
              selectedTdos={selectedTdoImages}
              loadingTdos={isLoadingCaseTdosInitial}
            />
            <MediaLibrary
              type="video"
              tdos={tdoMedia}
              deleteTdo={(tdoId) => {
                dispatch(componentActions.deleteTdo(tdoId));
              }}
              toggleTdo={(tdoId) => {
                dispatch(componentActions.toggleTdoMedia(tdoId));
              }}
              selectedTdos={selectedTdoMedia}
              loadingTdos={isLoadingCaseTdosInitial}
            />
          </div>
        </div>
        <Sidebar />
      </div>
    </AppWrapper>
  );
};

const AddMedia = () => (
  <ThemeProvider theme={defaultTheme}>
    <AddMediaContent />
  </ThemeProvider>
);

export default AddMedia;

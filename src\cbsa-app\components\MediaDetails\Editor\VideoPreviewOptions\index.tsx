import { useMemo } from 'react';
import * as React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import { I18nTranslate } from '@common/i18n';

import { useStyles } from './styles';
import { componentSelectors, componentActions } from './reduxHelpers';
import { OVERLAY_PREVIEW_OPTIONS } from '@helpers/constants';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const VideoPreviewOptions = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const { overlayPreview, hasVideo } = useSelector(componentSelectors);

  const handleOverlayPreviewChange = useMemo(
    () => (e: React.ChangeEvent<HTMLInputElement>) => {
      dispatch(
        componentActions.onOverlayPreviewChange(
          (e.target.value as any) || 'outline'
        )
      );
    },
    [dispatch]
  );

  return hasVideo ? (
    <div data-testid="file-detail-preview" className={classes.container}>
      <span>
        {I18nTranslate.TranslateMessage('preview')}: <br />
        <small>{I18nTranslate.TranslateMessage('videoOnly')}</small>
      </span>
      <FormControl>
        <RadioGroup
          row
          aria-label="Overlay Preview"
          name="overlayPreview"
          value={overlayPreview}
          onChange={handleOverlayPreviewChange}
        >
          {OVERLAY_PREVIEW_OPTIONS.map((option) => (
            <FormControlLabel
              key={`overlay-preview-${option.value}`}
              value={option.value}
              control={<Radio color="primary" />}
              label={option.label}
            />
          ))}
        </RadioGroup>
      </FormControl>
    </div>
  ) : null;
};

// export default VideoPreviewOptions;

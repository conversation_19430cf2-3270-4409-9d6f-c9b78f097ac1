import { modules } from '@veritone/glc-redux';
import { all, cancel, fork, take } from 'typed-redux-saga/macro';
import {
  changeCaseStatus,
  changeCaseStatusSuccess,
  changeCaseStatusFailure,
} from './changeCaseStatus';
import {
  archiveCase,
  archiveCaseSuccess,
  archiveCaseFailure,
} from './archiveCase';
import { getCaseAuditLogs } from './auditLog';
import { deleteCase, deleteCaseSuccess, deleteCaseFailure } from './deleteCase';
import { deleteTdo } from './deleteTdo';
import {
  createExportTdo,
  startExportJob,
  createExportTdoFailure,
  startExportJobFailure,
} from './exportCase';
import { fetchCase, fetchCaseDetailsSuccess } from './fetchCase';
import {
  processCase,
  processCaseSuccess,
  processCaseFailure,
} from './processCase';
import { updateCaseName, updateCaseNameSuccess } from './updateCaseName';
import {
  dismissNotification,
  clearNotification,
  dismissNotificationFailure,
  clearNotificationFailure,
} from './updateNotification';

const {
  user: { LOGOUT },
} = modules;

export interface Action<P, M = any> {
  type: string;
  payload: P;
  meta: M;
}

export function* initAddMedia() {
  const tasks = yield* all([
    /* Initialization Handlers */
    fork(fetchCase),

    /* Action Handlers */
    fork(changeCaseStatus),
    fork(changeCaseStatusSuccess),
    fork(changeCaseStatusFailure),

    fork(archiveCase),
    fork(archiveCaseSuccess),
    fork(archiveCaseFailure),

    fork(getCaseAuditLogs),

    fork(deleteCase),
    fork(deleteCaseSuccess),
    fork(deleteCaseFailure),

    fork(deleteTdo),

    fork(createExportTdo),
    fork(startExportJob),
    fork(createExportTdoFailure),
    fork(startExportJobFailure),

    fork(processCase),
    fork(processCaseSuccess),
    fork(processCaseFailure),

    fork(updateCaseNameSuccess),
    fork(updateCaseName),

    fork(dismissNotification),
    fork(clearNotification),
    fork(dismissNotificationFailure),
    fork(clearNotificationFailure),
    fork(fetchCaseDetailsSuccess),
  ]);

  yield* take(LOGOUT);

  for (const task of tasks) {
    yield* cancel(task);
  }
}

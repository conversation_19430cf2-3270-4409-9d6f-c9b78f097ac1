import {
  reducer as addMediaReducer,
  namespace as addMediaNamespace,
} from '@cbsa-modules/addMedia';
import {
  reducer as appWrapperReducer,
  namespace as appWrapperNamespace,
} from '@cbsa-modules/appWrapper';
import {
  reducer as mainPageReducer,
  namespace as mainPageNamespace,
} from '@cbsa-modules/mainPage';

export const cbsaReducers = {
  [addMediaNamespace]: addMediaReducer,
  [appWrapperNamespace]: appWrapperReducer,
  [mainPageNamespace]: mainPageReducer,
};

import { LockCheckResponse } from '../model/responses';
import { RequestHeader } from '../model/requests';
import { Messages } from '../errors/messages'
import { Logger } from '../logger';
import { getFilesByCaseIdAdapter } from './getFilesByCaseId';
import { checkTdoIsLockedAdapter } from './checkTdoIsLocked';
import { CaseId } from '../model/brands';

export const checkCaseLockAdapter = async (
  headers: RequestHeader,
  caseId: CaseId,
) => {
  const defaultLimit = 1000;
  const defaultOffset = 0;
  const result: LockCheckResponse = { isFailed: false, isLocked: false, isJobRunning: false };
  const activeJobStatus = [ "running", "pending" ];

  const getFiles = async(offset: number): Promise<LockCheckResponse> => {
    try {
      const res = await getFilesByCaseIdAdapter(headers, { caseId, limit: defaultLimit, offset});
      if (res.isFailed) {
        result.isFailed = true;
        return result;
      }
      if (!(res.tdos.length  > 0 )) return result;
      if (res.tdos?.length > 0) {
        for (const tdo of res.tdos) {
          const status = tdo.jobs?.records?.[0]?.status;
          if(status && activeJobStatus.includes(status)) {
            result.isJobRunning = true;
            return result;
          } else {
            const tdoIsLocked = await checkTdoIsLockedAdapter(headers, tdo.id);
            if (tdoIsLocked) {
              result.isLocked = true;
              return result;
            }
          }
        }
        if(res.tdos?.length === defaultLimit)    {
          return await getFiles(offset + defaultLimit);
        }
      }
    } catch (err) {
      result.isFailed = true;
      Logger.error(Messages.checkCaseLockFail + JSON.stringify(err));
    }
    return result;
  }
  return await getFiles(defaultOffset);
};

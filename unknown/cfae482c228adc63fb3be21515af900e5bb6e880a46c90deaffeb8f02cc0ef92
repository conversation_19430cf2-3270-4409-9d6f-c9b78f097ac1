import { markWorkerAction } from '@utils';
import { includes } from 'lodash';
import { processEngineRequestWithOverlayAction } from '@common-modules/engines/optical-tracking';
import {
  actionChangeOverlay,
  FACE_HIGHLIGHT,
  FACES_SELECT_GROUP_FACES,
  NEW_OVERLAY,
  UPDATE_CURRENT_TIME_MEDIA_PLAYER,
  RESIZE_OVERLAY_SEGMENT,
  sprayPaintSaveAction,
  deleteDetectionOverlayAction,
  deleteDetectionInFrameAction,
  deleteUDROverlayAction,
  deleteUDRInFrameAction,
  deleteDetectionSegmentAction,
  deleteUDRSegmentAction,
  ChangeShapeRequest,
  DeleteSegmentPayload,
  actionChangeUDRsPolyAssetGroupSeriesItemSubmit,
  actionChangeUDRsPolyAssetGroupSeriesItem,
} from '@common-modules/mediaDetails';
import { CHANGE_SHAPE, GroupedBoundingPoly } from '@worker';
import { BoundingPolyRect } from '@common-modules/mediaDetails/models';
import { DETECTION_TYPES } from '@helpers/constants';

export const componentActions = {
  onProcessEngineRequestWithOverlayAction:
    processEngineRequestWithOverlayAction,
  onUpdateCurrentTime: (currentPosition: number) =>
    UPDATE_CURRENT_TIME_MEDIA_PLAYER({ currentPosition }),
  onDeleteBoundingBox: (boundingBox: GroupedBoundingPoly) => {
    if (includes(DETECTION_TYPES, boundingBox.type)) {
      return deleteDetectionOverlayAction(boundingBox);
    } else {
      return deleteUDROverlayAction(boundingBox);
    }
  },
  onDeleteBoundingBoxInFrame: (
    boundingBox: GroupedBoundingPoly,
    timeMs: number
  ) => {
    if (includes(DETECTION_TYPES, boundingBox.type)) {
      return deleteDetectionInFrameAction(boundingBox, timeMs);
    } else {
      return deleteUDRInFrameAction(boundingBox, timeMs);
    }
  },
  onDeleteSegment: (payload: DeleteSegmentPayload) => {
    const type = payload.type;
    if (includes(DETECTION_TYPES, type)) {
      return deleteDetectionSegmentAction(payload);
    } else {
      return deleteUDRSegmentAction(payload);
    }
  },
  onAddBoundingBox: (
    payload: {
      boundingPoly: BoundingPolyRect;
      id: string;
      groupId: string;
    },
    currentPosition: number,
    opts: { shift: boolean } = { shift: false }
  ) =>
    markWorkerAction(
      NEW_OVERLAY({
        ...payload,
        currentPosition,
        ...opts,
      })
    ),
  actionChangeUDRsPolyAssetGroupSeriesItem:
    actionChangeUDRsPolyAssetGroupSeriesItem,
  actionChangeUDRsPolyAssetGroupSeriesItemSubmit:
    actionChangeUDRsPolyAssetGroupSeriesItemSubmit,
  onChangeOverlay: actionChangeOverlay,
  onSelectGroupFaces: (payload: GroupedBoundingPoly) =>
    FACES_SELECT_GROUP_FACES({ groupId: payload.groupId }),
  onFaceHighlight: (payload: GroupedBoundingPoly) =>
    FACE_HIGHLIGHT({
      id: payload.id,
      timeMs: payload.startTimeMs,
      type: payload.type,
      groupId: payload.groupId,
    }),
  onResizeBoundingBoxSegment: (boundingBox: GroupedBoundingPoly) =>
    markWorkerAction(RESIZE_OVERLAY_SEGMENT({ boundingBox })),

  onSprayPaintSave: (payload: {
    id: string;
    groupId: string;
    series: { boundingPoly: BoundingPolyRect; time: number }[];
  }) => sprayPaintSaveAction(payload),
  onBoxShapeChange: (payload: ChangeShapeRequest) =>
    markWorkerAction(CHANGE_SHAPE(payload)),
};

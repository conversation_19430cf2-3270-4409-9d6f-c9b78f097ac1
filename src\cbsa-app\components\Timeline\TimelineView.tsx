import { useEffect, useState } from 'react';

import * as styles from './styles.scss';

import Playhead from './Playhead';
import PositionBar from './PositionBar';
import { TimelinePropTypes } from './TimelinePropTypes';
import Timelines from './Timelines';

const Timeline = ({
  audiowaves,
  mediaDuration,
  currentTime,
  progress,
  detectionCollections,
  udrCollection,
  selectedPolys,
  transcriptRedactions,
  selectedTimeSlices,
  udrAsset,
  transcriptionList,
  globalSettings,
  highlightedOverlay,
  selectedUDRGroupId,

  onSeekMedia,
  onUnredactSlice,
  onRedactSlice,
  onSelectSlice,
  onDeselectAll,
  onSetSelectedUDRGroup,
  onChangeUDRGroupLabel,
  onChangeUDR,
  onChangeUDRSubmit,
  onUDRSelect,
  onSetFaceHighlight,
}: TimelinePropTypes) => {
  const minWindowMs = 0;
  const maxWindowMs = Math.ceil(mediaDuration * 1000);
  const [startWindowMs, onSetStart] = useState(minWindowMs);
  const [stopWindowMs, onSetStop] = useState(maxWindowMs);

  // Hack to redraw timeline when returning to Results tab.
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => onSetStop(maxWindowMs - 0.0001), []);

  // keep timeline centered-ish on currentTime at current interval
  useEffect(() => {
    const currentInterval = stopWindowMs - startWindowMs;
    const TIMELINE_START_BUFFER_PERCENTAGE = 0.1;
    const TIMELINE_STOP_BUFFER_PERCENTAGE = 0.05;
    const startCapBufferSize = Math.floor(
      TIMELINE_START_BUFFER_PERCENTAGE * currentInterval
    );
    const endCapBufferSize = Math.floor(
      TIMELINE_STOP_BUFFER_PERCENTAGE * currentInterval
    );

    const softStartMs = startWindowMs + startCapBufferSize;
    const softStopMs = stopWindowMs - endCapBufferSize;

    // if its inside the soft buffer, do nothing
    if (currentTime >= softStartMs && currentTime <= softStopMs) {
      return;
    }

    let newStartMs = startWindowMs;
    let newStopMs = stopWindowMs;

    // if the currentTime is outside the window completely, jump the window to
    // soft start buffer position on the currentTime
    if (currentTime < startWindowMs || currentTime > stopWindowMs) {
      newStartMs = Math.floor(currentTime - startCapBufferSize);
      newStopMs = newStartMs + currentInterval;
      if (newStartMs < 0) {
        newStartMs = 0;
        newStopMs = currentInterval;
      }
      if (newStopMs > maxWindowMs) {
        newStopMs = maxWindowMs;
        newStartMs = Math.max(0, newStopMs - currentInterval);
      }
      onSetStart(newStartMs);
      onSetStop(newStopMs);
      return;
    }

    // if current time jumps outside of the soft buffer, slide window so
    // currentTime stays inside the soft buffer
    if (currentTime < softStartMs) {
      // slide backwards
      newStartMs = currentTime - startCapBufferSize;
      if (newStartMs < 0) {
        newStartMs = 0;
      }
      newStopMs = newStartMs + currentInterval;
    }

    if (newStartMs !== startWindowMs || newStopMs !== stopWindowMs) {
      onSetStart(newStartMs);
      onSetStop(newStopMs);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentTime]);

  return (
    <div className={styles.timeline} data-testid="timeline-view-container">
      <PositionBar
        {...{
          currentTime,
          startWindowMs,
          stopWindowMs,
          minWindowMs,
          maxWindowMs,
          onSeekMedia,
        }}
      />
      <Playhead
        {...{
          minWindowMs,
          maxWindowMs,
          mediaDuration,
          startWindowMs,
          stopWindowMs,
          onSetStart,
          onSetStop,
          onSeekMedia,
        }}
      />

      <Timelines
        {...{
          audiowaves,
          mediaDuration,
          startWindowMs,
          stopWindowMs,
          progress,
          detectionCollections,
          udrCollection,
          selectedPolys,
          transcriptRedactions,
          selectedTimeSlices,
          udrAsset,
          globalSettings,
          highlightedOverlay,
          selectedUDRGroupId,

          onUnredactSlice,
          onRedactSlice,
          onSelectSlice,
          onDeselectAll,
          transcriptionList,
          onSetSelectedUDRGroup,
          onChangeUDRGroupLabel,
          onChangeUDR,
          onChangeUDRSubmit,
          onUDRSelect,
          onSetFaceHighlight,
        }}
      />
    </div>
  );
};

export default Timeline;

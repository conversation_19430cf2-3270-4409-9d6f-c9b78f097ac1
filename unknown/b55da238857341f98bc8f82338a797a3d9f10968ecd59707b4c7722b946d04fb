import { useMemo, useState } from 'react';
import Slider from '@mui/material/Slider';
import { useDispatch, useSelector } from 'react-redux';
import { useStyles } from './styles';
import { componentSelectors, sliderActions } from './reduxHelpers';
import { Controller, useFormContext } from 'react-hook-form';
import { SCALABLE_TYPES } from '@helpers/constants';
import { ArrayOrSingle } from 'ts-essentials';

export interface AutoScalingSliderProps {
  controlName: SCALABLE_TYPES;
  readonly initValue: number;
}

const AutoScalingSlider = (props: AutoScalingSliderProps) => {
  const { initValue, controlName } = props;
  const { control, setValue } = useFormContext();
  const { initialValues } = useSelector(componentSelectors);
  const classes = useStyles();
  const dispatch = useDispatch();
  const [sliderValue, setSliderValue] = useState(initValue ?? 0);

  const handleSliderChange = useMemo(
    () => (_event: Event, value: ArrayOrSingle<number>) => {
      if (value !== sliderValue) {
        setSliderValue(value as number);
        setValue(controlName, +value);
      }
    },
    [controlName, setValue, sliderValue]
  );

  const onChangeCommit = useMemo(
    () => () => {
      dispatch(
        sliderActions.onSaveGlobalSetting({
          ...initialValues,
          objectTypeEffects: {
            ...initialValues.objectTypeEffects,
            head: {
              ...initialValues.objectTypeEffects.head,
              scaling: sliderValue,
            },
          },
        })
      );
    },
    [dispatch, initialValues, sliderValue]
  );

  return (
    <Controller
      name={controlName}
      control={control}
      render={() => (
        <>
          <p style={{ fontSize: 12 }}>{sliderValue}%</p>
          <Slider
            data-testid="media-detail-slider"
            min={0}
            max={100}
            step={1}
            value={sliderValue}
            onChange={handleSliderChange}
            orientation={'vertical'}
            className={classes.slider}
            classes={{
              track: classes.track,
              thumb: classes.thumb,
            }}
            onChangeCommitted={onChangeCommit}
          />
        </>
      )}
    />
  );
};

export default AutoScalingSlider;

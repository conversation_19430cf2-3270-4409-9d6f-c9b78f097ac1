import { AddMediaStore } from '@cbsa-modules/addMedia';

export const defaultState: AddMediaStore = {
  caseDetails: null,
  tdos: {},
  sdos: {},
  notifications: [],
  selectedTdoImages: {},
  selectedTdoMedia: {},
  loaders: {
    isLoadingCaseDetails: true,
    isLoadingCaseTdosInitial: true,
    isUpdatingCaseName: false,
    isProcessingCase: false,
    isDeletingCase: false,
  },
  auditEvents: {},
  auditLogQueryPending: 0,
};

export const namespace = 'vtn-cbsa-state-addMedia';

.timelineRow {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  position: relative;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.timelineRow::-webkit-scrollbar {
  width: 0;
}

.virtualListContainer {
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    width: 0;
  }
}
%icon {
  z-index: 10;
  display: flex;
  margin-right: 2px;
  width: 30px;
  height: 32px;
  color: #5c5c5c;
  border-left: 10px solid transparent;
  background: #cecece;
  align-items: center;
  justify-content: left;
  padding: 8px;
}

.timelineContainer {
  display: flex;
  flex-direction: row;
  height: 32px;
  position: relative;
  margin-bottom: 2px;

  .icon {
    width: 30px;
    @extend %icon;
  }

  .iconContainer {
    white-space: nowrap;
    span img {
      filter: brightness(20%);
      opacity: 0.35;
      transform: translateY(3px);
    }
    label {
      padding: 8px;
      font-size: 10px;
    }
  }

  .arrowContainer {
    width: 100%;
    text-align: right;
    font-size: 12px;
  }

  .iconUdrGroup {
    @extend %icon;
    height: 30px;
    overflow-x: hidden;
    white-space: nowrap;
  }

  .iconUdrGroupSelected {
    @extend %icon;
    height: 30px;
    background: #1a4362;
    overflow-x: hidden;
    white-space: nowrap;
  }

  .groupLabel {
    font-size: 12px;
    padding-left: 25px;
    width: 100%;
    text-overflow: ellipsis;
    overflow-x: hidden;
  }

  .timeline {
    overflow: hidden;
    display: flex;
    flex: 1;
    background: #f2f2f2;
    > div {
      display: flex;
      flex: 1;
      overflow: hidden;
    }
  }

  .progress {
    display: block;
    position: absolute;
    background: url(resources/images/loading-stripes.svg);
    right: 0;
    width: 100%;
    height: 32px;
  }
}

.shorterTimelineContainer {
  @extend .timelineContainer;
  height: 32px;
}

import { render, screen } from '@testing-library/react';
import { I18nProvider, LOCALES } from '@i18n';
import configureStore from 'redux-mock-store';
import { Provider } from 'react-redux';

import FacesWrapperTab from './index';
import { namespace } from '@common-modules/mediaDetails';

const mockStore = configureStore();

describe('React testing library test', () => {
  it('renders correctly FacesWrapperTab with ClustersResultsTab', () => {
    const initState = {
      [namespace]: {
        tdo: {
          tasks: {
            records: [
              {
                engineId: '1',
                status: 'failed',
                engine: {
                  categoryId: '1',
                },
              },
              {
                engineId: '1',
                status: 'complete',
                engine: {
                  categoryId: '1',
                },
              },
              {
                engineId: '1',
                status: 'running',
                engine: {
                  categoryId: '1',
                },
              },
            ],
          },
        },
        clusterList: [
          {
            id: 'string',
            type: 'head',
            picUri: 'string',
            groups: [
              {
                id: 'string',
                type: 'head',
                startTimeMs: 0,
                stopTimeMs: 10,
                numAutoInterpolations: 1,
                isManualInterpolation: true,
                subsegmentIds: [],
                objectIds: [],
              },
            ],
          },
        ],
        sortPolyGroupsBy: {
          column: 'startTimeMs',
          direction: 'asc',
        },
        selectedPolyGroups: {},
        udrsState: { localUDRAsset: {} },
        dataFetchedForDetectionType: {
          laptop: false,
          licensePlate: false,
          head: false,
        },
      },
      config: {
        detectionCategory: '1',
      },
    };
    const store = mockStore(initState);
    const { asFragment } = render(
      <Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <FacesWrapperTab />
        </I18nProvider>
      </Provider>
    );
    expect(asFragment()).toMatchSnapshot();
    expect(screen.getByTestId('cluster-results-tab-view')).toBeInTheDocument();
    expect(
      screen.queryByTestId('faces-error-state-tab')
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('engine-processing-root')
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('faces-null-state-tab')
    ).not.toBeInTheDocument();
  });

  it('renders correctly FacesWrapperTab with EngineProcessing', () => {
    const initState = {
      [namespace]: {
        tdo: {
          tasks: {
            records: [
              {
                engineId: '1',
                status: 'failed',
                engine: {
                  categoryId: '1',
                },
              },
              {
                engineId: '1',
                status: 'complete',
                engine: {
                  categoryId: '1',
                },
              },
              {
                engineId: '1',
                status: 'running',
                engine: {
                  categoryId: '1',
                },
              },
            ],
          },
        },
        clusterList: [],
        sortPolyGroupsBy: {
          column: 'startTimeMs',
          direction: 'asc',
        },
        selectedPolyGroups: {},
      },
      config: {
        detectionCategory: '1',
      },
    };
    const store = mockStore(initState);
    const { asFragment } = render(
      <Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <FacesWrapperTab />
        </I18nProvider>
      </Provider>
    );
    expect(asFragment()).toMatchSnapshot();
    expect(
      screen.queryByTestId('cluster-results-tab-view')
    ).not.toBeInTheDocument();
    expect(screen.getByTestId('engine-processing-root')).toBeInTheDocument();
    expect(
      screen.queryByTestId('faces-error-state-tab')
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('faces-null-state-tab')
    ).not.toBeInTheDocument();
  });

  it('renders correctly FacesWrapperTab with TabErrorState', () => {
    const initState = {
      [namespace]: {
        tdo: {
          tasks: {
            records: [
              {
                engineId: '1',
                status: 'failed',
                engine: {
                  categoryId: '1',
                },
              },
              {
                engineId: '1',
                status: 'complete',
                engine: {
                  categoryId: '1',
                },
              },
            ],
          },
        },
        clusterList: [],
        sortPolyGroupsBy: {
          column: 'startTimeMs',
          direction: 'asc',
        },
        selectedPolyGroups: {},
      },
      config: {
        detectionCategory: '1',
      },
    };
    const store = mockStore(initState);
    const { asFragment } = render(
      <Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <FacesWrapperTab />
        </I18nProvider>
      </Provider>
    );
    expect(asFragment()).toMatchSnapshot();
    expect(
      screen.queryByTestId('cluster-results-tab-view')
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('engine-processing-root')
    ).not.toBeInTheDocument();
    expect(screen.getByTestId('faces-error-state-tab')).toBeInTheDocument();
    expect(
      screen.queryByTestId('faces-null-state-tab')
    ).not.toBeInTheDocument();
  });

  it('renders correctly FacesWrapperTab with TabNullState', () => {
    const initState = {
      [namespace]: {
        tdo: {
          tasks: {
            records: [
              {
                engineId: '1',
                status: 'complete',
                engine: {
                  categoryId: '1',
                },
              },
            ],
          },
        },
        clusterList: [],
        sortPolyGroupsBy: {
          column: 'startTimeMs',
          direction: 'asc',
        },
        selectedPolyGroups: {},
      },
      config: {
        detectionCategory: '1',
      },
    };
    const store = mockStore(initState);
    const { asFragment } = render(
      <Provider store={store}>
        <I18nProvider language={LOCALES.ENGLISH}>
          <FacesWrapperTab />
        </I18nProvider>
      </Provider>
    );
    expect(asFragment()).toMatchSnapshot();
    expect(
      screen.queryByTestId('cluster-results-tab-view')
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('engine-processing-root')
    ).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('faces-error-state-tab')
    ).not.toBeInTheDocument();
    expect(screen.getByTestId('faces-null-state-tab')).toBeInTheDocument();
  });
});

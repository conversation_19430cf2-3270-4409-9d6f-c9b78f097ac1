export interface Thumbnail {
  imageHeight: number;
  imageWidth: number;
  height: number;
  positionX: number;
  positionY: number;
  startTime: number;
  duration: number;
  uris: string[];
  width: number;
  sprite: boolean;
}

export interface ThumbnailTrack {
  id: number;
  active: boolean;
  type: string;
  bandwidth: number;
  language: string;
  label: string;
  kind: string;
  width: number;
  height: number;
  frameRate: number;
  pixelAspectRatio: string;
  hdr: string;
  mimeType: string;
  audioMimeType: string;
  videoMimeType: string;
  codecs: string;
  audioCodec: string;
  videoCodec: string;
  primary: boolean;
  roles: string[];
  audioRoles: string[];
  forced: boolean;
  videoId: number;
  audioId: number;
  channelsCount: number;
  audioSamplingRate: number;
  tilesLayout: string;
  spatialAudio: boolean;
  audioBandwidth: number;
  videoBandwidth: number;
  originalVideoId: string;
  originalAudioId: string;
  originalTextId: string;
  originalImageId: string;
}

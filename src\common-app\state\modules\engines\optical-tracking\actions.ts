import { TDOId } from '@common-modules/universal/models/Brands';
import { markWorkerAction } from '@utils';
import {
  CreateEngineJobRequest,
  ProcessEngineRequest,
  ProcessEngineRequestWithOverlay,
} from './models/services';
import { createAction } from '@reduxjs/toolkit';
import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import {
  CancelJobServiceResponse,
  CheckJobStatusServiceResponse,
} from './services';
import { Point } from '../../mediaDetails/models';

export const NOOP = 'noop';

export const PROCESS_ENGINE_REQUEST_OVERLAY =
  createAction<ProcessEngineRequestWithOverlay>(
    'vtn/redact/engine/optical-tracking/PROCESS_ENGINE_REQUEST_OVERLAY'
  );

/**
 * Begins the process of starting an Optical Tracking engine job.
 * Includes polling and loading results.
 */

export const PROCESS_ENGINE_REQUEST = createAction<ProcessEngineRequest>(
  'vtn/redact/engine/optical-tracking/PROCESS_ENGINE_REQUEST'
);
export const PROCESS_ENGINE_REQUEST_SUCCESS = createAction<{
  jobId: string;
  tdoId: TDOId;
}>('vtn/redact/engine/optical-tracking/PROCESS_ENGINE_REQUEST_SUCCESS');
export const PROCESS_ENGINE_REQUEST_FAILURE = createAction(
  'vtn/redact/engine/optical-tracking/PROCESS_ENGINE_REQUEST_FAILURE',
  (tdoId: TDOId, jobId: string, errorMsg: string) => ({
    payload: {
      tdoId,
      jobId,
      errorMsg,
    },
    error: true,
  })
);

/**
 *
 * @param { ProcessEngineRequestWithOverlay } payload
 */
export const processEngineRequestWithOverlayAction = (
  payload: ProcessEngineRequestWithOverlay
) => PROCESS_ENGINE_REQUEST_OVERLAY(payload);

/**
 *
 * @param { ProcessEngineRequest } payload
 */
export const processEngineRequestAction = (payload: ProcessEngineRequest) =>
  PROCESS_ENGINE_REQUEST(payload);

export const processEngineRequestSuccessAction = ({
  tdoId,
  jobId,
}: {
  jobId: string;
  tdoId: TDOId;
}) => PROCESS_ENGINE_REQUEST_SUCCESS({ tdoId, jobId });

export const processEngineRequestFailureAction = ({
  tdoId,
  jobId,
  errorMsg,
}: {
  tdoId: TDOId;
  jobId: string;
  errorMsg: string;
}) => PROCESS_ENGINE_REQUEST_FAILURE(tdoId, jobId, errorMsg);

/**
 * Create engine job actions.
 */

export const CREATE_ENGINE_JOB = createAction<CreateEngineJobRequest>(
  'vtn/redact/engine/optical-tracking/CREATE_ENGINE_JOB'
);
export const CREATE_ENGINE_JOB_SUCCESS = createGraphQLSuccessAction<
  {
    id: string;
    createJob: {
      id: string;
      targetId: TDOId;
      status: string;
    };
    createJobPayload: {
      id: string;
      overlay: string;
      timeMs: number;
      trackBack: boolean;
      trackForward: boolean;
      isUndo: boolean;
    };
  },
  {
    readonly input: {
      readonly targetId: string;
      readonly isReprocessJob: boolean;
      readonly tasks: ReadonlyArray<{
        readonly engineId: string;
        readonly payload: {
          readonly backTrackFrameLimit: number;
          readonly boundingPolys: ReadonlyArray<{
            readonly boundingPoly: ReadonlyArray<Point>;
            readonly timeMs: number;
            readonly parentUdrId: string;
          }>;
        };
      }>;
    };
  }
>('vtn/redact/engine/optical-tracking/CREATE_ENGINE_JOB_SUCCESS');
export const CREATE_ENGINE_JOB_FAILURE = createGraphQLFailureAction<
  any,
  {
    id: TDOId;
    tdoName: string;
  },
  true
>('vtn/redact/engine/optical-tracking/CREATE_ENGINE_JOB_FAILURE');

/**
 * @param { CreateEngineJobRequest } payload
 */
export const createEngineJobAction = (payload: CreateEngineJobRequest) =>
  markWorkerAction(CREATE_ENGINE_JOB(payload));

/**
 * Check job status actions.
 */

export const CHECK_JOB_STATUS = createAction<{
  jobId: string;
  tdoId: TDOId;
}>('vtn/redact/engine/optical-tracking/CHECK_JOB_STATUS');
export const CHECK_JOB_STATUS_SUCCESS =
  createGraphQLSuccessAction<CheckJobStatusServiceResponse>(
    'vtn/redact/engine/optical-tracking/CHECK_JOB_STATUS_SUCCESS'
  );
export const CHECK_JOB_STATUS_FAILURE = createGraphQLFailureAction(
  'vtn/redact/engine/optical-tracking/CHECK_JOB_STATUS_FAILURE'
);

export const checkJobStatusAction = ({
  tdoId,
  jobId,
}: {
  jobId: string;
  tdoId: TDOId;
}) => CHECK_JOB_STATUS({ tdoId, jobId });

/**
 * Start and stop polling job status actions.
 */

export const START_POLLING_ENGINE_RESULTS = createAction<{
  jobId: string;
  tdoId: TDOId;
}>('vtn/redact/engine/optical-tracking/START_POLLING_ENGINE_RESULTS');
export const STOP_POLLING_ENGINE_RESULTS = createAction<{
  tdoId: TDOId;
  jobId: string;
}>('vtn/redact/engine/optical-tracking/STOP_POLLING_ENGINE_RESULTS');

export const startPollingEngineResultsAction = ({
  tdoId,
  jobId,
}: {
  jobId: string;
  tdoId: TDOId;
}) => START_POLLING_ENGINE_RESULTS({ tdoId, jobId });

export const stopPollingEngineResultsAction = ({
  tdoId,
  jobId,
}: {
  tdoId: TDOId;
  jobId: string;
}) => STOP_POLLING_ENGINE_RESULTS({ tdoId, jobId });

/**
 * Actions to get the metadata for engine results.
 */

export const QUERY_ENGINE_RESULTS_RECORD = createAction<{
  tdoId: TDOId;
}>('vtn/redact/engine/optical-tracking/QUERY_ENGINE_RESULTS_RECORD');
export const QUERY_ENGINE_RESULTS_RECORD_SUCCESS =
  'vtn/redact/engine/optical-tracking/QUERY_ENGINE_RESULTS_RECORD_SUCCESS';
export const QUERY_ENGINE_RESULTS_RECORD_FAILURE =
  'vtn/redact/engine/optical-tracking/QUERY_ENGINE_RESULTS_RECORD_FAILURE';

export const queryEngineResultsRecordAction = ({ tdoId }: { tdoId: TDOId }) =>
  QUERY_ENGINE_RESULTS_RECORD({ tdoId });

/**
 * Actions to actually load the result data.
 */

export const GET_ENGINE_RESULTS_FROM_URI = createAction<{
  signedUri: string;
}>('vtn/redact/engine/optical-tracking/GET_ENGINE_RESULTS_FROM_URI');
export const GET_ENGINE_RESULTS_FROM_URI_SUCCESS =
  'vtn/redact/engine/optical-tracking/GET_ENGINE_RESULTS_FROM_URI_SUCCESS';
export const GET_ENGINE_RESULTS_FROM_URI_FAILURE =
  'vtn/redact/engine/optical-tracking/GET_ENGINE_RESULTS_FROM_URI_FAILURE';

export const getEngineResultsFromURIAction = ({
  signedUri,
}: {
  signedUri: string;
}) => GET_ENGINE_RESULTS_FROM_URI({ signedUri });

/**
 * Cancel a job actions.
 */

export const CANCEL_JOB = createAction<{
  id: string;
  jobId: string;
  tdoId: TDOId;
  overlay?: string;
  isUndo?: boolean;
}>('vtn/redact/engine/optical-tracking/CANCEL_JOB');
export const CANCEL_JOB_SUCCESS =
  createGraphQLSuccessAction<CancelJobServiceResponse>(
    'vtn/redact/engine/optical-tracking/CANCEL_JOB_SUCCESS'
  );
export const CANCEL_JOB_FAILURE = createGraphQLFailureAction(
  'vtn/redact/engine/optical-tracking/CANCEL_JOB_FAILURE'
);

export const cancelJobAction = ({
  id,
  tdoId,
  jobId,
}: {
  id: string;
  jobId: string;
  tdoId: TDOId;
}) => CANCEL_JOB({ id, tdoId, jobId });

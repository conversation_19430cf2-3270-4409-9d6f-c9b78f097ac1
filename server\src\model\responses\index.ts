import { FolderId, TDOId, TreeObjectId } from "../brands";

export interface ValidateTokenResponse {
  validateToken: {
    token: string;
  };
}

export interface IngestedFileStatusResponse {
  temporalDataObject: {
    id: TDOId;
    name: string;
    // primaryAsset: {
    //   signedUri: string;
    // };
    jobs: {
      records: Array<{ id: string; status: string }>;
    };
  };
}

export interface LastestSchemaResponse {
  dataRegistry: {
    publishedSchema: {
      id: string;
    };
  };
}

export interface CreateFolderResponse {
  createFolder: {
    id: FolderId;
    name: string;
    treeObjectId: TreeObjectId;
  };
}

export interface RootFoldersResponse {
  rootFolders: Array<RootFolder>;
}

export interface RootFolder {
  id: FolderId;
  ownerId?: string;
}

export interface CreateRequestSDOResponse {
  createStructuredData: {
    id: string;
  };
}

export interface CreateContentTemplateResponse {
  createFolderContentTemplate: {
    id: string;
  };
}

export interface CreateRootFolderResponse {
  createRootFolder: {
    id: FolderId;
    name: string;
  }
}

export interface CheckFolderExistsResponse {
  folder: {
    id: FolderId;
    contentTemplates: Array<{
      id: string;
    }>
    folderPath: Array<{
      id: string;
      parent: {
        id: FolderId;
      }
    }>
  }
}

export interface CheckNameExistsResponse {
  folder: {
    id: FolderId;
    childFolders: {
      count: number;
      records: Array<{
        id: FolderId;
        name: string;
        parent: {
          id: string;
        }
      }>
    }
  }
}

export interface CreateTdoResponse {
  createTDO: {
    id: TDOId;
  }
}

export interface IngestMediaResponse {
  requestId: string;
  tdos: Array<{id: string}>
}

export interface IngestJobResponse {
  createJob: {
    id: string;
    targetId: TDOId;
    status: string;
  }
}

export interface GetTokenUserInfoResponse {
  userId: string;
  email: string;
}

export interface IngestMediaResponse {
  id: string;
  targetId: TDOId;
  status: string;
}

export interface DeleteFolderResponse {
  deleteFolder ?: {
    id: string;
  };
  // response?: ErrorResponse
}

// export interface ErrorResponse {
//   response : {
//     errors: Array<{
//       message: string;
//       name: string;
//     }>
//   }
// }

export interface RequestCaseFileListResponse {
  case:  {
    name: string;
    files: {
      records: Array< {
        id: string;
        name: string;
        // primaryAsset: {
        //   signedUri: string;
        // }
      }>
    }
  }
}

export interface RedactedMediaResponse {
  temporalDataObject:  {
    redactedMediaAssets?: {
      records: Array<{
        type: string,
        signedUri: string,
        createdDateTime: string
      }>
    },
    auditLogAssets?: {
      records: Array<{
        type: string,
        signedUri: string,
        createdDateTime: string
      }>
    }
  }
}

export interface DeleteFileResponse {
  deleteTDO?: {
    id: TDOId;
    message: string;
  };
  // response: ErrorResponse;
}

export interface GetSdoResponse {
  folder?: {
    id: FolderId;
    treeObjectId: TreeObjectId;
    contentTemplates: Array<{
      sdoId: string;
    }>
  };
}

export interface DeleteSdoResponse {
  deleteStructuredData?: {
    id: string
  }
}

export interface  MediaFileDetailsResponse  {
  temporalDataObject:  {
    thumbnailUrl: string,
    // primaryAssets: {
    //   records: Array<{
    //     type: string,
    //     signedUri: string,
    //     createdDateTime: string
    //   }>
    // },
    redactedMediaAssets: {
      records: Array<{
        type: string,
        signedUri: string,
        createdDateTime: string
      }>
    },
    auditLogAssets: {
      records: Array<{
        type: string,
        signedUri: string,
        createdDateTime: string
      }>
    },
    redactedTranscriptAssets: {
      records: Array<{
        type: string,
        signedUri: string,
        createdDateTime: string
      }>
    },
    details: {
      tags?: Array<{redactionStatus?: string, value?: string} | null>
    }
  }
  // response: ErrorResponse;
}

export interface CreateJobSuccess {
    id: string;
    targetId: string;
    status: string;
}

export interface CreateJobFail {
  targetId: string;
  status: string;
}
export interface GetUserInfoResponse {
  me: {
    email: string;
  }
}
export interface AppConfig {
  configType: string;
  configKey: string;
  value: string | null;
  valueJSON: Record<string, unknown> | null;
}
export interface GetAppConfigsResponse {
  applicationConfig: {
    records: AppConfig[];
  }
}

export interface FilesByTreeObejectIdResponse {
  searchMedia: {
    jsondata: {
      results: Array<{
        recording: {
          recordingId: string;
        }
      }>
    };
  }
}

export interface GetChildFoldersResponse {
  folder: { childFolders: { records: Array<ChildFolderResponse> } };
}
export interface ChildFolderResponse {
   id: FolderId;
   contentTemplates: Array<{
    id: string;
  }>
}
export interface FileResponse {
  id: TDOId ;
  jobs: { records: Array<{ status: string }> };
}

export interface GetFilesResponse {
  folder: {
    childTDOs: {
      records: Array<FileResponse>;
    }
  }
}

export interface LockCheckResponse {
  isFailed: boolean,
  isLocked: boolean,
  isJobRunning: boolean
}

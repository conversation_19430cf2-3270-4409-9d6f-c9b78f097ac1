### Steps to update existing schema using core-graphql queries
----

1. Update Redact File Status Schema
```
mutation UpdateRedactFileStatusSchema {
  upsertSchemaDraft(input: {
    dataRegistryId: "008f68f6-cb62-4857-bd11-bb23932a7eb8"
    majorVersion: 1,
    schema: {     
      required: [
        "tdoId",
        "status"
      ],
      properties: {
        tdoId: {
          type: "string"
        },
        status: {
          enum: [
            "Draft",
            "Pending Review",
            "Complete"
          ],
          type: "string"
        }
      }
    }
  }) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}
```

2. Publish Schema Draft
```
# Note: Use the schema 'id' response from the 'UpdateRedactFileStatusSchema' mutation.
mutation publishFileStatusSchemaDraft($id: ID!) {
  updateSchemaState(input: {
    id: $id
    status: published
  }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```

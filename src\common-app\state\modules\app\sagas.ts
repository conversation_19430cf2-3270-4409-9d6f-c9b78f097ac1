import {
  all,
  fork,
  takeLatest,
  put,
  call,
  race,
  take,
  select,
  delay,
} from 'typed-redux-saga/macro';
import { find, isEmpty } from 'lodash';
import { modules } from '@veritone/glc-redux';
import { IN_SET_AUTH, IN_SET_CONFIG, AuthResponse } from '@worker';
import { redirect } from 'redux-first-router';

import * as Sen<PERSON> from '@sentry/browser';

import {
  ROUTE_AUTH,
  ROUTE_LOGOUT,
  selectRouteType,
  selectCurrentRoutePayload,
} from '../routing';
import { BOOT, bootFinished, boot, BOOT_FINISHED } from './index';
import { selectConfig } from '@common-modules/engines/selectors';
import { AnyAction } from '@reduxjs/toolkit';
import { Action } from '@redact/state/modules/casemedia/models';
import { queryApplicationConfigsService } from './services';

const {
  user: {
    fetchUser,
    fetchEnabledApps,
    FETCH_USER,
    <PERSON>ETCH_USER_SUCCESS,
    FETCH_USER_FAILURE,
    FETCH_USER_APPLICATIONS,
    FETCH_USER_APPLICATIONS_SUCCESS,
    FETCH_USER_APPLICATIONS_FAILURE,
    LOGOUT,
    LOGOUT_FAILURE,
    LOGOUT_SUCCESS,
    selectUser,
  },
  auth: { setOAuthToken, OAUTH_GRANT_FLOW_SUCCESS },
  config: { getConfig },
} = modules;

export const getAppStartupDependencies = function* () {
  // fetch stuff
  yield* all([
    put(fetchEnabledApps()),
    // ...other app dependencies
  ]);

  // wait for results
  const actions = [
    ...(yield* race([
      take(FETCH_USER_APPLICATIONS_SUCCESS),
      take([
        // requestError
        // TODO: Fix this deprecated type
        // eslint-disable-next-line @typescript-eslint/no-deprecated
        (a: AnyAction) => a.type === FETCH_USER_APPLICATIONS && !!a.error,
        // api error
        FETCH_USER_APPLICATIONS_FAILURE,
      ]),
    ])),
    // ...etc
  ];

  // fixme -- refactor FETCH_USER/FETCH_USER_APPLICATIONS in redux-common to thunk style
  // and graphql, then replace this ugly take block and use put.resolve

  const error = find(actions, { error: true });
  if (error) {
    console.error('there was an error', error);
  }
};

function* watchAppBoot() {
  yield* takeLatest(BOOT, watchAppBootHandle);
}

export function* watchAppBootHandle() {
  const config = yield* select(getConfig<Window['config']>);
  const user = yield* fetchUserWithStoredTokenOrCookie();

  // yield take(OUT_WORKER_READY);
  yield* put(IN_SET_CONFIG(config));

  if (user) {
    yield* put(IN_SET_AUTH(user));
    // login success with stored credentials or cookie
    yield* getAppStartupDependencies();
    yield* put(bootFinished());

    Sentry.setUser({
      id: user.userId,
      email: user.email ?? '',
    });
  } else {
    if (config.useOAuthGrant) {
      yield* redirectAndAwaitOAuthGrant();
    } else {
      yield* redirectToVeritoneInternalLogin();
    }
  }
}

export const redirectAndAwaitOAuthGrant = function* () {
  const routeType = yield* select(selectRouteType);
  const routePayload = yield* select(selectCurrentRoutePayload);

  if (routeType !== ROUTE_AUTH.type) {
    yield* put(
      ROUTE_AUTH({
        query: {
          nextType: routeType,
          nextPayload: !isEmpty(routePayload)
            ? JSON.stringify(routePayload)
            : undefined,
        },
      })
    );
  }

  yield* put(bootFinished());

  // retry boot after logging in
  yield* take(OAUTH_GRANT_FLOW_SUCCESS);
  yield* put(boot());
};

export const redirectToVeritoneInternalLogin = function* () {
  const config = yield* select(getConfig);
  if (config.loginRoute) {
    const loginRoute = new URL(config.loginRoute);
    if (loginRoute.hostname.match(/\.veritone\.com$/)) {
      const redirect = new URL(window.location.href);
      if (redirect.hostname.match(/\.veritone\.com$/)) {
        loginRoute.searchParams.set('redirect', redirect.href);
        window.location.href = loginRoute.href;
      } else {
        window.location.href = loginRoute.href;
      }
    }
  }
};

export const fetchUserWithStoredTokenOrCookie = function* () {
  const existingOAuthToken = yield* call(
    [localStorage, 'getItem'],
    'OAuthToken'
  );

  if (existingOAuthToken) {
    yield* put(setOAuthToken(existingOAuthToken));
  }

  yield* put(fetchUser());

  const { successAction } = yield* race({
    successAction: take<Action<AuthResponse>>(FETCH_USER_SUCCESS),
    _: take([
      // TODO: Fix deprecated type
      // eslint-disable-next-line @typescript-eslint/no-deprecated, @typescript-eslint/no-unsafe-return
      (a: AnyAction) => a.type === FETCH_USER && a.error,
      FETCH_USER_FAILURE,
    ]),
  });

  // todo: this could differentiate between auth error (expired token) and failure
  // (api error)
  return successAction ? successAction.payload : false;
};

function* storeTokenAfterSuccessfulOAuthGrant() {
  yield* takeLatest(
    OAUTH_GRANT_FLOW_SUCCESS,
    // TODO: Can we type the imported actions?
    function* ({ payload: { OAuthToken } }: any) {
      yield* call([localStorage, 'setItem'], 'OAuthToken', OAuthToken);
    }
  );
}

function* clearStoredTokenAfterLogout() {
  yield* takeLatest(LOGOUT, clearStoredTokenAfterLogoutHandle);
}

export function* clearStoredTokenAfterLogoutHandle() {
  const user = yield* select(selectUser);
  if (!isEmpty(user)) {
    yield* call([localStorage, 'removeItem'], 'OAuthToken');
    yield* put(redirect(ROUTE_LOGOUT()));
    yield* take([LOGOUT_FAILURE, LOGOUT_SUCCESS]);
    yield* delay(1000);
    const { loginRoute } = yield* select(selectConfig);
    try {
      const loginUrl = new URL(loginRoute);
      loginUrl.searchParams.set('redirect', window.location.origin);
      window.location.assign(loginUrl.href);
    } catch {
      console.warn('Warning: Login route is not a valid url');
    }
  }
}

function* watchAppBootFinished() {
  yield* takeLatest(BOOT_FINISHED, function* () {
    const config = yield* select(getConfig<Window['config']>);
    yield* put(
      queryApplicationConfigsService(
        config.veritoneAppId || '766e9916-9536-47e9-8dcb-dc225654bab3'
      )
    );
  });
}

export default function* auth() {
  yield* all([
    fork(watchAppBoot),
    fork(storeTokenAfterSuccessfulOAuthGrant),
    fork(clearStoredTokenAfterLogout),
    fork(watchAppBootFinished),
  ]);
}

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import ClearIcon from '@mui/icons-material/Clear';

import RequestQuoteForm from '../RequestQuoteForm';
import UpgradeSelection from '../UpgradeSelection';
import { UpgradeAccountModalPropTypes } from './UpgradeAccountModalPropTypes';

import * as styles from './styles.scss';

const UpgradeAccountModalView = ({
  isOpen,
  onClose,
  onExited,
  showRequestQuoteForm,
}: UpgradeAccountModalPropTypes) => (
  <Dialog open={isOpen} maxWidth="lg" TransitionProps={{ onExited }}>
    <IconButton
      aria-label="close-upgrade-account-modal"
      classes={{ root: styles.closeButton }}
      onClick={onClose}
      size="large"
    >
      <ClearIcon />
    </IconButton>
    <DialogTitle
      id="upgrade-account-title"
      classes={{ root: styles.upgradeAccountTitle }}
    >
      Upgrade Your Account
    </DialogTitle>
    <DialogContent>
      {!showRequestQuoteForm && <UpgradeSelection />}
      {showRequestQuoteForm && <RequestQuoteForm onSubmit={onClose} />}
    </DialogContent>
    {!showRequestQuoteForm && (
      <DialogActions classes={{ root: styles.upgradeActions }}>
        <Button
          color="primary"
          classes={{ textPrimary: styles.textPrimary }}
          onClick={onClose}
        >
          Cancel
        </Button>
      </DialogActions>
    )}
  </Dialog>
);

export default UpgradeAccountModalView;

import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Button,
  Grid2 as Grid,
  ThemeProvider,
  StyledEngineProvider,
  Paper,
  Typography,
} from '@mui/material';
import { CREATE_JOB_FACES_ACTION } from '@common-modules/facesTabModule';
import errorStateImage from '@resources/images/engine-error.svg';
import {
  selectTdo,
  selectDisableFacesButton,
} from '@common-modules/mediaDetails';
import { I18nTranslate } from '@common/i18n';

import { buttonTheme } from '@cbsa/styles/materialThemes';

import { useStyles } from './styles';
import { createSelector } from 'reselect';

const FacesErrorStateTab = () => {
  const { tdo, buttonDetectFaceDisable } = useSelector(componentSelectors);
  const dispatch = useDispatch();
  const classes = useStyles();
  const handleCreateJobs = useCallback(() => {
    const tdoId = tdo?.id;
    if (tdoId) {
      dispatch(componentActions.actionCreateJobFace(tdoId));
    }
  }, [dispatch, tdo]);

  return (
    <Paper
      className={classes.container}
      data-veritone-component="faces-error-state-tab"
      data-testid="faces-error-state-tab"
    >
      <Grid container className={classes.tabNameWrapper}>
        <Grid size={{ xs: 12 }} className={classes.tabName}>
          {I18nTranslate.TranslateMessage('objectDetection')}
        </Grid>
      </Grid>
      <Grid
        container
        alignItems="center"
        direction="column"
        className={classes.content}
      >
        <Grid>
          <img src={errorStateImage} className={classes.errorStateIcon} />
        </Grid>
        <Grid size={{ xs: 6 }} style={{ width: '292px', maxWidth: '292px' }}>
          <Typography
            align="center"
            variant="body2"
            color={'secondary'}
            className={classes.message}
          >
            {I18nTranslate.TranslateMessage('errorObjectDetectionRun')}
          </Typography>
        </Grid>
        <Grid>
          <StyledEngineProvider injectFirst>
            <ThemeProvider theme={buttonTheme}>
              <Button
                variant="contained"
                color={'primary'}
                className={classes.buttonDerp}
                onClick={handleCreateJobs}
                disabled={buttonDetectFaceDisable}
                data-veritone-element="face-error-state-rerun-button"
              >
                {I18nTranslate.TranslateMessage('reRunEngine')}
              </Button>
            </ThemeProvider>
          </StyledEngineProvider>
        </Grid>
      </Grid>
    </Paper>
  );
};

const componentSelectors = createSelector(
  selectTdo,
  selectDisableFacesButton,
  (tdo, buttonDetectFaceDisable) => ({
    tdo: tdo,
    buttonDetectFaceDisable: buttonDetectFaceDisable,
  })
);

const componentActions = {
  actionCreateJobFace: (targetId: string) =>
    CREATE_JOB_FACES_ACTION({ targetId }),
};

export default FacesErrorStateTab;

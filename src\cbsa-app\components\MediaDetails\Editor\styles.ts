import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles((_theme) => ({
  editorGrid: {
    height: '100%',
    minHeight: '100%',
    maxHeight: '100%',
    display: 'flex',
    flexDirection: 'column',
    gridTemplateColumns: 'minmax(0, 1fr) 480px',
    overflow: 'hidden',

    '@media (max-width: 1280px)': {
      gridTemplateColumns: 'auto 400px',
    },
  },

  container: {
    display: 'flex',
    maxHeight: '65vh',
  },

  mediaPlayer: {
    flex: 1,
  },

  previewOptions: {
    gridArea: 'previewOptions',
  },

  mainButtons: {
    gridArea: 'mainButtons',
    alignSelf: 'end',
  },

  iconButtonGroup: {
    alignItems: 'flex-start',
    zIndex: 100,
    marginRight: 20,
    marginTop: 52,

    '& img': {
      filter: 'invert(1)',
    },
  },

  timeline: {
    // grid-area: timeline,

    '&>div': {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
    },

    '& #timeline-auto-scaling-btn': {
      '& p': {
        color: 'black',
      },
    },
  },

  clusterList: {
    // grid-area: clusterList,
    position: 'relative',

    '&>div': {
      // position: absolute,
      // top: 0,
      // bottom: 0,
      // left: 0,
      // right: 0,
      overflow: 'hidden',
    },
  },
}));

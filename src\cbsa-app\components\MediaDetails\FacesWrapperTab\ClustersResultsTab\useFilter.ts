import { useMemo } from 'react';

import {
  ClusterItem,
  ClusterItemGroup,
  ClusterSegment,
  FilterParameters,
} from '@common-modules/mediaDetails/models';
// import { arrayHasContents } from '@common/shared/util';
// import { ElementOf } from 'ts-essentials';

import { convertToFilterParameterType, NonEmptyArray } from '@utils';

// const FILTERS = ['udr', 'laptop', 'poim', 'licensePlate', 'head'];

/**
// TODO This can be optimized
export const useFilter = (
  clusterList: ReadonlyArray<ClusterItem>,
  filter: FilterList
) =>
  useMemo(() => {
    // // filter when user select MI
    if (filter === 'manualInterp') {
      return clusterList.reduce<ClusterItem[]>((acc, cur) => {
        const tmp: ElementOf<ClusterItemGroup['segments']>[] = [];
        cur.groups[0].segments.forEach((item) => {
          if (item.isManualInterpolation) {
            tmp.push(item);
          }
        });
        if (arrayHasContents(tmp)) {
          // acc.push({ ...cur, segments: tmp });
          acc.push({ ...cur, groups: [{ ...cur.groups[0], segments: tmp }] });
        }
        return acc;
      }, []);
    } else if (FILTERS.includes(filter)) {
      return clusterList.reduce<ClusterItem[]>((acc, cur) => {
        if (filter === selectFilterType(cur.type)) {
          acc.push({ ...cur });
        }
        return acc;
      }, []);
    }
    return clusterList;
  }, [clusterList, filter]);

  */

export const useFilter = (
  clusterList: ReadonlyArray<ClusterItem>,
  filterParameters: FilterParameters
) =>
  useMemo(() => {
    // No clusters should be returned if filterList is empty
    // if (filterList.length === 0) {
    //   return [];
    // }
    // if (filterList.includes('manualInterp')) {
    //   return clusterList.reduce<ClusterItem[]>((acc, cur) => {
    //     const tmp: ElementOf<ClusterItem['groups']>[] = [];
    //     cur.groups.forEach((item) => {
    //       if (item.isManualInterpolation) {
    //         tmp.push(item);
    //       }
    //     });
    //     if (arrayHasContents(tmp)) {
    //       acc.push({ ...cur, groups: tmp });
    //     }
    //     return acc;
    //   }, []);
    // } else {

    /*
      return clusterList.filter((clusterItem) =>
        // check for any matches within the groups
        clusterItem.groups.find((group) => {
          let type = group.type;
          if (type === 'vehicle') {
            // TODO move this conversion to common location
            type = 'licensePlate';
          }
          return filterList.includes(type);
        })
      );
      */

    const filteredClusterList: ClusterItem[] = [];

    clusterList.forEach((clusterItem) => {
      // check for any matches within the groups
      const filteredGroups = clusterItem.groups.filter((group) => {
        const filterType = convertToFilterParameterType(group.type);
        if (!filterType) {
          return;
        }
        return filterParameters.show[filterType];
      });

      if (filteredGroups.length === 0) {
        return;
      }

      // if only 1 original done
      if (clusterItem.groups.length === 1) {
        filteredClusterList.push(clusterItem);
        return;
      }

      // need to filter the segments as well when orig cluster had multiple groups
      const filteredClusterItem = {
        ...clusterItem,
        groups: filteredGroups as NonEmptyArray<ClusterItemGroup>,
        segments: clusterItem.segments.filter((segment) => {
          const filterType = convertToFilterParameterType(segment.type);
          if (!filterType) {
            return;
          }
          return filterParameters.show[filterType];
        }) as NonEmptyArray<ClusterSegment>,
      };
      filteredClusterList.push(filteredClusterItem);
    });

    return filteredClusterList;
  }, [clusterList, filterParameters]);

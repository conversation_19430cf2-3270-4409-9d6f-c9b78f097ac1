import {
  BoundingPolyObjectNew,
  OverlayPosition,
  OverlayPropType,
  StagedBoundingBoxPosition,
} from './OverlayTypes';
import RndBox from './RndBox';
import memoize from 'memoize-one';
import { v4 as uuid } from 'uuid';
import RndBoxSimple from './RndBoxSimple';
import {
  convertPolyToScalable,
  DEFAULT_REDACTION_CONFIGURATION,
  DETECTION_TYPES,
  MANUAL_TRACKING_CHANGE_INTERVAL_MS,
  OBJECT_TYPE,
} from '@helpers/constants';
import { guid } from '@common/shared/util';
import useCallbackRef from '@helpers/hooks';
import { getMousePosition } from '../helpers/dom';
import { RndResizeCallback } from 'react-rnd/lib';
import withContextProps from './withContextProps';
import { DraggableEventHandler } from 'react-draggable';
import {
  percentagePolyToPixelXYWidthHeight,
  pixelXYWidthHeightToPercentagePoly,
} from './helpers';
import {
  BoundingPolyRect,
  PolyCenter,
} from '@common-modules/mediaDetails/models/BoundingPoly';
import {
  GlobalSettings,
  OverlayPreviewOptionsType,
  RedactionConfig,
  ShapeType,
} from '@common-modules/mediaDetails/models';
import { MouseEventHandler, useEffect, useState } from 'react';
import {
  GroupIdAndType,
  FACE_HIGHLIGHT_UNSELECT,
  actionSetDraggedOverlayId,
  selectDraggedOverlayId,
  setFaceHighlight,
  selectClusterMap,
  selectUdrClusterGroups,
  selectDetectionClusterGroups,
  actionLogAuditEvent,
} from '@common-modules/mediaDetails';
import { OverlayPositioningContext } from './OverlayPositioningProvider';
import {
  debounce,
  clamp,
  find,
  has,
  includes,
  isEmpty,
  omit,
  throttle,
} from 'lodash';
import TimeStampMenu from '@redact/components/MediaDetailsPage/Editor/MediaPlayer/TimeStampMenu';
import RedactionConfigMenu from '@redact/components/MediaDetailsPage/Editor/MediaPlayer/RedactionConfigMenu';
import AddRedactionCodeMenu from '@redact/components/MediaDetailsPage/Editor/MediaPlayer/AddRedactionCodeMenu';
import { getRedactedOverlayStyle } from '@redact/components/MediaDetailsPage/Editor/MediaPlayer/getOverlayStyles';
import { GroupedBoundingPoly } from '@common/web-worker';
import { IndividualRedactionCode } from '@redact/state/modules/redactionCodes/models';
import { useDispatch, useSelector } from 'react-redux';
import isEqual from 'fast-deep-equal';
import { produce } from 'immer';
import { findClusterGroupIds } from '@utils';
import ConfirmWithMergeGroupDialog from '@redact/components/MediaDetailsPage/Editor/MediaPlayer/Dialogs/ConfirmWithMergeGroupDialog';

const clampPoly = (poly: BoundingPolyRect) =>
  poly.map((point) => ({
    x: clamp(point.x, 0, 1),
    y: clamp(point.y, 0, 1),
  }));

const mapPolysToInternalFormat = memoize(
  (
    polys: Array<GroupedBoundingPoly>,
    width: number,
    height: number,
    objectTypeEffects?: GlobalSettings['objectTypeEffects']
  ) =>
    polys.map(({ boundingPoly, type, ...rest }): BoundingPolyObjectNew => {
      let scale = 0;
      if (objectTypeEffects) {
        const convertedType = convertPolyToScalable(type);
        scale = convertedType ? objectTypeEffects[convertedType].scaling : 0;
      }

      return {
        boundingPoly: percentagePolyToPixelXYWidthHeight(
          clampPoly(boundingPoly),
          width,
          height,
          scale
        ),
        type,
        ...rest,
      };
    }),
  isEqual
);

const typeMapping: Record<
  OBJECT_TYPE,
  keyof GlobalSettings['objectTypeEffects']
> = {
  udr: 'udr',
  licensePlate: 'plate',
  laptop: 'laptop',
  vehicle: 'vehicle',
  head: 'head',
  poim: 'head',
  card: 'card',
  notepad: 'notepad',
  person: 'person',
};

const DEFAULTS = {
  confirmAdd: 'Add',
  readOnly: false,
  addOnly: false,
  initialBoundingBoxPolys: [],
  showUnselectedBoundingPolySeries: [],
  toolBarOffset: 0,
  stagedBoundingBoxStyles: {},
  defaultBoundingBoxStyles: {
    backgroundColor: '#FF646480',
    borderWidth: 1,
    borderColor: '#FFF',
  },
  unselectedBoundingBoxStyles: {
    backgroundColor: '#c8c8c826',
    borderWidth: 1,
    borderColor: '#c8c8c880',
  },
  playerPaused: true,
  currentTime: 0,
  isTimeStampOpen: false,
  isRedactionConfigOpen: false,
  isAddRedactionCodeOpen: false,
};

const Overlay = ({
  readOnly = DEFAULTS.readOnly,
  addOnly = DEFAULTS.addOnly,
  initialBoundingBoxPolys = DEFAULTS.initialBoundingBoxPolys,
  showUnselectedBoundingPolySeries = DEFAULTS.showUnselectedBoundingPolySeries,
  stagedBoundingBoxStyles = DEFAULTS.stagedBoundingBoxStyles,
  stylesByObjectType,
  defaultBoundingBoxStyles = DEFAULTS.defaultBoundingBoxStyles,
  unselectedBoundingBoxStyles = DEFAULTS.unselectedBoundingBoxStyles,
  playerPaused = DEFAULTS.playerPaused,
  currentTime = DEFAULTS.currentTime,
  actionMenuItems,
  onAddBoundingBox,
  onChangeBoundingBoxStart,
  onChangeBoundingBoxStop,
  onSprayPaintBoxChangeStop,
  onBoxShapeChange,
  onBoxRedactionChange,
  onBoxCodeChange,
  overlayPositioningContext,
  onChangeBoundingBox,
  setSelectedGroupsByGroupId,
  setMergeGroupId,
  setMergeClusterId,
  selectedUDRGroupId,
  highlightedOverlay,
  wrapperStyles,
  maxMs,
  udrGroup,
  lastActivePoly,
  isTimeStampOpen = DEFAULTS.isTimeStampOpen,
  onCloseTimeStamp,
  clearLocalOverlayBeingUpdated,
  isRedactionConfigOpen = DEFAULTS.isRedactionConfigOpen,
  onCloseRedactionConfig,
  isAddRedactionCodeOpen = DEFAULTS.isAddRedactionCodeOpen,
  onCloseAddRedactionCode,
  overlayPreviewOption,
  globalSettings,
  featureFlags,
}: OverlayPropType) => {
  const dispatch = useDispatch();
  const draggedOverlayId = useSelector(selectDraggedOverlayId);
  const udrClusterGroups = useSelector(selectUdrClusterGroups);
  const detectionClusterGroups = useSelector(selectDetectionClusterGroups);
  const clusterMap = useSelector(selectClusterMap);
  const hasWorkingOverlayContext =
    overlayPositioningContext?.width && overlayPositioningContext?.height;
  const isRequestVideoFrameCallback =
    'requestVideoFrameCallback' in HTMLVideoElement.prototype;

  const [pinnedOverlayId, setPinnedOverlayId] = useState<string | undefined>(
    undefined
  );

  const [boundingBoxPositions, setBoundingBoxPositions] = useState<
    BoundingPolyObjectNew[]
  >([]);
  const [unselectedBoundingBoxPositions, setUnselectedBoundingBoxPositions] =
    useState<BoundingPolyObjectNew[]>([]);
  const [stagedBoundingBoxPosition, setStagedBoundingBoxPosition] = useState<
    StagedBoundingBoxPosition | undefined
  >(undefined);
  const [drawingInitialBoundingBox, setDrawingInitialBoundingBox] =
    useState(false);
  const [exitPointerLockMode, setExitPointerLockMode] = useState(false);
  const [videoDimensions, setVideoDimensions] = useState<
    { width: number; height: number } | undefined
  >(undefined);
  const [videoDimensionsUpdated, setVideoDimensionsUpdated] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState<
    | {
        top: number;
        left: number;
      }
    | undefined
  >(undefined);
  const [prevObjectTypeEffects, setPrevObjectTypeEffects] = useState(
    globalSettings.objectTypeEffects
  );
  const [isOpenConfirmDialog, setIsOpenConfirmDialog] =
    useState<boolean>(false);
  const [currentBoundingPoly, setCurrentBoundingPoly] = useState<
    GroupedBoundingPoly | undefined
  >(undefined);
  const [clusterGroups, setClusterGroups] = useState<{
    udrGroupIds: string[];
    detectionGroups: GroupIdAndType[];
  }>({ udrGroupIds: [], detectionGroups: [] });

  const [backgroundRef, backgroundCallback] = useCallbackRef(
    (node) => {
      node.addEventListener('wheel', handleWheel, { passive: false });
      document.addEventListener('wheel', handleWheel, { passive: false });
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('pointerlockchange', lockChangeAlert, false);
    },
    (node) => {
      node.removeEventListener('wheel', handleWheel);
      document.removeEventListener('wheel', handleWheel);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('pointerlockchange', lockChangeAlert);
    }
  );

  const backgroundBoundingRect = backgroundRef.current?.getBoundingClientRect();

  /* Debounce to account for delay when maximizing the window */
  const onVideoDimensions = debounce(() => {
    if (backgroundRef.current) {
      const { height, width } = backgroundRef.current.getBoundingClientRect();
      if (!isEqual(videoDimensions, { height, width })) {
        setVideoDimensions({ height, width });
        setVideoDimensionsUpdated(true);
      }
    }
  }, 100);

  useEffect(() => {
    window.addEventListener('resize', onVideoDimensions);
    return () => window.removeEventListener('resize', onVideoDimensions);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    onVideoDimensions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [backgroundBoundingRect?.width, backgroundBoundingRect?.height]);

  useEffect(() => {
    if (hasWorkingOverlayContext) {
      let newBoundingBoxPositions: BoundingPolyObjectNew[] =
        mapPolysToInternalFormat(
          initialBoundingBoxPolys,
          overlayPositioningContext.width,
          overlayPositioningContext.height,
          globalSettings.objectTypeEffects
        );
      // Check if a new UDR is drawn, on initial load for all detections, or an Overlay is pinned for live tracking
      if (
        boundingBoxPositions.length !== newBoundingBoxPositions.length ||
        pinnedOverlayId
      ) {
        // Check if it's a pinned Overlay, and update the current position
        if (pinnedOverlayId) {
          const pinnedBoundingBoxPositions = boundingBoxPositions.find(
            ({ id }: { id: string }) => id === pinnedOverlayId
          );
          if (pinnedBoundingBoxPositions) {
            newBoundingBoxPositions = newBoundingBoxPositions.map(
              (boundingBoxPosition: BoundingPolyObjectNew) =>
                boundingBoxPosition.id === pinnedBoundingBoxPositions.id
                  ? pinnedBoundingBoxPositions
                  : boundingBoxPosition
            );
          }
        }
        // Check if the new boundingBoxPositions is not the same as the current boundingBoxPositions to prevent extra renders when the web worker is updated,
        // and the boundingBoxes are editable before updating
        if (
          !isEqual(boundingBoxPositions, newBoundingBoxPositions) &&
          newBoundingBoxPositions.every((boundingBox) =>
            has(boundingBox, 'isEditable')
          )
        ) {
          setBoundingBoxPositions(newBoundingBoxPositions);
        }
      } else if (
        boundingBoxPositions.some(
          ({ version }, index) =>
            version !== newBoundingBoxPositions?.[index]?.version
        ) ||
        !isEqual(
          boundingBoxPositions.map((boundingBox) =>
            omit(boundingBox, 'boundingPoly')
          ),
          newBoundingBoxPositions.map((boundingBox) =>
            omit(boundingBox, 'boundingPoly')
          )
        )
      ) {
        // Check if any versions any other attributes have changed
        setBoundingBoxPositions(newBoundingBoxPositions);
      } else if (
        !isEqual(prevObjectTypeEffects, globalSettings.objectTypeEffects)
      ) {
        // Check if objectTypeEffects has changed
        setBoundingBoxPositions(newBoundingBoxPositions);
        setPrevObjectTypeEffects(globalSettings.objectTypeEffects);
      } else if (playerPaused && videoDimensionsUpdated) {
        // Check if the player is paused and the videoDimensions updated
        setBoundingBoxPositions(newBoundingBoxPositions);
        setVideoDimensionsUpdated(false);
      }
    }
  }, [
    boundingBoxPositions,
    globalSettings.objectTypeEffects,
    hasWorkingOverlayContext,
    initialBoundingBoxPolys,
    overlayPositioningContext?.height,
    overlayPositioningContext?.width,
    pinnedOverlayId,
    playerPaused,
    prevObjectTypeEffects,
    videoDimensions,
    videoDimensionsUpdated,
  ]);

  useEffect(() => {
    if (overlayPositioningContext?.height && overlayPositioningContext?.width) {
      const newUnselectedBoundingBoxPositions = mapPolysToInternalFormat(
        showUnselectedBoundingPolySeries,
        overlayPositioningContext.width,
        overlayPositioningContext.height,
        globalSettings.objectTypeEffects
      );
      // Check if the newUnselectedBoundingBoxPositions is not the same as the current unselectedBoundingBoxPositions to prevent extra renders
      if (
        !isEqual(
          unselectedBoundingBoxPositions,
          newUnselectedBoundingBoxPositions
        )
      ) {
        setUnselectedBoundingBoxPositions(newUnselectedBoundingBoxPositions);
      }
    }
  }, [
    globalSettings.objectTypeEffects,
    overlayPositioningContext?.height,
    overlayPositioningContext?.width,
    showUnselectedBoundingPolySeries,
    unselectedBoundingBoxPositions,
  ]);

  const handleFaceHighlightUnselect = () => dispatch(FACE_HIGHLIGHT_UNSELECT());

  const handleFaceHighlight = (payload: {
    id: string;
    timeMs: number;
    type: OBJECT_TYPE;
    groupId: string;
  }) => dispatch(setFaceHighlight(payload));

  const handleWheel = (evt: WheelEvent) => {
    if (!pinnedOverlayId) {
      return;
    }

    const boundingBoxPosition = getHighlightBoundingBoxPosition(
      highlightedOverlay,
      boundingBoxPositions
    );

    if (!boundingBoxPosition) {
      return;
    }

    evt.preventDefault();

    if (evt.deltaY > 0) {
      changeBoundingBoxSizeByPercent(boundingBoxPosition, 1);
    } else {
      changeBoundingBoxSizeByPercent(boundingBoxPosition, -1);
    }
  };

  const handleKeyDown = (evt: KeyboardEvent) => {
    const { code, altKey, ctrlKey, shiftKey } = evt;
    if (!pinnedOverlayId) {
      return;
    }

    const boundingBoxPosition = getHighlightBoundingBoxPosition(
      highlightedOverlay,
      boundingBoxPositions
    );

    if (!boundingBoxPosition) {
      return;
    }

    const keyPercentChanges: { [key: string]: number } = {
      KeyC: altKey || (ctrlKey && shiftKey) || (!shiftKey && !ctrlKey) ? 5 : 0,
      KeyZ: altKey || (ctrlKey && shiftKey) || (!shiftKey && !ctrlKey) ? -5 : 0,
    };
    const pctChange = keyPercentChanges[code] || 0;

    if (pctChange) {
      evt.preventDefault();
      changeBoundingBoxSizeByPercent(
        boundingBoxPosition,
        pctChange,
        altKey || !ctrlKey,
        ctrlKey || !altKey
      );
    }
  };

  // useCallback is removed because there is no memo
  const handleResizeExistingBox: RndResizeCallback = (
    _e,
    _direction,
    ref,
    _delta,
    position
  ) => {
    removeStagedBoundingBox();

    const focusedId = ref.getAttribute('data-boxid') || undefined;

    const focusedBoundingBoxIndex = boundingBoxPositions.findIndex(
      (boundingBoxPosition) => boundingBoxPosition.id === focusedId
    );

    if (focusedBoundingBoxIndex !== -1) {
      setBoundingBoxPositions((prevBoundingBoxPositions) =>
        produce(prevBoundingBoxPositions, (draft) => {
          const boundingBoxPosition = draft[focusedBoundingBoxIndex];

          if (boundingBoxPosition) {
            boundingBoxPosition.boundingPoly = {
              width: ref.offsetWidth,
              height: ref.offsetHeight,
              ...position,
            };
          }
        })
      );
    }
  };

  // no need for useCallback because there is no memo
  const handleResizeStagedBox: RndResizeCallback = (
    _e,
    _direction,
    ref,
    _delta,
    position
  ) => {
    if (stagedBoundingBoxPosition) {
      setStagedBoundingBoxPosition({
        ...stagedBoundingBoxPosition,
        width: ref.offsetWidth,
        height: ref.offsetHeight,
        ...position,
      });
    }
  };

  // no need for useCallback because there is no memo
  const handleResizeExistingBoxStop: RndResizeCallback = (
    _e,
    _direction,
    ref,
    _delta,
    _position
  ) => {
    const focusedId = ref.getAttribute('data-boxid') || undefined;
    const boundingBoxPosition = find(boundingBoxPositions, {
      id: focusedId,
    });
    const hasResized = isResized(ref, 0, 0, boundingBoxPositions);

    if (
      !boundingBoxPosition ||
      !overlayPositioningContext?.width ||
      !overlayPositioningContext?.height
    ) {
      return;
    }

    const payload = {
      ...boundingBoxPosition,
      boundingPoly: pixelXYWidthHeightToPercentagePoly(
        boundingBoxPosition.boundingPoly,
        overlayPositioningContext?.width,
        overlayPositioningContext?.height
      ),
    };
    onChangeBoundingBoxStop?.(payload, hasResized);
    dispatch(
      actionLogAuditEvent(`Resize Bounding Box:\n${JSON.stringify(payload)}`)
    );
  };

  const handleDragExistingBox: DraggableEventHandler = (_e, { node, x, y }) => {
    if (pinnedOverlayId || isEmpty(overlayPositioningContext)) {
      return;
    }

    const data = getChangeBoundingPolyRect(
      node,
      x,
      y,
      boundingBoxPositions,
      initialBoundingBoxPolys,
      overlayPositioningContext
    );
    if (!data) {
      return;
    }

    onChangeBoundingBox?.(data);

    removeStagedBoundingBox();
  };

  // no need for useCallback because there is no memo
  const throttledHandleDragExistingBox = throttle(
    handleDragExistingBox,
    MANUAL_TRACKING_CHANGE_INTERVAL_MS
  );

  // no need for useCallback because there is no memo
  const handleDragExistingBoxStart: DraggableEventHandler = (
    _e,
    { node, x, y }
  ) => {
    if (pinnedOverlayId || isEmpty(overlayPositioningContext)) {
      return;
    }

    const data = getChangeBoundingPolyRect(
      node,
      x,
      y,
      boundingBoxPositions,
      initialBoundingBoxPolys,
      overlayPositioningContext
    );
    if (!data) {
      return;
    }

    dispatch(actionSetDraggedOverlayId(data.id));
    onChangeBoundingBoxStart?.(data);
  };

  // no need for useCallback because there is no memo
  const handleDragExistingBoxStop: DraggableEventHandler = (
    e,
    { node, x, y }
  ) => {
    throttledHandleDragExistingBox.cancel();
    if (pinnedOverlayId || isEmpty(overlayPositioningContext)) {
      return;
    }

    const data = getChangeBoundingPolyRect(
      node,
      x,
      y,
      boundingBoxPositions,
      initialBoundingBoxPolys,
      overlayPositioningContext
    );
    if (!data) {
      return;
    }

    // Highlight was only selected must be called in handleDragExistingBoxStop to determine if box has moved
    const hasMoved = isMoved(node, x, y, boundingBoxPositions);
    if (
      !hasMoved &&
      (e.target as HTMLDivElement).getAttribute('type') === 'udr'
    ) {
      if (clearLocalOverlayBeingUpdated) {
        clearLocalOverlayBeingUpdated();
        return;
      }
    }

    const newBoundingBoxPosition: BoundingPolyObjectNew[] =
      mapPolysToInternalFormat(
        [data],
        overlayPositioningContext.width,
        overlayPositioningContext.height
      );

    const updatedBoundingBoxPositions = boundingBoxPositions.map(
      (boundingBoxPosition: BoundingPolyObjectNew) =>
        boundingBoxPosition.id === data.id && newBoundingBoxPosition[0]
          ? newBoundingBoxPosition[0]
          : boundingBoxPosition
    );

    setBoundingBoxPositions(updatedBoundingBoxPositions);
    dispatch(actionSetDraggedOverlayId(null));
    onChangeBoundingBoxStop?.(data, hasMoved);

    if (hasMoved) {
      dispatch(
        actionLogAuditEvent(`Move Bounding Box: \n${JSON.stringify(data)} `)
      );
    }
  };

  // no need for useCallback because there is no memo
  const handleDragStagedBoxStop: DraggableEventHandler = (_e, d) => {
    if (stagedBoundingBoxPosition) {
      setStagedBoundingBoxPosition({
        ...stagedBoundingBoxPosition,
        x: d.x,
        y: d.y,
      });
    }
  };

  // no need for useCallback because there is no memo
  const handleClickBox: MouseEventHandler<HTMLElement> = (e) => {
    e.stopPropagation();
    // fixme: try to ignore clicks that are the result of mouseup after resize/drag
    const focusedId = (e.target as HTMLElement).getAttribute('data-boxid');

    // full read/add only
    if (!focusedId || readOnly || addOnly) {
      return;
    }

    // individual box marked as readonly
    // const focusedIndex = findIndex(boundingBoxPositions, {
    //   id: focusedId,
    // });

    // if (get(boundingBoxPositions[focusedIndex], 'readOnly')) {
    //   return;
    // }

    const focusedBoundingBox = find(boundingBoxPositions, {
      id: focusedId,
    });

    if (focusedBoundingBox?.readOnly || !focusedBoundingBox) {
      return;
    }

    if (e.altKey) {
      setMergeGroupId?.(focusedBoundingBox.groupId); // hidden shortcut to select/unselect group
    }

    if (e.ctrlKey || e.metaKey) {
      if (focusedBoundingBox.clusterId) {
        setMergeClusterId?.(
          focusedBoundingBox.clusterId,
          e.shiftKey ? false : true
        ); // use shift modifier to unselect
      }
    }

    removeStagedBoundingBox();
  };

  // no need for useCallback because there is no memo
  const handleClickUnselectedBox: MouseEventHandler<HTMLElement> = (e) => {
    e.stopPropagation();

    if (highlightedOverlay?.id) {
      handleFaceHighlightUnselect();
    }

    // fixme: try to ignore clicks that are the result of mouseup after resize/drag
    const focusedId = (e.target as HTMLElement).getAttribute('data-boxid');
    if (!focusedId) {
      return;
    }
    // const focusedIndex = findIndex(unselectedBoundingBoxPositions, {
    //   id: focusedId,
    // });

    // const boundingBox = unselectedBoundingBoxPositions[focusedIndex];
    const boundingBox = find(unselectedBoundingBoxPositions, {
      id: focusedId,
    });

    if (!isEmpty(boundingBox) && handleFaceHighlight) {
      handleFaceHighlight({
        id: boundingBox.id,
        timeMs: boundingBox.startTimeMs,
        type: boundingBox.type,
        groupId: boundingBox.groupId,
      });
    }
  };

  // no need for useCallback because there is no memo
  const handleSelectUnselectedBox: MouseEventHandler<HTMLElement> = (e) => {
    e.stopPropagation();

    if (highlightedOverlay?.id) {
      handleFaceHighlightUnselect();
    }

    // fixme: try to ignore clicks that are the result of mouseup after resize/drag
    const focusedId = (e.target as HTMLElement).getAttribute('data-boxid');

    const boundingBox = unselectedBoundingBoxPositions.find(
      (bbp) => bbp.id === focusedId
    );

    if (setSelectedGroupsByGroupId && boundingBox && boundingBox.groupId) {
      setSelectedGroupsByGroupId(boundingBox.groupId);
    }
  };

  const removeStagedBoundingBox = () => setStagedBoundingBoxPosition(undefined);

  const handleBackgroundMouseDown: MouseEventHandler<HTMLElement> = (e) => {
    if (!readOnly && e.button === 0) {
      const { x: mouseX, y: mouseY } = getMousePosition(e);

      setDrawingInitialBoundingBox(true);
      setStagedBoundingBoxPosition({
        x: mouseX,
        y: mouseY,
        origX: mouseX,
        origY: mouseY,
        width: 0,
        height: 0,
      });
    }
  };

  const handleBackgroundMouseMove = (e: MouseEvent) => {
    if (stagedBoundingBoxPosition) {
      const { origX, origY } = stagedBoundingBoxPosition;
      const { x: mouseX, y: mouseY } = getMousePosition(
        e,
        backgroundRef.current
      );

      if (origX && origY) {
        setStagedBoundingBoxPosition({
          ...stagedBoundingBoxPosition,
          width: Math.abs(origX - mouseX),
          height: Math.abs(origY - mouseY),
          x: mouseX < origX ? mouseX : origX,
          y: mouseY < origY ? mouseY : origY,
        });
      }
    }
  };

  const handleBackgroundMouseUp = (e: MouseEvent) => {
    confirmStagedBoundingBox({ shift: e.shiftKey });
    setDrawingInitialBoundingBox(false);

    if (highlightedOverlay?.id) {
      handleFaceHighlightUnselect();
    }
  };

  useEffect(() => {
    if (drawingInitialBoundingBox) {
      document.addEventListener('mousemove', handleBackgroundMouseMove);
      document.addEventListener('mouseup', handleBackgroundMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleBackgroundMouseMove);
      document.removeEventListener('mouseup', handleBackgroundMouseUp);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [drawingInitialBoundingBox, handleBackgroundMouseUp]);

  const validateStagedBoundingBoxSize = () =>
    stagedBoundingBoxPosition &&
    stagedBoundingBoxPosition.width > 10 &&
    stagedBoundingBoxPosition.height > 10;

  const confirmStagedBoundingBox = (opts?: { shift: boolean }) => {
    if (
      !isEmpty(overlayPositioningContext) &&
      stagedBoundingBoxPosition &&
      validateStagedBoundingBoxSize()
    ) {
      const groupId = selectedUDRGroupId || guid();
      const id = uuid();

      onAddBoundingBox?.(
        {
          boundingPoly: pixelXYWidthHeightToPercentagePoly(
            stagedBoundingBoxPosition,
            overlayPositioningContext.width,
            overlayPositioningContext.height
          ),
          id,
          groupId,
        },
        opts
      );

      removeStagedBoundingBox();
    }
  };

  const stagedBoundingBoxTopLeft = (position: StagedBoundingBoxPosition) => ({
    x: Math.min(position.x + position.width, position.x),
    y: Math.min(position.y + position.height, position.y),
  });

  // no need for useCallback because there is no memo
  const stagedBoundingBoxDimensions = (
    position: StagedBoundingBoxPosition
  ) => ({
    width: Math.max(Math.abs(position.width), 15),
    height: Math.max(Math.abs(position.height), 15),
  });

  const emitChangeBoundingBoxEvent = (
    changeBoundingBoxData: GroupedBoundingPoly
  ) => {
    onChangeBoundingBox?.(changeBoundingBoxData);
  };

  const throttledEmitChangeBoundingBoxEvent = throttle(
    emitChangeBoundingBoxEvent,
    MANUAL_TRACKING_CHANGE_INTERVAL_MS
  );

  const lockChangeAlert = () => {
    if (!document.pointerLockElement && !isEmpty(overlayPositioningContext)) {
      const mediaDetailTab = document.getElementById('media-detail-tab');
      if (mediaDetailTab) {
        mediaDetailTab.style.overflow = 'auto';
      }
      throttledEmitChangeBoundingBoxEvent.cancel();
      const boundingBoxPosition = boundingBoxPositions.find(
        ({ id }) => id === pinnedOverlayId
      );

      if (!boundingBoxPosition) {
        return;
      }

      const data = boundingBoxPositionToBoundingPolyRect(
        {
          ...boundingBoxPosition,
          boundingPoly: {
            ...boundingBoxPosition.boundingPoly,
          },
        },
        initialBoundingBoxPolys,
        overlayPositioningContext
      );

      if (!data) {
        return;
      }

      onSprayPaintBoxChangeStop?.(data);

      setPinnedOverlayId(undefined);
      setExitPointerLockMode(false);
    }
  };

  const requestPointerLock = (
    targetElement: HTMLElement,
    retryNumber: number
  ) => {
    const currentRetryNumber = retryNumber + 1;
    // Casting due to platform differences in support of promise return from requestPointerLock
    const promise = targetElement.requestPointerLock() as
      | Promise<undefined>
      | undefined;

    if (!promise) {
      console.log('Disabling mouse acceleration is not supported');
      return;
    }

    return promise
      .then(() => console.log('Pointer is locked'))
      .catch(() => {
        console.log(`Pointer is lock failed: ${currentRetryNumber}`);
        // Retry 4 times for requestPointerLock.
        // Tested on the local need to retry 1 time will successfully with timeout 500.
        if (currentRetryNumber < 5) {
          setTimeout(() => {
            void requestPointerLock(targetElement, currentRetryNumber);
          }, 500);
        } else {
          // If it still fails will reset pinned and userAct
          setPinnedOverlayId(undefined);
          setExitPointerLockMode(false);
        }
      });
  };

  // no need for useCallback because there is no memo
  const handleDBClick = (evt: { target: HTMLElement }) => {
    const focusedId = evt.target.getAttribute('data-boxid');

    const boundingBoxPosition =
      focusedId && boundingBoxPositions.find(({ id }) => id === focusedId);

    if (!boundingBoxPosition || isEmpty(overlayPositioningContext)) {
      return;
    }

    if (includes(DETECTION_TYPES, boundingBoxPosition.type)) {
      return;
    }

    const xPos = boundingBoxPosition.boundingPoly.x;
    const yPos = boundingBoxPosition.boundingPoly.y;

    const newBoundingBoxPositions = buildNewBoundingBoxPosition(
      focusedId,
      {
        ...boundingBoxPosition.boundingPoly,
        x: xPos,
        y: yPos,
      },
      boundingBoxPositions
    );
    setBoundingBoxPositions(newBoundingBoxPositions);

    const data = boundingBoxPositionToBoundingPolyRect(
      {
        ...boundingBoxPosition,
        boundingPoly: {
          ...boundingBoxPosition.boundingPoly,
          x: xPos,
          y: yPos,
        },
      },
      initialBoundingBoxPolys,
      overlayPositioningContext
    );

    if (!data) {
      return;
    }

    if (pinnedOverlayId) {
      setExitPointerLockMode(false);
      document.exitPointerLock();
    } else {
      onChangeBoundingBoxStart?.(data);
      setPinnedOverlayId(focusedId);
      const mediaDetailTab = document.getElementById('media-detail-tab');
      if (mediaDetailTab) {
        mediaDetailTab.style.overflow = 'hidden';
      }
      if (!document.pointerLockElement) {
        void requestPointerLock(evt.target, 0);
      }
    }
  };

  const handleMouseMoveInPinnedMode: MouseEventHandler = (evt) => {
    if (
      !pinnedOverlayId ||
      exitPointerLockMode ||
      isEmpty(overlayPositioningContext)
    ) {
      return;
    }

    const { height, width } = overlayPositioningContext;
    const focusedId = pinnedOverlayId;
    const boundingBoxPosition = boundingBoxPositions.find(
      ({ id }) => id === focusedId
    );

    if (!boundingBoxPosition) {
      return;
    }

    const xPos =
      boundingBoxPosition.boundingPoly.x + evt.movementX < 0
        ? 0
        : boundingBoxPosition.boundingPoly.x +
              evt.movementX +
              boundingBoxPosition.boundingPoly.width >
            width
          ? width - boundingBoxPosition.boundingPoly.width
          : boundingBoxPosition.boundingPoly.x + evt.movementX;
    const yPos =
      boundingBoxPosition.boundingPoly.y + evt.movementY < 0
        ? 0
        : boundingBoxPosition.boundingPoly.y +
              evt.movementY +
              boundingBoxPosition.boundingPoly.height >
            height
          ? height - boundingBoxPosition.boundingPoly.height
          : boundingBoxPosition.boundingPoly.y + evt.movementY;

    const newBoundingBoxPositions = buildNewBoundingBoxPosition(
      focusedId,
      {
        ...boundingBoxPosition.boundingPoly,
        x: xPos,
        y: yPos,
      },
      boundingBoxPositions
    );
    setBoundingBoxPositions(newBoundingBoxPositions);

    const data = boundingBoxPositionToBoundingPolyRect(
      {
        ...boundingBoxPosition,
        boundingPoly: {
          ...boundingBoxPosition.boundingPoly,
          x: xPos,
          y: yPos,
        },
      },
      initialBoundingBoxPolys,
      overlayPositioningContext
    );

    if (!data) {
      return;
    }

    throttledEmitChangeBoundingBoxEvent(data);
  };

  const changeBoundingBoxSizeByPercent = throttle(
    (
      boundingBoxPosition: BoundingPolyObjectNew,
      deltaPercent: number,
      isChangeX = true,
      isChangeY = true
    ) => {
      if (isEmpty(overlayPositioningContext)) {
        return;
      }
      const boundingPoly = boundingBoxPosition.boundingPoly;

      const deltaX = isChangeX ? (boundingPoly.width * deltaPercent) / 100 : 0;

      const deltaY = isChangeY ? (boundingPoly.height * deltaPercent) / 100 : 0;

      const x = Math.max(boundingPoly.x - deltaX, 0);

      const width =
        x + boundingPoly.width + deltaX * 2 > // linter insist on splitting condition into two lines
        overlayPositioningContext.width
          ? overlayPositioningContext.width - x
          : boundingPoly.width + deltaX * 2;

      const y = Math.max(boundingPoly.y - deltaY, 0);

      const height =
        y + boundingPoly.height + deltaY * 2 > overlayPositioningContext.height
          ? overlayPositioningContext.height - y
          : boundingPoly.height + deltaY * 2;

      const newBoundingBoxPositions = buildNewBoundingBoxPosition(
        boundingBoxPosition.id,
        {
          x,
          y,
          width,
          height,
        },
        boundingBoxPositions
      );
      setBoundingBoxPositions(newBoundingBoxPositions);

      const data = boundingBoxPositionToBoundingPolyRect(
        {
          ...boundingBoxPosition,
          boundingPoly: {
            x,
            y,
            width,
            height,
          },
        },
        initialBoundingBoxPolys,
        overlayPositioningContext
      );

      if (!data) {
        return;
      }

      onChangeBoundingBox?.(data);
    },
    MANUAL_TRACKING_CHANGE_INTERVAL_MS
  );

  const isActiveSelectedCluster = (groupId: string) => {
    const isActive = highlightedOverlay?.groupId === groupId;

    return isActive;
  };

  const onChangeShape = (
    id: string,
    groupId: string,
    shapeType?: ShapeType
  ) => {
    const boundingPolyPos = getBoundingPolyPos(id, boundingBoxPositions);
    if (
      boundingPolyPos &&
      !isEmpty(overlayPositioningContext) &&
      onBoxShapeChange
    ) {
      const boundingPoly = boundingBoxPositionToBoundingPolyRect(
        boundingPolyPos,
        initialBoundingBoxPolys,
        overlayPositioningContext
      );
      setCurrentBoundingPoly(boundingPoly);
      const clusterGroups = findClusterGroupIds(
        clusterMap,
        udrClusterGroups,
        detectionClusterGroups,
        groupId
      );
      if (clusterGroups.isMergedGroup) {
        setIsOpenConfirmDialog(true);
        setClusterGroups({
          udrGroupIds: clusterGroups.udrGroupIds,
          detectionGroups: clusterGroups.detectionGroups,
        });
      } else {
        onChangeGroupShape(shapeType, boundingPoly);
      }
    }
  };

  const onChangeGroupShape = (
    newShape?: ShapeType,
    boundingBox?: GroupedBoundingPoly
  ) => {
    const boundingPoly = boundingBox ?? currentBoundingPoly;
    if (boundingPoly && onBoxShapeChange) {
      const udrGroupIds: string[] = [];
      const detectionGroups: GroupIdAndType[] = [];
      if (boundingPoly.type === 'udr') {
        udrGroupIds.push(boundingPoly.groupId);
      } else {
        detectionGroups.push({
          id: boundingPoly.groupId,
          type: boundingPoly.type,
        });
      }
      onBoxShapeChange(
        { ...boundingPoly, shapeType: newShape },
        udrGroupIds,
        detectionGroups
      );
      setIsOpenConfirmDialog(false);
      setCurrentBoundingPoly(undefined);
    }
  };

  const onChangeMergedGroupShape = (newShape?: ShapeType) => {
    const { udrGroupIds, detectionGroups } = clusterGroups;
    if (currentBoundingPoly && onBoxShapeChange) {
      onBoxShapeChange(
        { ...currentBoundingPoly, shapeType: newShape },
        udrGroupIds,
        detectionGroups
      );
      setIsOpenConfirmDialog(false);
      setClusterGroups({ udrGroupIds: [], detectionGroups: [] });
      setCurrentBoundingPoly(undefined);
    }
  };

  const onChangeRedaction = (
    lastActivePoly: GroupedBoundingPoly,
    udrGroupIds: string[],
    detectionGroups: GroupIdAndType[],
    redactionConfig?: RedactionConfig
  ) => {
    const boundingPolyPos = getBoundingPolyPos(
      lastActivePoly.id,
      boundingBoxPositions
    );
    if (
      boundingPolyPos &&
      !isEmpty(overlayPositioningContext) &&
      onBoxRedactionChange
    ) {
      const boundingPoly = boundingBoxPositionToBoundingPolyRect(
        boundingPolyPos,
        initialBoundingBoxPolys,
        overlayPositioningContext
      );

      if (boundingPoly) {
        onBoxRedactionChange(
          {
            ...boundingPoly,
            redactionConfig,
            startTimeMs: lastActivePoly.startTimeMs,
            stopTimeMs: lastActivePoly.stopTimeMs,
          },
          udrGroupIds,
          detectionGroups
        );
      }
    }
  };

  const onChangeCode = (
    lastActivePoly: GroupedBoundingPoly,
    redactionCode: IndividualRedactionCode,
    udrGroupIds: string[],
    detectionGroups: GroupIdAndType[]
  ) => {
    if (isEmpty(overlayPositioningContext)) {
      return;
    }
    const boundingPolyPos = getBoundingPolyPos(
      lastActivePoly.id,
      boundingBoxPositions
    );
    if (boundingPolyPos && onBoxCodeChange) {
      const boundingPoly = boundingBoxPositionToBoundingPolyRect(
        boundingPolyPos,
        initialBoundingBoxPolys,
        overlayPositioningContext
      );

      if (boundingPoly) {
        onBoxCodeChange(
          boundingPoly.id,
          redactionCode,
          udrGroupIds,
          detectionGroups
        );
      }
    }
  };

  const { top, left, height, width } = overlayPositioningContext || {};

  const boundingBoxCommonStyles = {
    willChange: 'left, top, width, height',
  };

  return hasWorkingOverlayContext ? (
    <div
      id="overlay-container"
      data-test="overlay-container"
      onMouseMove={handleMouseMoveInPinnedMode}
      ref={backgroundCallback}
      style={{
        position: 'absolute',
        overflow: 'hidden',
        top,
        left,
        height,
        width,
        pointerEvents: readOnly ? 'none' : 'auto',
        ...wrapperStyles,
      }}
    >
      {boundingBoxPositions.map(
        ({
          id,
          groupId,
          type,
          readOnly,
          boundingPoly: { x, y, ...size },
          isEditable,
          shapeType,
          redactionConfig,
          redactionCode,
        }) =>
          playerPaused || pinnedOverlayId === id || draggedOverlayId === id ? (
            <RndBox
              key={id}
              data-boxid={id}
              onClick={handleClickBox}
              onDoubleClick={handleDBClick}
              style={{
                ...boundingBoxCommonStyles,
                ...defaultBoundingBoxStyles,
                ...(isEditable && stylesByObjectType
                  ? getStylesByObjectType(
                      type,
                      redactionConfig,
                      size,
                      overlayPreviewOption,
                      globalSettings,
                      stylesByObjectType
                    )
                  : stylesByObjectType?.disabled),
                borderStyle:
                  (groupId && selectedUDRGroupId === groupId) ||
                  id === pinnedOverlayId
                    ? 'dashed'
                    : 'solid',
                pointerEvents:
                  readOnly || addOnly || drawingInitialBoundingBox
                    ? 'none'
                    : 'auto',
              }}
              size={size}
              position={{ x, y }}
              currentTime={currentTime}
              onDragStart={handleDragExistingBoxStart}
              onDragStop={handleDragExistingBoxStop}
              onDrag={throttledHandleDragExistingBox}
              onResize={handleResizeExistingBox}
              onResizeStop={handleResizeExistingBoxStop}
              disableDragging={
                !isEditable ||
                readOnly ||
                addOnly ||
                (type !== 'udr' && !playerPaused)
              }
              enableResizing={isEditable && isActiveSelectedCluster(groupId)}
              type={type}
              menuItems={actionMenuItems}
              liveTrackingEnabled={!!pinnedOverlayId}
              shape={shapeType}
              onChangeShape={(shapeType?: ShapeType) =>
                onChangeShape(id, groupId, shapeType)
              }
              redactionCode={redactionCode}
              videoDimensions={videoDimensions}
              onFaceHighlight={handleFaceHighlight}
              groupId={groupId}
              menuAnchorEl={menuAnchorEl}
              setMenuAnchorEl={setMenuAnchorEl}
            />
          ) : (
            !isRequestVideoFrameCallback && (
              <RndBoxSimple
                key={id}
                shape={shapeType}
                style={{
                  ...boundingBoxCommonStyles,
                  ...defaultBoundingBoxStyles,
                  ...(stylesByObjectType &&
                    getStylesByObjectType(
                      type,
                      redactionConfig,
                      size,
                      overlayPreviewOption,
                      globalSettings,
                      stylesByObjectType
                    )),
                  borderStyle:
                    (groupId && selectedUDRGroupId === groupId) ||
                    id === pinnedOverlayId
                      ? 'dashed'
                      : 'solid',
                }}
                redactionCode={redactionCode}
                type={type}
                position={{ x, y }}
                size={size}
                videoDimensions={videoDimensions}
              />
            )
          )
      )}
      {stagedBoundingBoxPosition &&
        validateStagedBoundingBoxSize() &&
        !readOnly && (
          <RndBox
            style={{
              ...boundingBoxCommonStyles,
              ...defaultBoundingBoxStyles,
              ...stagedBoundingBoxStyles,
              // do not let this box interfere with mouse events as we draw it out
              pointerEvents: drawingInitialBoundingBox ? 'none' : 'auto',
              borderStyle: 'solid',
            }}
            currentTime={currentTime}
            size={stagedBoundingBoxDimensions(stagedBoundingBoxPosition)}
            position={stagedBoundingBoxTopLeft(stagedBoundingBoxPosition)}
            onDragStop={handleDragStagedBoxStop}
            // onDrag={handleDragStagedBox}
            onResize={handleResizeStagedBox}
            // onResizeStop={handleResizeStagedBoxStop}
            enableResizing={!drawingInitialBoundingBox}
            type="udr"
            menuAnchorEl={menuAnchorEl}
            setMenuAnchorEl={setMenuAnchorEl}
          />
        )}
      {(playerPaused || !isRequestVideoFrameCallback) &&
        unselectedBoundingBoxPositions.map(
          ({ id, type, boundingPoly: { x, y, ...size }, shapeType }) => (
            <RndBox
              key={id}
              data-test="RndBox"
              data-boxid={id}
              onClick={handleClickUnselectedBox}
              onDoubleClick={handleSelectUnselectedBox}
              style={{
                ...boundingBoxCommonStyles,
                ...unselectedBoundingBoxStyles,
                ...stylesByObjectType?.disabled,
                borderStyle: 'dashed',
                pointerEvents: 'auto',
              }}
              size={size}
              position={{ x, y }}
              currentTime={currentTime}
              enableResizing={false}
              disableDragging
              shape={shapeType}
              unselectedBox
              type={type}
              menuAnchorEl={menuAnchorEl}
              setMenuAnchorEl={setMenuAnchorEl}
            />
          )
        )}
      {!!lastActivePoly && !!onCloseTimeStamp && !!maxMs && (
        <TimeStampMenu
          minMs={0}
          maxMs={maxMs}
          udrGroup={udrGroup}
          lastActivePoly={lastActivePoly}
          isTimeStampOpen={!!isTimeStampOpen}
          menuAnchorEl={menuAnchorEl}
          onClose={onCloseTimeStamp}
        />
      )}
      {!!lastActivePoly && !!onCloseRedactionConfig && (
        <RedactionConfigMenu
          onChangeRedaction={onChangeRedaction}
          lastActivePoly={lastActivePoly}
          isRedactionConfigOpen={!!isRedactionConfigOpen}
          menuAnchorEl={menuAnchorEl}
          onClose={onCloseRedactionConfig}
        />
      )}
      {featureFlags['redactionCodes'] &&
        !!lastActivePoly &&
        !!onCloseAddRedactionCode && (
          <AddRedactionCodeMenu<GroupedBoundingPoly>
            data={lastActivePoly}
            onChangeOverlayCode={onChangeCode}
            isAddRedactionCodeOpen={!!isAddRedactionCodeOpen}
            menuAnchorEl={menuAnchorEl}
            onClose={onCloseAddRedactionCode}
          />
        )}
      <div
        style={{
          width: '100%',
          height: '100%',
          cursor: readOnly ? 'auto' : 'crosshair',
        }}
        data-test="overlay-draw"
        onMouseDown={handleBackgroundMouseDown}
      />

      <ConfirmWithMergeGroupDialog
        isOpen={isOpenConfirmDialog}
        onClose={() => setIsOpenConfirmDialog(false)}
        onSubmitGroup={onChangeGroupShape}
        onSubmitMergedGroup={onChangeMergedGroupShape}
        isChangeShape
        shapeType={currentBoundingPoly?.shapeType}
      />
    </div>
  ) : (
    <></>
  );
};

function getBoundingPolyPos(
  lastActivePolyId: string,
  boundingBoxPositions: BoundingPolyObjectNew[]
) {
  return boundingBoxPositions.find((box) => box.id === lastActivePolyId);
}

function getHighlightBoundingBoxPosition(
  highlightedOverlay:
    | {
        id: string;
        groupId: string;
        timeMs: number;
      }
    | undefined,
  boundingBoxPositions: BoundingPolyObjectNew[]
) {
  if (!highlightedOverlay) {
    return;
  }

  const { id } = highlightedOverlay;

  return boundingBoxPositions.find((bbp) => bbp.id === id);
}

function isMoved(
  node: HTMLElement,
  x: number,
  y: number,
  boundingBoxPositions: BoundingPolyObjectNew[]
) {
  const focusedId = node.getAttribute('data-boxid');
  const box = boundingBoxPositions.find((box) => box.id === focusedId);
  return box && (x !== box.boundingPoly.x || y !== box.boundingPoly.y);
}

function isResized(
  node: HTMLElement,
  width: number,
  height: number,
  boundingBoxPositions: BoundingPolyObjectNew[]
) {
  const focusedId = node.getAttribute('data-boxid');
  const box = boundingBoxPositions.find((box) => box.id === focusedId);
  return (
    box &&
    (width !== box.boundingPoly.width || height !== box.boundingPoly.height)
  );
}

function getBoundingPolyData(
  stagedBoundingBoxPosition: PolyCenter,
  overlayPositioningContext: OverlayPosition
) {
  return {
    boundingPoly: pixelXYWidthHeightToPercentagePoly(
      stagedBoundingBoxPosition,
      overlayPositioningContext.width,
      overlayPositioningContext.height
    ),
  };
}

function getChangeBoundingPolyRect(
  node: HTMLElement,
  x: number,
  y: number,
  boundingBoxPositions: BoundingPolyObjectNew[],
  initialBoundingBoxPolys: GroupedBoundingPoly[],
  overlayPositioningContext: OverlayPosition
) {
  const focusedId = node.getAttribute('data-boxid');
  const box = boundingBoxPositions.find((box) => box.id === focusedId);
  if (!box) {
    return;
  }
  const { width, height } = box.boundingPoly;

  const { boundingPoly } = getBoundingPolyData(
    {
      x,
      y,
      width,
      height,
    },
    overlayPositioningContext
  );

  const poly = initialBoundingBoxPolys.find((poly) => poly.id === focusedId);

  if (!poly) {
    return;
  }

  return {
    ...poly,
    boundingPoly,
  };
}

function boundingBoxPositionToBoundingPolyRect(
  boundingBoxPosition: BoundingPolyObjectNew,
  initialBoundingBoxPolys: GroupedBoundingPoly[],
  overlayPositioningContext: OverlayPosition
): GroupedBoundingPoly | undefined {
  const { boundingPoly } = getBoundingPolyData(
    boundingBoxPosition.boundingPoly,
    overlayPositioningContext
  );

  const poly = initialBoundingBoxPolys.find(
    (poly) => poly.id === boundingBoxPosition.id
  );

  if (!poly) {
    return;
  }

  return {
    ...poly,
    boundingPoly,
  };
}

function getStylesByObjectType(
  type: OBJECT_TYPE,
  redactionConfig: RedactionConfig | undefined,
  dimensions: { width: number; height: number },
  overlayPreviewOption: OverlayPreviewOptionsType | undefined,
  globalSettings: GlobalSettings,
  stylesByObjectType: Record<OBJECT_TYPE | 'disabled', React.CSSProperties>
) {
  let redactionConfig_ = redactionConfig ?? DEFAULT_REDACTION_CONFIGURATION;
  if (overlayPreviewOption === 'redacted' && redactionConfig === undefined) {
    const redactedDefaults =
      globalSettings.objectTypeEffects[typeMapping[type]];
    redactionConfig_ = redactedDefaults.redactionConfig;
  }

  if (overlayPreviewOption === 'redacted' && type !== 'poim') {
    return getRedactedOverlayStyle(redactionConfig_, dimensions);
  }

  //  handles none, black_fill, outline preview types
  return stylesByObjectType[type];
}

function buildNewBoundingBoxPosition(
  boundingBoxId: string,
  boundingPoly: PolyCenter,
  boundingBoxPositions: BoundingPolyObjectNew[]
) {
  const updateAtIndex = boundingBoxPositions.findIndex(
    (box) => box.id === boundingBoxId
  );

  return boundingBoxPositions.map((item, index) => {
    if (index === updateAtIndex) {
      return {
        ...item,
        boundingPoly,
      };
    } else {
      return item;
    }
  });
}

// important: There is potential memory leak caused by udrCollection in
// MediaPlayerView. If memo(Overlay) is to be used, please take memory snapshot
// to make sure no stale udrCollection is referenced
export default withContextProps(
  OverlayPositioningContext.Consumer,
  (context: OverlayPosition) => ({ overlayPositioningContext: context })
)(Overlay);

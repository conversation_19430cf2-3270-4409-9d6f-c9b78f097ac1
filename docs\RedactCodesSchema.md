### Steps to create redaction codes schema using core-graphql queries

1. Create new data registry 
```
mutation createRedactionCodesDataRegistry {
  createDataRegistry(input: {
    id: "bfcab898-4b80-40e1-b6a2-20f491ac189d"
    name: "redaction-code"
    description: "Redaction Code"
    source: "field deprecated"
  }) {
    id
  }
}

```

2. Create Schema Draft
```
# Note: Use the `dataRegistryId` value from the `createDataRegistry` mutation.
mutation createRedactionCodeSchemaDraft {
  upsertSchemaDraft(
    input: {
      dataRegistryId: "bfcab898-4b80-40e1-b6a2-20f491ac189d"
      majorVersion: 1
      schema: {
        type: "object",
        title: "redaction-code",
        required: [
          "code",
          "codeName",
          "description"
        ],
        properties: {
          code: {
            type: "string",
            maxLength: 20,
            minLength: 2
          },
          codeName: {
            type: "string"
          },
          codeSize: {
            type: "string"
          },
          codeColor: {
            type: "string"
          },
          description: {
            type: "string",
            maxLength: 100
          },
          codeLocation: {
            enum: [
              "above",
              "below",
              "left",
              "right",
              "center"
            ],
            type: "string"
          },
          codeOverflow: {
            type: "boolean"
          },
          codeSizeMinPx: {
            type: "number"
          },
          createdDateTime: {
            type: "dateTime"
          },
          modifiedDateTime: {
            type: "dateTime"
          }
        },
        description: "Redaction Codes"
      }
    }
  ) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}

```

3. Publish Schema Draft

```
# Note: Pass in the Schema ID for the `id` value
mutation publishSchemaDraft($id: ID!) {
  updateSchemaState(input: {  id: $id , status: published }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```


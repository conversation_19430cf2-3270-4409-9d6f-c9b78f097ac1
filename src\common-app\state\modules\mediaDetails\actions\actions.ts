import { markWorkerAction } from '@utils';
import shaka from 'shaka-player';
import { ROUTE_HOME } from '@common-modules/routing';
import { BoundingPolyRect } from '../models/BoundingPoly';
import {
  AudiowaveFrame,
  UDRsPolyAssetGroupSeriesItem,
  UDRsPolyAsset,
  DetailTDO,
  ClusterSegment,
} from '../models';
import { GroupedBoundingPoly, PollDownloadAssetStop } from '@worker';
import {
  GovQAIntegration,
  FOIAXpressIntegration,
  CasepointIntegration,
  ExterroIntegration,
  NuixIntegration,
  ExternalIntegration,
} from '../store.models';
import { TDOId } from '@common-modules/universal/models/Brands';
import { createAction } from '@reduxjs/toolkit';
import {
  DeleteLastAssetResponse,
  FecthDownloadAssetStatusResponse,
  FetchRedactedMediaResponse,
  FetchRedactionJobStatusResponse,
  PollToDownloadResponse,
  REFRESH_TDO_TASKS_QUERY_RESPONSE,
  RefreshTdoPrimaryAssetResponse,
  RetrieveTdoIdFromJobIdResponse,
  UpdateLastRedactedFileMetricsResponse,
  UpdateNameResponse,
  UpdateSettingsOnDetailsResponse,
  UpdateStatusResponse,
} from '../services';
import {
  createGraphQLFailureAction,
  createGraphQLRequestAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { V3FJobResponse } from '@common/web-worker/services/sendToDownloadAsset';
import { v4 as uuid } from 'uuid';
import { GroupIdAndType } from './view';
import {
  CLUSTER_SEGMENT_SORT_TYPE,
  OBJECT_TYPE,
  SEGMENT_TYPE,
} from '@helpers/constants';

export const FETCH_TDO = 'request to fetch a tdo';
export const FETCH_TDO_SUCCESS = createAction<{
  temporalDataObject: DetailTDO;
}>('tdo fetched successfully');
export const FETCH_TDO_WITH_FAILURE =
  createGraphQLFailureAction('tdo fetch failed');

export const REFRESH_TDO_TASKS = 'details/refresh-tdo-tasks-start';
export const REFRESH_TDO_TASKS_SUCCESS =
  createGraphQLSuccessAction<REFRESH_TDO_TASKS_QUERY_RESPONSE>(
    'details/refresh-tdo-tasks-success'
  );
export const REFRESH_TDO_TASKS_FAILURE = createGraphQLFailureAction(
  'details/refresh-tdo-tasks-failure'
);

export const REFRESH_TDO_PRIMARY_ASSET_ID =
  'details/refresh-tdo-primary-asset-start';
export const REFRESH_TDO_PRIMARY_ASSET_ID_SUCCESS =
  createGraphQLSuccessAction<RefreshTdoPrimaryAssetResponse>(
    'details/refresh-tdo-primary-asset-success'
  );
export const REFRESH_TDO_PRIMARY_ASSET_ID_FAILURE = createGraphQLFailureAction(
  'details/refresh-tdo-primary-asset-failure'
);

export const FETCH_WAVEFORM_SUCCESS = createAction<AudiowaveFrame[]>(
  'details/fetch-waveform-success'
);
export const FETCH_WAVEFORM_FAILURE = createAction<string | undefined>(
  'details/fetch-waveform-failure'
);
export const SET_AUDIOWAVES = createAction<AudiowaveFrame[]>(
  'details/set-audiowaves'
);

export const FACES_SELECT_FACE = 'faces/select-face';
export const FACES_SELECT_GROUP = 'faces/select-group';
export const FACES_SHIFT_SELECT_FACE = 'faces/shift-select-face';

export const SELECT_REDACTED_FILES_TAB = createAction<{
  tdoId: TDOId;
}>('select redacted files tab');
export const SELECT_RESULTS_TAB = createAction('select results tab');
export const REDACTED_FILE_COMPLETED = createAction<{ tdoId: TDOId }>(
  'redact file completed'
);

export const FACES_SELECT_GROUP_FACES = createAction<{
  groupId: string;
}>('faces/select-cluster-faces');
export const FACES_SELECT_ENGINE = createAction<{
  facesSelectedEngineId: string;
}>('faces/select-another-engine');
export const FACE_HIGHLIGHT = createAction<{
  id: string;
  timeMs: number;
  groupId: string;
  type: OBJECT_TYPE;
} | null>('faces/highlight');
export const FACE_HIGHLIGHT_UNSELECT = createAction('faces/highlight-unselect');

export const CLUSTER_ITEM_HIGHLIGHT = 'clusteritem/highlight';
export const CHANGE_CLUSTER_LABEL = createAction<{
  clusterId: string;
  type: string;
  userLabel: string;
}>('clusteritem/change-label');
export const CHANGE_CLUSTER_PINNED = createAction<{
  clusterId: string;
  type: string;
  pinnedDate: number | undefined;
  generatedId: string;
}>('clusteritem/change-pinned');
export const CHANGE_CLUSTER_SEGMENT_SORT_TYPE = createAction<{
  clusterId: string;
  segmentSortType: CLUSTER_SEGMENT_SORT_TYPE;
}>('clusteritem/change-segment-sort-type');

// license plates
export const LICENSE_PLATES_SELECT_LICENSE_PLATE =
  'license-plates/select-license-plate';
export const LICENSE_PLATES_SELECT_ALL_LICENSE_PLATES =
  'license-plates/select-all-license-plates';
export const LICENSE_PLATES_SELECT_GROUP_LICENSE_PLATES =
  'license-plates/select-group';
export const LICENSE_PLATES_SELECT_ENGINE =
  'license-plates/select-another-engine';

// SAVE button
export const FETCH_UPDATE_ASSET_ACTION = createAction<UpdateAssetPayload>(
  'action/update-asset'
);
export const UPDATE_CURRENT_TIME_MEDIA_PLAYER = createAction<{
  currentPosition: number;
}>('player/update-time');
export const DISCARD_CHANGES = createAction('changes/discard');

export const START_DRAG_AND_DROP_POSITION_BAR = createAction(
  'position/start-drag-and-drop'
);
export const DROP_POSITION_BAR = createAction('position/stop-drag-and-drop');

export const DELETE_LAST_ASSET = createAction<{
  id?: string;
  ids?: string[];
}>('asset/delete-last');
export const FETCH_DELETE_LAST_ASSET = createGraphQLRequestAction(
  'asset/delete-last-fetch'
);
export const FETCH_DELETE_LAST_ASSET_SUCCESS =
  createGraphQLSuccessAction<DeleteLastAssetResponse>(
    'asset/delete-last-fetch-success'
  );
export const FETCH_DELETE_LAST_ASSET_FAILURE = createGraphQLFailureAction(
  'asset/delete-last-fetch-failure'
);

export const REDACT_TDO = createAction('tdo/send-to-redact');

// status change
export const STATUS_CHANGE = createAction<{
  status?: string;
}>('status/change');
export const FETCH_STATUS_CHANGE = 'status/send-to-change-fetch';
export const FETCH_STATUS_CHANGE_SUCCESS =
  createGraphQLSuccessAction<UpdateStatusResponse>(
    'status/send-to-change-fetch-success'
  );
export const FETCH_STATUS_CHANGE_FAILURE = createGraphQLFailureAction(
  'status/send-to-change-fetch-failure'
);
export const IN_REDACTION_TAG_KEY = 'in redaction';
export const REDACTION_TDO_STATUS_DRAFT = 'draft';
export const REDACTION_TDO_STATUS_PENDING_REVIEW = 'pending review';
export const REDACTION_TDO_STATUS_COMPLETE = 'complete';

// redacted asset play
export const PLAY_REDACTED_ASSET = createAction('redacted-asset/play');

// download
export const DOWNLOAD_ASSET = createAction('asset/download-redacted-asset');
export const RETRY_DOWNLOAD = createAction<PollDownloadAssetStop>(
  'asset/retry_download'
);
export const DOWNLOAD_ALL_FILES_SNACKBAR = createAction(
  'asset/download-all-files-snackbar'
);
export const DOWNLOAD_ASSET_JOB_STATUS = createGraphQLRequestAction(
  'request to get status of Download Asset job'
);
export const DOWNLOAD_ASSET_JOB_STATUS_SUCCESS =
  createGraphQLSuccessAction<FecthDownloadAssetStatusResponse>(
    'get status of Download Asset job successfully'
  );
export const DOWNLOAD_ASSET_JOB_STATUS_FAILURE = createGraphQLFailureAction(
  'get status of Download Asset job failed'
);
export const FETCH_DOWNLOAD_ASSET = createGraphQLRequestAction(
  'asset/send-to-download-redacted-asset-fetch'
);
export const FETCH_DOWNLOAD_ASSET_SUCCESS =
  createGraphQLSuccessAction<V3FJobResponse>(
    'asset/send-to-download-redacted-asset-fetch-success'
  );
export const FETCH_DOWNLOAD_ASSET_FAILURE = createGraphQLFailureAction(
  'asset/send-to-download-redacted-asset-fetch-failure'
);
export const STOP_POLLING_DOWNLOAD_JOB_STATUS =
  'asset/get-job-status-stop-polling-download';
export const UPDATE_DOWNLOAD_URL = createAction<{
  downloadUrl?: string;
}>('asset/update-download-url');
// polling download
export const POLL_DOWNLOAD_ASSET = 'asset/send-to-download-redacted-asset-poll';
export const POLL_DOWNLOAD_ASSET_SUCCESS =
  createGraphQLSuccessAction<PollToDownloadResponse>(
    'asset/send-to-download-redacted-asset-poll-success'
  );
export const POLL_DOWNLOAD_ASSET_FAILURE = createGraphQLFailureAction(
  'asset/send-to-download-redacted-asset-poll-failure'
);
export const RETRIEVE_TDO_FROM_JOB = 'asset/retrieve-tdo-from-job';
export const RETRIEVE_TDO_FROM_JOB_SUCCESS =
  createGraphQLSuccessAction<RetrieveTdoIdFromJobIdResponse>(
    'asset/retrieve-tdo-from-job-success'
  );
export const RETRIEVE_TDO_FROM_JOB_FAILURE = createGraphQLFailureAction(
  'asset/retrieve-tdo-from-job-failure'
);

interface BasicIntegrationAuth {
  username?: string;
  password?: string;
}
// GovQA
export const SEND_REDACTED_FILE_TO_GOVQA = createAction<
  BasicIntegrationAuth | undefined
>('redacted_send/SEND_REDACTED_FILE_TO_GOVQA');
// FOIAXpress
export const SEND_REDACTED_FILE_TO_FOIAXPRESS = createAction<
  BasicIntegrationAuth | undefined
>('redacted_send/SEND_REDACTED_FILE_TO_FOIAXPRESS');
// External Integration
export const SEND_REDACTED_FILE_TO_EXTERNAL_INTEGRATION = createAction<
  BasicIntegrationAuth | undefined
>('redacted_send/SEND_REDACTED_FILE_TO_EXTERNAL_INTEGRATION');
// Casepoint
export const SEND_REDACTED_FILE_TO_CASEPOINT = createAction<
  BasicIntegrationAuth | undefined
>('redacted_send/SEND_REDACTED_FILE_TO_CASEPOINT');
// Exterro
// export const SEND_REDACTED_FILE_TO_EXTERRO = createAction<any | undefined>(
export const SEND_REDACTED_FILE_TO_EXTERRO = createAction<
  BasicIntegrationAuth | undefined
>('redacted_send/SEND_REDACTED_FILE_TO_EXTERRO');
// Nuix
export const SEND_REDACTED_FILE_TO_NUIX = createAction<
  BasicIntegrationAuth | undefined
>('redacted_send/SEND_REDACTED_FILE_TO_NUIX');

// update redacted file metrics
export const UPDATE_REDACTED_FILE_METRICS = createAction('metrics/update');
export const FETCH_UPDATE_REDACTED_FILE_METRICS = createGraphQLRequestAction(
  'metrics/update-fetch'
);
export const FETCH_UPDATE_REDACTED_FILE_METRICS_SUCCESS =
  createGraphQLSuccessAction<UpdateLastRedactedFileMetricsResponse>(
    'metrics/update-fetch-success'
  );
export const FETCH_UPDATE_REDACTED_FILE_METRICS_FAILURE =
  createGraphQLFailureAction('metrics/update-fetch-failure');

// bounding poly actions
export const CHANGE_OVERLAY = createAction<{
  id: string;
  groupId: string;
  boundingPoly: BoundingPolyRect;
  type: OBJECT_TYPE;
}>('overlay/change');
export const NEW_OVERLAY = createAction<{
  boundingPoly: BoundingPolyRect;
  id: string;
  groupId: string;
  currentPosition: number;
  shift: boolean;
}>('overlay/new');

export const DELETE_CLUSTER = createAction<{
  clusterId: string;
  udrGroupIds: string[];
  detectionGroups: GroupIdAndType[];
}>('overlay/delete cluster');

export const DELETE_DETECTION_OVERLAY = createAction<{
  boundingBox: GroupedBoundingPoly;
}>('overlay/delete detection overlay');
export const DELETE_DETECTION_IN_FRAME = createAction<{
  boundingBox: GroupedBoundingPoly;
  timeMs: number;
}>('overlay/delete detection in frame');

export interface DeleteSegmentPayload {
  segmentId?: string; // only available when deleting via cluster panel
  timeMs: number;
  groupId: string;
  type: SEGMENT_TYPE;
}
export const DELETE_DETECTION_SEGMENT = createAction<DeleteSegmentPayload>(
  'overlay/delete detection segment'
);
export const DELETE_DETECTION_GROUP = createAction<{
  boundingBox: GroupedBoundingPoly;
}>('overlay/delete detection group');

export const DELETE_UDR_OVERLAY = createAction<{
  boundingBox: GroupedBoundingPoly;
}>('overlay/delete udr overlay');
export const DELETE_UDR_IN_FRAME = createAction<{
  boundingBox: GroupedBoundingPoly;
  timeMs: number;
}>('overlay/delete udr in frame');
export const DELETE_UDR_SEGMENT = createAction<DeleteSegmentPayload>(
  'overlay/delete udr segment'
);
export const DELETE_UDR_GROUP = createAction<{
  boundingBox: GroupedBoundingPoly;
}>('overlay/delete udr group');

export const RESIZE_OVERLAY_SEGMENT = createAction<{
  boundingBox: GroupedBoundingPoly;
}>('overlay/resize segment');

export const CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM = createAction<{
  groupId: string;
  id: string;
  seriesItem: UDRsPolyAssetGroupSeriesItem;
  changeType: 'start' | 'end' | 'stick';
}>('overlay/CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM');
export const CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM_SUBMIT = createAction<{
  id: string;
  groupId: string;
  changeType: 'start' | 'end' | 'stick';
  seriesItem: UDRsPolyAssetGroupSeriesItem;
}>('overlay/CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM_SUBMIT');
export const UPDATE_START_END_UDRS_LIVE_TRACKING = createAction<{
  startPayload: {
    id: string;
    groupId: string;
    changeType: 'start' | 'end' | 'stick';
    seriesItem: UDRsPolyAssetGroupSeriesItem;
  };
  endPayload: {
    id: string;
    groupId: string;
    changeType: 'start' | 'end' | 'stick';
    seriesItem: UDRsPolyAssetGroupSeriesItem;
  };
}>('overlay/UPDATE_START_END_UDRS_LIVE_TRACKING');
export const SAVE_LOCAL_UDR_ASSETS = createAction<UDRsPolyAsset>(
  'overlay/SAVE_LOCAL_UDR_ASSETS'
);

export const CHANGE_UDR_GROUP_LABEL = createAction<{
  groupId: string;
  label: string;
}>('overlay/CHANGE_UDR_GROUP_LABEL');
export const UDR_SELECTED_FROM_TIMELINE = createAction<{
  groupId: string;
  id: string;
  seriesItem: UDRsPolyAssetGroupSeriesItem;
}>('overlay/UDR_SELECTED_FROM_TIMELINE');

export const UPDATE_CLUSTER_ASSIGNMENT = createAction<{
  assignedClusterId: string | undefined;
  groupIds?: string[] | undefined;
  segments?: ClusterSegment[] | undefined;
  clusterUserLabel?: string;
}>('update cluster assignment');

export const ON_SPRAY_PAINT_SAVE = createAction<{
  id: string;
  groupId: string;
  series: { boundingPoly: BoundingPolyRect; time: number }[];
}>('udr/ON_SPRAY_PAINT_SAVE');

// close
export const REDIRECT_TO_HOME = createAction('redirect/to-home');
export const PAGE_LOADED = createAction('page/loaded');

// settings
export const FETCH_UPDATE_SETTINGS = 'details/update-settings';
export const FETCH_UPDATE_SETTINGS_SUCCESS =
  createGraphQLSuccessAction<UpdateSettingsOnDetailsResponse>(
    'details/update-settings-success'
  );
export const FETCH_UPDATE_SETTINGS_FAILURE = createGraphQLFailureAction(
  'details/update-settings-failure'
);

//    VTN-10079 MDP Page not updating when Redact Engine Process Completes
export const REDACTION_JOB_STATUS = 'request to get status of Redaction job ';
export const REDACTION_JOB_STATUS_SUCCESS =
  createGraphQLSuccessAction<FetchRedactionJobStatusResponse>(
    'get status of Redaction job successfully'
  );
export const REDACTION_JOB_STATUS_FAILURE = createGraphQLFailureAction(
  'get status of Redaction job failed'
);
export const REDACTION_JOB_STATUS_ACTION_STOP_POLLING =
  'redaction/get-job-status-stop-polling';
export const FETCH_REDACTED_MEDIA =
  'request to get redacted media after polling';
export const FETCH_REDACTED_MEDIA_SUCCESS =
  createGraphQLSuccessAction<FetchRedactedMediaResponse>(
    'request to get redacted media after polling'
  );
export const FETCH_REDACTED_MEDIA_FAILURE = createGraphQLFailureAction(
  'request to get redacted media after polling'
);

export const SHOW_BLOCK_NAVIGATION_MODAL = createAction<{
  message: string;
  canLeave: (can: boolean) => void;
}>('show block navigation modal');
export const HIDE_BLOCK_NAVIGATION_MODAL = createAction(
  'hide block navigation modal'
);

export const ACTION_KEY_SHIFT = createAction<{ isKeyDown: boolean }>(
  'key shift'
);

export const MEDIA_HAS_AUDIO = createAction<{
  hasAudio: boolean;
}>('video has audio');

export const VIDEO_HAS_LOADED = createAction<{ videoLoaded: boolean }>(
  'video has loaded'
);

export const SET_THUMBNAIL_TRACK = createAction<{
  thumbnailTracks: shaka.Track;
}>('set thumbnail track');

export const SET_SHAKA_PLAYER = createAction<{
  shakaPlayer: shaka.Player | null;
}>('set shaka player');

export const EXIT_MEDIA_DETAILS = createAction<{
  tdoId?: TDOId;
  userId?: string;
}>('exit media details');

export const SET_GOVQA_SESSIONID = createAction<{
  sessionId: string | null;
}>('integrations/SET_GOVQA_SESSIONID');
export const PATCH_GOVQA_STATE = createAction<Partial<GovQAIntegration>>(
  'integrations/PATCH_GOVQA_STATE'
);
export const PATCH_EXTERNAL_INTEGRATION_STATE = createAction<
  Partial<ExternalIntegration>
>('integrations/PATCH_EXTERNAL_INTEGRATION_STATE');
export const PATCH_FOIAXPRESS_STATE = createAction<
  Partial<FOIAXpressIntegration>
>('integrations/PATCH_FOIAXPRESS_STATE');
export const PATCH_CASEPOINT_STATE = createAction<
  Partial<CasepointIntegration>
>('integrations/PATCH_CASEPOINT_STATE');
export const PATCH_EXTERRO_STATE = createAction<Partial<ExterroIntegration>>(
  'integrations/PATCH_EXTERRO_STATE'
);
export const PATCH_NUIX_STATE = createAction<Partial<NuixIntegration>>(
  'integrations/PATCH_NUIX_STATE'
);

export const LOAD_ENGINE_RESULTS_AFTER_DETECT = createAction<void>(
  'integrations/LOAD_ENGINE_RESULTS_AFTER_DETECT'
);

export const TOGGLE_CASE_MEDIA_DRAWER = createAction(
  'details/TOGGLE_CASE_MEDIA_DRAWER'
);
export const SET_IS_SAVE_RUNNING = createAction<{
  isSaveRunning: boolean;
}>('status/SET_IS_SAVE_RUNNING');

// HEAD DETECTION
// change buttonDetectFaceDisable is true when has task headDetection Running
export const UN_DISABLE_FACE_BUTTON = createAction<void>(
  'head/UN_DISABLE_FACE_BUTTON'
);
export const DISABLE_FACE_BUTTON = createAction<void>(
  'head/DISABLE_FACE_BUTTON'
);
export const actionUnDisableFacesButton = UN_DISABLE_FACE_BUTTON;
export const actionDisableFacesButton = DISABLE_FACE_BUTTON;

export const actionFetchEngineResultsAfterDetect =
  LOAD_ENGINE_RESULTS_AFTER_DETECT;

export const goToMainPage = () => ROUTE_HOME();

export const goToResultsTab = () => SELECT_RESULTS_TAB();

export const actionKeyShift = (isKeyDown: boolean) =>
  ACTION_KEY_SHIFT({
    isKeyDown: isKeyDown,
  });

export const onDiscardChange = DISCARD_CHANGES;

export type UpdateAssetPayload =
  | undefined
  | {
      readonly force?: boolean;
      readonly alternativeAction?: string;
    };
export const actionUpdateAsset = (payload: UpdateAssetPayload) =>
  FETCH_UPDATE_ASSET_ACTION(payload);

export const deleteClusterAction = (
  clusterId: string,
  udrGroupIds: string[],
  detectionGroups: GroupIdAndType[]
) =>
  markWorkerAction(DELETE_CLUSTER({ clusterId, udrGroupIds, detectionGroups }));

export const deleteDetectionOverlayAction = (
  boundingBox: GroupedBoundingPoly
) => markWorkerAction(DELETE_DETECTION_OVERLAY({ boundingBox }));

export const deleteDetectionInFrameAction = (
  boundingBox: GroupedBoundingPoly,
  timeMs: number
) => markWorkerAction(DELETE_DETECTION_IN_FRAME({ boundingBox, timeMs }));

export const deleteDetectionSegmentAction = (payload: DeleteSegmentPayload) =>
  markWorkerAction(DELETE_DETECTION_SEGMENT(payload));

export const deleteDetectionGroupAction = (boundingBox: GroupedBoundingPoly) =>
  markWorkerAction(DELETE_DETECTION_GROUP({ boundingBox }));

export const deleteUDROverlayAction = (boundingBox: GroupedBoundingPoly) =>
  markWorkerAction(DELETE_UDR_OVERLAY({ boundingBox }));

export const deleteUDRInFrameAction = (
  boundingBox: GroupedBoundingPoly,
  timeMs: number
) => markWorkerAction(DELETE_UDR_IN_FRAME({ boundingBox, timeMs }));

export const deleteUDRSegmentAction = (payload: DeleteSegmentPayload) =>
  markWorkerAction(DELETE_UDR_SEGMENT(payload));

export const deleteUDRGroupAction = (boundingBox: GroupedBoundingPoly) =>
  markWorkerAction(DELETE_UDR_GROUP({ boundingBox }));

export const actionChangeOverlay = (payload: {
  id: string;
  groupId: string;
  boundingPoly: BoundingPolyRect;
  type: OBJECT_TYPE;
}) => markWorkerAction(CHANGE_OVERLAY(payload));

export const actionUDRSelectedFromTimeline = (payload: {
  groupId: string;
  id: string;
  seriesItem: UDRsPolyAssetGroupSeriesItem;
}) => UDR_SELECTED_FROM_TIMELINE(payload);

export const actionChangeUDRsPolyAssetGroupSeriesItem = (payload: {
  id: string;
  groupId: string;
  seriesItem: UDRsPolyAssetGroupSeriesItem;
  changeType: 'start' | 'end' | 'stick';
}) => CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM(payload);

export const actionChangeUDRsPolyAssetGroupSeriesItemSubmit = (payload: {
  id: string;
  groupId: string;
  changeType: 'start' | 'end' | 'stick';
  seriesItem: UDRsPolyAssetGroupSeriesItem;
}) => CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM_SUBMIT(payload);

export const actionUpdateStartEndLiveTrackingUdr = (payload: {
  startPayload: {
    id: string;
    groupId: string;
    changeType: 'start' | 'end' | 'stick';
    seriesItem: UDRsPolyAssetGroupSeriesItem;
  };
  endPayload: {
    id: string;
    groupId: string;
    changeType: 'start' | 'end' | 'stick';
    seriesItem: UDRsPolyAssetGroupSeriesItem;
  };
}) => UPDATE_START_END_UDRS_LIVE_TRACKING(payload);

export const saveLocalUDRAssets = (payload: UDRsPolyAsset) =>
  markWorkerAction(SAVE_LOCAL_UDR_ASSETS(payload));

export const actionChangeUDRsPolyAssetGroupLabel = (payload: {
  groupId: string;
  label: string;
}) => markWorkerAction(CHANGE_UDR_GROUP_LABEL(payload));

export const actionUpdateClusterAssignment = ({
  assignedClusterId,
  segments,
  groupIds,
  clusterUserLabel,
}: {
  assignedClusterId: string | undefined;
  clusterUserLabel?: string;
  segments?: ClusterSegment[] | undefined;
  groupIds?: string[] | undefined;
}) =>
  markWorkerAction(
    UPDATE_CLUSTER_ASSIGNMENT({
      assignedClusterId,
      clusterUserLabel,
      segments,
      groupIds,
    })
  );

export interface IChangeClusterLabelRequest {
  clusterId: string;
  type: string;
  userLabel: string;
}
export const actionChangeClusterLabel = (payload: IChangeClusterLabelRequest) =>
  markWorkerAction(CHANGE_CLUSTER_LABEL(payload));

export interface IChangeClusterSegmentSortTypeRequest {
  clusterId: string;
  segmentSortType: CLUSTER_SEGMENT_SORT_TYPE;
}
export const actionChangeSegmentSortType = (
  payload: IChangeClusterSegmentSortTypeRequest
) => markWorkerAction(CHANGE_CLUSTER_SEGMENT_SORT_TYPE(payload));

export interface IChangeClusterPinnedRequest {
  clusterId: string;
  type: string;
  pinnedDate: number | undefined;
}
export const actionChangeClusterPinned = (
  payload: IChangeClusterPinnedRequest
) =>
  markWorkerAction(CHANGE_CLUSTER_PINNED({ ...payload, generatedId: uuid() }));

export const sprayPaintSaveAction = (payload: {
  id: string;
  groupId: string;
  series: { boundingPoly: BoundingPolyRect; time: number }[];
}) => markWorkerAction(ON_SPRAY_PAINT_SAVE(payload));

export const actionMediaHasAudio = (hasAudio: boolean) =>
  MEDIA_HAS_AUDIO({ hasAudio });

export const actionVideoHasLoaded = (videoLoaded: boolean) =>
  VIDEO_HAS_LOADED({
    videoLoaded,
  });

export const setThumbnailTracks = (thumbnailTracks: shaka.Track) =>
  SET_THUMBNAIL_TRACK({ thumbnailTracks });

export const setShakaPlayer = (shakaPlayer: shaka.Player | null) =>
  SET_SHAKA_PLAYER({
    shakaPlayer,
  });

export const actionExitMediaDetails = (tdoId?: TDOId, userId?: string) =>
  EXIT_MEDIA_DETAILS({ tdoId, userId });

export const setFaceHighlight = (
  payload: {
    id: string;
    timeMs: number;
    type: OBJECT_TYPE;
    groupId: string;
  } | null
) => FACE_HIGHLIGHT(payload);

export const setGovQASessionId = (sessionId: string | null) =>
  SET_GOVQA_SESSIONID({ sessionId });

export const patchGovQAState = (payload: Partial<GovQAIntegration>) =>
  PATCH_GOVQA_STATE(payload);

export const patchExternalIntegrationState = (
  payload: Partial<ExternalIntegration>
) => PATCH_EXTERNAL_INTEGRATION_STATE(payload);

export const patchFOIAXpressState = (payload: Partial<FOIAXpressIntegration>) =>
  PATCH_FOIAXPRESS_STATE(payload);

export const patchCasepointState = (payload: Partial<CasepointIntegration>) =>
  PATCH_CASEPOINT_STATE(payload);

export const patchExterroState = (payload: Partial<ExterroIntegration>) =>
  PATCH_EXTERRO_STATE(payload);

export const patchNuixState = (payload: Partial<NuixIntegration>) =>
  PATCH_NUIX_STATE(payload);

export const setAudiowaves = (payload: AudiowaveFrame[]) =>
  SET_AUDIOWAVES(payload);

export const toggleCaseMediaDrawer = () => TOGGLE_CASE_MEDIA_DRAWER();

export const actionSetIsSaveIsRunning = (payload: { isSaveRunning: boolean }) =>
  SET_IS_SAVE_RUNNING(payload);

export const TDO_NAME_CHANGE_SUCCESS =
  createGraphQLSuccessAction<UpdateNameResponse>(
    'name/send-to-change-tdo-name-success'
  );

export const TDO_NAME_CHANGE_FAILURE = createGraphQLFailureAction(
  'name/send-to-change-tdo-name-failure'
);

export const SET_TDO_NAME = createAction<string>('name/SET_TDO_NAME');
export const setTDOName = (name: string) => SET_TDO_NAME(name);

export const ROTATE_VIDEO = createAction<number>('details/rotate-video');
export const rotateVideo = (degrees: number) => ROTATE_VIDEO(degrees);

export const CREATE_ROTATE_VIDEO_JOB_SUCCESS = createGraphQLSuccessAction(
  'details/rotate-video-successfully'
);

export const CREATE_ROTATE_VIDEO_JOB_FAILURE = createGraphQLFailureAction(
  'details/rotate-video-failure'
);

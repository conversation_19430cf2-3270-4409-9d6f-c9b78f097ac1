import { Case, TDOId, UploadMedia } from '@cbsa-modules/universal';
import { FETCH_MEDIA_RESPONSE } from './services/queries/fetchMedia';
import { ElementOf } from 'ts-essentials';

export interface AppWrapperStore {
  readonly cases: { readonly [id: string]: Case };
  readonly media: {
    readonly [id: TDOId]: ElementOf<
      FETCH_MEDIA_RESPONSE['folder']['childTDOs']['records']
    >;
  };
  readonly uploadingFiles: {
    readonly queue: Array<UploadMedia>;
    readonly processing: Array<UploadMedia>;
    readonly successful: Array<UploadMedia>;
    readonly failed: Array<UploadMedia>;
  };
  readonly loaders: {
    readonly isFetchingCases: boolean;
    readonly isFetchingMedia: boolean;
  };
}

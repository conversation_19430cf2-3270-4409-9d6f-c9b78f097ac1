import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';

import { SnackbarProvider } from 'notistack';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';

import { defaultTheme } from '@cbsa/styles/materialThemes';
import { helpers } from '@veritone/glc-redux';

import configureAppStore from '@cbsa-state/configureStore';
import SnackbarCustom from '@common-components/Snackbar';
import UserOnboardingRoot from '@user-onboarding/components/UserOnboardingRoot';

// Load external/global CSS
import '@resources/styles/veritone-icon.css';
import '@resources/styles/veritone-engines.css';
import '@resources/styles/common-global.scss';
import '@resources/styles/cbsa-global.scss';
import 'video-react/dist/video-react.css';
import React from 'react';

const styleNode = document.createComment('jss-insertion-point');
if (document.head) {
  document.head.insertBefore(styleNode, document.head.firstChild);
}

const store = configureAppStore();

const { handleImplicitRedirect } = helpers;

/**
 * Bootstrap the necessary dependencies into your React app.
 */
export function bootstrap(Application: React.JSX.Element, element: string) {
  if (window.name === '_auth') {
    // If this is an OAuth redirect window, deal with the OAuth response.
    return handleImplicitRedirect(window.location.hash, window.opener);
  }
  const container = document.getElementById(element);
  const root = createRoot(container!);
  root.render(
    <StyledEngineProvider injectFirst>
      <ThemeProvider theme={defaultTheme}>
        <Provider store={store}>
          <SnackbarProvider
            autoHideDuration={5000}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            style={{ marginTop: '5px' }}
          >
            {Application}
            <SnackbarCustom />
            <UserOnboardingRoot />
          </SnackbarProvider>
        </Provider>
      </ThemeProvider>
    </StyledEngineProvider>
  );
}

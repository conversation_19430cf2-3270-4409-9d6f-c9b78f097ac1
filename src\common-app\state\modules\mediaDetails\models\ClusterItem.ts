import {
  CLUSTER_TYPE,
  GROUP_TYPE,
  SEGMENT_TYPE,
  CLUSTER_SEGMENT_SORT_TYPE,
} from '@helpers/constants';
import { IndividualRedactionCode } from '@redact-modules/redactionCodes/models';
import { RedactionConfig } from '@common-modules/mediaDetails/models/RedactionPolys';
import { NonEmptyArray } from 'ts-essentials';

export interface ClusterItem {
  readonly id: string;
  readonly userLabel?: string;
  readonly pinnedDate: number | undefined;
  readonly type: CLUSTER_TYPE;
  readonly dominantType?: SEGMENT_TYPE; // for mixed cluster useful to know most common segment type
  readonly segmentSortType: CLUSTER_SEGMENT_SORT_TYPE;
  readonly picUri: string;
  readonly groups: Readonly<NonEmptyArray<ClusterItemGroup>>; // now cluster can hold multiple groups
  readonly segments: Readonly<NonEmptyArray<ClusterSegment>>; // all segments concatenated together - this might be temporary
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
  readonly isMergedGroup?: boolean;
  readonly version?: number; // do we need this?
}

export interface ClusterItemGroup {
  readonly id: string;
  readonly userLabel?: string;
  readonly clusterId: string;
  readonly type: GROUP_TYPE;
  readonly picUri: string;
  readonly segments: Readonly<NonEmptyArray<ClusterSegment>>;
  readonly redactionCode?: IndividualRedactionCode;
  readonly redactionConfig?: RedactionConfig;
  // readonly startTimeMs: number;
  // readonly stopTimeMs: number;
  readonly version?: number; // do we need this?
}
export interface ClusterSegment {
  readonly id: string;
  readonly groupId: string; // reference to ClusterItemGroup it belongs to
  readonly type: SEGMENT_TYPE;
  readonly startTimeMs: number;
  readonly stopTimeMs: number;
  readonly numAutoInterpolations: number;
  readonly isManualInterpolation: boolean;
  readonly subsegmentIds: ReadonlyArray<string>; // TODO: Can this be guaranteed to be NonEmptyArray?
  readonly objectIds: ReadonlyArray<string>; // TODO: Can this be guaranteed to be NonEmptyArray?
}

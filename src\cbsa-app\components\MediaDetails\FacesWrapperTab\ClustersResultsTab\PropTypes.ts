import {
  ClusterItem,
  FilterParameters,
  HighlightedOverlay,
  MediaPlayerTime,
} from '@common-modules/mediaDetails/models';
import {
  actionSetSelectedGroups,
  actionChangeClusterLabel,
  actionSetSortBy,
  selectDetectionRunning,
  selectGlobalSettings,
  selectSelected,
  selectSortBy,
  selectDataFetchedForDetectionType,
} from '@common-modules/mediaDetails';
import { OBJECT_TYPE } from '@helpers/constants';

export default interface PropTypes {
  readonly clusterList: ReadonlyArray<ClusterItem>;
  readonly detectionRunning: ReturnType<typeof selectDetectionRunning>;
  readonly filterParameters: FilterParameters;
  readonly highlightedOverlay?: HighlightedOverlay | undefined;
  readonly selected: ReturnType<typeof selectSelected>;
  readonly settings: ReturnType<typeof selectGlobalSettings>;
  readonly sortBy: ReturnType<typeof selectSortBy>;
  readonly setSelectedGroups: typeof actionSetSelectedGroups;
  readonly changeClusterLabel: typeof actionChangeClusterLabel;
  readonly setSortBy: typeof actionSetSortBy;
  readonly buttonDetectFaceDisable: boolean;
  readonly detectFaces: () => void;
  readonly seekMediaTime: (payload: MediaPlayerTime) => void;
  readonly onFaceHighlight: (payload: {
    id: string;
    timeMs: number;
    type: OBJECT_TYPE;
    groupId: string;
  }) => void;
  readonly dataFetchedForDetectionType: ReturnType<
    typeof selectDataFetchedForDetectionType
  >;
}

export type SeekMediaTimePayload = MediaPlayerTime & {
  id: string;
  // type: string;
  type: OBJECT_TYPE;
  groupId: string;
};

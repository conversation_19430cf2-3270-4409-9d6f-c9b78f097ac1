import { createSelector } from 'reselect';

import { selectConfigEngines } from '../selectors';
import { defaultJobSlice, namespace, OpticalTrackingStore } from './store';
import { selectCurrentTdoId } from '../../mediaDetails';
import { TDOId } from '@common-modules/universal/models/Brands';
export const selectOpticalEngineId = createSelector(
  selectConfigEngines,
  (engines) => engines.opticalTrackingEngineId
);

export const selectStore = (state: { [namespace]: OpticalTrackingStore }) =>
  state[namespace];

export const selectAllEngineStatuses = (tdoId: TDOId) =>
  createSelector(selectStore, (s) => s[tdoId] || {});

export const selectAllEngineStatusesByCurrentTdoId = createSelector(
  selectStore,
  selectCurrentTdoId,
  (s, tdoId) => (tdoId ? s[tdoId] || {} : {})
);

export const selectEngineStatus = (tdoId: TDOId, jobId: string) =>
  createSelector(
    selectAllEngineStatuses(tdoId),
    (s) => s[jobId] || defaultJobSlice()
  );

export const selectEngineStatusByCurrentTdoId = (jobId: string) =>
  createSelector(
    selectAllEngineStatusesByCurrentTdoId,
    (s) => s[jobId] || defaultJobSlice()
  );

import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import { redactedMediaQuery } from '../../src/api/queries';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { redactedMedia } from '../../src/controllers/redactedMedia';

describe('redactedMedia', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
  });

  it('retrieves redacted media and audit log files w/ tdoId', async () => {
    const params = { tdoId: '123456789' };
    const req = getMockReq({
      params,
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.resolve());

    await redactedMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, redactedMediaQuery(params.tdoId));
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('fails to retrieve redacted media and audit log files w/o tdoId', async () => {
    const req = getMockReq({
      params: { tdoId: '' },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.reject());

    await redactedMedia(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.caseIdRequired });
  });

  it('fails to retrieve redacted media and audit log files w/ error', async () => {
    const params = { tdoId: '123456789' };
    const req = getMockReq({
      params,
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementation(() => Promise.reject());

    await redactedMedia(req, res);
    expect(error).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, redactedMediaQuery(params.tdoId));
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
  });
});

import { createAction } from '@reduxjs/toolkit';
import { ApplicationConfigsResponse } from './services';
import { createGraphQLSuccessAction } from '@helpers/callGraphQLApi';

export const SHOW_OR_HIDE_APPBAR_MENU = createAction<boolean>(
  'appbar/SHOW_OR_HIDE_APPBAR_MENU'
);
export const actionOpenOrHideAppBarMenu = (showOrHide: boolean) =>
  SHOW_OR_HIDE_APPBAR_MENU(showOrHide);

export const FETCH_APPLICATION_CONFIGS_SUCCESS =
  createGraphQLSuccessAction<ApplicationConfigsResponse>(
    'app/FETCH_APPLICATION_CONFIGS_SUCCESS'
  );

### Steps to create comments schema using core-graphql queries

1. Create new data registry 
```
mutation createDataRegistry {
  createDataRegistry(input: {
    id: "e7cc4338-5297-450f-8988-d285157d4474"
    name: "redact-media-comment"
    description: "redact media comment"
    source: "veritone-1.datasets"
  }) {
    id
  }
}
```

2. Create Schema Draft
```
# Note: Use the `dataRegistryId` value from the `createDataRegistry` mutation.
mutation createSchemaDraft {
  upsertSchemaDraft(input: {
    dataRegistryId: "e7cc4338-5297-450f-8988-d285157d4474"
    majorVersion: 1,
    schema: {
  	  type: "object",
            title: "redact-media-comment",
            required: [
              "comment",
              "createdBy",
              "createdDateTime",
              "mediaTimestamp",
              "modifiedDateTime",
              "tdoId",
              "commentId",
              "done",
              "read",
              "archived",
              "deleted",
              "searchName"
            ],
            properties: {
              done: {
                type: "boolean"
              },
              read: {
                type: "boolean"
              },
              tdoId: {
                type: "string"
              },
              comment: {
                type: "string",
                maxLength: 300
              },
              deleted: {
                type: "boolean"
              },
              archived: {
                type: "boolean"
              },
              commentId: {
                type: "string"
              },
              createdBy: {
                type: "string"
              },
              searchName: {
                type: "string"
              },
              mediaTimestamp: {
                type: "integer"
              },
              createdDateTime: {
                type: "dateTime"
              },
              modifiedDateTime: {
                type: "dateTime"
              }
            },
            description: "redact media comment"
    }
  }) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}
```

3. Publish Schema Draft

```
# Note: Pass in the Schema ID for the `id` value
mutation publishSchemaDraft($id: ID!) {
  updateSchemaState(input: {
    id: $id
    status: published
  }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```


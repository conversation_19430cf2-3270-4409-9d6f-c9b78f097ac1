import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles((theme) => ({
  tdoWrapper: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    minWidth: '180px',
    height: '145px',

    '& .Thumbnail': {
      background: '#EEE',
      display: 'flex',
      height: '120px',
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'center',

      '& .Checkbox': {
        padding: 0,
        position: 'absolute',
        top: '10px',
        left: '10px',
        width: '16px',
        height: '16px',
        color: 'white !important',
      },

      '& .MoreVert': {
        background: ' #0006',
        borderRadius: '2px',
        cursor: 'pointer',
        position: 'absolute',
        top: '10px',
        right: '10px',
        width: '16px',
        height: '16px',

        svg: {
          verticalalign: 'top',
        },
      },

      '& img': {
        maxHeight: '100%',
        maxWidth: '100%',
      },

      '&.active': {
        border: '2px solid #004CFF',
      },

      '&.processed': {
        border: '2px solid #43A047',
      },

      '&.error': {
        border: `2px solid ${theme.palette.error.main}`,
      },
    },

    '& .Status': {
      background: '#C8D7FB',
      color: '#004CFF',
      display: 'flex',
      fontSize: '12px',
      height: '25px',
      justifyContent: 'center',
      alignItems: 'center',
      textTransform: 'uppercase',

      '&.processed': {
        background: theme.palette.success.light,
        color: theme.palette.success.main,
      },

      '&.error': {
        background: theme.palette.error.light,
        color: theme.palette.error.main,
      },
    },

    '& .MuiCircularProgress-root': {
      color: theme.palette.primary.main,
    },
  },
}));

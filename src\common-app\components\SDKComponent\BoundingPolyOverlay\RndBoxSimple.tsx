import { clamp } from 'lodash';
import { useSelector } from 'react-redux';
import { CSSProperties, memo } from 'react';
import * as styles from './styles.scss';
import { ShapeType } from '@common-modules/mediaDetails/models';
import { borderRadiusByPosition } from './helpers';
import { selectGlobalSettings } from '@common-modules/mediaDetails';
import { IndividualRedactionCode } from '@redact-modules/redactionCodes/models';
import {
  OBJECT_TYPE,
  REDACTION_CODE_FONT_SCALING_COEFF,
  REDACTION_CODE_FONT_SIZE,
  REDACTION_CODE_MIN_FONT_PX,
} from '@helpers/constants';

const RndBoxSimple = ({
  shape,
  style,
  redactionCode,
  type,
  position,
  size,
  videoDimensions,
}: Props) => {
  const globalSettings = useSelector(selectGlobalSettings);
  const defaultShape = (
    {
      head: globalSettings.objectTypeEffects.head.shapeType,
      udr: globalSettings.objectTypeEffects.udr.shapeType,
      laptop: globalSettings.objectTypeEffects.laptop.shapeType,
      vehicle: globalSettings.objectTypeEffects.vehicle.shapeType,
      licensePlate: globalSettings.objectTypeEffects.plate.shapeType,
      notepad: globalSettings.objectTypeEffects.notepad.shapeType,
      card: globalSettings.objectTypeEffects.card.shapeType,
      poim: 'rectangle',
      person: globalSettings.objectTypeEffects.person.shapeType,
    } as const
  )[type];

  const redactionCodePosition = {
    center: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    top: {
      alignItems: 'flex-start',
      justifyContent: 'center',
    },
    bottom: {
      alignItems: 'flex-end',
      justifyContent: 'center',
    },
    above: {
      left: '50%',
      top: `-${clamp(position.y, 0, 20)}px`,
      position: 'absolute' as const,
      transform: 'translateX(-50%)',
    },
    below: {
      left: '50%',
      top: `calc(100% - ${clamp(
        size.height + position.y + 20 - (videoDimensions?.height ?? 1080),
        0,
        20
      )}px)`,
      position: 'absolute' as const,
      transform: 'translateX(-50%)',
    },
    left: {
      alignItems: 'center',
      justifyContent: 'flex-start',
    },
    right: {
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    default: {},
  }[redactionCode?.codeLocation ?? 'default'];

  return (
    <div
      className={styles.rndParent}
      style={{
        ...style,
        position: 'absolute',
        height: size.height,
        width: size.width,
        top: position.y,
        left: position.x,
        borderRadius: borderRadiusByPosition({
          defaultShape,
          position,
          shape,
          size,
          videoDimensions,
        }),
      }}
    >
      {redactionCode && (
        <div
          className={styles.code}
          style={{
            fontSize: `clamp(${REDACTION_CODE_MIN_FONT_PX}, ${
              REDACTION_CODE_FONT_SCALING_COEFF / redactionCode.code.length
            }cqw, ${
              REDACTION_CODE_FONT_SIZE *
              (document.getElementById('overlay-container')?.offsetHeight ?? // This calculation is using getElementById of the video container, videoDimensions is inaccurate when adjusting window sizes
                1080)
            }px)`,
            color: redactionCode.codeColor,
            height: '100%',
            display: 'flex',
            overflow: 'hidden',
            ...redactionCodePosition,
            justifyContent:
              size?.width < 35
                ? 'flex-start'
                : redactionCodePosition.justifyContent,
          }}
        >
          {redactionCode.code}
        </div>
      )}
    </div>
  );
};

interface Props {
  shape?: ShapeType;
  style: CSSProperties;
  redactionCode?: IndividualRedactionCode;
  type: OBJECT_TYPE;
  position: { x: number; y: number };
  size: { width: number; height: number };
  videoDimensions?: { width?: number; height?: number };
}

export default memo(RndBoxSimple);

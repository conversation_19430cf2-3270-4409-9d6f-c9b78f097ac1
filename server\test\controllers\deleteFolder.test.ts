import 'jest';
import { Logger } from '../../src/logger';
import * as gql from '../../src/api/callGraphql';
import { Messages } from '../../src/errors/messages';
import { getSdoIdQuery } from '../../src/api/queries';
import { StatusCodes } from '../../src/errors/statusCodes';
import { getMockReq, getMockRes } from '@jest-mock/express';
import { deleteFolder } from '../../src/controllers/deleteFolder';
import * as isLocked from '../../src/adapters/checkFolderLock';
import { FolderId } from '../../src/model/brands';

describe('deleteFolder', () => {
  const callGQL = jest.spyOn(gql, 'callGQL');
  const error = jest.spyOn(Logger, 'error');
  const { res, mockClear } = getMockRes();

  const checkFolderIsLocked = jest.spyOn(isLocked, 'checkFolderLockAdapter');
  checkFolderIsLocked.mockImplementation(() => Promise.resolve({ isFailed: false, isLocked: false, isJobRunning: false}));

  afterEach(() => {
    mockClear();
    jest.clearAllMocks();
    callGQL.mockReset();
    error.mockReset();
  });

  it('deletes a folder w/ folderId', async () => {
    const params = { folderId: '123456789' };
    const req = getMockReq({
      params,
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() => Promise.resolve({
      folder: {
        id: '123456789',
        treeObjectId: '123456789',
        contentTemplates: [],
      },
    }));
    callGQL.mockImplementationOnce(() => Promise.resolve({ searchMedia: { jsondata: { results: [] } } }));
    callGQL.mockImplementationOnce(() => Promise.resolve({ deleteFolder: { id: '123456789' } }));

    await deleteFolder(req, res);

    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(3);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, getSdoIdQuery(params.folderId as FolderId));
    expect(isLocked.checkFolderLockAdapter).toHaveBeenCalledTimes(1);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.Success);
  });

  it('fails to delete a folder w/o folderId', async () => {
    const req = getMockReq({
      params: { folderId: '' },
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() => Promise.resolve({
      folder: {
        id: '123456789',
        treeObjectId: '123456789',
        contentTemplates: [],
      },
    }));

    await deleteFolder(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(0);
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.folderIdRequired });
  });

  it('fails to delete a case', async () => {
    const params = { folderId: '123456789' };
    const req = getMockReq({
      params,
      headers: {
        authorization: 'Bearer token',
      },
    });

    callGQL.mockImplementationOnce(() => Promise.resolve({
      folder: {
        id: '123456789',
        treeObjectId: '123456789',
        contentTemplates: [{ sdoId: '123456789' }],
      },
    }));
    callGQL.mockImplementationOnce(() => Promise.resolve({ searchMedia: { jsondata: { results: [] } } }));
    callGQL.mockImplementationOnce(() => Promise.resolve({ deleteFolder: { id: '123456789' } }));

    await deleteFolder(req, res);
    expect(error).not.toHaveBeenCalled();
    expect(gql.callGQL).toHaveBeenCalledTimes(1);
    expect(gql.callGQL).toHaveBeenCalledWith(req.headers, getSdoIdQuery(params.folderId as FolderId));
    expect(res.status).toHaveBeenCalledWith(StatusCodes.BadRequest);
    expect(res.json).toHaveBeenCalledWith({ error: Messages.notCaseDeleteEndpoint });
  });
});

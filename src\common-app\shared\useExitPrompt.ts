import { useState, useEffect } from 'react';

const initBeforeUnLoad = (showExitPrompt: boolean) => {
  window.onbeforeunload = (event: BeforeUnloadEvent) => {
    if (showExitPrompt) {
      // TODO: When can drop using `window.event`
      // eslint-disable-next-line @typescript-eslint/no-deprecated
      const e = event || window.event;
      e.preventDefault();
      if (e) {
        // For browser compatibility - TODO: Potentially update to use visibility APIs rather than beforeunload
        // eslint-disable-next-line @typescript-eslint/no-deprecated
        e.returnValue = '';
      }
      return '';
    }
  };
};

// Hook
export default function useExitPrompt(
  bool: boolean
): [boolean, React.Dispatch<React.SetStateAction<boolean>>] {
  const [showExitPrompt, setShowExitPrompt] = useState(bool);

  window.onload = function () {
    initBeforeUnLoad(showExitPrompt);
  };

  useEffect(() => {
    initBeforeUnLoad(showExitPrompt);
  }, [showExitPrompt]);

  return [showExitPrompt, setShowExitPrompt];
}

// import { BoundingPoly } from './BoundingPoly';

// export interface BoundingPolyTrie {
//   readonly left?: BoundingPolyTrie;
//   readonly right?: BoundingPolyTrie;
//   readonly start: number;
//   readonly end: number;
//   readonly ms: number;
//   readonly value?: ReadonlyArray<BoundingPoly>;
// }

// export type Factory = (
//   fps: number
// ) => (
//   period: [number, number],
//   ps: ReadonlyArray<BoundingPoly>
// ) => BoundingPolyTrie | undefined;

// export type Splitter = (
//   p: [number, number]
// ) => [number, number, number, number];

// export type Searcher = (
//   c: BoundingPolyTrie,
//   mark: number
// ) => ReadonlyArray<BoundingPoly>;

// // /**
// //  * Create a tree collection from and array of BoundingPolys.
// //  * @param fps - the frames per second to support.
// //  * @returns tree factory
// //  */
// export const build: Factory = (fps) => {
//   const frameMs = 1000 / fps;
//   /**
//    * Trie factory.
//    * @param span - start and stop of current span.
//    * @param ps - array of BoundingPolys
//    */
//   return function factory(span, ps) {
//     if (ps.length === 0) {
//       return undefined;
//     }
//     const ms = span[1] - span[0];
//     if (ms <= frameMs) {
//       return {
//         start: span[0],
//         end: span[1],
//         ms,
//         value: mergePolys(ps),
//       };
//     }

//     const left = [];
//     const right = [];
//     const [ls, le, rs, re] = splitter(span);

//     for (const item of ps) {
//       // If item straddles the split
//       if (item.startTimeMs <= le && item.stopTimeMs >= rs) {
//         left.push({
//           ...item,
//           stopTimeMs: le,
//         });
//         right.push({
//           ...item,
//           startTimeMs: rs,
//         });
//       } else if (item.stopTimeMs < le) {
//         left.push(item);
//       } else if (item.startTimeMs >= rs) {
//         right.push(item);
//       }
//     }

//     return {
//       left: factory([ls, le], left),
//       right: factory([rs, re], right),
//       start: span[0],
//       end: span[1],
//       ms,
//     };
//   };
// };

// // /**
// //  * Search the given tree for all BoundingPolys that include the mark.
// //  * @param collection - a BoundingPolyTrie
// //  * @param pos - the current position to search
// //  */
// export const search: Searcher = (collection, pos) => {
//   let response: ReadonlyArray<BoundingPoly> = [];
//   let nextColl: BoundingPolyTrie | undefined = collection;

//   while (nextColl) {
//     response = merge(response, nextColl.value);
//     if (nextColl.left.start >= pos && nextColl.left.end < pos) {
//       nextColl = nextColl.left;
//       continue;
//     }
//     if (nextColl.right.start >= pos && nextColl.right.end < pos) {
//       nextColl = nextColl.right;
//       continue;
//     }
//     break;
//   }

//   return response;
// };

// const splitter: Splitter = ([start, end]) => {
//   const half = ((end - start) / 2) | 0;
//   return [start, start + half, start + half + 1, end];
// };

// const merge = (
//   a: ReadonlyArray<BoundingPoly>,
//   b: ReadonlyArray<BoundingPoly>
// ) => {
//   const arr: BoundingPoly[] = [];
//   for (const poly of a) {
//     arr.push(poly);
//   }
//   for (const poly of b) {
//     arr.push(poly);
//   }
//   return arr;
// };

// const mergePolys = (ps: ReadonlyArray<BoundingPoly>): BoundingPoly => {
//   const numCoord = ps[0].object.boundingPoly.length;
//   const merged = {
//     ...ps[0],
//     object: {
//       ...ps[0].object,
//       boundingPoly: new Array(numCoord).map(() => ({ x: 0, y: 0 })),
//     },
//   };

//   for (const poly of ps) {
//     merged.startTimeMs = Math.min(merged.startTimeMs, poly.startTimeMs);
//     merged.stopTimeMs = Math.max(merged.stopTimeMs, poly.stopTimeMs);
//     for (let j = 0; j < numCoord; j++) {
//       merged.object.boundingPoly[j] = {
//         x:
//           merged.object.boundingPoly[j]!.x +
//           poly.object.boundingPoly[j]!.x / numCoord,
//         y:
//           merged.object.boundingPoly[j]!.y +
//           poly.object.boundingPoly[j]!.x / numCoord,
//       };
//     }
//   }

//   return merged;
// };

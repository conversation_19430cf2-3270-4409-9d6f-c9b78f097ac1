import { BoundingPoly } from './BoundingPoly';
import {
  BlurLevel,
  ShapeType,
} from '@common-modules/mediaDetails/models/GlobalSettings';
import { RedactionType } from '.';

// TODO may be able to remove DetectionBoundingPoly (combine with BoundingPoly)
export interface DetectionBoundingPoly extends BoundingPoly {
  // add group level attributes - since vtn-standard does not have hierarchical attributes
  readonly groupId: string;
  readonly clusterId?: string;
  readonly shapeType?: ShapeType;
  readonly userLabel?: string;
}

export interface RedactionConfig {
  blurLevel: BlurLevel;
  fillType: RedactionType;
}

import express from 'express';
import { Messages } from '../errors/messages';
import { Logger } from '../logger';
import { mediaFileDetailsQuery } from '../api/queries';
import { pick } from 'lodash';
import { callGQL } from '../api/callGraphql';
import { MediaFileDetailsResponse } from '../model/responses';
import { StatusCodes  } from '../errors/statusCodes';
import { isTdoId } from '../validations/helpers';

const IN_REDACTION_TAG_KEY = 'in redaction';

export const mediaFileDetails = async (
    req: express.Request,
    res: express.Response
  ) => {
    const tdoId = req.params.tdoId;
    if (!isTdoId(tdoId)) {
        return res.status(StatusCodes.BadRequest).json({
          error: Messages.caseIdRequired
        });
    }
    const headers = pick(req.headers, ['authorization']);
    const query = mediaFileDetailsQuery(tdoId);
    try {
      const response = await callGQL<MediaFileDetailsResponse>(headers, query);
      const { thumbnailUrl, /* primaryAssets,*/ redactedMediaAssets, auditLogAssets, redactedTranscriptAssets, details } = response.temporalDataObject;
      const mediaFileDetails = {
        reviewStatus: details.tags?.find(tag=>tag?.value === IN_REDACTION_TAG_KEY && !!tag.redactionStatus)?.redactionStatus ?? "Draft",
        isRedacted: redactedMediaAssets?.records?.length > 0,
        thumbnailUrl: thumbnailUrl,
        // originalFile: primaryAssets?.records?.[0]?.signedUri,
        redactedMediaFile: redactedMediaAssets?.records?.[0]?.signedUri,
        auditLog: auditLogAssets?.records?.[0]?.signedUri,
        redactedTranscript: redactedTranscriptAssets?.records?.[0]?.signedUri,
      }
      return res.status(StatusCodes.Success).json(mediaFileDetails );
    } catch(err) {
      Logger.error(err);
      return res.status(StatusCodes.BadRequest).send(err);
    }
  };
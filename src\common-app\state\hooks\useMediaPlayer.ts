import { useEffect, useState } from 'react';

import {
  animationFrameScheduler,
  BehaviorSubject,
  interval,
  Observable,
} from 'rxjs';
import {
  distinctUntilChanged,
  filter,
  map,
  withLatestFrom,
} from 'rxjs/operators';
import { notNil } from '@utils';

// /**
//  * Subscribe to the media player's current time emissions.
//  *
//  * useMediaPlayer will use single instances of observable raf$ to
//  * push media player's current time to subscribe at an interval.
//  *
//  * Both MediaPlayerView and Display use useMediaPlayer hook to synchronize
//  * current time between player and transcript
//  *
//  * @param mediaPlayer - optional media player to use.
//  * @returns current time MS
//  */

// Single instances of player$ and raf$ are needed. When the components mount,
// first call of useEffect will initialize player$ and raf$, and establish
// subscription. When the components unmount, the observers unsubscribe, and
// then set player$ and raf$ to null if no more observers.
//
let player$: BehaviorSubject<HTMLMediaElement | null> | null = null;
let raf$: Observable<number> | null = null;

export const useMediaPlayer = (mediaPlayer?: HTMLMediaElement | null) => {
  const [currentTime, setCurrentTime] = useState(0);

  useEffect(() => {
    // first call of useEffect will initialize player$ and raf$.
    if (raf$ === null) {
      player$ = new BehaviorSubject<HTMLMediaElement | null>(null);
      raf$ = interval(15, animationFrameScheduler).pipe(
        withLatestFrom(player$, (_, player) => player),
        filter(notNil),
        map((player) => player.currentTime * 1000),
        distinctUntilChanged()
      );
    }
    if (mediaPlayer) {
      player$?.next(mediaPlayer);
    }

    // When subscribing to the raf$ observable, we need to keep track of the subscription
    // so that we can unsubscribe when the component unmounts. Also the BehaviorSubject
    // will keep track of the number of subscribers in the observers array.
    const subscription = raf$?.subscribe(setCurrentTime);
    return () => {
      // When the component unmounts, we need to unsubscribe from the raf$ observable
      // and also automatically remove the subscription from the observers array in the
      // BehaviorSubject.
      subscription?.unsubscribe();
      // set player$ and raf$ to null if no more observers.
      if (!player$?.observed) {
        player$ = null;
        raf$ = null;
      }
    };
  }, [mediaPlayer]);

  return currentTime;
};

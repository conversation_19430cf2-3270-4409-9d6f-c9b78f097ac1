import { channel } from 'redux-saga';
import {
  all,
  select,
  fork,
  put,
  take,
  takeEvery,
} from 'typed-redux-saga/macro';
import { redirect } from 'redux-first-router';

import { sagaIntl } from '@i18n';

import { enqueueSnackbar } from '../snackbar';
import { ROUTE_MEDIA_DETAILS } from '../routing';
import { redactFileCompleted } from './index';
import {
  REDACTED_FILE_COMPLETED,
  SELECT_REDACTED_FILES_TAB,
} from '../mediaDetails/actions';
import { selectCurrentTdoId } from '../mediaDetails/selectors';
import { TDOId } from '@common-modules/universal/models/Brands';

export const redactedFilesChannel =
  channel<ReturnType<typeof SELECT_REDACTED_FILES_TAB>>();

// interface Action<P> {
//   type: string;
//   payload: P;
// }

export function* initRedactFile() {
  yield* all([fork(watchRedactFileCompleted), fork(watchRedactedFilesChannel)]);
}

export const handelSelectRedactedFilesTab = (tdoId: TDOId) => () =>
  redactedFilesChannel.put(SELECT_REDACTED_FILES_TAB({ tdoId }));

export function* redactFileCompletedHandling(action: {
  payload: { tdoId: TDOId };
}) {
  const { tdoId } = action.payload;

  const intl = sagaIntl();
  yield* put(
    enqueueSnackbar({
      message: intl.formatMessage({
        id: 'successfulRedact',
        defaultMessage:
          'File redacted successfully. Click View File to review.',
      }),
      action: 'VIEW FILE',
      handleAction: handelSelectRedactedFilesTab(tdoId),
      variant: 'success',
    })
  );
  yield* put(redactFileCompleted(tdoId));
}

export function* watchRedactFileCompleted() {
  yield* takeEvery(REDACTED_FILE_COMPLETED, redactFileCompletedHandling);
}

// handle `handleAction` of snack bar when click to View File
export function* watchRedactedFilesChannel() {
  while (true) {
    const action = yield* take(redactedFilesChannel);
    const tdoId = yield* select(selectCurrentTdoId);
    if (action.payload.tdoId !== tdoId) {
      yield* put(
        redirect(ROUTE_MEDIA_DETAILS({ tdoId: action.payload.tdoId }))
      );
    }
    yield* put(action);
  }
}

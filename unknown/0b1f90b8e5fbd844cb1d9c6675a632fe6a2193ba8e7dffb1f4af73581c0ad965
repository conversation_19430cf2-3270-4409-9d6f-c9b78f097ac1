import express from 'express';
import { fetchCaseFileListQuery } from '../api/queries';
import { callGQL } from '../api/callGraphql';
import { isEmpty, merge, pick } from 'lodash';
import { RequestCaseFileListResponse } from '../model/responses';
import { Logger } from '../logger';
import { Messages } from '../errors/messages';
import { StatusCodes } from '../errors/statusCodes';
import { isCaseId } from '../validations/helpers';

export const caseFileList = async (
  req: express.Request,
  res: express.Response
) => {
  const maxLimit = 9999;
  const caseId = req.params.caseId;
  if (!isCaseId(caseId)) {
    return res.status(StatusCodes.BadRequest).json({
      error: Messages.caseIdRequired
    });
  }
  const queryParams = req.query;
  if(!isEmpty(queryParams.limit) ) {
    if (isNaN(+queryParams.limit!)) { // Safe due to isEmpty check
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.limitNotNumber
      });
    }
    if (+queryParams.limit! > maxLimit) { // Safe due to isEmpty and isNaN check
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.maxLimitReached
      });
    }
  }
  if(!isEmpty(queryParams.offset) && isNaN(+queryParams.offset!)) { // Safe due to isEmpty check
    return res.status(StatusCodes.BadRequest).json({
      error: Messages.offsetNotNumber
    });
  }
  const headers = pick(req.headers, ['authorization']);
  const defaultLimit = 30;
  const defaultOffset = 0;
  const limit = (queryParams.limit && +queryParams.limit) || defaultLimit;
  const offset = (queryParams.offset && +queryParams.offset) || defaultOffset;

  try {
    const query = fetchCaseFileListQuery(caseId, limit , offset ); 
    const gQLresponse = await callGQL<RequestCaseFileListResponse>(headers, query);
    const  response = merge({}, gQLresponse, { case: { files: gQLresponse.case.files.records || [] } });
    return res.status(StatusCodes.Success).json(response);
  } catch(err: any) {
    Logger.error(err);
    if(err?.response?.errors) {
     const errName = err.response.errors[0].name
     if (errName === "not_found") {
      return res.status(StatusCodes.BadRequest).json({error: Messages.InvalidCaseId });
     }
    }
    return res.status(StatusCodes.BadRequest).send(err);
  }
};

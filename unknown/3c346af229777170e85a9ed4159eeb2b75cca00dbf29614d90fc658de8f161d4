import cn from 'classnames';
import { useIntl } from 'react-intl';
import Case from './components/Case';
import { Case as TCase, TreeObjectId } from '@cbsa-modules/universal';
import Media from './components/Media';
import { useEffect, useState } from 'react';
import { Search } from '@mui/icons-material';
import { I18nTranslate } from '@common/i18n';
import Accordion from './components/Accordion';
import Dropzone from '@cbsa-components/Dropzone';
import { InputAdornment, TextField } from '@mui/material';
import { Props, connector } from './props';
import * as styles from './index.scss';

const LIMIT = 1_000;

const FileMenu = ({
  isOpen,
  cases,
  media,
  caseId,
  loaders,
  fetchCases,
  fetchMedia,
  uploadMedia,
}: Props) => {
  const intl = useIntl();
  const [query, setQuery] = useState('');
  const [selectedCase, setSelectedCase] = useState<TreeObjectId | undefined>();
  const [caseLimit] = useState({ offset: 0, limit: LIMIT });
  const [mediaLimit] = useState({ offset: 0, limit: LIMIT });
  const { isFetchingCases, isFetchingMedia } = loaders;

  useEffect(() => {
    if (isOpen) {
      fetchCases({ query, ...caseLimit });
      setSelectedCase(caseId);
    } else {
      setSelectedCase(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, query]);

  useEffect(() => {
    if (selectedCase) {
      fetchMedia({ caseId: selectedCase, ...mediaLimit });
    }
  }, [fetchMedia, mediaLimit, selectedCase]);

  const filteredCases = Object.values(cases);
  const openCaseIndex = filteredCases.findIndex(
    ({ treeObjectId: id }) => id === caseId
  );
  if (openCaseIndex !== -1) {
    const openCase = filteredCases.splice(openCaseIndex, 1).pop();
    if (openCase) {
      filteredCases.push(openCase);
    }
  }

  type ArrayOfCasesArray = [TCase[], TCase[], TCase[], TCase[]];
  const [activeCases, reviewCases, approvedCases, archivedCases] =
    filteredCases.reduce<ArrayOfCasesArray>(
      ([a, b, c, d], caseData) => {
        if (caseData.status === 'deleted') {
          return [a, b, c, d];
        }

        switch (
          caseData.archive as string // TODO: This is temporary to keep same behavior -- need to verify type limitations
        ) {
          case 'readyForReview':
            return [a, [caseData, ...b], c, d];
          case 'approved':
            return [a, b, [caseData, ...c], d];
          case 'archived':
            return [a, b, c, [caseData, ...d]];
          default:
            switch (
              caseData.status as string // TODO: This is temporary to keep same behavior -- need to verify type limitations
            ) {
              case 'readyForReview':
                return [a, [caseData, ...b], c, d];
              case 'approved':
                return [a, b, [caseData, ...c], d];
              case 'archived':
                return [a, b, c, [caseData, ...d]];
              default:
                return [[caseData, ...a], b, c, d];
            }
        }
      },
      [[], [], [], []]
    );

  const [sourceMedia, redactedMedia] = Object.values(media).reduce<
    [(typeof media)['0'][], (typeof media)['0'][]]
  >(
    ([s, r], tdo) => {
      const isRedacted = (tdo?.assets?.records || []).find(
        (asset) => asset?.assetType === 'redacted-media'
      );
      return isRedacted
        ? [
            [...s, tdo],
            [...r, tdo],
          ]
        : [[...s, tdo], r];
    },
    [[], []]
  );

  const handleSelectCase = (id: TreeObjectId) => {
    if (id === selectedCase) {
      setSelectedCase(undefined);
    } else {
      setSelectedCase(id);
    }
  };

  const handleUploadMedia = (file: File) => {
    if (selectedCase) {
      uploadMedia({ file, caseId: selectedCase });
    }
  };

  // Leaving these commented for now - we can remove later if fully unneeded
  // const handleLoadMoreCases = () => {
  //   if (!isFetchingCases) {
  //     setCaseLimit({ offset: 0, limit: caseLimit.limit + LIMIT });
  //   }
  // };

  // const handleLoadMoreMedia = () => {
  //   if (!isFetchingMedia) {
  //     setMediaLimit({ offset: 0, limit: caseLimit.limit + LIMIT });
  //   }
  // };

  return (
    <div
      className={cn(
        styles.fileMenuBackground,
        styles.closed && { [styles.closed]: !isOpen }
      )}
      data-testid="file-menu"
    >
      <div
        className={styles.fileMenu}
        style={{
          width: isOpen ? (selectedCase === '' ? '324px' : '668px') : 0,
        }}
      >
        <div className={styles.fileMenuTitle}>
          {I18nTranslate.TranslateMessage('caseMedia')}
        </div>
        <div className={styles.fileMenuSearch}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder={intl.formatMessage({ id: 'searchPlaceholder' })}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            slotProps={{
              htmlInput: {
                style: { fontSize: '14px' },
              },
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Search
                      style={{
                        color: '#005C7E',
                      }}
                    />
                  </InputAdornment>
                ),
              },
            }}
          />
        </div>
        <div className={styles.fileMenuContainer}>
          <div className={styles.fileMenuContainerCase}>
            <Accordion
              title={`${intl.formatMessage({ id: 'activeCases' })} (${
                activeCases.length
              })`}
              isLoading={isFetchingCases}
            >
              {activeCases.length
                ? activeCases.map((caseDetails) => {
                    const { treeObjectId: id, name } = caseDetails;
                    return (
                      <Case
                        key={id}
                        name={name}
                        isSelected={selectedCase === id}
                        onClick={() => {
                          if (id) {
                            handleSelectCase(id);
                          }
                        }}
                      />
                    );
                  })
                : I18nTranslate.TranslateMessage('noMedia')}
            </Accordion>
            <Accordion
              title={`${intl.formatMessage({ id: 'reviewCases' })} (${
                reviewCases.length
              })`}
              isLoading={isFetchingCases}
            >
              {reviewCases.length > 0
                ? reviewCases.map((caseDetails) => {
                    const { treeObjectId: id, name } = caseDetails;
                    return (
                      <Case
                        key={id}
                        name={name}
                        isSelected={selectedCase === id}
                        onClick={() => {
                          if (id) {
                            handleSelectCase(id);
                          }
                        }}
                      />
                    );
                  })
                : I18nTranslate.TranslateMessage('noCases')}
            </Accordion>
            <Accordion
              title={`${intl.formatMessage({ id: 'approvedCases' })} (${
                approvedCases.length
              })`}
              isLoading={isFetchingCases}
            >
              {approvedCases.length > 0
                ? approvedCases.map((caseDetails) => {
                    const { treeObjectId: id, name } = caseDetails;
                    return (
                      <Case
                        key={id}
                        name={name}
                        isSelected={selectedCase === id}
                        onClick={() => {
                          if (id) {
                            handleSelectCase(id);
                          }
                        }}
                      />
                    );
                  })
                : I18nTranslate.TranslateMessage('noCases')}
            </Accordion>
            <Accordion
              title={`${intl.formatMessage({ id: 'archivedCases' })} (${
                archivedCases.length
              })`}
              isLoading={isFetchingCases}
            >
              {archivedCases.length > 0
                ? archivedCases.map((caseDetails) => {
                    const { treeObjectId: id, name } = caseDetails;
                    return (
                      <Case
                        key={id}
                        name={name}
                        isSelected={selectedCase === id}
                        onClick={() => {
                          if (id) {
                            handleSelectCase(id);
                          }
                        }}
                      />
                    );
                  })
                : I18nTranslate.TranslateMessage('noCases')}
            </Accordion>
          </div>
          <div className={styles.fileMenuContainerFile}>
            {caseId !== selectedCase && (
              <a
                className={styles.fileMenuContainerLink}
                href={`/case/${selectedCase}`}
              >
                {I18nTranslate.TranslateMessage('openCase')}
              </a>
            )}
            <Accordion
              title={`${intl.formatMessage({ id: 'sourceMedia' })} (${
                sourceMedia.length
              })`}
              isLoading={isFetchingMedia}
            >
              {sourceMedia.length > 0 && selectedCase
                ? sourceMedia.map((media) => {
                    const { id } = media;
                    return (
                      <Media
                        key={id}
                        media={media}
                        caseId={selectedCase}
                        isRedacted={false}
                      />
                    );
                  })
                : I18nTranslate.TranslateMessage('noMedia')}
            </Accordion>
            <Accordion
              title={`${intl.formatMessage({ id: 'redactedMedia' })} (${
                redactedMedia.length
              })`}
              isLoading={isFetchingMedia}
            >
              {redactedMedia.length > 0 && selectedCase
                ? redactedMedia.map((media) => {
                    const { id } = media;
                    return (
                      <Media
                        key={id}
                        media={media}
                        caseId={selectedCase}
                        isRedacted
                      />
                    );
                  })
                : I18nTranslate.TranslateMessage('noMedia')}
            </Accordion>
            <div className={styles.fileMenuContainerFileDropzone}>
              <Dropzone
                acceptedTypes={{
                  'image/*': ['.png', '.gif', '.jpeg', '.jpg'],
                  'video/*': ['.mp4', '.avi', '.wmv', '.ts', '.mov'],
                }}
                onUpload={handleUploadMedia}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default connector(FileMenu);

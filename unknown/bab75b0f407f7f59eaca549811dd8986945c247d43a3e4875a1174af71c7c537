import { Snack<PERSON><PERSON><PERSON> } from 'notistack';
export interface SnackbarStore {
  readonly notifications: ReadonlyArray<SnackbarInterface>;
}
export interface SnackbarInterface {
  readonly key: Snackbar<PERSON>ey;
  readonly message: string;
  readonly dismissed?: boolean;
  readonly action?: string;
  readonly handleAction?: () => any;
  readonly variant?: 'default' | 'success' | 'error' | 'warning' | 'info';
}

export interface SnackbarAddInterface {
  readonly message: string;
  readonly action?: string;
  readonly handleAction?: () => any;
  readonly autoHideDuration?: number;
  readonly variant?: 'default' | 'success' | 'error' | 'warning' | 'info';
}

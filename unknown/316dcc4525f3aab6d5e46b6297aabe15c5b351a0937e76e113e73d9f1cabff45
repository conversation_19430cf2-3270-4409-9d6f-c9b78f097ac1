import cn from 'classnames';
import FolderOpen from './FolderOpen.svg';
import FolderClosed from './FolderClosed.svg';
import * as styles from './index.scss';

const Case = ({ name, isSelected, onClick }: Props) => {
  const renderIcon = () =>
    isSelected ? (
      <img
        data-testid="img-is-selected"
        src={FolderOpen}
        className={styles.caseFolder}
      />
    ) : (
      <img
        data-testid="img-not-selected"
        src={FolderClosed}
        className={cn(
          styles.caseFolder,
          styles.closed && { [styles.closed]: true }
        )}
      />
    );

  return (
    <div data-testid="case" className={styles.case} onClick={onClick}>
      {renderIcon()}
      <div
        className={cn(
          styles.caseName,
          styles.selected && { [styles.selected]: isSelected }
        )}
      >
        {name}
      </div>
    </div>
  );
};

interface Props {
  readonly name: string;
  readonly isSelected: boolean;
  readonly onClick: () => void;
}

export default Case;

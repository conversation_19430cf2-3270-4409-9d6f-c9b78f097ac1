import { RootFoldersResponse } from '../model/responses';
import { callGQL } from '../api/callGraphql';
import { getRootFoldersQuery } from '../api/queries';
import { isEmpty } from 'lodash';
import { RequestHeader } from '../model/requests';
import { createRootFolderAdapter } from './createRootFolder';
import { Logger } from '../logger';
import { Messages } from '../errors/messages';

export const getRootFolderAdapter = async (headers: RequestHeader) => {
  const rootFolderId = await callGQL<RootFoldersResponse>(
    headers,
    getRootFoldersQuery
  )
    .then((res) => {
      if (res.rootFolders?.length > 0) {
       return res.rootFolders?.find((rf) => isEmpty(rf.ownerId))?.id
      } else {
        return createRootFolderAdapter(headers);
      }
    })
    .catch((err) => {
      Logger.error(Messages.fetchRootFolderError + JSON.stringify(err));
    });
  return rootFolderId;
};

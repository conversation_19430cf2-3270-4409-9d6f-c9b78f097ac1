import {
  createGraphQLFailureAction,
  createGraphQLSuccessAction,
} from '@helpers/callGraphQLApi';
import { GetAuditLogsResponse } from '../services/queries/auditLog';
import { createAction } from '@reduxjs/toolkit';

export const GET_CASE_AUDIT_LOGS = createAction('CBSA/GET_CASE_AUDIT_LOGS');

export const GET_TDO_AUDIT_LOGS = 'CBSA/GET_TDO_AUDIT_LOGS';
export const GET_TDO_AUDIT_LOGS_SUCCESS =
  createGraphQLSuccessAction<GetAuditLogsResponse>(
    'CBSA/GET_TDO_AUDIT_LOGS_SUCCESS'
  );
export const GET_TDO_AUDIT_LOGS_FAILURE = createGraphQLFailureAction(
  'CBSA/GET_TDO_AUDIT_LOGS_FAILURE'
);

export const ADD_AUDIT_LOG_QUERY_PENDING = createAction(
  'CBSA/GET_TDO_AUDIT_LOGS'
);
export const SUBTRACT_AUDIT_LOG_QUERY_PENDING = createAction(
  'CBSA/GET_TDO_AUDIT_LOGS'
);

export const addAuditLogQueryPending = () => ADD_AUDIT_LOG_QUERY_PENDING();

export const subtractAuditLogQueryPending = () =>
  SUBTRACT_AUDIT_LOG_QUERY_PENDING();

export const getCaseAuditLogs = () => GET_CASE_AUDIT_LOGS();

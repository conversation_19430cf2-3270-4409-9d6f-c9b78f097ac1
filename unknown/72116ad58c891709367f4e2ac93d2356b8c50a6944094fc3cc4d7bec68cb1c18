import makeStyles from '@mui/styles/makeStyles';

export const useStyles = makeStyles((theme) => ({
  mediaLibrary: {
    background: theme.palette.grey[100],
    border: `1px solid ${theme.palette.grey[300]}`,
    boxShadow: theme.shadows[4],
    display: `flex`,
    flex: 1,
    flexDirection: `column`,

    '& .Header': {
      background: theme.palette.primary.main,
      border: `1px solid ${theme.palette.grey[300]}`,
      display: 'flex',
      justifyContent: 'space-between',

      '& .Title': {
        color: '#FFF',
        height: `60px`,
        fontFamily: `Roboto, sans-serif`,
        fontSize: `18px`,
        letterSpacing: `0`,
        lineHeight: `60px`,
        paddingLeft: `21px`,
      },

      '& .Checkbox': {
        color: `white !important`,
      },

      '& .Label': {
        color: `white`,
        fontSize: `12px`,
      },
    },

    '& .Content': {
      background: theme.palette.grey[100],
      overflow: `scroll`,
      position: `relative`,
      height: `100%`,

      '& .Loading': {
        background: theme.palette.grey[50],
        display: `flex`,
        alignItems: `center`,
        justifyContent: `center`,
        position: `absolute`,
        width: `100%`,
        height: `100%`,
        top: 0,
        left: 0,
      },
    },

    '& .Footer': {
      height: `60px`,
      background: theme.palette.grey[300],
      borderTop: `1px solid ${theme.palette.divider}`,
      display: `flex`,
      alignItems: `center`,
      justifyContent: `center`,
    },

    '& .MuiCircularProgress-root': {
      color: theme.palette.primary.main,
    },
  },
}));

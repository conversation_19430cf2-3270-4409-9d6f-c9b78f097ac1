export enum RedactionCodeDataSelector {
  AddNewCodeIconButton = 'AddIcon',
  AddBtn = 'redaction-code-add-btn',
  Drawer = 'redaction-codes-drawer',
  DescriptionInput = 'redaction-code-description-input',
  Table = 'redaction-code-table',
  ConfirmBtn = 'confirm-dialog-button',
  ConfirmTitle = 'confirm-dialog',
}

export enum RedactionCodeText {
  ModalDeleteDescription = "Are you sure you'd like to delete this? Once deleted, it cannot be recovered.",
  EditButton = 'Edit',
  DeleteButton = 'Delete',
  LeftArrowButtonTitle = 'Previous Page',
  RightArrowButtonTitle = 'Next Page',
  CodeNamePlaceholder = 'Redaction Code 1',
  RedactionCodePlaceholder = '4MF93848',
  DeleteCode = 'Delete Redaction Code',
  SaveBtn = 'Save',
}
export enum RedactionCodeGraphQlQuery {
  FetchRedactionCode = `\n  query ($schemaId: ID!, $offset: Int, $limit: Int) {\n    structuredDataObjects(schemaId: $schemaId, offset: $offset, limit: $limit) {\n      count\n      records {\n        id\n        createdDateTime\n        modifiedDateTime\n        data\n      }\n    }\n  }\n`,
  DeleteRedactionCode = `\n  mutation deleteRedactionCode($sdoId: ID!, $schemaId: ID!) {\n    deleteStructuredData(input: {\n      id: $sdoId,\n      schemaId: $schemaId\n    }) {\n      id\n    }\n  }\n`,
}

export type RTableHeader =
  | 'Code Name'
  | 'Code'
  | 'Description'
  | 'Code Color'
  | 'Date Added'
  | 'Date Modified';

export type RSortedBy = 'a-z' | 'z-a';

export type RowPerPage = 10 | 20 | 100;

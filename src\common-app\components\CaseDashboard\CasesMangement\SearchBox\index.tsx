import { useSelector, useDispatch } from 'react-redux';
import { useIntl } from 'react-intl';

import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';

import { selectSearchText } from '@cbsa-modules/mainPage/selectors';
import {
  setSearchText,
  setPaginationStart,
} from '@cbsa-modules/mainPage/actions';

import makeStyles from '@mui/styles/makeStyles';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const useStyles = makeStyles((theme) => ({
  searchIcon: {
    color: theme.palette.primary.main,
  },
}));

const SearchBoxContent = () => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const intl = useIntl();

  const searchText = useSelector(selectSearchText);

  return (
    <Grid size={{ xs: 6 }}>
      <TextField
        data-testid="searchInputBox"
        variant="outlined"
        placeholder={intl.formatMessage({
          id: 'searchPlaceholder',
          defaultMessage: 'Search...',
        })}
        fullWidth
        slotProps={{
          input: {
            startAdornment: (
              <InputAdornment
                position="start"
                classes={{ root: classes.searchIcon }}
              >
                <SearchIcon />
              </InputAdornment>
            ),
          },
        }}
        value={searchText}
        onChange={(event) => {
          dispatch(setSearchText(event.target.value));
          dispatch(setPaginationStart(1));
        }}
      />
    </Grid>
  );
};

const SearchBox = () => (
  <ThemeProvider theme={defaultTheme}>
    <SearchBoxContent />
  </ThemeProvider>
);

export default SearchBox;

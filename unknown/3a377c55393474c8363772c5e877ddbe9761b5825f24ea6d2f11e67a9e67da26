import useStyles from './styles';
import { useEffect } from 'react';
import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import Dialog from '@common-components/ConfirmDialog';
import type { AuditEvent } from '@cbsa-modules/universal';
import { Button, CircularProgress, ThemeProvider } from '@mui/material';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const AuditLogDialogContent = ({
  isOpen,
  onClose,
  auditEvents,
  getCaseAuditLogs,
  auditLogsLoading,
}: Props) => {
  const intl = useIntl();
  const auditEventArray = Object.values(auditEvents).reduce(
    (acc, ae) => [
      ...acc,
      ...ae.map((a) => {
        const objectIndex = a.action.indexOf('{');
        if (objectIndex > -1) {
          a.action = a.action.slice(0, objectIndex);
        }
        return a;
      }),
    ],
    []
  );

  const classes = useStyles();

  useEffect(() => {
    if (isOpen) {
      getCaseAuditLogs();
    }
  }, [isOpen, getCaseAuditLogs]);

  const onDownloadCsv = () => {
    const csvFile = [
      'user-name, timestamp, audit-log-id, action',
      ...auditEventArray.map((ae) =>
        [ae.userName, ae.timestamp, ae.auditLogId, ae.action].join(',')
      ),
    ].join('\n');

    const blob = new Blob([csvFile], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'audit-log.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <Dialog
      isOpen={isOpen}
      title={I18nTranslate.TranslateMessage('auditLogDialogTitle')}
      dialogClasses={{
        paperScrollPaper: classes.dialogPaper,
      }}
      content={
        <div className={classes['audit-event-dialog-content']}>
          <div className={classes.heading}>
            <div className={classes.headingResults}>
              <b>
                {I18nTranslate.TranslateMessage('results')}
                {`:`}
              </b>{' '}
              {auditLogsLoading ? (
                <CircularProgress color="secondary" />
              ) : (
                ` ${auditEventArray.length} `
              )}
              {I18nTranslate.TranslateMessage('logs')}
            </div>
            <Button
              classes={{ root: classes.headingDownload }}
              onClick={onDownloadCsv}
            >
              <CloudDownloadIcon />
              {I18nTranslate.TranslateMessage('download')}
            </Button>
          </div>
          <div className={classes.auditEventContainer}>
            {auditEventArray.map((ae) => (
              <div
                className={classes.auditEventItem}
                key={`AuditEvent-${ae.timestamp}`}
              >
                <div className={classes.auditEventTitle}>
                  {I18nTranslate.TranslateMessage('user')}
                </div>
                <div className={classes.auditEventContent}>{ae.userName}</div>
                <div className={classes.auditEventTitle}>
                  {I18nTranslate.TranslateMessage('date')}
                </div>
                <div className={classes.auditEventContent}>{ae.timestamp}</div>
                <div className={classes.auditEventTitle}>
                  {I18nTranslate.TranslateMessage('id')}
                </div>
                <div className={classes.auditEventContent}>{ae.auditLogId}</div>
                <div className={classes.auditEventTitle}>
                  {I18nTranslate.TranslateMessage('action')}
                </div>
                <div className={classes.auditEventContent}>{ae.action}</div>
              </div>
            ))}
          </div>
        </div>
      }
      cancelText={intl.formatMessage({ id: 'close' })}
      onCancel={onClose}
    />
  );
};

interface Props {
  readonly isOpen: boolean;
  readonly onClose: () => void;
  readonly getCaseAuditLogs: () => void;
  readonly auditLogsLoading: boolean;
  readonly auditEvents: {
    readonly [tdoId: string]: Array<AuditEvent>;
  };
}

const AuditLogDialog = ({
  isOpen,
  onClose,
  auditEvents,
  getCaseAuditLogs,
  auditLogsLoading,
}: Props) => (
  <ThemeProvider theme={defaultTheme}>
    <AuditLogDialogContent
      isOpen={isOpen}
      onClose={onClose}
      auditEvents={auditEvents}
      getCaseAuditLogs={getCaseAuditLogs}
      auditLogsLoading={auditLogsLoading}
    />
  </ThemeProvider>
);

export default AuditLogDialog;

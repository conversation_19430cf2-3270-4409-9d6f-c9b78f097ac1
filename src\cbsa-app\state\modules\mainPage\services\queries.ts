// TODO: Has this api spelling been fixed?
/* cspell:words Tempate */

import { Case, TreeObjectId } from '@cbsa-modules/universal';

export const newFolder_parentIdQuery = `
query rootFolders {
  rootFolders(type: cms) {
    id
  }
}
`;
export interface NewFolderParentIdQueryResponse {
  rootFolders: [
    {
      id: string;
    },
  ];
}

export const newFolder_folderQuery = `
mutation createFolder($parentId: ID!, $newCaseName: String!) {
  createFolder(input: { parentId: $parentId, name: $newCaseName, description: $newCaseName }) {
    id
    treeObjectId
  }
}
`;
export interface NewFolderFolderQueryResponse {
  createFolder: {
    id: string;
    treeObjectId: TreeObjectId;
  };
}

export const newFolder_sdoQuery = `
mutation insertRedactCase($newCaseName: String!, $folderTreeObjectId: ID!, $currentTime: String!, $schemaId: ID!){
  createStructuredData(input:{
    id: ""
    schemaId: $schemaId,
    data:{
      caseName: $newCaseName,
      status:"new",
      folderTreeObjectId: $folderTreeObjectId,
      createdDateTime: $currentTime,
      modifiedDateTime: $currentTime,
      priority: 1,
      processingTime: 3600
    }
  }){
    id
    data
  }
}
`;
export interface NewFolderSdoQueryResponse {
  createStructuredData: {
    id: string;
    data: Case;
  };
}

export const newFolder_linkQuery = `
mutation createFolderContentTempate($schemaId: ID!, $folderId: ID!, $sdoId: ID!) {
  createFolderContentTempate(
    input:{
      folderId: $folderId,
      schemaId: $schemaId,
      sdoId: $sdoId
    }
  )
  {
    id
  }
}
`;

export const deleteFolder_folderSummaryQuery = `
query folderData($folderTreeObjectId: ID!){
  folder(id: $folderTreeObjectId){
    id
    treeObjectId
    contentTemplates {
      id
      sdo {
        id,
        data
      }
    }
    childTDOs{
      count
    }
  }
}`;

export const deleteFolder_deleteFolder = `
mutation deleteFolder($folderTreeObjectId: ID!){
  deleteFolder(
    input:{
      id: $folderTreeObjectId,
      orderIndex: 0
    }
  ) {
    id
  }
}
`;

export const deleteFolder_updateSdo = `
mutation updateRedactCase($updatedCaseData: JSONData!, $sdoId: ID!, $schemaId: ID!){
  createStructuredData(
    input:{
      id: $sdoId,
      schemaId: $schemaId,
      data: $updatedCaseData,
    }
  ) {
    id
  }
}
`;

export const archiveCase_caseInfo = `
query getRedactCaseSdo($folderTreeObjectId: ID!, $schemaId: ID!){
  structuredDataObjects(
    schemaId: $schemaId,
    filter:{
      folderTreeObjectId: $folderTreeObjectId,
    }
  ) {
    records {
      id
      data
      createdDateTime
      modifiedDateTime
    }
  }
}
`;
export interface ArchiveCaseCaseInfo {
  structuredDataObjects: {
    records: {
      id: string;
      createdDateTime: string;
      modifiedDateTime: string;
      data: Case;
    }[];
  };
}

import { TDOId, Thunk } from '@cbsa-modules/universal';
import { processCaseSuccess, processCaseFailure } from '@cbsa-modules/addMedia';

export const processCase: Thunk<{
  readonly referenceImageUrls: Array<string>;
  readonly videoUrl: string;
  readonly tdoId: TDOId;
  readonly batchId: string;
  readonly name: string;
  readonly endpoint: string;
  readonly token: string | null;
  readonly veritoneAppId: string;
}> =
  ({
    referenceImageUrls,
    videoUrl,
    tdoId,
    batchId,
    name,
    endpoint,
    token,
    veritoneAppId,
  }) =>
  async (dispatch) =>
    await fetch(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        referenceImageUrls,
        videoUrl,
        tdoId,
        batchId,
      }),
      headers: {
        Authorization: token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json',
        'x-veritone-application': veritoneAppId,
      },
    })
      .then(() => dispatch(processCaseSuccess(name)))
      .catch(() => dispatch(processCaseFailure(name)));

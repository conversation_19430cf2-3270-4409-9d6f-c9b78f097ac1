import { useSelector, useDispatch } from 'react-redux';

import Grid from '@mui/material/Grid2';
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';

import { selectStatusFilter } from '@cbsa-modules/mainPage/selectors';
import {
  setStatusFilter,
  setPaginationStart,
} from '@cbsa-modules/mainPage/actions';

import { I18nTranslate } from '@common/i18n';

import makeStyles from '@mui/styles/makeStyles';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const useStyles = makeStyles((theme) => ({
  statusBox: {
    '& .MuiInputBase-input': {
      fontWeight: 'bold',
      color: theme.palette.primary.main,
    },
    '& .MuiSelect-icon': {
      color: theme.palette.primary.main,
    },
  },
  statusItem: {
    fontWeight: 'bold',
    color: theme.palette.primary.main,
  },
}));

const StatusFilterContent = () => {
  const dispatch = useDispatch();
  const classes = useStyles();

  const statusFilter = useSelector(selectStatusFilter);

  return (
    <Grid size={{ xs: 2 }}>
      <TextField
        data-testid="caseStatusFilter"
        select
        variant="outlined"
        fullWidth
        value={statusFilter}
        classes={{ root: classes.statusBox }}
        slotProps={{
          select: {
            MenuProps: {
              anchorOrigin: {
                vertical: 'bottom',
                horizontal: 'left',
              },
              transformOrigin: {
                vertical: 'top',
                horizontal: 'left',
              },
            },
          },
        }}
        onChange={(event) => {
          dispatch(setStatusFilter(event.target.value as typeof statusFilter));
          dispatch(setPaginationStart(1));
        }}
      >
        <MenuItem value="all" classes={{ root: classes.statusItem }}>
          {I18nTranslate.TranslateMessage('allStatuses')}
        </MenuItem>
        <MenuItem value="new" classes={{ root: classes.statusItem }}>
          {I18nTranslate.TranslateMessage('new')}
        </MenuItem>
        <MenuItem value="processing" classes={{ root: classes.statusItem }}>
          {I18nTranslate.TranslateMessage('processing')}
        </MenuItem>
        <MenuItem value="error" classes={{ root: classes.statusItem }}>
          {I18nTranslate.TranslateMessage('error')}
        </MenuItem>
        <MenuItem value="readyForReview" classes={{ root: classes.statusItem }}>
          {I18nTranslate.TranslateMessage('readyForReview')}
        </MenuItem>
        <MenuItem value="approved" classes={{ root: classes.statusItem }}>
          {I18nTranslate.TranslateMessage('approved')}
        </MenuItem>
        <MenuItem value="readyForExport" classes={{ root: classes.statusItem }}>
          {I18nTranslate.TranslateMessage('readyForExport')}
        </MenuItem>
      </TextField>
    </Grid>
  );
};

const StatusFilter = () => (
  <ThemeProvider theme={defaultTheme}>
    <StatusFilterContent />
  </ThemeProvider>
);

export default StatusFilter;

import { sagaIntl } from '@i18n';
import * as Actions from '../actions';
import * as Services from '../services';
import { put, takeEvery } from 'typed-redux-saga/macro';
import { enqueueSnackbar } from '@common-modules/snackbar';

export function* deleteTdo() {
  yield* takeEvery(Actions.DELETE_TDO, function* ({ payload }) {
    const { tdoId } = payload;

    const intl = sagaIntl();

    try {
      yield* put(Services.deleteTdo({ tdoId }));

      yield* put(Actions.deleteTdoSuccess(tdoId));
      yield* put(
        enqueueSnackbar({
          message: intl.formatMessage({ id: 'fileDeleted' }),
          variant: 'success',
        })
      );
    } catch {
      yield* put(
        enqueueSnackbar({
          message: intl.formatMessage({ id: 'fileFailedDeleted' }),
          variant: 'error',
        })
      );
    }
  });
}

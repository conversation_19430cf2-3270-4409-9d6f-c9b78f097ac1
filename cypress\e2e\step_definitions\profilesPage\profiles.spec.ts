import {
  Given,
  When,
  Then,
  Before,
} from '@badeball/cypress-cucumber-preprocessor';
import {
  DataVeritoneSelector,
  veritoneDefaultProfile,
  dateTimeFormat,
  VideoType,
  ProfileGraphQlQuery,
  ProfileDataTestSelector,
  ProfileDisplayText,
} from '../../../support/helperFunction/profilesPageHelper';

Before(() => {
  cy.LoginLandingPage();
  cy.interceptGraphQLQuery(ProfileGraphQlQuery.CreateProfile, 'createProfile');
  cy.interceptGraphQLQuery(ProfileGraphQlQuery.UpdateProfile, 'updateProfile');
});

Before({ tags: '@small-screen' }, () => {
  cy.viewport(1280, 720);
});

/* Background: User goes to the profiles page */
Given('The user is on the {string} screen', (screenName: string) => {
  let screenUrl = '';

  switch (screenName) {
    case 'Profiles':
      screenUrl = 'settings-profile';
      break;
    case 'Redaction Codes':
      screenUrl = 'redaction-codes';
      break;
    case 'Redactions':
      cy.visit('/');
      return;
    default:
      throw new Error(`Unknown screen name: "${screenName}"`);
  }

  if (screenUrl) {
    cy.navigateToSectionByName({
      sectionName: screenUrl,
    });
  }
});

Then(
  'The header should contain {string} and {string} button',
  (header: string, addNewProfileBtn: string) => {
    cy.url().should('include', 'settings-profile');
    cy.getDataIdCy({ idAlias: 'profile-settings-title' }).should(
      'contain.text',
      header
    );
    cy.get('button').contains(addNewProfileBtn).should('be.visible');
  }
);

Then(
  'The table contains the following columns:',
  (dataTable: { raw: () => string[][] }) => {
    const columns = dataTable.raw().flat();
    columns.forEach((column) => {
      cy.get('table thead').should('contain.text', column);
    });
  }
);

When('The user clicks on Add new profile button', () => {
  cy.getDataIdCy({ idAlias: ProfileDataTestSelector.AddIcon }).click();
});

Then('The profile settings dialog should be displayed', () => {
  cy.get('[role="dialog"]').should('be.visible');
});

When('The user enters name {string} to Profile Name', (name: string) => {
  cy.contains('label', `${ProfileDisplayText.ProfileNameLabelTextInput}`)
    .parent()
    .find('input')
    .as('profileNameInput');
  cy.get('@profileNameInput').clear();
  cy.get('@profileNameInput').type(name);
});

When('The user deletes the profile {string} if exists', (name: string) => {
  cy.deleteProfileByName(name);
});

Then('The user saves the {string} profile', (profileStatus: string) => {
  const alias = profileStatus === 'New' ? '@createProfile' : '@updateProfile';
  cy.getDataIdCy({
    idAlias: 'interpolation-duration-setting-modal-save-button',
  }).click();
  cy.awaitNetworkResponseCode({ alias: alias, code: 200 });
  if (profileStatus === 'New') {
    cy.reload(); // Bug: Page not automatically load new Profiles
  }
});

Then('The profile {string} should be visible in table', (name: string) => {
  cy.get('table tbody').contains(name);
});

When('The user opens hamburger menu icon', () => {
  cy.getDataIdCy({ idAlias: DataVeritoneSelector.AppBarMenu }).click();
});

Then('The user selects {string} button', (UploadMediaBtn: string) => {
  cy.get('button[type="button"]').contains(UploadMediaBtn).click();
});

When('The user clicks on profiles dropdown', () => {
  cy.get(`[id=${DataVeritoneSelector.ProfileDropdownId}]`).click();
});

When('The user selects {string} for Video Type', (videoType: VideoType) => {
  cy.get('input[name="videoType"]').check(videoType);
});

When(
  'The user changes the Confident Threshold by {string} to {int}',
  (type: string, thresholdNumber: number) => {
    cy.get(`input[name="faceDetectionThreshold"][type="${type}"]`).clear();
    cy.get(`input[name="faceDetectionThreshold"][type="${type}"]`).type(
      thresholdNumber.toString()
    );
  }
);

Then('The Profiles list should contain profile {string}', (name: string) => {
  cy.get(`[id=${DataVeritoneSelector.ProfileSettingsDropdownId}]`).should(
    'exist'
  );
  cy.get(`[id=${DataVeritoneSelector.ProfileSettingsDropdownId}]`).contains(
    name
  );

  cy.contains('li[role="option"]', veritoneDefaultProfile).click();

  cy.get(`[data-veritone-element=${DataVeritoneSelector.CloseHeaderIcon}`)
    .should('be.visible')
    .click();
});

When('user clicks on Edit button on {string}', (name: string) => {
  cy.get('table')
    .find('tr')
    .filter(`:has(td:contains("${name}"))`)
    .within(() => {
      cy.get(`button[aria-label=${ProfileDisplayText.EditButton}]`)
        .first()
        .click();
    });
});

Then('Video Type should be {string}', (videoType: VideoType) => {
  cy.get(`input[name="videoType"][value="${videoType}"`).should('be.checked');
});

Then(
  'Confident Threshold by {string} should be {int}',
  (type: string, thresholdNumber: number) => {
    cy.get(`input[name="faceDetectionThreshold"][type="${type}"]`).should(
      'have.value',
      thresholdNumber
    );
  }
);

When(
  'The user clicks the Delete button on profile {string}',
  (profileName: string) => {
    cy.contains('tr', profileName).within(() => {
      cy.getDataIdCy({ idAlias: ProfileDataTestSelector.DeleteIcon }).click();
    });
  }
);

Then('The Delete Profile dialog should be displayed', () => {
  cy.get('[role="dialog"]').should('be.visible');
  cy.contains('Delete Profile').should('be.visible');
  cy.contains(`${ProfileDisplayText.ModalDeleteDescription}`).should('exist');
});

When('user confirms the deletion', () => {
  cy.get('button[type="button"]')
    .contains(ProfileDisplayText.DeleteButton)
    .click();
});

Then('Profile {string} should be deleted', (profileName: string) => {
  cy.get('table tbody').not(`:contains(${profileName})`);
});

When(
  'user clicks on Is Default checkbox on profile {string}',
  (profileName: string) => {
    cy.contains('tr', profileName).within(() => {
      cy.getDataIdCy({ idAlias: ProfileDataTestSelector.NonCheckIcon })
        .should('exist')
        .parent()
        .click();
    });
  }
);

When('The user reset to default profile', () => {
  cy.get('table tbody tr').should('have.length.greaterThan', 2);
  cy.contains('tr', veritoneDefaultProfile).within(() => {
    cy.get('td input').then(($input) => {
      if (!$input.is(':checked')) {
        return cy
          .getDataIdCy({ idAlias: ProfileDataTestSelector.NonCheckIcon })
          .should('exist')
          .parent()
          .click();
      } else {
        return cy.log('default profile is selected');
      }
    });
  });
});

Then('The checkbox for {string} is selected', (profileName: string) => {
  cy.contains('tr', profileName).within(() => {
    cy.getDataIdCy({ idAlias: ProfileDataTestSelector.CheckedIcon }).should(
      'be.visible'
    );
  });
});

Then(
  'Profile {string} is displayed in Profile list by default',
  (profileName: string) => {
    cy.get(DataVeritoneSelector.SelectProfileDropdownId).should(
      'contain',
      profileName
    );
  }
);

Then(
  'The format of {string} should be DD:YYYY:hh:mm',
  (profileName: string) => {
    cy.contains('tr', profileName)
      .first()
      .within(() => {
        cy.get('td').eq(4).invoke('text').should('match', dateTimeFormat);
        cy.get('td').eq(5).invoke('text').should('match', dateTimeFormat);
      });
  }
);

When('The user moves the navigation bar {string}', (scrollTo: string) => {
  cy.get('table').parent().as('profileScroll');
  cy.get('@profileScroll').scrollTo(scrollTo === 'down' ? 'bottom' : 'top');
});

Then('The navigation bar should be scrolled {string}', (scrollTo: string) => {
  cy.get('@profileScroll').should(($el) => {
    if (scrollTo === 'down') {
      expect($el[0].scrollTop).to.be.greaterThan(0);
    } else {
      expect($el[0].scrollTop).to.equal(0);
    }
  });
});

Then('Show more files below', () => {
  cy.get('.MuiTable-root tbody tr').should('have.length.greaterThan', 0);
});

When(
  'user checks the Row per page default is {string}',
  (rowPerPage: string) => {
    cy.get('.MuiSelect-nativeInput').should('have.value', rowPerPage);

    cy.get('tr').should('have.length.at.least', 10);
  }
);

When('user selects {int} from the dropdown', (rowPerPage: number) => {
  cy.get('div[role="combobox"][aria-haspopup="listbox"]').click();
  cy.get(
    `ul[role="listbox"] li[data-value="${rowPerPage.toString()}"]`
  ).click();
});

Then(
  'The page is updated and the total number of rows displayed is up to {string}',
  (rowPerPage: string) => {
    cy.get('tr').should('have.length.at.most', parseInt(rowPerPage) + 1);
  }
);

When(
  'The user clicks the {string} button in the pagination section',
  (btnName: string) => {
    if (btnName === '>') {
      cy.get(
        `button[aria-label="${ProfileDisplayText.RightArrowButtonTitle}"]`
      ).click();
    } else {
      cy.get(
        `button[aria-label="${ProfileDisplayText.LeftArrowButtonTitle}"]`
      ).click();
    }
  }
);

Then('The user should go to page: {string}', (profileRange: string) => {
  cy.get('div.MuiTablePagination-root')
    .contains('p', profileRange)
    .should('exist');
});

Then('User should go to the previous page', () => {
  cy.get('div.MuiToolbar-root p')
    .eq(1)
    .invoke('text')
    .should('match', /\d+-\d+/)
    .then((text) => {
      expect(text).to.include('1-10');
      return;
    });
});

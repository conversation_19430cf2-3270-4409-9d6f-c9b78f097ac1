import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import Dialog from '@common-components/ConfirmDialog';

const ProcessCaseDialog = ({ isOpen, onConfirm, onClose }: Props) => {
  const intl = useIntl();
  return (
    <Dialog
      isOpen={isOpen}
      title={I18nTranslate.TranslateMessage('processCaseTitle')}
      content={I18nTranslate.TranslateMessage('processCaseContent')}
      confirmText={intl.formatMessage({ id: 'process' })}
      onConfirm={onConfirm}
      cancelText={intl.formatMessage({ id: 'cancel' })}
      onCancel={onClose}
    />
  );
};
interface Props {
  readonly isOpen: boolean;
  readonly onConfirm: () => void;
  readonly onClose: () => void;
}

export default ProcessCaseDialog;

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { I18nTranslate } from '@common/i18n';

const SaveChangesModal = ({
  onSubmit,
  onClose,
  open,
}: SaveChangesPropTypes) => (
  <Dialog
    open={open}
    onClose={(_evt, reason) => {
      if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
        onClose();
      }
    }}
    maxWidth="md"
    data-veritone-component="save-redaction-modal"
  >
    <DialogTitle>{I18nTranslate.TranslateMessage('saveChanges')}</DialogTitle>
    <DialogContent>
      <DialogContentText>
        {I18nTranslate.TranslateMessage('saveChangesMessage')}
      </DialogContentText>
    </DialogContent>
    <DialogActions>
      <Button
        onClick={onClose}
        color="primary"
        data-veritone-element="save-redaction-modal-discard-button"
      >
        {I18nTranslate.TranslateMessage('discard')}
      </Button>
      <Button
        type="submit"
        color="primary"
        onClick={onSubmit}
        data-testid="save-redaction-modal-save-button"
      >
        {I18nTranslate.TranslateMessage('save')}
      </Button>
    </DialogActions>
  </Dialog>
);

export default SaveChangesModal;

export interface SaveChangesPropTypes {
  onSubmit: () => void;
  onClose: () => void;
  open: boolean;
}

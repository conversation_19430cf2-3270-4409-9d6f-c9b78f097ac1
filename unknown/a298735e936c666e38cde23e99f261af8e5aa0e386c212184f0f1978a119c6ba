import { sagaIntl } from '@i18n';
import { Action } from '../saga';
import * as Actions from '../actions';
import * as Services from '../services';
import { put, takeLatest } from 'typed-redux-saga/macro';
import { enqueueSnackbar } from '@common-modules/snackbar';

export function* fetchCaseMedia() {
  yield* takeLatest<
    Action<ReturnType<typeof Actions.fetchCaseMedia>['payload']>
  >(Actions.FETCH_CASE_MEDIA, function* ({ payload }) {
    const { caseId } = payload;
    yield* put(Services.fetchCaseMedia({ caseId }));
  });
}

export function* fetchCaseMediaFailure() {
  yield* takeLatest(Actions.FETCH_CASE_MEDIA_FAILURE, function* () {
    const intl = sagaIntl();
    yield enqueueSnackbar({
      message: intl.formatMessage({
        id: 'failedToFetchMedia',
        defaultMessage: 'Failed to fetch media',
      }),
      variant: 'error',
    });
  });
}

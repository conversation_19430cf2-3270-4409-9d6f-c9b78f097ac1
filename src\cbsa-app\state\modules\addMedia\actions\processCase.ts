import { Case, SummaryTDO } from '@cbsa-modules/universal';
import { createAction } from '@reduxjs/toolkit';

export const PROCESS_CASE = createAction<{
  images: Array<string>;
  media: Array<SummaryTDO>;
  batchId: string;
  caseDetails: Case;
}>('CBSA/PROCESS_CASE');
export const PROCESS_CASE_SUCCESS = createAction<{
  name: string;
}>('CBSA/PROCESS_CASE_SUCCESS');
export const PROCESS_CASE_FAILURE = createAction<{
  name: string;
}>('CBSA/PROCESS_CASE_FAILURE');

export const processCase = ({
  images,
  media,
  batchId,
  caseDetails,
}: {
  images: Array<string>;
  media: Array<SummaryTDO>;
  batchId: string;
  caseDetails: Case;
}) => PROCESS_CASE({ images, media, batchId, caseDetails });

export const processCaseSuccess = (name: string) =>
  PROCESS_CASE_SUCCESS({ name });

export const processCaseFailure = (name: string) =>
  PROCESS_CASE_FAILURE({ name });

import { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import makeStyles from '@mui/styles/makeStyles';

import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid2';
import IconButton from '@mui/material/IconButton';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import Typography from '@mui/material/Typography';

import ChevronLeft from '@mui/icons-material/ChevronLeft';
import ChevronRight from '@mui/icons-material/ChevronRight';
import KeyboardArrowDown from '@mui/icons-material/KeyboardArrowDown';

import CaseRow from './CaseRow';

import {
  selectCasesList,
  selectCasesTotal,
  selectCasesSortColumn,
  selectCasesSortOrder,
  selectPaginationAmount,
  selectPaginationStart,
} from '@cbsa-modules/mainPage/selectors';

import {
  setCasesSortColumn,
  setCasesSortOrder,
  setPaginationAmount,
  setPaginationStart,
} from '@cbsa-modules/mainPage/actions';

import { useIntl } from 'react-intl';
import { TreeObjectId } from '@common-modules/universal/models/Brands';
import { Theme, ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';
import { ArrayOrSingle } from 'ts-essentials';

const useStyles = makeStyles((theme: Theme) => ({
  casesTableGrid: {
    paddingTop: theme.spacing(3),
    paddingBottom: theme.spacing(3),
  },
  tableHeadRow: {
    paddingLeft: theme.spacing(3),
    paddingRight: theme.spacing(3),
  },
  alternateRow: {
    padding: theme.spacing(3),
  },
  tableHeadText: {
    fontSize: theme.spacing(1.5),
    color: theme.palette.text.secondary,
    fontWeight: theme.typography.fontWeightRegular,
    textTransform: 'uppercase',
  },
  paginationRow: {
    padding: theme.spacing(3),
  },
  allArrows: {
    transition: `transform ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut}, color ${theme.transitions.duration.standard}ms ${theme.transitions.easing.easeInOut}`,
    '&:hover': {
      cursor: 'pointer',
    },
  },
  inactiveArrow: {
    color: theme.palette.text.secondary,
  },
  activeArrow: {
    color: theme.palette.primary.main,
  },
  upArrow: {
    transform: 'rotate(-180deg)',
  },
}));

const PAGINATION_AMOUNTS = [10, 25, 50, 100];

export interface CaseTableProps {
  onCaseClick: (caseId: TreeObjectId) => void;
}
const CasesTableContent = (props: CaseTableProps) => {
  const { onCaseClick } = props;

  const classes = useStyles();
  const intl = useIntl();

  const dispatch = useDispatch();
  const casesSortColumn = useSelector(selectCasesSortColumn);
  const casesSortOrder = useSelector(selectCasesSortOrder);
  const paginationAmount = useSelector(selectPaginationAmount);
  const paginationStart = useSelector(selectPaginationStart);
  const casesList = useSelector(selectCasesList);
  const casesTotal = useSelector(selectCasesTotal);

  let paginationEnd = paginationStart + paginationAmount - 1;
  if (paginationEnd > casesTotal) {
    paginationEnd = casesTotal;
  }

  const [nameArrowStyles, createdArrowStyles, modifiedArrowStyles] =
    useMemo(() => {
      const nameArrowStyles = [classes.allArrows];
      if (casesSortColumn !== 'caseName') {
        nameArrowStyles.push(classes.inactiveArrow);
      } else {
        nameArrowStyles.push(classes.activeArrow);
        if (casesSortOrder === 'asc') {
          nameArrowStyles.push(classes.upArrow);
        }
      }

      const createdArrowStyles = [classes.allArrows];
      if (casesSortColumn !== 'createdDateTime') {
        createdArrowStyles.push(classes.inactiveArrow);
      } else {
        createdArrowStyles.push(classes.activeArrow);
        if (casesSortOrder === 'asc') {
          createdArrowStyles.push(classes.upArrow);
        }
      }

      const modifiedArrowStyles = [classes.allArrows];
      if (casesSortColumn !== 'modifiedDateTime') {
        modifiedArrowStyles.push(classes.inactiveArrow);
      } else {
        modifiedArrowStyles.push(classes.activeArrow);
        if (casesSortOrder === 'asc') {
          modifiedArrowStyles.push(classes.upArrow);
        }
      }

      return [nameArrowStyles, createdArrowStyles, modifiedArrowStyles];
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [casesSortColumn, casesSortOrder]);

  let caseRows: ArrayOrSingle<React.JSX.Element> = (
    <Grid
      size={12}
      container
      justifyContent="center"
      alignItems="center"
      className={classes.alternateRow}
    >
      <CircularProgress variant="indeterminate" />
    </Grid>
  );
  if (casesList) {
    if (casesList.length > 0) {
      caseRows = casesList.map((currentCase) => (
        <CaseRow
          key={`caseRow_${currentCase.id}`}
          caseInfo={currentCase}
          onCaseClick={onCaseClick}
        />
      ));
    } else {
      caseRows = (
        <Grid
          size={12}
          container
          justifyContent="center"
          alignItems="center"
          className={classes.alternateRow}
        >
          <Typography>
            {intl.formatMessage({
              id: 'noItemsToDisplay',
              defaultMessage: 'No Items to Display',
            })}
          </Typography>
        </Grid>
      );
    }
  }

  return (
    <Grid
      container
      direction="column"
      justifyContent="flex-start"
      className={classes.casesTableGrid}
    >
      <Grid container className={classes.tableHeadRow}>
        <Grid container alignItems="center" size={2}>
          <Typography classes={{ root: classes.tableHeadText }}>
            {intl.formatMessage({
              id: 'caseName',
              defaultMessage: 'Case Name',
            })}
          </Typography>
          <KeyboardArrowDown
            classes={{ root: nameArrowStyles.join(' ') }}
            onClick={() => {
              if (casesSortColumn !== 'caseName') {
                dispatch(setCasesSortColumn('caseName'));
                dispatch(setCasesSortOrder('desc'));
              } else {
                dispatch(
                  setCasesSortOrder(casesSortOrder === 'asc' ? 'desc' : 'asc')
                );
              }
            }}
          />
        </Grid>
        <Grid container alignItems="center" size={2}>
          <Typography classes={{ root: classes.tableHeadText }}>
            {intl.formatMessage({
              id: 'createdDate',
              defaultMessage: 'Created Date',
            })}
          </Typography>
          <KeyboardArrowDown
            classes={{ root: createdArrowStyles.join(' ') }}
            onClick={() => {
              if (casesSortColumn !== 'createdDateTime') {
                dispatch(setCasesSortColumn('createdDateTime'));
                dispatch(setCasesSortOrder('desc'));
              } else {
                dispatch(
                  setCasesSortOrder(casesSortOrder === 'asc' ? 'desc' : 'asc')
                );
              }
            }}
          />
        </Grid>
        <Grid container alignItems="center" size={2}>
          <Typography classes={{ root: classes.tableHeadText }}>
            {intl.formatMessage({
              id: 'lastUpdated',
              defaultMessage: 'Last Updated',
            })}
          </Typography>
          <KeyboardArrowDown
            classes={{ root: modifiedArrowStyles.join(' ') }}
            onClick={() => {
              if (casesSortColumn !== 'modifiedDateTime') {
                dispatch(setCasesSortColumn('modifiedDateTime'));
                dispatch(setCasesSortOrder('desc'));
              } else {
                dispatch(
                  setCasesSortOrder(casesSortOrder === 'asc' ? 'desc' : 'asc')
                );
              }
            }}
          />
        </Grid>
        <Grid container alignItems="center" size={2}>
          <Typography classes={{ root: classes.tableHeadText }}>
            {intl.formatMessage({ id: 'age', defaultMessage: 'Age' })}
          </Typography>
        </Grid>
        <Grid container alignItems="center" size={2}>
          <Typography classes={{ root: classes.tableHeadText }}>
            {intl.formatMessage({ id: 'status', defaultMessage: 'Status' })}
          </Typography>
        </Grid>
        <Grid
          container
          justifyContent="flex-end"
          alignItems="center"
          textAlign="end"
          size={2}
        >
          <Typography
            component="span"
            classes={{ root: classes.tableHeadText }}
          >
            {intl.formatMessage({
              id: 'caseActions',
              defaultMessage: 'Case Actions',
            })}
          </Typography>
        </Grid>
      </Grid>
      {caseRows}
      <Grid container className={classes.paginationRow}>
        <Grid container alignItems="center" size={6}>
          <Select
            value={paginationAmount}
            onChange={(event) => {
              dispatch(setPaginationAmount(event.target.value as number));
            }}
            variant="outlined"
            margin="dense"
          >
            {PAGINATION_AMOUNTS.map((currentAmount) => (
              <MenuItem
                key={`paginationSelect-${currentAmount}`}
                value={currentAmount}
              >
                {`${currentAmount} ${intl.formatMessage({
                  id: 'perPage',
                  defaultMessage: 'Per Page',
                })}`}
              </MenuItem>
            ))}
          </Select>
        </Grid>
        <Grid container justifyContent="flex-end" alignItems="center" size={6}>
          <Typography>
            <IconButton
              onClick={() => {
                let newStart = paginationStart - paginationAmount;
                if (newStart < 1) {
                  newStart = 1;
                }
                dispatch(setPaginationStart(newStart));
              }}
              size="small"
              color="primary"
              disabled={paginationStart <= 1}
            >
              <ChevronLeft />
            </IconButton>
            &nbsp;
            <span style={{ fontWeight: 'bold' }}>
              {paginationStart} - {paginationEnd}
            </span>{' '}
            {`${intl.formatMessage({ id: 'of', defaultMessage: 'of' })} ${casesTotal}`}
            &nbsp;
            <IconButton
              onClick={() => {
                dispatch(
                  setPaginationStart(paginationStart + paginationAmount)
                );
              }}
              size="small"
              color="primary"
              disabled={paginationStart + paginationAmount - 1 >= casesTotal}
            >
              <ChevronRight />
            </IconButton>
          </Typography>
        </Grid>
      </Grid>
    </Grid>
  );
};

const CasesTable = (props: CaseTableProps) => (
  <ThemeProvider theme={defaultTheme}>
    <CasesTableContent {...props} />
  </ThemeProvider>
);

export default CasesTable;

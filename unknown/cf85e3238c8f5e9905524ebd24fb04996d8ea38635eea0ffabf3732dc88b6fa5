import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import Dialog from '@common-components/ConfirmDialog';

const ArchiveCaseDialog = ({
  isArchived,
  isOpen,
  onConfirm,
  onClose,
}: Props) => {
  const intl = useIntl();
  return (
    <Dialog
      isOpen={isOpen}
      title={I18nTranslate.TranslateMessage(
        isArchived ? 'reopenCaseContent' : 'archiveCaseTitle'
      )}
      content={I18nTranslate.TranslateMessage(
        isArchived ? 'reopenCaseContent' : 'archiveCaseContent'
      )}
      confirmText={intl.formatMessage({
        id: isArchived ? 'reopen' : 'archive',
      })}
      onConfirm={onConfirm}
      cancelText={intl.formatMessage({ id: 'cancel' })}
      onCancel={onClose}
    />
  );
};

interface Props {
  readonly isArchived: boolean;
  readonly isOpen: boolean;
  readonly onConfirm: () => void;
  readonly onClose: () => void;
}

export default ArchiveCaseDialog;

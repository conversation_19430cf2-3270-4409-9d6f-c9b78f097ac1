import { selectUser } from '@helpers/tdoHelper';

import * as govQ<PERSON><PERSON> from '@helpers/govQAAPI';
import * as foiaXpress<PERSON>I from '@helpers/foiaXpressAPI';
import * as casepoint<PERSON><PERSON> from '@helpers/casepointAPI';
import * as exterro<PERSON><PERSON> from '@helpers/exterroAPI';
import * as nuixAPI from '@helpers/nuixAPI';
import * as externalIntegrationAPI from '@helpers/externalIntegrationApi';

import moment from 'moment';

import {
  removeTdoLock,
  selectRedactionJob,
  selectRedactionJobCreatedDateTime,
  selectRedactionJobStatus,
  selectTags,
  selectTdoReadonly,
  selectTrimInterval,
} from '../index';
// import { actionTryPermission, selectAllowDownload } from '@user-permissions';
import { actionTryPermission, selectFeatureFlags } from '@user-permissions';
import {
  // actionRunDownloadWhenRefresh,
  IN_CLEAR_MEDIA_DATA,
  IN_CLOSE_MEDIA,
  IN_LOAD_DETECTION_DATA,
  IN_DOWNLOAD_ASSET,
  POLL_DOWNLOAD_ASSET_STOP,
  IN_LOAD_MEDIA_DATA,
  IN_LOAD_TRACKING_ENGINE_RESULTS,
  IN_LOAD_TRANSCRIPTION_DATA,
  IN_REDACT_MEDIA,
  IN_SAVE_CHANGES,
  IN_SET_GLOBAL_SETTINGS,
  IN_SET_TDO,
  // actionOutProgress,
  // OUT_FETCH_DOWNLOAD_ASSET_SUCCESS,
  OUT_FETCH_REDACT_TDO_SUCCESS,
  OUT_POLL_DOWNLOAD_ASSET_SUCCESS,
  OUT_POLL_DOWNLOAD_ASSET_FAILED,
  OUT_UDR_ASSET,
  OUT_BOUNDING_POLY_COLLECTION,
  OUT_DETECTION_CLUSTER_GROUPS,
  OUT_SELECTED_BOUNDINGPOLY_GROUP,
  OUT_UDR_COLLECTION,
  OUT_UDR_CLUSTER_GROUPS,
  OUT_CLUSTER_MAP,
} from '@worker';
import { get, isString, maxBy } from 'lodash';
import { redirect } from 'redux-first-router';
import { channel } from 'redux-saga';
import {
  all,
  call,
  cancel,
  fork,
  put,
  race,
  select,
  take,
  takeEvery,
  throttle,
  takeLatest,
  delay,
  debounce,
} from 'typed-redux-saga/macro';
import { modules } from '@veritone/glc-redux';
import { enqueueSnackbar } from '../../snackbar';

import { actionStopAllEnginePolling } from '../../engines/actions';
import * as OpticalTracking from '../../engines/optical-tracking';
import { selectConfigEngines } from '../../engines/selectors';
import * as Transcription from '../../engines/transcription';
import { selectTranscriptionEngineId } from '../../engines/transcription';
// import {
// CREATE_JOB_FACES_SUCCESS,
// FACES_JOB_STATUS_ACTION_START_POLLING,
// FACES_JOB_STATUS_ACTION_STOP_POLLING,
// FACES_JOB_STATUS_SUCCESS,
// } from '../../facesTabModule';
import { redactingFile } from '../../redactFile';
import {
  ROUTE_HOME,
  ROUTE_LOAD_FAILED,
  ROUTE_MEDIA_DETAILS,
} from '@common-modules/routing';
import {
  // actionSetSelectedGroups,
  DELETE_LAST_ASSET,
  DOWNLOAD_ASSET,
  RETRY_DOWNLOAD,
  FETCH_TDO_SUCCESS,
  FETCH_TDO_WITH_FAILURE,
  FETCH_UPDATE_ASSET_ACTION,
  FETCH_STATUS_CHANGE_SUCCESS,
  FETCH_UPDATE_SETTINGS_SUCCESS,
  goToMainPage,
  IN_REDACTION_TAG_KEY,
  NEW_OVERLAY,
  PAGE_LOADED,
  REDACT_TDO,
  REDACTION_JOB_STATUS_ACTION_STOP_POLLING,
  REDIRECT_TO_HOME,
  REFRESH_TDO_PRIMARY_ASSET_ID_SUCCESS,
  SET_GLOBAL_SETTINGS,
  UPDATE_REDACTED_FILE_METRICS,
  REDACTED_FILE_COMPLETED,
  CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM,
  actionSeekVideo,
  UDR_SELECTED_FROM_TIMELINE,
  EXIT_MEDIA_DETAILS,
  CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM_SUBMIT,
  UPDATE_START_END_UDRS_LIVE_TRACKING,
  saveLocalUDRAssets,
  FETCH_DELETE_LAST_ASSET_FAILURE,
  FETCH_DELETE_LAST_ASSET_SUCCESS,
  SEND_REDACTED_FILE_TO_GOVQA,
  setGovQASessionId,
  patchGovQAState,
  FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS,
  FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE,
  FACE_HIGHLIGHT,
  setSelectedUdrGroupOnWebWorkerOnly,
  DELETE_DETECTION_OVERLAY,
  DELETE_DETECTION_IN_FRAME,
  DELETE_DETECTION_SEGMENT,
  DELETE_DETECTION_GROUP,
  DELETE_UDR_OVERLAY,
  DELETE_UDR_IN_FRAME,
  DELETE_UDR_SEGMENT,
  DELETE_UDR_GROUP,
  actionSetDeletedOverlaysCache,
  SEND_REDACTED_FILE_TO_FOIAXPRESS,
  patchFOIAXpressState,
  SEND_REDACTED_FILE_TO_CASEPOINT,
  patchCasepointState,
  SEND_REDACTED_FILE_TO_EXTERRO,
  patchExterroState,
  SEND_REDACTED_FILE_TO_NUIX,
  patchNuixState,
  FETCH_WAVEFORM_SUCCESS,
  FETCH_WAVEFORM_FAILURE,
  setAudiowaves,
  LOAD_ENGINE_RESULTS_AFTER_DETECT,
  UPDATE_DOWNLOAD_URL,
  DELETE_CASE_DRAWER_TDO_REQUEST,
  DeleteCaseDrawerPayload,
  actionDeleteCaseDrawerSuccess,
  DOWNLOAD_ALL_FILES_SNACKBAR,
  SET_TDO_NAME,
  TDO_NAME_CHANGE_SUCCESS,
  TDO_NAME_CHANGE_FAILURE,
  // actionFilterToggleAll,
  actionFilterToggleShowType,
  VIDEO_HAS_LOADED,
  actionSetSelectedGroups,
  ROTATE_VIDEO,
  CREATE_ROTATE_VIDEO_JOB_FAILURE,
  CREATE_ROTATE_VIDEO_JOB_SUCCESS,
  DISCARD_CHANGES,
  SEND_REDACTED_FILE_TO_EXTERNAL_INTEGRATION,
  patchExternalIntegrationState,
} from '../actions';
import { DetailTDO, UDRsPolyAssetGroupSeriesItem } from '../models';
import {
  pickLastCompletedTaskByCreatedDate,
  pickLastCompletedTaskByCompletedDate,
  local,
  selectAllDownloadTask,
  selectAllRedactTask,
  selectCurrentTdoId,
  selectGlobalSettings,
  selectIsLoaded,
  selectIsFileStatusChanged,
  selectIsTdoReady,
  selectMediaDuration,
  selectRedactionRunning,
  selectTdo,
  getLastRedactExport,
  selectTrackingRunning,
  selectTranscriptionRunning,
  isFaceDetectionReadySelector,
  selectTdoIsChanged,
  selectUdrAsset,
  selectGovQASessionId,
  selectSelectedUDRGroupId,
  selectDeletedOverlaysCache,
  selectFilterParameters,
  selectTranscriptionView,
  selectDetectObjects,
  selectCaseMedia,
  selectOriginalFileStatus,
  selectWordsRedactedMap,
  selectTranscriptAssets,
} from '../selectors';
import {
  deleteLastAsset,
  fetchRedactedMedia,
  fetchRedactionJobStatus,
  fetchTdo,
  fetchWaveformCsv,
  refreshTdoPrimaryAsset,
  refreshTdoTasks,
  updateLastRedactedFileMetrics,
  updateSettingsOnDetails,
  updateStatusOnDetails,
  fetchLatestTDOAuditLogAsset,
  updateNameOnDetails,
  deleteAllHeadDetectionResults,
  updateTDOStatus,
  rotateVideo,
} from '../services';
import { auditLogsSaga } from './audit-logs';
import { fetchCaseMedia, fetchCaseMediaFailure } from './fetchCaseMedia';
import { keyboardSaga } from './keyboard';
import { videoSaga } from './video';
import { workerOutSaga } from './worker-out';
import {
  cleanDeleteOverlayCache,
  DELETE_OVERLAY_CACHE_INTERVAL,
} from '../helpers/cache-helper';
import { selectSDOById } from '@redact-modules/mainPage/selectors';
import { watchDeleteFiles } from '@redact-state/modules/casemedia/sagas';
import {
  actionUpsertSDO,
  UPSERT_ORGANIZATION_SDO,
  actionFetchOrgSDOsByTDOsId,
  FETCH_ORGANIZATION_SDOS_BY_ID,
  FETCH_ORGANIZATION_SDOS_BY_ID_SUCCESS,
  FETCH_ORGANIZATION_SDOS_BY_ID_FAILURE,
} from '@redact-modules/mainPage/actions';
import {
  upsertOrganizationSDO,
  fetchOrganizationSDOsByTDOsId,
} from '@redact-modules/mainPage/services';
import {
  fetchMediaComments,
  fetchMediaCommentsSuccess,
  updateComment,
  updateCommentSuccess,
  updateCommentFailure,
  fetchMediaCommentsUser,
  fetchMediaCommentsUserFailure,
  fetchMediaUser,
  fetchMediaUserFailure,
} from '@common-modules/mediaDetails/saga/mediaComments';
import {
  fetchRedactionCodes,
  fetchRedactionCodesFailure,
} from '@redact-modules/redactionCodes/sagas';
import {
  viewSaga,
  watchRemoveTdoLock,
  watchTdoLock,
  watchTdoLockFound,
  watchTdoLockNotFound,
} from './view';
import {
  actionDeleteFile,
  actionUpdateRedactionStatusChange,
  DELETE_FILE_FAILURE,
  DELETE_FILE_SUCCESS,
} from '@redact/state/modules/casemedia/actions';
import { VirtualFolderNames } from '@redact/state/modules/casemedia/store';
import { selectEnableFolderView } from '@common/user-onboarding/selectors';
import { isRedactionStatus } from '@redact/state/modules/casemedia/models';
import { actionSetHasStartedPlayer } from '@common/state/modules/player';
import { undoSaga } from '@common-modules/mediaDetails/saga/undo';
import getLastRedactedFile from '@helpers/getLastRedactedFile';
import { saveMediaData } from '@common/web-worker/services';
import { sagaIntl } from '@i18n';

const {
  user: { fetchUser },
} = modules;

export function* initMediaDetails() {
  yield* all([
    fork(toggleAutoSave),
    fork(watchUpdateFileStage),
    fork(onActionStatusChangeSuccess),
    fork(onActionFetchOrgSDOsByTDOsId),
    fork(onActionUpsertOrgSDO),
    fork(watchDeleteAsset),

    fork(watchSendDownloadRequestHandler),
    fork(downloadAssetToWorker),
    fork(watchDownloadFile),
    fork(watchDownloadFileFailed),
    fork(watchRetryDownloadChannel),
    fork(watchRetryDownload),
    fork(watchDownloadAllRedactedFile),

    fork(watchUpdateLastRedactedFileMetrics),
    fork(watchGoToHomePage),
    fork(watchGoToMediaDetailsPage),
    fork(watchRedactTDOToWorker),
    fork(watchPollRedactionJob),
    fork(watchRetrieveRedactionJobResults),
    // fork(watchTdoTaskChanges),
    fork(watchUdrTrackingStart),
    fork(watchUdrTrackingSuccess),
    fork(watchTranscriptionSuccess),
    fork(loadSettingsFromDetails),
    fork(watchForSetGlobalSettings),
    fork(watchForChangeName),
    fork(watchSendToReloadResults),
    // fork(onActionNewOverlay),
    fork(auditLogsSaga),
    fork(viewSaga),
    fork(keyboardSaga),
    fork(workerOutSaga),
    fork(videoSaga),
    fork(onChangeUdrsPolyAssetGroupSeriesItem),
    fork(onChangeUdrsPolyAssetGroupSeriesItemSubmit),
    fork(onUpdateStartEndLiveTrackingUdr),
    fork(onUDRSelectedFromTimeline),
    fork(onFetchTDOSuccess),
    fork(onExitMediaDetails),
    fork(onNewOverlay),
    fork(sendRedactedFileToGovQa),
    fork(sendRedactedFileToFOIAXpress),
    fork(sendRedactedFileToExternalIntegration),
    fork(sendRedactedFileToCasepoint),
    fork(sendRedactedFileToExterro),
    fork(sendRedactedFileToNuix),
    fork(watchOverlaySelectionChanges),
    fork(onCleanDeletedOverlaysCache),

    fork(fetchCaseMedia),
    fork(fetchCaseMediaFailure),

    fork(fetchMediaComments),
    fork(fetchMediaCommentsSuccess),
    fork(updateComment),
    fork(updateCommentSuccess),
    fork(updateCommentFailure),
    fork(fetchMediaCommentsUser),
    fork(fetchMediaCommentsUserFailure),
    fork(fetchMediaUser),
    fork(fetchMediaUserFailure),
    fork(watchDeleteCaseDrawerTdo),
    fork(fetchRedactionCodes),
    fork(fetchRedactionCodesFailure),

    fork(watchTdoLock),
    fork(watchTdoLockFound),
    fork(watchTdoLockNotFound),
    fork(watchRemoveTdoLock),

    fork(watchVideoHasLoaded),

    fork(undoSaga),
    fork(watchRotateVideo),
    fork(watchRotateVideoSuccess),
    fork(watchRotateVideoFailure),
  ]);
}

export interface Action<P> {
  type: string;
  payload: P;
}

export interface ActionWithMeta<P, M> {
  type: string;
  payload: P;
  meta: M;
}
export function* loadMediaDetailsPage() {
  yield* all([fork(loadRouteData), fork(watchDeleteFiles)]);
}

export function* loadRouteData() {
  yield* all([
    fork(loadTdoData),
    fork(watchForTdoSuccess),
    fork(watchForTdoFailed),
  ]);
}

function* watchDeleteCaseDrawerTdo() {
  yield* all([
    takeEvery(DELETE_CASE_DRAWER_TDO_REQUEST, deleteCaseDrawerTdoRequest),
  ]);
}

function* deleteCaseDrawerTdoRequest(action: Action<DeleteCaseDrawerPayload>) {
  const { tdoId, fileName, isActiveFile } = action.payload;

  yield* put(
    actionDeleteFile({
      tdoId,
      fileName,
      type: VirtualFolderNames.INPUT,
      isSoftDelete: true,
    })
  );

  const { ok } = yield* race({
    ok: take([DELETE_FILE_SUCCESS]),
    err: take(DELETE_FILE_FAILURE),
  });
  if (ok) {
    yield* put(actionDeleteCaseDrawerSuccess({ ...action.payload }));
    if (isActiveFile) {
      window.history.back();
    }
  }
}

function* toggleAutoSave() {
  yield* takeEvery(FETCH_TDO_SUCCESS, toggleAutoSaveHandle);
}

export function* toggleAutoSaveHandle() {
  const globalSettings = yield* select(selectGlobalSettings);
  // check if settings have been saved on the tdo details
  if (!globalSettings.isSaved) {
    yield* put(SET_GLOBAL_SETTINGS(globalSettings));
  }

  while (true) {
    const { go } = yield* race({
      go: delay(60_000 * 5),
      _1: take(FETCH_TDO_SUCCESS),
      _2: take((a: any) => {
        if (a.type && !isString(a.type)) {
          console.warn('Non-string action type:', a.type.type);
        }
        // TODO: Fix race types so we can use UnknownAction here
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-return
        return a.type?.startsWith?.('route/');
      }),
    });
    if (go) {
      yield* put(FETCH_UPDATE_ASSET_ACTION());
    } else {
      break;
    }
  }
}

/**
 * Load TDO async cycle.
 */

export const loadTdoData = function* () {
  const tdoId = yield* select(selectCurrentTdoId);
  if (tdoId) {
    yield* put(fetchTdo(tdoId));
  }
};

export const watchForTdoSuccess = function* () {
  yield* takeEvery(FETCH_TDO_SUCCESS, watchForTdoSuccessHandle);
};

export const watchForTdoSuccessHandle = function* ({
  payload: { temporalDataObject: tdo },
}: {
  payload: { temporalDataObject: DetailTDO };
}) {
  const tdoId = tdo.id;
  const {
    opticalTrackingEngineId,
    transcriptionCategoryId,
    detectionCategory,
    transcriptionEngineId,
    apiRoot,
  } = yield* select(selectConfigEngines);

  const mediaDuration = yield* select(selectMediaDuration);

  // filters through tasks to select [detection engine id, object tracking id, transcription engine id]
  const hasTranscriptionEngine = (tdo?.tasks?.records || [])?.find(
    (r) => r?.engine.categoryId === transcriptionCategoryId
  );
  const engines = (tdo?.tasks?.records || [])
    // TODO: veritone-types exports TaskStatus from a pure definition file which is invalid - should replace veritone-types
    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
    .filter(({ status }) => status === 'complete')
    .reduce<[string | null, string, string | null]>(
      ([id0, id1, id2], { engineId, engine }) => [
        engine.categoryId === detectionCategory ? engineId : id0,
        id1,
        engine.categoryId === transcriptionCategoryId ||
        get(tdo, 'details.veritoneProgram.sourceType', '') === 'trimmed'
          ? engineId
          : id2,
      ],
      [
        null,
        opticalTrackingEngineId,
        get(tdo, 'details.veritoneProgram.sourceType', '') === 'trimmed' ||
        hasTranscriptionEngine
          ? transcriptionEngineId
          : null,
      ]
    );

  if (tdo.waveformAssets) {
    yield* all([
      fork(loadWaveformCsv),
      fork(watchForWaveformSuccess),
      fork(watchForWaveformFailure),
    ]);
  }

  yield* all([
    fork(lookupRunningJobs),
    put(
      IN_LOAD_MEDIA_DATA({
        tdoId,
        engines,
        mediaDuration,
      })
    ),
    put(
      IN_SET_TDO({
        apiRoot: apiRoot,
        tdo: {
          id: tdo.id,
          startDateTime: tdo.startDateTime,
          details: tdo.details,
        },
      })
    ),
  ]);

  const isPageLoaded = yield* select(selectIsLoaded);
  if (!isPageLoaded) {
    yield* put(PAGE_LOADED());
  }
};

export const watchForTdoFailed = function* () {
  yield* takeEvery(FETCH_TDO_WITH_FAILURE, function* () {
    yield* put(ROUTE_LOAD_FAILED());
  });
};

/**
 * Check for running jobs. Start polling, if found.
 */
export function* lookupRunningJobs() {
  // Pull all running job tasks.
  const tdoId = yield* select(selectCurrentTdoId);
  // const detectOn = yield select(selectDetectionRunning);
  const trackingOn = yield* select(selectTrackingRunning);
  const transcriptionOn = yield* select(selectTranscriptionRunning);
  const redactOn = yield* select(selectRedactionRunning);
  // const downloadOn = yield select(selectDownloadRunning);

  // TODO: type this better
  const startPollingActions = tdoId
    ? [
        // ...detectOn.filter(oldJobs).map(({ jobId: id }) => [
        //   put,
        //   {
        //     type: FACES_JOB_STATUS_ACTION_START_POLLING,
        //     payload: { id },
        //   },
        // ]),
        ...trackingOn.map(
          ({ jobId }) =>
            [
              put,
              OpticalTracking.startPollingEngineResultsAction({ tdoId, jobId }),
            ] as const
        ),
        ...transcriptionOn.map(
          ({ jobId }) =>
            [
              put,
              Transcription.startPollingEngineResultsAction({ tdoId, jobId }),
            ] as const
        ),
        ...redactOn.map(
          ({ jobId }) =>
            [pollRedactionJobEngineResults, { tdoId, jobId }] as const
        ),
        // ...downloadOn.map(({ jobId }: any) => [
        //   refreshDownload,
        //   { createJob: { targetId: tdoId, id: jobId } },
        // ]),
      ]
    : [];

  for (const [fn, args] of startPollingActions) {
    yield fn(args); // yield call(fn, args);
  }
}

// SAVE button
function* watchUpdateFileStage() {
  yield* takeLatest(FETCH_UPDATE_ASSET_ACTION, watchUpdateFileStageHandleSaga);
}

export function* watchUpdateFileStageHandleSaga({
  payload,
}: ReturnType<typeof FETCH_UPDATE_ASSET_ACTION>) {
  const isFileStatusChanged = yield* select(selectIsFileStatusChanged);
  const { tdoIsChanged } = yield* select(local);

  // new always update status so modifiedDateTime on SDO is updated with every save
  if (isFileStatusChanged || tdoIsChanged || payload?.force) {
    yield* put(updateStatusOnDetails());
  }

  const tdo = yield* select(selectTdo);
  const isFaceDetectionReady = yield* select(isFaceDetectionReadySelector);
  if (
    tdo?.primaryAsset &&
    (tdoIsChanged || payload?.force) &&
    isFaceDetectionReady
  ) {
    const user = yield* select(selectUser);
    yield* put(
      IN_SAVE_CHANGES({
        tdoId: tdo.id,
        email: user.email,
      })
    );
    // refreshToken using sdk call
    window.aiware?.auth?.reportAppActivity?.();
  }
}

function* onActionFetchOrgSDOsByTDOsId() {
  yield* takeEvery(FETCH_ORGANIZATION_SDOS_BY_ID, function* ({ payload }) {
    yield* put(fetchOrganizationSDOsByTDOsId(payload));
  });
}

/**
 * Watches for TDO status changes. Updates the paired SDO.
 */
export function* onActionStatusChangeSuccess() {
  yield* takeEvery(
    FETCH_STATUS_CHANGE_SUCCESS,
    onActionStatusChangeSuccessHandle
  );
}

export function* onActionStatusChangeSuccessHandle({
  payload,
}: ReturnType<typeof FETCH_STATUS_CHANGE_SUCCESS>) {
  const intl = sagaIntl();
  const tdoId = payload.updateTDO.id;
  const isEnableFolderView = yield* select(selectEnableFolderView);
  if (isEnableFolderView) {
    const redactionStatus = (payload?.updateTDO?.details?.tags || []).filter(
      (tag) => tag && Object.keys(tag).some((key) => key === 'redactionStatus')
    )?.[0]?.redactionStatus;
    if (isRedactionStatus(redactionStatus)) {
      yield* put(actionUpdateRedactionStatusChange({ tdoId, redactionStatus }));
    }
  }
  // if coming from main page sdo should already available
  const sdo = yield* select(selectSDOById(tdoId));
  let sdoId;
  if (sdo) {
    sdoId = sdo.id;
  } else {
    yield* put(actionFetchOrgSDOsByTDOsId({ tdoId: tdoId }));
    const { ok, err } = yield* race({
      ok: take(FETCH_ORGANIZATION_SDOS_BY_ID_SUCCESS),
      err: take(FETCH_ORGANIZATION_SDOS_BY_ID_FAILURE),
    });

    if (ok && ok.payload?.structuredDataObjects?.records?.length) {
      sdoId = ok.payload.structuredDataObjects.records[0]?.id;
    }
    if (err) {
      console.error(
        'onActionStatusChangeSuccessHandle() FETCH_ORGANIZATION_SDOS_BY_ID_FAILURE',
        err
      );
      yield* put(
        enqueueSnackbar({
          message: intl.formatMessage({
            id: 'errorUpdatingMedia',
            defaultMessage: 'An error occurred while updating media.',
          }),
        })
      );
    }
  }

  if (sdoId) {
    const { redactionStatus: status } = (
      payload.updateTDO.details?.tags || []
    ).find((tag) => tag?.value === IN_REDACTION_TAG_KEY) || {
      redactionStatus: 'Draft',
    };
    yield* put(actionUpsertSDO({ id: sdoId, data: { tdoId, status } }));
  }
}

/**
 * Handle Upsert SDO Actions.
 */
function* onActionUpsertOrgSDO() {
  yield* takeEvery(UPSERT_ORGANIZATION_SDO, function* ({ payload }) {
    // !! only allow an update - currently this saga is running even when on the mainPage which otherwise would cause a duplicate sdo to be created!
    if (payload?.id) {
      yield* put(upsertOrganizationSDO(payload));
    }
  });
}

// DELETE button
export function* deleteAsset(action: {
  payload: { id?: string; ids?: string[] };
}) {
  yield* put(deleteLastAsset(action));
  const { ok } = yield* race({
    ok: take(FETCH_DELETE_LAST_ASSET_SUCCESS),
    err: take(FETCH_DELETE_LAST_ASSET_FAILURE),
  });

  const intl = sagaIntl();

  if (ok) {
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'latestRedactDeleted',
          defaultMessage: 'Latest redacted file has been successfully deleted',
        }),
      })
    );
    yield* cancel();
  }

  yield* put(
    enqueueSnackbar({
      message: intl.formatMessage({
        id: 'latestRedactDeletedFail',
        defaultMessage: 'Latest redacted file deletion failed',
      }),
    })
  );
}

function* watchDeleteAsset() {
  yield* takeLatest(DELETE_LAST_ASSET, deleteAsset);
}

function* watchForSetGlobalSettings() {
  yield* takeLatest(SET_GLOBAL_SETTINGS, watchForSetGlobalSettingsHandle);
}

export function* watchForSetGlobalSettingsHandle({ payload }: any) {
  const tdoId = yield* select(selectCurrentTdoId);
  const isSaved = true;
  if (tdoId) {
    yield* put(updateSettingsOnDetails(tdoId, { ...payload, isSaved }));
  }
}

function* watchForChangeName() {
  yield* takeLatest(SET_TDO_NAME, watchForChangeNameHandle);
}

export function* watchForChangeNameHandle({ payload }: { payload: string }) {
  if (payload) {
    yield* put(updateNameOnDetails(payload));
    const { ok } = yield* race({
      ok: take(TDO_NAME_CHANGE_SUCCESS),
      err: take(TDO_NAME_CHANGE_FAILURE),
    });

    const intl = sagaIntl();

    if (ok) {
      yield* put(
        enqueueSnackbar({
          message: intl.formatMessage({
            id: 'editNameTDOSuccess',
            defaultMessage: 'TDO name updated successfully.',
          }),
        })
      );
      yield* cancel();
    }

    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'editNameTDOFailed',
          defaultMessage: 'TDO update failed.',
        }),
      })
    );
  }
}

// download
export function* sendDownloadRequestHandler() {
  yield* put(
    actionTryPermission({
      permissionKey: 'allowDownload',
      onAllow: function* () {
        yield* put(UPDATE_REDACTED_FILE_METRICS());
      },
    })
  );
}

export function* watchSendDownloadRequestHandler() {
  yield* takeEvery(DOWNLOAD_ASSET, sendDownloadRequestHandler);
}

export const pickLastAssetByModifiedDate = <
  T extends { modifiedDateTime?: string | number | null },
>(
  assets: T[]
) => {
  if (!assets) {
    return null;
  }

  // if (assets.length === 0) {
  //   return { modifiedDateTime: 0 };
  // }

  return maxBy<T>(assets, (mdt) =>
    new Date(mdt.modifiedDateTime || 0).getTime()
  );
};

export function* watchDownloadFile() {
  yield* takeEvery(OUT_POLL_DOWNLOAD_ASSET_SUCCESS, function* ({ payload }) {
    const exportAssets =
      payload?.payload?.temporalDataObject?.assets?.records || [];

    const hasTDOChanged = yield* select(selectTdoIsChanged);
    if (exportAssets.length > 0) {
      if (hasTDOChanged) {
        yield* put(
          UPDATE_DOWNLOAD_URL({
            downloadUrl: pickLastAssetByModifiedDate(exportAssets)?.signedUri,
          })
        );
      } else {
        const uri = pickLastAssetByModifiedDate(exportAssets)?.signedUri;
        if (uri) {
          window.location.href = uri;
        }
      }
    }
  });
}

export function* watchRetryDownloadChannel() {
  while (true) {
    const action = yield* take(downloadFileChannel);
    yield* put(action);
  }
}

export function* watchRetryDownload() {
  yield* takeEvery(RETRY_DOWNLOAD, retryDownload);
}

export function* retryDownload(action: ReturnType<typeof RETRY_DOWNLOAD>) {
  const { payload } = action;
  const { downloadEngineId, defaultClusterId } =
    yield* select(selectConfigEngines);
  const user = yield* select(selectUser);
  yield* put(
    IN_DOWNLOAD_ASSET({
      tdoId: payload.tdoId,
      tdoName: payload.tdoName,
      downloadEngineID: downloadEngineId,
      clusterId: defaultClusterId,
      email: user.email,
    })
  );
}

export function* watchDownloadAllRedactedFile() {
  yield* takeEvery(DOWNLOAD_ALL_FILES_SNACKBAR, downloadAllSnackbar);
}

export function* downloadAllSnackbar() {
  const intl = sagaIntl();
  yield* put(
    enqueueSnackbar({
      message: intl.formatMessage({
        id: 'downloadAllComplete',
        defaultMessage: 'Download All completed.',
      }),
      variant: 'success',
    })
  );
}

export const downloadFileChannel = channel<ReturnType<typeof RETRY_DOWNLOAD>>();

export function* watchDownloadFileFailed() {
  yield* takeEvery(OUT_POLL_DOWNLOAD_ASSET_FAILED, showSnackbarFailed);
}

export function* showSnackbarFailed(
  action: ReturnType<typeof OUT_POLL_DOWNLOAD_ASSET_FAILED>
) {
  const { payload } = action;

  const intl = sagaIntl();
  yield* put(
    enqueueSnackbar({
      message: intl.formatMessage({
        id: 'downloadMediaFail',
        defaultMessage: 'Attempt to download media failed',
      }),
      action: 'Retry',
      handleAction: () => downloadFileChannel.put(RETRY_DOWNLOAD(payload)),
    })
  );
}

// update metrics for last redacted asset
export function* watchUpdateLastRedactedFileMetrics() {
  yield* takeLatest(
    UPDATE_REDACTED_FILE_METRICS,
    updateLastRedactedFileMetricsHandler
  );
}

export function* checkBeforeDownload() {
  const tdo = yield* select(selectTdo);
  if (!tdo) {
    console.log('Error: TDO should have existed');
    return;
  }

  // if export asset contains most recent redacted-media asset will get it here
  const lastExportAsset = getLastRedactExport(tdo);

  let downloadReady = !!lastExportAsset;

  // however if export asset is missing details it must be a legacy asset so must use legacy method to check if up to date
  if (lastExportAsset && !lastExportAsset.details) {
    const downloadTasks = yield* select(selectAllDownloadTask);
    const redactTasks = yield* select(selectAllRedactTask);
    const lastCreatedDownloadTask =
      pickLastCompletedTaskByCreatedDate(downloadTasks);
    const lastCompletedRedactTask =
      pickLastCompletedTaskByCompletedDate(redactTasks);

    downloadReady =
      new Date(lastCreatedDownloadTask?.createdDateTime || 0).getTime() >
      new Date(lastCompletedRedactTask?.completedDateTime || 0).getTime();
  }

  if (downloadReady) {
    // download directly without use engine download
    yield* put(
      POLL_DOWNLOAD_ASSET_STOP({
        jobId: '',
        tdoId: tdo.id,
        status: 'complete',
        tdoName: tdo.name,
      })
    );
  } else {
    // use engine download to create data
    const { downloadEngineId, defaultClusterId } =
      yield* select(selectConfigEngines);
    const user = yield* select(selectUser);
    yield* put(
      IN_DOWNLOAD_ASSET({
        tdoId: tdo.id,
        tdoName: tdo.name,
        downloadEngineID: downloadEngineId,
        clusterId: defaultClusterId,
        email: user.email,
      })
    );
  }
}

// send tdoId, downloadEngineID, email to create jobID
export function* downloadAssetToWorker() {
  yield* takeEvery(DOWNLOAD_ASSET, checkBeforeDownload);
}

export function* updateLastRedactedFileMetricsHandler() {
  yield* put(updateLastRedactedFileMetrics());
}

function* watchGoToHomePage() {
  yield* takeLatest([REDIRECT_TO_HOME, ROUTE_HOME], function* (action) {
    // stop all polls
    if (REDIRECT_TO_HOME.match(action)) {
      const isEnableFolderView = yield* select(selectEnableFolderView);
      if (isEnableFolderView) {
        yield* put(redirect(goToMainPage()));
      } else {
        // window.location.href = '/';
        yield* put(redirect(goToMainPage()));
      }
    } else if (
      get(action, 'meta.location.prev.type') === ROUTE_MEDIA_DETAILS.type
    ) {
      yield* all([
        put(IN_CLOSE_MEDIA()),
        put(IN_CLEAR_MEDIA_DATA()),
        put({
          type: REDACTION_JOB_STATUS_ACTION_STOP_POLLING,
          payload: { noSubActions: true },
        }),
        put(actionStopAllEnginePolling()),
        // put({
        //   type: FACES_JOB_STATUS_ACTION_STOP_POLLING,
        //   payload: { noSubActions: true },
        // }),
      ]);
    }
  });
}

function* watchGoToMediaDetailsPage() {
  yield* takeLatest(ROUTE_MEDIA_DETAILS, function* () {
    yield* put(IN_CLEAR_MEDIA_DATA());
    yield* put(fetchUser());
  });
}

function* watchUdrTrackingStart() {
  yield* takeLatest(OpticalTracking.PROCESS_ENGINE_REQUEST, function* () {
    yield* put(FETCH_UPDATE_ASSET_ACTION());
  });
}

function* watchUdrTrackingSuccess() {
  yield* takeEvery(
    OpticalTracking.PROCESS_ENGINE_REQUEST_SUCCESS,
    watchUdrTrackingSuccessHandle
  );
}

export function* watchUdrTrackingSuccessHandle({
  payload,
}: ReturnType<typeof OpticalTracking.PROCESS_ENGINE_REQUEST_SUCCESS>) {
  const { tdoId, jobId } = payload;
  const { status } = yield* select(
    OpticalTracking.selectEngineStatus(tdoId, jobId)
  );
  const { opticalTrackingEngineId, detectionCategory } =
    yield* select(selectConfigEngines);
  const mediaDuration = yield* select(selectMediaDuration);
  // DELAY HACK START: Added so engine results have time to become available.
  yield* delay(5_000);
  // DELAY HACK END
  yield* put(
    IN_LOAD_TRACKING_ENGINE_RESULTS({
      ...payload,
      engines: [opticalTrackingEngineId],
      mediaDuration,
      categoryId: detectionCategory,
      // TODO: Is it really valid to pass status here without validating it? Should the selectEngineStatus selector be different?
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      job: { id: jobId, status: status as any },
    })
  );
}

function* watchTranscriptionSuccess() {
  yield* takeLatest(
    Transcription.PROCESS_ENGINE_REQUEST_SUCCESS,
    watchTranscriptionSuccessHandle
  );
}

export function* watchTranscriptionSuccessHandle() {
  const tdoId = yield* select(selectCurrentTdoId);
  const engineId = yield* select(selectTranscriptionEngineId);
  const mediaDuration = yield* select(selectMediaDuration);
  const { detectionCategory } = yield* select(selectConfigEngines);
  if (tdoId) {
    yield* put(
      IN_LOAD_TRANSCRIPTION_DATA({
        tdoId,
        engines: [engineId],
        mediaDuration,
        categoryId: detectionCategory,
      })
    );
  }
}

export function* redactTDOToWorker() {
  const tdo = yield* select(selectTdo);

  if (tdo) {
    // const hasRequestId =
    //   !!tdo.details.govQARequestId ||
    //   !!tdo.details.foiaXpressRequestId ||
    //   !!tdo.details.casepointRequestId;

    const { redactEngineId, defaultClusterId } =
      yield* select(selectConfigEngines);
    const user = yield* select(selectUser);
    const { objectTypeEffects, audioType, patchPreviousVersion } =
      yield* select(selectGlobalSettings);

    const {
      feather: featherEnabled,
      detectCards,
      detectNotepads,
    } = yield* select(selectFeatureFlags);

    const trimInterval = yield* select(selectTrimInterval);

    // Refresh primary asset ID because transcoding job may have changed it since last TDO load.
    yield* put(refreshTdoPrimaryAsset(tdo.id));
    const { payload } = yield* take(REFRESH_TDO_PRIMARY_ASSET_ID_SUCCESS);

    if (payload.temporalDataObject?.primaryAsset) {
      yield* put(redactingFile(tdo.id));
      const folderId =
        tdo.foldersTreeObjectIds && tdo.foldersTreeObjectIds.length > 0
          ? tdo.foldersTreeObjectIds[0]
          : null;
      const batchId = folderId ? `${folderId}_redaction` : null;

      const redactTasks = yield* select(selectAllRedactTask);
      const assetVersion = redactTasks ? redactTasks.length + 1 : 1;

      yield* put(
        // TODO Compiler: This payload type is very wrong.
        IN_REDACT_MEDIA({
          tdoId: tdo.id,
          tdoName: tdo.name,
          redactEngineID: redactEngineId,
          clusterId: defaultClusterId,
          batchId,
          primaryAssetId: payload.temporalDataObject.primaryAsset.id,
          email: user.email,
          featherEnabled,
          detectCards,
          detectNotepads,
          objectTypeEffects,
          patchPreviousVersion: patchPreviousVersion,
          assetVersion,
          audioType,
          auditLogs: true, // always create blur-audit-log (used to only happen for external requests)
          ...(trimInterval && {
            trimStartTime: trimInterval.startTimeMs / 1000, // ms -> s
            trimStopTime: trimInterval.stopTimeMs / 1000, // ms -> s
            // trimAssetType: 'redacted-media',
          }),
        })
      );
    }
  }
}

export function* watchRedactTDOToWorker() {
  yield* takeEvery(REDACT_TDO, redactTDOToWorker);
}

// update MDP after Redaction job completes
export function* watchPollRedactionJob() {
  while (true) {
    yield* take(OUT_FETCH_REDACT_TDO_SUCCESS);
    const job = yield* select(selectRedactionJob);

    yield* race({
      0: call(pollRedactionJobEngineResults, { jobId: job.id }),
      1: take(REDACTION_JOB_STATUS_ACTION_STOP_POLLING),
    });
  }
}

export const pollRedactionJobEngineResults = function* (payload: any) {
  while (true) {
    try {
      yield* call(loadRedactionJobStatus, payload);

      // check response with watermark
      const jobStatus = yield* select(selectRedactionJobStatus);

      const jobCreatedDateTime = yield* select(
        selectRedactionJobCreatedDateTime
      );

      let jobStuck = false;
      if (jobCreatedDateTime) {
        const delayMs = moment().diff(moment(jobCreatedDateTime));

        // if job status is pending for more than 3 hours something probably has gone wrong
        if (delayMs > 10800000 && jobStatus === 'pending') {
          jobStuck = true;
        }

        // if job status is running for more than 6 hours something probably has gone wrong
        if (delayMs > 21600000 && jobStatus === 'running') {
          jobStuck = true;
        }
      }

      if (
        ['complete', 'failed', 'cancelled', 'aborted'].includes(jobStatus) ||
        jobStuck
      ) {
        if (jobStatus === 'complete') {
          const tdoId = yield* select(selectCurrentTdoId);
          if (tdoId) {
            yield* put(REDACTED_FILE_COMPLETED({ tdoId }));
          }
        }

        yield* put({
          type: REDACTION_JOB_STATUS_ACTION_STOP_POLLING,
          payload: { jobStatus },
        });
        yield* cancel();
      }
    } catch (err) {
      console.error('Error polling:', err);
    }
    yield* delay(5000);
  }
};

export function* loadRedactionJobStatus(payload: { jobId: string }) {
  yield* put(fetchRedactionJobStatus(payload));
}

export function* watchRetrieveRedactionJobResults() {
  yield* takeLatest(
    REDACTION_JOB_STATUS_ACTION_STOP_POLLING,
    retrieveRedactionJobResults
  );
}

export function* retrieveRedactionJobResults(action: any) {
  if (!get(action, 'payload.noSubActions')) {
    yield* put(fetchRedactedMedia(action));
  }
}

/**
 * UPDATE 3/28/2022 this function is being disabled.
 * The task refresh these action trigger is unnecessary since tasks are already be refreshed continuously in the onFetchTDOSuccessHandle function.
 *
 * Watch for actions that indicate a change in Tasks.
 * Refresh tasks in TDO.
 */
/**
function* watchTdoTaskChanges() {
  const chan: TakeableChannel<Action<unknown>> = yield actionChannel(
    [
      OpticalTracking.CREATE_ENGINE_JOB_SUCCESS,
      OpticalTracking.STOP_POLLING_ENGINE_RESULTS,
      OpticalTracking.CANCEL_JOB_SUCCESS,
      OpticalTracking.PROCESS_ENGINE_REQUEST_SUCCESS,

      Transcription.CREATE_ENGINE_JOB_SUCCESS,
      Transcription.STOP_POLLING_ENGINE_RESULTS,
      Transcription.CANCEL_JOB_SUCCESS,
      Transcription.PROCESS_ENGINE_REQUEST_SUCCESS,

      CREATE_JOB_FACES_SUCCESS,
      // FACES_JOB_STATUS_SUCCESS,
      // FACES_JOB_STATUS_ACTION_STOP_POLLING,

      // POLL_DOWNLOAD_ASSET_SUCCESS,
      // OUT_FETCH_DOWNLOAD_ASSET_SUCCESS,

      OUT_POLL_DOWNLOAD_ASSET_SUCCESS,
      OUT_FETCH_REDACT_TDO_SUCCESS,
      REDACTION_JOB_STATUS_ACTION_STOP_POLLING,
    ],
    buffers.dropping(1)
  );
  yield takeEvery(chan, function* () {
    yield delay(2000); // Let the tasks update

    const tdoId = yield* select(selectCurrentTdoId);
    if (tdoId) {
      yield put(refreshTdoTasks(tdoId, 300));
    }
    yield delay(1000); // Slight debounce
  });
}
*/

function* loadSettingsFromDetails() {
  yield* takeEvery(
    [FETCH_TDO_SUCCESS, FETCH_UPDATE_SETTINGS_SUCCESS],
    loadSettingsFromDetailsHandle
  );
}

export function* loadSettingsFromDetailsHandle() {
  const isReady = yield* select(selectIsTdoReady);
  yield* emptyStatusHandler();
  // If ingestion is not done,
  if (!isReady) {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'procNotFinishedWait',
          defaultMessage:
            'This file has not finished processing. Wait for ingestion to complete.',
        }),
      })
    );
    yield* put(goToMainPage());
  } else {
    const globalSettings = yield* select(selectGlobalSettings);
    yield* put(IN_SET_GLOBAL_SETTINGS(globalSettings));
  }
}

// first page open
export const emptyStatusHandler = function* () {
  const tags = yield* select(selectTags);
  if (
    !tags.find(
      (tag) => tag?.value === IN_REDACTION_TAG_KEY && !!tag.redactionStatus
    )
  ) {
    yield* put(updateStatusOnDetails());
  }
};

export function* handleChangeUdrsPolyAssetGroupSeriesItem({
  payload,
}: Action<{
  id: string;
  seriesItem: UDRsPolyAssetGroupSeriesItem;
  changeType: 'start' | 'end' | 'stick';
}>) {
  const { seriesItem, changeType } = payload;

  if (changeType === 'start' || changeType === 'end') {
    yield* put(
      actionSeekVideo({
        startTimeMs:
          changeType === 'start'
            ? seriesItem.startTimeMs
            : seriesItem.stopTimeMs,
      })
    );
  }
}

function* onChangeUdrsPolyAssetGroupSeriesItemSubmit() {
  yield* takeEvery(
    CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM_SUBMIT,
    function* () {
      const localUDRAsset = yield* select(selectUdrAsset); // selectUdrAsset return UDRsPolyAsset

      yield* put(saveLocalUDRAssets(localUDRAsset));
    }
  );
}

function* onUpdateStartEndLiveTrackingUdr() {
  yield* takeEvery(
    UPDATE_START_END_UDRS_LIVE_TRACKING,
    function* ({ payload: { startPayload, endPayload } }) {
      yield* put(CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM(startPayload));
      yield* put(CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM_SUBMIT(startPayload));
      // Must wait until OUT_UDR_ASSET is called meaning UDR has been fully updated
      yield* take(OUT_UDR_ASSET);
      yield* put(CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM(endPayload));
      yield* put(CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM_SUBMIT(endPayload));
    }
  );
}

function* onChangeUdrsPolyAssetGroupSeriesItem() {
  yield* throttle(
    300,
    CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM,
    handleChangeUdrsPolyAssetGroupSeriesItem
  );
}

export function* handleUDRSelectedFromTimeline({
  payload,
}: Action<{
  groupId: string;
  id: string;
  seriesItem: UDRsPolyAssetGroupSeriesItem;
}>) {
  const { seriesItem } = payload;

  yield* put(actionSeekVideo({ startTimeMs: seriesItem.startTimeMs }));
}

function* onUDRSelectedFromTimeline() {
  yield* takeEvery(UDR_SELECTED_FROM_TIMELINE, handleUDRSelectedFromTimeline);
}

/* Removed because selected groups are already set in udrs.ts -> onNewOverlay() */
// function* onActionNewOverlay() {
//   yield* takeEvery(NEW_OVERLAY, function* ({ payload }) {
//     yield* put(actionSetSelectedGroups({ selected: { [payload.id]: true } }));
//   });
// }

function* onFetchTDOSuccess() {
  yield* takeEvery(FETCH_TDO_SUCCESS, onFetchTDOSuccessHandle);
}

function* onExitMediaDetails() {
  yield* takeEvery(EXIT_MEDIA_DETAILS, function* ({ payload }) {
    const { tdoId, userId } = payload;
    const { isLocked } = yield* select(selectTdoReadonly);
    const { tdoLock } = yield* select(selectFeatureFlags);

    const hasTDOChanged = yield* select(selectTdoIsChanged);
    if (hasTDOChanged) {
      yield* put(goToMainPage());
    } else {
      if (tdoLock && tdoId && userId && !isLocked) {
        yield* put(removeTdoLock(tdoId, userId));
      }
      yield* put(REDIRECT_TO_HOME());
    }
  });
}

export function* onFetchTDOSuccessHandle() {
  // legacy polling ran a full refresh of all tasks every 10 seconds
  // this is overkill and is an expensive query when there are many tasks on a TDO
  // we already poll any running jobs which is more efficient and used to trigger most updates
  // currently this refresh appear to only really be needed to trigger the pop-up task completion notifications

  // to reduce poll load use updated polling pattern
  // every 10 second refresh recent tasks (last 10 tasks)
  // every 1 minute load last 100 tasks in

  // in future we could possibly remove this polling or modulate refresh interval based on whether there are any active tasks

  const REFRESH_INTERVAL = 10_000; // polling interval refresh tasks
  const DEEP_REFRESH_FREQ = 6; // perform deeper task refresh every X number of refreshes
  const SHALLOW_REFRESH_TASK_LIMIT = 10;
  const DEEP_REFRESH_TASK_LIMIT = 100;

  let mod = 0;
  while (true) {
    const { go } = yield* race({
      go: delay(REFRESH_INTERVAL),
      _1: take(ROUTE_HOME),
      _2: take(ROUTE_MEDIA_DETAILS),
    });

    if (go) {
      mod = (mod + 1) % DEEP_REFRESH_FREQ;
      const tdoId = yield* select(selectCurrentTdoId);
      if (tdoId) {
        yield* put(
          refreshTdoTasks(
            tdoId,
            mod === 0 ? DEEP_REFRESH_TASK_LIMIT : SHALLOW_REFRESH_TASK_LIMIT
          )
        );
      }
    } else {
      break;
    }
  }
}

export function* onNewOverlay() {
  yield* takeEvery(NEW_OVERLAY, function* () {
    const filterParameters = yield* select(selectFilterParameters);
    if (filterParameters.show.udr) {
      return;
    }

    // TODO if is cbsa all need to toggle all
    // yield* put(actionFilterToggleAll({ filter: true, preventUndo: true }));

    // new - just activate the udr filter no longer is a drop down menu
    yield* put(
      actionFilterToggleShowType({
        filterType: 'udr',
        value: true,
        preventUndo: true,
      })
    );
  });
}

export function* sendRedactedFileToGovQa() {
  const intl = sagaIntl();
  yield* takeEvery(SEND_REDACTED_FILE_TO_GOVQA, function* ({ payload }) {
    yield* put(
      patchGovQAState({
        isSendingFile: true,
      })
    );

    let tdo = yield* select(selectTdo);
    if (!tdo) {
      console.error('TDO should have existed');
      return;
    }
    yield* put(fetchLatestTDOAuditLogAsset(tdo.id));

    const { fetchAuditLogAssetResponse, errFetchingAuditLogAsset } =
      yield* race({
        fetchAuditLogAssetResponse: take(
          FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS
        ),
        errFetchingAuditLogAsset: take(
          FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE
        ),
      });

    if (errFetchingAuditLogAsset) {
      yield* put(
        patchGovQAState({
          successMessage: '',
          isStatusDialogShown: true,
          errorMessage: intl.formatMessage({
            id: 'errorFetchingAuditLog',
            defaultMessage: 'Error fetching audit log',
          }),
        })
      );

      yield* put(
        patchGovQAState({
          isSendingFile: false,
        })
      );

      return;
    }

    const auditLog =
      fetchAuditLogAssetResponse?.payload?.temporalDataObject?.assets
        ?.records?.[0];

    // make sure latest tdo is fetched before sending
    yield* put(fetchTdo(tdo.id));

    const { errFetchingTDO } = yield* race({
      _: take(FETCH_TDO_SUCCESS),
      errFetchingTDO: take(FETCH_TDO_WITH_FAILURE),
    });

    if (errFetchingTDO) {
      yield* put(
        patchGovQAState({
          successMessage: '',
          isStatusDialogShown: true,
          errorMessage: intl.formatMessage({
            id: 'errorFetchingTDOState',
            defaultMessage: 'Error fetching TDO state',
          }),
        })
      );

      yield* put(
        patchGovQAState({
          isSendingFile: false,
        })
      );

      return;
    }

    yield* put(govQAAPI.fetchIntegrationInfo());

    const { ok, err } = yield* race({
      ok: take(govQAAPI.FETCH_INTEGRATION_INFO_SUCCESS),
      err: take(govQAAPI.FETCH_INTEGRATION_INFO_FAILURE),
    });

    if (err) {
      yield* put(
        patchGovQAState({
          successMessage: '',
          isStatusDialogShown: true,
          errorMessage: intl.formatMessage({
            id: 'errorFetchingGovQAIntegrationInfo',
            defaultMessage: 'Error fetching GovQA integration info',
          }),
        })
      );

      yield* put(
        patchGovQAState({
          isSendingFile: false,
        })
      );

      return;
    }

    const {
      BASEURL: baseUrl,
      AUTHKEY: authKey,
      ACTIVATIONKEY: activationKey,
      USERNAME: username,
      PASSWORD: password,
    } = ok?.payload?.me?.organization?.integrationConfig?.config || {};

    if (!baseUrl || !authKey || !activationKey || !username || !password) {
      yield* put(
        patchGovQAState({
          successMessage: '',
          isStatusDialogShown: true,
          errorMessage: intl.formatMessage({
            id: 'missingGovQAIntegrationConfig',
            defaultMessage: 'Missing GovQA integration config',
          }),
        })
      );

      yield* put(
        patchGovQAState({
          isSendingFile: false,
        })
      );

      return;
    }

    let sessionId: string | undefined;
    // payload ||= { username, password }; // eslint-disable-line no-param-reassign
    const auth_payload = {
      username: payload?.username || username,
      password: payload?.password || password,
    };
    if (auth_payload) {
      const { username: username_, password: password_ } = auth_payload;

      let authResponse;
      try {
        authResponse = yield* call(govQAAPI.auth, {
          login: username_,
          password: password_,
          baseUrl,
          authKey,
          activationKey,
        });
      } catch (errMsg) {
        yield* put(
          patchGovQAState({
            authErrorMessage: isString(errMsg)
              ? errMsg
              : intl.formatMessage({
                  id: 'errorUnknown',
                  defaultMessage: 'Unknown error',
                }),
            isFormShown: true,
          })
        );

        yield* put(
          patchGovQAState({
            isSendingFile: false,
          })
        );

        return;
      }

      sessionId = authResponse.current_session_id;

      yield* put(setGovQASessionId(sessionId));
    } else {
      sessionId = yield* select(selectGovQASessionId);

      if (!sessionId) {
        console.error(
          'sendRedactedFileToGovQa: sessionId must be set in the store if auth payload is not passed to saga'
        );

        yield* put(
          patchGovQAState({
            isSendingFile: false,
          })
        );

        return;
      }
    }

    tdo = yield* select(selectTdo);

    if (!tdo) {
      yield* put(
        patchGovQAState({
          isSendingFile: false,
        })
      );

      return;
    }

    const { govQARequestId: uniqueId } = tdo.details;

    if (!uniqueId) {
      console.error(
        'sendRedactedFileToGovQa: govQARequestId must be set in TDO details'
      );

      yield* put(
        patchGovQAState({
          isSendingFile: false,
        })
      );

      return;
    }

    const mediaFile = getLastRedactedFile(tdo);
    // add this if statement to make compiler happy, but actually if
    // this saga fires fileUrl must be set
    if (!mediaFile?.signedUri) {
      console.error('sendRedactedFileToGovQa: signedUri is not present on TDO');

      yield* put(
        patchGovQAState({
          isSendingFile: false,
        })
      );

      return;
    }

    try {
      yield* call(govQAAPI.sendFile, {
        sessionId: sessionId,
        uniqueId: uniqueId,
        mediaFile: mediaFile,
        auditLog: auditLog,
        activationKey: activationKey,
        baseUrl: baseUrl,
      });
    } catch (_errMsg) {
      yield* put(setGovQASessionId(null));
      // NOTE: if used cached session id, need to pop up authentication dialog
      if (!payload) {
        yield* put(
          patchGovQAState({
            isFormShown: true,
          })
        );
      } else {
        yield* put(
          patchGovQAState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorSendingFile',
              defaultMessage: 'Error sending file',
            }),
          })
        );
      }

      yield* put(
        patchGovQAState({
          isSendingFile: false,
        })
      );

      return;
    }

    yield* put(
      patchGovQAState({
        successMessage: 'Successfully sent file to GovQA.',
        isStatusDialogShown: true,
        errorMessage: '',
      })
    );

    yield* put(
      patchGovQAState({
        isSendingFile: false,
      })
    );
  });
}

export interface FetchIntegrationInfoResponse {
  me: {
    organization: {
      id: string;
      integrationConfig?: {
        config: Record<string, string>;
      };
    };
  };
}

export function* sendRedactedFileToExternalIntegration() {
  const intl = sagaIntl();
  yield* takeEvery(
    SEND_REDACTED_FILE_TO_EXTERNAL_INTEGRATION,
    function* ({ payload: _payload }) {
      yield* put(
        patchExternalIntegrationState({
          isSendingFile: true,
        })
      );

      let tdo = yield* select(selectTdo);
      if (!tdo) {
        console.error('TDO should have existed');
        return;
      }
      yield* put(fetchLatestTDOAuditLogAsset(tdo.id));

      const { fetchAuditLogAssetResponse, errFetchingAuditLogAsset } =
        yield* race({
          fetchAuditLogAssetResponse: take(
            FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS
          ),
          errFetchingAuditLogAsset: take(
            FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE
          ),
        });

      if (errFetchingAuditLogAsset) {
        yield* put(
          patchExternalIntegrationState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingAuditLog',
              defaultMessage: 'Error fetching audit log',
            }),
          })
        );

        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      const auditLog =
        fetchAuditLogAssetResponse?.payload?.temporalDataObject?.assets
          ?.records?.[0];

      // make sure latest tdo is fetched before sending
      yield* put(fetchTdo(tdo.id));

      const { errFetchingTDO } = yield* race({
        _: take(FETCH_TDO_SUCCESS),
        errFetchingTDO: take(FETCH_TDO_WITH_FAILURE),
      });

      if (errFetchingTDO) {
        yield* put(
          patchExternalIntegrationState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingTDOState',
              defaultMessage: 'Error fetching TDO state',
            }),
          })
        );

        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(externalIntegrationAPI.fetchIntegrationInfo());

      const { ok, err } = yield* race({
        ok: take(externalIntegrationAPI.FETCH_INTEGRATION_INFO_SUCCESS),
        err: take(externalIntegrationAPI.FETCH_INTEGRATION_INFO_FAILURE),
      });

      if (err) {
        yield* put(
          patchExternalIntegrationState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingExternalIntegrationInfo',
              defaultMessage: 'Error fetching external integration info',
            }),
          })
        );

        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      const integrationConfig =
        ok?.payload?.me?.organization?.integrationConfig?.config;

      if (!integrationConfig) {
        yield* put(
          patchExternalIntegrationState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'missingExternalIntegrationConfig',
              defaultMessage: 'Missing external integration config',
            }),
          })
        );

        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      tdo = yield* select(selectTdo);

      if (!tdo) {
        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      const mediaFile = getLastRedactedFile(tdo);
      if (!mediaFile?.signedUri) {
        console.error(
          'sendRedactedFileToExternalIntegration: signedUri is not present on TDO'
        );

        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      const tdoId = tdo.id;
      const tdoName = tdo.name;

      const caseMedia = yield* select(selectCaseMedia);

      if (!caseMedia) {
        console.error('Case media should have existed');

        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      const folderId = caseMedia.caseId;
      const folderName = caseMedia.caseName;

      const reviewStatus = yield* select(selectOriginalFileStatus);
      if (!reviewStatus) {
        console.error('Review status should have existed');

        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      const redactedWords = yield* select(selectWordsRedactedMap);
      if (!redactedWords) {
        console.error('Redacted words should have existed');

        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      const redactedTranscript = yield* select(selectTranscriptAssets);

      try {
        yield* call(externalIntegrationAPI.sendFile, {
          tdoId: tdoId,
          tdoName: tdoName,
          folderId: folderId,
          folderName: folderName,
          reviewStatus: reviewStatus,
          transcriptionURL: redactedTranscript?.records[0]?.signedUri || '',
          mediaFile: mediaFile,
          auditLog: auditLog,
          integrationConfig: integrationConfig,
        });
      } catch (_errMsg) {
        yield* put(
          patchExternalIntegrationState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorSendingFile',
              defaultMessage: 'Error sending file',
            }),
          })
        );

        yield* put(
          patchExternalIntegrationState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(
        patchExternalIntegrationState({
          successMessage: 'Successfully sent file to External Integration.',
          isStatusDialogShown: true,
          errorMessage: '',
        })
      );

      yield* put(
        patchExternalIntegrationState({
          isSendingFile: false,
        })
      );
    }
  );
}

export function* sendRedactedFileToFOIAXpress() {
  const intl = sagaIntl();
  yield* takeEvery(
    SEND_REDACTED_FILE_TO_FOIAXPRESS,
    function* ({ payload: _payload }) {
      yield* put(
        patchFOIAXpressState({
          isSendingFile: true,
        })
      );

      let tdo = yield* select(selectTdo);
      if (!tdo) {
        console.error('TDO should have existed');
        return;
      }
      yield* put(fetchLatestTDOAuditLogAsset(tdo.id));

      const { fetchAuditLogAssetResponse, errFetchingAuditLogAsset } =
        yield* race({
          fetchAuditLogAssetResponse: take(
            FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS
          ),
          errFetchingAuditLogAsset: take(
            FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE
          ),
        });

      if (errFetchingAuditLogAsset) {
        yield* put(
          patchFOIAXpressState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingAuditLog',
              defaultMessage: 'Error fetching audit log',
            }),
          })
        );

        yield* put(
          patchFOIAXpressState({
            isSendingFile: false,
          })
        );

        return;
      }

      const auditLog =
        fetchAuditLogAssetResponse?.payload?.temporalDataObject?.assets
          ?.records?.[0];

      // make sure latest tdo is fetched before sending
      yield* put(fetchTdo(tdo.id));

      const { errFetchingTDO } = yield* race({
        _: take(FETCH_TDO_SUCCESS),
        errFetchingTDO: take(FETCH_TDO_WITH_FAILURE),
      });

      if (errFetchingTDO) {
        yield* put(
          patchFOIAXpressState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingTDOState',
              defaultMessage: 'Error fetching TDO state',
            }),
          })
        );

        yield* put(
          patchFOIAXpressState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(foiaXpressAPI.fetchIntegrationInfo());

      const { ok, err } = yield* race({
        ok: take(foiaXpressAPI.FETCH_INTEGRATION_INFO_SUCCESS),
        err: take(foiaXpressAPI.FETCH_INTEGRATION_INFO_FAILURE),
      });

      if (err) {
        yield* put(
          patchFOIAXpressState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingFOIAXpressIntegrationInfo',
              defaultMessage: 'Error fetching FOIAXpress integration info',
            }),
          })
        );

        yield* put(
          patchFOIAXpressState({
            isSendingFile: false,
          })
        );

        return;
      }

      const { BASEURL: baseUrl } =
        ok?.payload?.me?.organization?.integrationConfig?.config || {};

      if (!baseUrl) {
        yield* put(
          patchFOIAXpressState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'missingFOIAXpressIntegrationConfig',
              defaultMessage: 'Missing FOIAXpress integration config',
            }),
          })
        );

        yield* put(
          patchFOIAXpressState({
            isSendingFile: false,
          })
        );

        return;
      }

      tdo = yield* select(selectTdo);

      if (!tdo) {
        yield* put(
          patchFOIAXpressState({
            isSendingFile: false,
          })
        );

        return;
      }

      const { foiaXpressRequestId: uniqueId } = tdo.details;

      if (!uniqueId) {
        console.error(
          'sendRedactedFileToFOIAXpress: foiaXpressRequestId must be set in TDO details'
        );

        yield* put(
          patchFOIAXpressState({
            isSendingFile: false,
          })
        );

        return;
      }

      const mediaFile = getLastRedactedFile(tdo);
      // add this if statement to make compiler happy, but actually if
      // this saga fires fileUrl must be set
      if (!mediaFile?.signedUri) {
        console.error(
          'sendRedactedFileToFOIAXpress: signedUri is not present on TDO'
        );

        yield* put(
          patchFOIAXpressState({
            isSendingFile: false,
          })
        );

        return;
      }

      try {
        yield* call(foiaXpressAPI.sendFile, {
          // sessionId,
          uniqueId,
          mediaFile,
          auditLog,
          // activationKey,
          baseUrl,
        });
      } catch (_errMsg) {
        // yield put(setFOIAXpressSessionId(null));
        // NOTE: if used cached session id, need to pop up authentication dialog
        // if (!payload) {
        //   yield put(
        //     patchFOIAXpressState({
        //       isFormShown: true,
        //     })
        //   );
        // } else {
        yield* put(
          patchFOIAXpressState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorSendingFile',
              defaultMessage: 'Error sending file',
            }),
          })
        );
        // }

        yield* put(
          patchFOIAXpressState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(
        patchFOIAXpressState({
          successMessage: 'Successfully sent file to FOIAXpress.',
          isStatusDialogShown: true,
          errorMessage: '',
        })
      );

      yield* put(
        patchFOIAXpressState({
          isSendingFile: false,
        })
      );
    }
  );
}

export function* sendRedactedFileToCasepoint() {
  const intl = sagaIntl();
  yield* takeEvery(
    SEND_REDACTED_FILE_TO_CASEPOINT,
    function* ({ payload: _payload }) {
      yield* put(
        patchCasepointState({
          isSendingFile: true,
        })
      );

      let tdo = yield* select(selectTdo);
      if (!tdo) {
        console.error('TDO should have existed');
        return;
      }
      yield* put(fetchLatestTDOAuditLogAsset(tdo.id));

      const { fetchAuditLogAssetResponse, errFetchingAuditLogAsset } =
        yield* race({
          fetchAuditLogAssetResponse: take(
            FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS
          ),
          errFetchingAuditLogAsset: take(
            FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE
          ),
        });

      if (errFetchingAuditLogAsset) {
        yield* put(
          patchCasepointState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingAuditLog',
              defaultMessage: 'Error fetching audit log',
            }),
          })
        );

        yield* put(
          patchCasepointState({
            isSendingFile: false,
          })
        );

        return;
      }

      const auditLog =
        fetchAuditLogAssetResponse?.payload?.temporalDataObject?.assets
          ?.records?.[0];

      // make sure latest tdo is fetch before sending
      yield* put(fetchTdo(tdo.id));

      const { errFetchingTDO } = yield* race({
        _: take(FETCH_TDO_SUCCESS),
        errFetchingTDO: take(FETCH_TDO_WITH_FAILURE),
      });

      if (errFetchingTDO) {
        yield* put(
          patchCasepointState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingTDOState',
              defaultMessage: 'Error fetching TDO state',
            }),
          })
        );

        yield* put(
          patchCasepointState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(casepointAPI.fetchIntegrationInfo());

      const { ok, err } = yield* race({
        ok: take(casepointAPI.FETCH_INTEGRATION_INFO_SUCCESS),
        err: take(casepointAPI.FETCH_INTEGRATION_INFO_FAILURE),
      });

      if (err) {
        yield* put(
          patchCasepointState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingCasepointIntegrationInfo',
              defaultMessage: 'Error fetching Casepoint integration info',
            }),
          })
        );

        yield* put(
          patchCasepointState({
            isSendingFile: false,
          })
        );

        return;
      }

      const { BASEURL: baseUrl } =
        ok?.payload?.me?.organization?.integrationConfig?.config || {};

      if (!baseUrl) {
        yield* put(
          patchCasepointState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'missingCasepointIntegrationConfig',
              defaultMessage: 'Missing Casepoint integration config',
            }),
          })
        );

        yield* put(
          patchCasepointState({
            isSendingFile: false,
          })
        );

        return;
      }

      tdo = yield* select(selectTdo);

      if (!tdo) {
        yield* put(
          patchCasepointState({
            isSendingFile: false,
          })
        );

        return;
      }

      const { casepointRequestId: uniqueId } = tdo.details;

      if (!uniqueId) {
        console.error(
          'sendRedactedFileToCasepoint: casepointRequestId must be set in TDO details'
        );

        yield* put(
          patchCasepointState({
            isSendingFile: false,
          })
        );

        return;
      }

      const mediaFile = getLastRedactedFile(tdo);
      // add this if statement to make compiler happy, but actually if
      // this saga fires fileUrl must be set
      if (!mediaFile?.signedUri) {
        console.error(
          'sendRedactedFileToCasepoint: signedUri is not present on TDO'
        );

        yield* put(
          patchCasepointState({
            isSendingFile: false,
          })
        );

        return;
      }

      try {
        yield* call(casepointAPI.sendFile, {
          // sessionId,
          uniqueId,
          mediaFile,
          auditLog,
          // activationKey,
          baseUrl,
        });
      } catch (_errMsg) {
        yield* put(
          patchCasepointState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorSendingFile',
              defaultMessage: 'Error sending file',
            }),
          })
        );
        // }

        yield* put(
          patchCasepointState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(
        patchCasepointState({
          successMessage: 'Successfully sent file to Casepoint.',
          isStatusDialogShown: true,
          errorMessage: '',
        })
      );

      yield* put(
        patchCasepointState({
          isSendingFile: false,
        })
      );
    }
  );
}

export function* sendRedactedFileToExterro() {
  const intl = sagaIntl();
  yield* takeEvery(
    SEND_REDACTED_FILE_TO_EXTERRO,
    function* ({ payload: _payload }) {
      yield* put(
        patchExterroState({
          isSendingFile: true,
        })
      );

      let tdo = yield* select(selectTdo);
      if (!tdo) {
        console.error('TDO should have existed');
        return;
      }
      yield* put(fetchLatestTDOAuditLogAsset(tdo.id));

      const { fetchAuditLogAssetResponse, errFetchingAuditLogAsset } =
        yield* race({
          fetchAuditLogAssetResponse: take(
            FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS
          ),
          errFetchingAuditLogAsset: take(
            FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE
          ),
        });

      if (errFetchingAuditLogAsset) {
        yield* put(
          patchExterroState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingAuditLog',
              defaultMessage: 'Error fetching audit log',
            }),
          })
        );

        yield* put(
          patchExterroState({
            isSendingFile: false,
          })
        );

        return;
      }

      const auditLog =
        fetchAuditLogAssetResponse?.payload?.temporalDataObject?.assets
          ?.records?.[0];

      // make sure latest tdo is fetch before sending
      yield* put(fetchTdo(tdo.id));

      const { errFetchingTDO } = yield* race({
        _: take(FETCH_TDO_SUCCESS),
        errFetchingTDO: take(FETCH_TDO_WITH_FAILURE),
      });

      if (errFetchingTDO) {
        yield* put(
          patchExterroState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingTDOState',
              defaultMessage: 'Error fetching TDO state',
            }),
          })
        );

        yield* put(
          patchExterroState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(exterroAPI.fetchIntegrationInfo());

      const { ok, err } = yield* race({
        ok: take(exterroAPI.FETCH_INTEGRATION_INFO_SUCCESS),
        err: take(exterroAPI.FETCH_INTEGRATION_INFO_FAILURE),
      });

      if (err) {
        yield* put(
          patchExterroState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingExterroIntegrationInfo',
              defaultMessage: 'Error fetching Exterro integration info',
            }),
          })
        );

        yield* put(
          patchExterroState({
            isSendingFile: false,
          })
        );

        return;
      }

      const { BASEURL: baseUrl, SIGNATURE: signature } =
        ok?.payload?.me?.organization?.integrationConfig?.config || {};

      if (!baseUrl || !signature) {
        yield* put(
          patchExterroState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'missingExterroIntegrationConfig',
              defaultMessage: 'Missing Exterro integration config',
            }),
          })
        );

        yield* put(
          patchExterroState({
            isSendingFile: false,
          })
        );

        return;
      }

      tdo = yield* select(selectTdo);

      if (!tdo) {
        yield* put(
          patchExterroState({
            isSendingFile: false,
          })
        );

        return;
      }

      const { exterroRequestId: uniqueId } = tdo.details;

      if (!uniqueId) {
        console.error(
          'sendRedactedFileToExterro: exterroRequestId must be set in TDO details'
        );

        yield* put(
          patchExterroState({
            isSendingFile: false,
          })
        );

        return;
      }

      const mediaFile = getLastRedactedFile(tdo);
      // add this if statement to make compiler happy, but actually if
      // this saga fires fileUrl must be set
      if (!mediaFile?.signedUri) {
        console.error(
          'sendRedactedFileToExterro: signedUri is not present on TDO'
        );

        yield* put(
          patchExterroState({
            isSendingFile: false,
          })
        );

        return;
      }

      try {
        yield* call(exterroAPI.sendFile, {
          // sessionId,
          uniqueId,
          mediaFile,
          auditLog,
          // activationKey,
          signature,
          baseUrl,
        });
      } catch (_errMsg) {
        yield* put(
          patchExterroState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorSendingFile',
              defaultMessage: 'Error sending file',
            }),
          })
        );
        // }

        yield* put(
          patchExterroState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(
        patchExterroState({
          successMessage: 'Successfully sent file to Exterro.',
          isStatusDialogShown: true,
          errorMessage: '',
        })
      );

      yield* put(
        patchExterroState({
          isSendingFile: false,
        })
      );
    }
  );
}

export function* sendRedactedFileToNuix() {
  const intl = sagaIntl();
  yield* takeEvery(
    SEND_REDACTED_FILE_TO_NUIX,
    function* ({ payload: _payload }) {
      yield* put(
        patchNuixState({
          isSendingFile: true,
        })
      );

      let tdo = yield* select(selectTdo);
      if (!tdo) {
        console.error('TDO should have existed');
        return;
      }
      // yield* put(fetchLatestTDOAuditLogAsset(tdo.id));

      // const { fetchAuditLogAssetResponse, errFetchingAuditLogAsset } =
      //   yield* race({
      //     fetchAuditLogAssetResponse: take(FETCH_LATEST_TDO_AUDIT_LOG_ASSET_SUCCESS),
      //     errFetchingAuditLogAsset: take(
      //       FETCH_LATEST_TDO_AUDIT_LOG_ASSET_FAILURE
      //     ),
      //   });

      // if (errFetchingAuditLogAsset) {
      //   yield* put(
      //     patchNuixState({
      //       successMessage: '',
      //       isStatusDialogShown: true,
      //       errorMessage:
      //         'Unexpected error fetching audit logs. Please try again.',
      //     })
      //   );

      //   yield* put(
      //     patchNuixState({
      //       isSendingFile: false,
      //     })
      //   );

      //   return;
      // }

      // const auditLog =
      //   fetchAuditLogAssetResponse?.payload?.temporalDataObject?.assets
      //     ?.records?.[0];

      // make sure latest tdo is fetch before sending
      yield* put(fetchTdo(tdo.id));

      const { errFetchingTDO } = yield* race({
        _: take(FETCH_TDO_SUCCESS),
        errFetchingTDO: take(FETCH_TDO_WITH_FAILURE),
      });

      if (errFetchingTDO) {
        yield* put(
          patchNuixState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingTDOState',
              defaultMessage: 'Error fetching TDO state',
            }),
          })
        );

        yield* put(
          patchNuixState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(nuixAPI.fetchIntegrationInfo());

      const { ok, err } = yield* race({
        ok: take(nuixAPI.FETCH_INTEGRATION_INFO_SUCCESS),
        err: take(nuixAPI.FETCH_INTEGRATION_INFO_FAILURE),
      });

      if (err) {
        yield* put(
          patchNuixState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorFetchingNuixIntegrationInfo',
              defaultMessage: 'Error fetching Nuix integration info',
            }),
          })
        );

        yield* put(
          patchNuixState({
            isSendingFile: false,
          })
        );

        return;
      }

      const { BASEURL: baseUrl, SIGNATURE: signature } =
        ok?.payload?.me?.organization?.integrationConfig?.config || {};

      if (!signature) {
        yield* put(
          patchNuixState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'missingNuixIntegrationConfig',
              defaultMessage: 'Missing Nuix integration config',
            }),
          })
        );

        yield* put(
          patchNuixState({
            isSendingFile: false,
          })
        );

        return;
      }

      tdo = yield* select(selectTdo);

      if (!tdo) {
        yield* put(
          patchNuixState({
            isSendingFile: false,
          })
        );

        return;
      }

      const { nuixRequestId: uniqueId } = tdo.details;

      if (!uniqueId) {
        console.error(
          'sendRedactedFileToNuix: nuixRequestId must be set in TDO details'
        );

        yield* put(
          patchNuixState({
            isSendingFile: false,
          })
        );

        return;
      }

      const mediaFile = getLastRedactedFile(tdo);
      // add this if statement to make compiler happy, but actually if
      // this saga fires fileUrl must be set
      if (!mediaFile?.signedUri) {
        console.error(
          'sendRedactedFileToNuix: signedUri is not present on TDO'
        );

        yield* put(
          patchNuixState({
            isSendingFile: false,
          })
        );

        return;
      }

      const url =
        baseUrl ||
        'https://ringtaildr.us.nuix.com/ringtail-svc-portal/api/query';
      try {
        yield* call(nuixAPI.sendFile, {
          uniqueId,
          mediaFile,
          signature,
          baseUrl: url,
        });
      } catch (_errMsg) {
        yield* put(
          patchNuixState({
            successMessage: '',
            isStatusDialogShown: true,
            errorMessage: intl.formatMessage({
              id: 'errorSendingFile',
              defaultMessage: 'Error sending file',
            }),
          })
        );
        // }

        yield* put(
          patchNuixState({
            isSendingFile: false,
          })
        );

        return;
      }

      yield* put(
        patchNuixState({
          successMessage: 'Successfully sent file to Nuix.',
          isStatusDialogShown: true,
          errorMessage: '',
        })
      );

      yield* put(
        patchNuixState({
          isSendingFile: false,
        })
      );
    }
  );
}

export function* watchOverlaySelectionChanges() {
  yield* takeEvery([UDR_SELECTED_FROM_TIMELINE, FACE_HIGHLIGHT], function* () {
    const selectedUDRGroupId = yield* select(selectSelectedUDRGroupId);

    // NOTE: saga fires after reducers so selectedUDRGroupId should be already updated
    if (!selectedUDRGroupId) {
      yield* put(setSelectedUdrGroupOnWebWorkerOnly());
    }
  });
}

export function* onCleanDeletedOverlaysCache() {
  yield* debounce(
    // NOTE: should have something to remove after this interval
    DELETE_OVERLAY_CACHE_INTERVAL + 5000,
    [
      DELETE_DETECTION_OVERLAY,
      DELETE_DETECTION_IN_FRAME,
      DELETE_DETECTION_SEGMENT,
      DELETE_DETECTION_GROUP,
      DELETE_UDR_OVERLAY,
      DELETE_UDR_IN_FRAME,
      DELETE_UDR_SEGMENT,
      DELETE_UDR_GROUP,
    ],
    function* () {
      let deletedOverlaysCache = yield* select(selectDeletedOverlaysCache);

      deletedOverlaysCache = cleanDeleteOverlayCache(deletedOverlaysCache);

      yield* put(actionSetDeletedOverlaysCache(deletedOverlaysCache));
    }
  );
}

export function* loadWaveformCsv() {
  const tdo = yield* select(selectTdo);

  const waveformAsset = tdo?.waveformAssets?.records?.[0];
  if (waveformAsset) {
    const { signedUri } = waveformAsset;
    yield* put(fetchWaveformCsv(signedUri));
  }
}

export function* watchForWaveformSuccess() {
  yield* takeEvery(FETCH_WAVEFORM_SUCCESS, function* ({ payload: waveforms }) {
    yield* put(setAudiowaves(waveforms));
  });
}

export function* watchForWaveformFailure() {
  yield* takeEvery(FETCH_WAVEFORM_FAILURE, function* () {});
}

function* watchSendToReloadResults() {
  yield* takeEvery(LOAD_ENGINE_RESULTS_AFTER_DETECT, function* () {
    const tdoId = yield* select(selectCurrentTdoId);
    const { detectionEngineId, detectionCategory } =
      yield* select(selectConfigEngines);
    const mediaDuration = yield* select(selectMediaDuration);
    if (tdoId) {
      yield* put(
        IN_LOAD_DETECTION_DATA({
          tdoId,
          engines: [detectionEngineId],
          mediaDuration,
          categoryId: detectionCategory,
        })
      );
    }
  });
}

function* watchVideoHasLoaded() {
  yield* takeLatest(VIDEO_HAS_LOADED, function* () {
    // TODO: this is a workaround to set video react hasStarted to true
    // This is the replacement of player.hasStarted = true at line
    // https://github.com/veritone/redact-app/pull/2762/files#diff-0682378934ad17c6b2d725411dca9def66a798875f1f23b36d6b3c802e216795L89
    // We can not add dispatch(actionSetHasStartedPlayer(true)) to MediaPlayerView.tsx (infinite loop), or
    // add dispatch(actionSetHasStartedPlayer(true)) to useEffect (overwritten by action video-react/LOAD_START).
    // Alternative: remove hasStarted from redux store, and add videoReactHasStarted as property to those components need it, and
    // has videoReactHasStarted react state in MediaPlayerView.tsx and pass it to those components.
    yield* put(actionSetHasStartedPlayer(true));
  });
}

export function* watchRotateVideo() {
  yield* takeLatest(ROTATE_VIDEO, rotateVideoHandle);
}

export function* watchRotateVideoSuccess() {
  yield* takeLatest(CREATE_ROTATE_VIDEO_JOB_SUCCESS, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'rotateVideoSuccess',
          defaultMessage: 'Processing Video Rotation.',
        }),
        variant: 'success',
      })
    );

    yield* put(redirect(REDIRECT_TO_HOME()));
  });
}

export function* watchRotateVideoFailure() {
  yield* takeLatest(CREATE_ROTATE_VIDEO_JOB_FAILURE, function* () {
    const intl = sagaIntl();
    yield* put(
      enqueueSnackbar({
        message: intl.formatMessage({
          id: 'rotateVideoFailure',
          defaultMessage: 'Unexpected error occurred while rotating video.',
        }),
        variant: 'error',
      })
    );

    yield* put(redirect(REDIRECT_TO_HOME()));
  });
}

export function* rotateVideoHandle({
  payload: rotationDegrees,
}: {
  payload: number;
}) {
  const tdo: DetailTDO | null = yield* select(selectTdo);

  if (tdo) {
    const detectObjects: {
      headObjects: boolean;
      personObjects: boolean;
    } = yield* select(selectDetectObjects);

    const { redactions: audioRedactions } = yield* select(
      selectTranscriptionView
    );

    // Remove existing UDRs
    yield* put(OUT_UDR_CLUSTER_GROUPS({}));
    yield* put(OUT_UDR_COLLECTION());
    yield* put(OUT_UDR_ASSET({ boundingPolys: {} }));

    // Remove existing detections
    yield* put(OUT_DETECTION_CLUSTER_GROUPS({}));
    yield* put(OUT_BOUNDING_POLY_COLLECTION({}));

    // Remove selected polys
    yield* put(OUT_SELECTED_BOUNDINGPOLY_GROUP({ selected: {} }));
    yield* put(actionSetSelectedGroups({ selected: {} }));

    // Remove clusters map
    yield* put(OUT_CLUSTER_MAP({}));

    // Discard changes because UDR/head detections is deleted
    // Make sure user can auto-exit TDO after successful rotate job creation
    yield* put(DISCARD_CHANGES());

    yield* put(
      saveMediaData({
        tdoId: tdo.id,
        selectedPolyGroups: {},
        boundingPolys: {},
        clusterMap: {},
        audioRedactions,
      })
    );

    yield* put(deleteAllHeadDetectionResults());

    // update tdo status to recording
    yield* put(updateTDOStatus('recording'));

    yield* put(rotateVideo(tdo.id, rotationDegrees, detectObjects));
  }
}

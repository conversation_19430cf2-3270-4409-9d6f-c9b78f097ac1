import {
  fork,
  all,
  call,
  put,
  take,
  takeEvery,
  select,
  delay,
} from 'typed-redux-saga/macro';
import { isArray, noop } from 'lodash';
import { modules } from '@veritone/glc-redux';
import { fetchGraphQLApi } from '@helpers/callApi';
import { actionTryPermission } from '@user-permissions';
import { enqueueSnackbar } from '@common-modules/snackbar';
import { CaseId } from '@common-modules/universal/models/Brands';
import uploadFilesChannel, {
  UploadDescriptor,
} from '@common/shared/uploadFilesChannel';
import { actionCloseFilePicker } from '@redact-modules/mainPage/actions';
import {
  UPLOAD_REQUEST,
  uploadProgress,
  uploadComplete,
  endPick,
} from './index';
import { ArrayOrSingle } from 'ts-essentials';
import { v4 as uuidV4 } from 'uuid';

const { auth: authModule, config: configModule } = modules;

// interface Action<P, M = unknown> {
//   type: string;
//   payload: P;
//   meta: M;
// }

export interface UploadResult {
  key: string;
  bucket: string;
  expiresInSeconds: number;
  fileName: string;
  size: number;
  type: string;
  error: any;
  unsignedUrl: string | null;
  getUrl: string | null;
}

export function* finishUpload({
  id,
  govQARequestId,
  foiaXpressRequestId,
  casepointRequestId,
  nuixRequestId,
  exterroRequestId,
  result,
  status: { warning, error },
  callback,
  caseId,
  settingsProfileId,
}: {
  id: string;
  govQARequestId?: string;
  foiaXpressRequestId?: string;
  casepointRequestId?: string;
  nuixRequestId?: string;
  exterroRequestId?: string;
  result: UploadResult[] | null;
  status: {
    warning?: any;
    error?: any;
  };
  callback: () => void;
  caseId?: CaseId;
  settingsProfileId?: string;
}) {
  yield* put(
    uploadComplete(
      id,
      result || [],
      // TODO: need to type these better
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      { warning, error },
      {
        govQARequestId,
        foiaXpressRequestId,
        casepointRequestId,
        nuixRequestId,
        exterroRequestId,
      },
      caseId,
      settingsProfileId
    )
  );
  // fixme -- handle this better
  yield* delay(warning || error ? 1500 : 500);
  yield* put(endPick(id));
  // Need to type warning better
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  yield* call(callback, result, { warning, error, cancelled: false });
}

export function* uploadFileSaga({
  id,
  govQARequestId,
  foiaXpressRequestId,
  casepointRequestId,
  nuixRequestId,
  exterroRequestId,
  fileOrFiles,
  callback = noop,
  caseId,
  settingsProfileId,
}: {
  id: string;
  govQARequestId?: string;
  foiaXpressRequestId?: string;
  casepointRequestId?: string;
  nuixRequestId?: string;
  exterroRequestId?: string;
  fileOrFiles: ArrayOrSingle<File>;
  callback: () => void;
  caseId?: CaseId;
  settingsProfileId?: string;
}) {
  const files = isArray(fileOrFiles) ? fileOrFiles : [fileOrFiles];
  const getUrlQuery = `query urls($name: String!) {
        getSignedWritableUrl(key: $name, expiresInSeconds: 43200) {
          url
          key
          bucket
          expiresInSeconds
          getUrl
          unsignedUrl
        }
      }`;
  interface getUrlResponse {
    getSignedWritableUrl: {
      url: string;
      key: string;
      bucket: string;
      expiresInSeconds: number;
      getUrl: string;
      unsignedUrl: string;
    };
  }

  const config = yield* select(configModule.getConfig);
  // config correct type is Window['config'] but returns Veritone BaseConfigState
  const { apiRoot, graphQLEndpoint, veritoneAppId } = config;
  const graphQLUrl = `${apiRoot}/${graphQLEndpoint}`;
  const sessionToken = yield* select(authModule.selectSessionToken);
  const oauthToken = yield* select(authModule.selectOAuthToken);

  // get a signed URL for each object to be uploaded
  let signedWritableUrlResponses;
  try {
    signedWritableUrlResponses = yield* all(
      files.map(({ name }) =>
        call(fetchGraphQLApi<getUrlResponse>, {
          endpoint: graphQLUrl,
          query: getUrlQuery,
          variables: { name: `${name}-${uuidV4()}` },
          operationName: null,
          token: sessionToken || oauthToken,
          veritoneAppId,
        })
      )
    );
  } catch (error) {
    return yield* finishUpload({
      id,
      govQARequestId,
      foiaXpressRequestId,
      casepointRequestId,
      nuixRequestId,
      exterroRequestId,
      result: null,
      status: { error },
      callback: function () {},
      caseId,
      settingsProfileId,
    });
  }

  let uploadDescriptors: (UploadDescriptor | undefined)[] = [];
  try {
    uploadDescriptors = signedWritableUrlResponses.map(
      ({
        data: { getSignedWritableUrl } = { getSignedWritableUrl: undefined },
        errors,
      }) => {
        if (errors?.length) {
          throw new Error(
            `Call to getSignedWritableUrl returned error: ${errors[0].message}`
          );
        }

        return getSignedWritableUrl;
      }
    );
  } catch (error) {
    return yield* finishUpload({
      id,
      govQARequestId,
      foiaXpressRequestId,
      casepointRequestId,
      nuixRequestId,
      exterroRequestId,
      result: null,
      status: { error },
      callback,
      caseId,
      settingsProfileId,
    });
  }

  let resultChan;
  try {
    resultChan = yield* call(
      uploadFilesChannel,
      uploadDescriptors.filter((d) => d !== undefined),
      files
    );
  } catch (error) {
    return yield* finishUpload({
      id,
      govQARequestId,
      foiaXpressRequestId,
      casepointRequestId,
      nuixRequestId,
      exterroRequestId,
      result: null,
      status: { error },
      callback,
      caseId,
      settingsProfileId,
    });
  }

  const result: UploadResult[] = [];

  // refreshToken using sdk call
  window.aiware?.auth?.reportAppActivity?.();

  while (result.length !== files.length) {
    const {
      progress = 0,
      error,
      success,
      file,
      descriptor: { key, bucket, expiresInSeconds, getUrl, unsignedUrl, type },
    } = yield* take(resultChan);

    if (success || error) {
      yield* put(uploadProgress(id, key, 100));

      result.push({
        key,
        bucket,
        expiresInSeconds,
        fileName: file.name,
        size: file.size,
        type: type || file.type,
        error: error || false,
        unsignedUrl: error ? null : unsignedUrl,
        getUrl: error ? null : getUrl,
      });
      continue;
    }

    yield* put(uploadProgress(id, key, progress));
    // refreshToken using sdk call
    window.aiware?.auth?.reportAppActivity?.();
  }

  const isError = result.every((e) => e.error);
  const isWarning = !isError && result.some((e) => e.error);

  yield* finishUpload({
    id,
    govQARequestId,
    foiaXpressRequestId,
    casepointRequestId,
    nuixRequestId,
    exterroRequestId,
    result,
    status: {
      warning: isWarning ? 'Some files failed to upload.' : false,
      error: isError ? 'All files failed to upload.' : false,
    },
    callback,
    caseId,
    settingsProfileId,
  });
}

function* watchUploadRequest() {
  yield* takeEvery(
    UPLOAD_REQUEST,
    function* ({
      payload: {
        id,
        files: payloadFiles,
        callback,
        govQARequestId,
        foiaXpressRequestId,
        casepointRequestId,
        nuixRequestId,
        exterroRequestId,
        caseId,
        settingsProfileId,
      },
    }) {
      const files = validateFileTypes(payloadFiles);

      const MAX_FILE_SIZE = 104_857_600;
      const fileIsArray = Array.isArray(files);
      /* Check if there are non-empty files to upload */
      if (
        (fileIsArray && files.every((f) => f.size === 0)) ||
        (!fileIsArray && files.size === 0)
      ) {
        yield* put(actionCloseFilePicker());
        yield* put(
          enqueueSnackbar({
            message:
              'WARNING: Cannot upload files that are 0 bytes! Please ensure you are not dragging and dropping from a zip file preview! Files must be extracted/unzipped prior to uploading.',
            variant: 'error',
          })
        );
      } else {
        if (fileIsArray) {
          let emptyFiles = false;
          files.reduceRight<File[]>((_, f, i, a) => {
            if (f.size === 0) {
              emptyFiles = true;
              a.splice(i, 1);
            }
            return a;
          }, []);

          if (emptyFiles) {
            yield* put(
              enqueueSnackbar({
                message:
                  'WARNING: Cannot upload files that are 0 bytes! Please ensure you are not dragging and dropping from a zip file preview! Files must be extracted/unzipped prior to uploading.',
                variant: 'warning',
              })
            );
          }
        }

        const args = {
          id,
          govQARequestId,
          foiaXpressRequestId,
          casepointRequestId,
          nuixRequestId,
          exterroRequestId,
          fileOrFiles: files,
          callback,
          caseId,
          settingsProfileId,
        };

        /* Upload non-empty files */
        yield* put(
          actionTryPermission({
            permissionKey: 'allowNoUploadSizeLimit',
            onAllow: function* () {
              yield* call(uploadFileSaga, {
                id,
                govQARequestId,
                foiaXpressRequestId,
                casepointRequestId,
                nuixRequestId,
                exterroRequestId,
                fileOrFiles: files,
                callback,
                caseId,
                settingsProfileId,
              });
            },
            onBlock: function* () {
              if (
                (fileIsArray && files.every((f) => f.size < MAX_FILE_SIZE)) ||
                (!fileIsArray && files.size < MAX_FILE_SIZE)
              ) {
                yield* call(uploadFileSaga, args);
              } else {
                yield* put(actionCloseFilePicker());
                yield* put(
                  enqueueSnackbar({
                    message:
                      'Your file size must be below 100MB. Please try a smaller file.',
                    variant: 'error',
                  })
                );
              }
            },
          })
        );
      }
    }
  );
}

/* Fixes a bug where macOS and Windows cannot identify certain file types */
const validateFileTypes = (files: ArrayOrSingle<File>) => {
  const fixMissingTypes = (file: File) => {
    /* Check if file has extension .vob */
    if (file.name.match(/\.[0-9a-z]+$/i)?.[0]?.toLowerCase() === '.vob') {
      return new File([file], file.name, { type: 'video/mpeg' });
    } else if (!file.type) {
      return new File([file], file.name, { type: 'binary/octet-stream' });
    }
    return file;
  };

  if (!isArray(files)) {
    return fixMissingTypes(files);
  } else {
    files.forEach((file, i, fs) => (fs[i] = fixMissingTypes(file)));
    return files;
  }
};

export default function* root() {
  yield* all([fork(watchUploadRequest)]);
}

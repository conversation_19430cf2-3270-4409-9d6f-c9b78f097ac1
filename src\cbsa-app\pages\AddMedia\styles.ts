import makeStyles from '@mui/styles/makeStyles';

const topOffset = '115px';

export const useStyles = makeStyles((theme) => ({
  topBar: {
    backgroundColor: theme.palette.primary.light,
    boxShadow: theme.shadows[4],
    border: `1px solid ${theme.palette.divider}`,
    height: '60px',
    width: '100%',

    '& .Back': {
      alignItems: 'center',
      display: 'flex',
      height: '100%',
      marginLeft: '30px',

      '& .Link': {
        display: 'flex',
        alignItems: 'center',

        '& .Text': {
          color: theme.palette.primary.main,
          fontSize: '20px',
          fontWeight: 'bold',
        },

        '&:hover': {
          textDecoration: 'none',
        },
      },
    },
  },

  content: {
    background: 'white',
    display: 'flex',
    flex: 1,
    position: 'relative',
    minWidth: '960px',
    height: `calc(100vh - ${topOffset})`,

    '& .Grid': {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      gap: '30px',
      padding: '30px',
      height: '100%',
      overflow: 'scroll',

      '& .Dropzone': {
        minHeight: '250px',
        height: '250px',
      },

      '& .MediaLibraries': {
        display: 'flex',
        flex: 1,
        gap: '30px',
        overflow: 'hidden',
        minHeight: '250px',
      },
    },
  },

  primaryColor: {
    color: theme.palette.primary.main,
  },
}));

import { clamp, get } from 'lodash';
import { getOffset } from '@helpers/mouseUtils';
import { memo, useEffect, useMemo, useRef, useState, MouseEvent } from 'react';
import {
  animationFrameScheduler,
  BehaviorSubject,
  combineLatest,
  fromEvent,
  interval,
  Subject,
} from 'rxjs';
import {
  distinctUntilChanged,
  map,
  mergeAll,
  sample,
  startWith,
  switchMap,
  takeUntil,
  tap,
  windowToggle,
  withLatestFrom,
} from 'rxjs/operators';

import { OFFSET_LEFT_X } from '../Playhead/renderer/constants';
import { aToB } from '../Playhead/renderer/utils';
import { PositionBarPropTypes } from './PositionBarPropTypes';
import PositionBarView from './PositionBarView';

const PositionBar = ({
  currentTime,
  startWindowMs,
  stopWindowMs,
  minWindowMs,
  maxWindowMs,
  onSeekMedia,
}: PositionBarPropTypes) => {
  const [barTime, setBarTime] = useState(currentTime);
  const ref = useRef<HTMLDivElement>(null);
  const { onCurrentTime, onStartWindow, onStopWindow, destroy } =
    useMemo(() => {
      const timelineRowItem = document
        .getElementsByClassName('timeline-row')
        .item(0);
      let timelineRowWidth = 0;
      if (timelineRowItem) {
        timelineRowWidth = timelineRowItem.clientWidth;
      }
      const div = ref.current;
      // Setup Subjects
      const onTime = new BehaviorSubject(currentTime);
      const onStartWindowMs = new BehaviorSubject(startWindowMs);
      const onStopWindowMs = new BehaviorSubject(stopWindowMs);
      const onDestroy = new Subject<boolean>();

      if (div) {
        // Setup Observables
        const currentTime$ = onTime.pipe(
          distinctUntilChanged(),
          map((ms) => clamp(ms, minWindowMs, maxWindowMs))
        );
        const startWindowMs$ = onStartWindowMs.pipe(distinctUntilChanged());
        const stopWindowMs$ = onStopWindowMs.pipe(distinctUntilChanged());
        const mouseDown$ = fromEvent<MouseEvent>(div, 'mousedown');
        const mouseMove$ = fromEvent<MouseEvent>(document, 'mousemove');
        const mouseUp$ = fromEvent<MouseEvent>(document, 'mouseup');
        const raf$ = interval(undefined, animationFrameScheduler);
        const onResize$ = fromEvent(window, 'resize').pipe(
          map(() => get(div, 'parentElement.clientWidth') || 0),
          distinctUntilChanged(),
          startWith(0)
        );

        const fns$ = combineLatest(
          [startWindowMs$, stopWindowMs$, onResize$],
          (startMs, stopMs, _resize) => ({
            startMs,
            stopMs,
            pxWidth: get(div, 'parentElement.clientWidth') || 0,
          })
        ).pipe(
          map(({ startMs, stopMs, pxWidth }) => {
            if (timelineRowItem) {
              timelineRowWidth = timelineRowItem.clientWidth;
            }
            const mult = (pxWidth - OFFSET_LEFT_X) / (stopMs - startMs);
            const m2p = aToB(startMs, stopMs, OFFSET_LEFT_X, pxWidth);
            return {
              p2m: aToB(OFFSET_LEFT_X, pxWidth, startMs, stopMs),
              m2p: (ms: number) => m2p(ms) - mult * (startMs - minWindowMs),
            };
          })
        );
        const barPosition$ = mouseDown$.pipe(
          switchMap(() =>
            mouseMove$.pipe(
              takeUntil(
                mouseUp$.pipe(
                  withLatestFrom(fns$, (event, { p2m }) =>
                    p2m(getOffset(event).x - OFFSET_LEFT_X)
                  ),
                  tap((ms) => {
                    onTime.next(ms);
                    onSeekMedia(ms);
                  })
                )
              ),
              map((event) => {
                const el = event.target as HTMLElement;
                return el.clientWidth === timelineRowWidth
                  ? getOffset(event).x
                  : getOffset(event).x - OFFSET_LEFT_X;
              })
            )
          )
        );

        combineLatest([barPosition$, fns$], (px, { p2m }) => p2m(px))
          .pipe(
            takeUntil(onDestroy),
            windowToggle(mouseDown$, () => mouseUp$),
            mergeAll()
          )
          .subscribe(onTime);

        combineLatest([currentTime$, fns$], (ms, { m2p }) => ({
          ms,
          px: m2p(ms) | 0,
        }))
          .pipe(takeUntil(onDestroy), sample(raf$))
          .subscribe(({ ms, px }) => {
            const parentWidth = get(div, 'parentElement.clientWidth') || 0;
            setBarTime(ms);
            if (px < 150 || px > parentWidth) {
              div.style.display = 'none';
            } else {
              div.style.display = 'block';
              div.style.transform = `translateX(${px}px)`;
            }
          });
      }

      return {
        onCurrentTime: (v: number) => onTime.next(v),
        onStartWindow: (v: number) => onStartWindowMs.next(v),
        onStopWindow: (v: number) => onStopWindowMs.next(v),
        destroy: () => onDestroy.next(true),
      };
    }, [
      currentTime,
      maxWindowMs,
      minWindowMs,
      onSeekMedia,
      startWindowMs,
      stopWindowMs,
    ]);

  useEffect(() => () => destroy(), [destroy]);

  useEffect(() => {
    onCurrentTime(currentTime);
    onStartWindow(startWindowMs);
    onStopWindow(stopWindowMs);
  }, [
    currentTime,
    onCurrentTime,
    onStartWindow,
    onStopWindow,
    startWindowMs,
    stopWindowMs,
  ]);

  return <PositionBarView currentTime={barTime} ref={ref} />;
};

export default memo(PositionBar);

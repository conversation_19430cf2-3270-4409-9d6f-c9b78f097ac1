import {
  Given,
  When,
  Then,
  Before,
} from '@badeball/cypress-cucumber-preprocessor';
import '../common/mediaPlayerCommonStep';

Before(() => {
  cy.LoginLandingPage();
});

Given('I have unselected all filters', () => {
  cy.get('[data-testid="filter-button"]').last().as('filter').click();
  cy.get('.MuiFormGroup-root > .MuiGrid2-container').within(() => {
    cy.get('[type="checkbox"]').first().as('allCheck');
    cy.get('@allCheck').uncheck();
    cy.get('@allCheck').should('not.be.checked');
  });
});

Given('each detection name should not be empty', () => {
  cy.get('[data-testid="results-tab-object"]').each(($resultObject) => {
    cy.wrap($resultObject)
      .find('[data-test="detection-name"]')
      .should('not.be.empty');
  });
});

Given('no detection should be visible', () => {
  cy.get('[data-test="detection-name"]').should('not.exist');
});

When('I filter the video tab by {string}', (type: string) => {
  cy.get('[data-testid="filter-button"]').eq(1).click({ force: true });
  cy.get('.MuiFormGroup-root > .MuiGrid2-container').within(() => {
    cy.contains(type).click();
  });
});

Then('every result detection should contain {string}', (type: string) => {
  cy.get('[data-testid="results-tab-object"]').each(($el) => {
    const detectionName = $el.children().find('[data-test="detection-name"]');
    expect(detectionName).to.contain(type.toUpperCase());
  });
});

When('I click the audio tab button', () => {
  cy.get('[data-testid="audio-tab-button"]').click({ force: true });
});

Then('I should see {string} in file details transcript', (text: string) => {
  cy.get('[data-testid="file-details-trans"]').contains(text);
});

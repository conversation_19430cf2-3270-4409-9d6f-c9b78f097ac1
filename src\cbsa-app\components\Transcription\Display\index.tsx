import { TranscriptionViewableWords } from '@common-modules/mediaDetails/models';
import { useMediaPlayer } from '@common-state/hooks/useMediaPlayer';

import { TranscriptionPropTypes } from '../TranscriptionPropTypes';
import DisplayView from './DisplayView';

const Display = ({
  transcription,
  currentWord,
  redactedWords,
  selectedWords,
  searchResults,
  searchFocus,
  selectedRow,

  onTranscriptionLinesReady,
}: DisplayPropTypes) => {
  const currentTime = useMediaPlayer();
  const currWord = currentWord(currentTime);

  return (
    <DisplayView
      transcription={transcription}
      focusRowIdx={selectedRow}
      redactedWords={redactedWords}
      selectedWords={selectedWords}
      searchResults={searchResults}
      currWord={currWord}
      searchFocus={searchFocus}
      onTranscriptionLinesReady={onTranscriptionLinesReady}
    />
  );
};

export default Display;

export interface DisplayPropTypes
  extends Pick<
    TranscriptionPropTypes,
    'transcription' | 'currentWord' | 'redactedWords' | 'selectedWords'
  > {
  readonly searchResults: ReadonlyArray<
    Record<string, TranscriptionViewableWords>
  >;
  readonly searchFocus: number;
  readonly selectedRow?: number;
  onTranscriptionLinesReady: (
    transcriptionLines: TranscriptionViewableWords[][]
  ) => void;
}

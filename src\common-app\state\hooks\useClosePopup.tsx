import { useEffect } from 'react';

type HandleClose = () => void;

interface Props {
  handleCloses: HandleClose[];
}

// this hook is used to close the popup when the user exits fullscreen mode
const useClosePopup = ({ handleCloses }: Props) => {
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement) {
        handleCloses.forEach((handleClose) => {
          handleClose();
        });
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

export default useClosePopup;

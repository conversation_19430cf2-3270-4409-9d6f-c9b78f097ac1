import { TimelinesPropTypes } from '../TimelinesPropTypes';

export interface TranscriptionPropTypes
  extends Pick<
    TimelinesPropTypes,
    | 'audiowaves'
    | 'mediaDuration'
    | 'startWindowMs'
    | 'stopWindowMs'
    | 'onUnredactSlice'
    | 'onRedactSlice'
    | 'onSelectSlice'
    | 'onDeselectAll'
    | 'transcriptionList'
  > {
  readonly list: TimelinesPropTypes['transcriptRedactions'];
  readonly selected: TimelinesPropTypes['selectedTimeSlices'];
}

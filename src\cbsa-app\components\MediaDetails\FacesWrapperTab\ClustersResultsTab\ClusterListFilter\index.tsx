import Box from '@mui/material/Box';
import { memo, useState } from 'react';
import { useIntl } from 'react-intl';
import { v4 as uuidv4 } from 'uuid';

import {
  FILTER_PARAMETER_TYPE,
  FILTER_PARAMETER_TYPES,
} from '@helpers/constants';
import { useDispatch } from 'react-redux';
import PropTypes from '../PropTypes';
import { useStyles } from '../styles';

import {
  actionFilterToggleAll,
  actionFilterToggleShowType,
} from '@common-modules/mediaDetails';

const FILTER_LABELS: Record<FILTER_PARAMETER_TYPE | 'all', string> = {
  all: 'all',
  head: 'head',
  poim: 'poi',
  udr: 'udr',
  laptop: 'laptop',
  notepad: 'notepad',
  card: 'card',
  licensePlate: 'licensePlate',
  // manualInterp: 'manual',
  person: 'person',
};

const ClusterListFilter = ({
  filterParameters,
}: ClusterListFilterPropTypes) => {
  const classes = useStyles();
  const [group_uuid] = useState(uuidv4());
  const dispatch = useDispatch();
  const intl = useIntl();

  const handleShowFilterChange =
    (filterType: FILTER_PARAMETER_TYPE | 'all') => () => {
      if (filterType === 'all') {
        dispatch(actionFilterToggleAll({ filter: true }));
      } else {
        dispatch(
          actionFilterToggleShowType({
            filterType,
            value: true,
            isDropDown: true,
          })
        );
      }
    };

  return (
    <Box
      style={{ minHeight: 60, color: '#999' }}
      display="flex"
      justifyContent="flex-start"
      alignItems="center"
    >
      {/* <span>Filter by: </span> */}
      {FILTER_PARAMETER_TYPES.map((filterType, i) => (
        <div
          style={
            filterParameters.show[filterType]
              ? { color: 'white', margin: '0 5px', cursor: 'pointer' }
              : { margin: '0 5px', cursor: 'pointer' }
          }
          onClick={handleShowFilterChange(filterType)}
          className={classes.filterItem}
          // TODO: Fix or explain why necessary - possibly use filterType rather than i?
          // eslint-disable-next-line react/no-array-index-key
          key={`redact_clusterListFilter_div_${group_uuid}_${i}`}
        >
          {intl.formatMessage({
            id: FILTER_LABELS[filterType],
          })}
        </div>
      ))}
    </Box>
  );
};

export default memo(ClusterListFilter);

export type ClusterListFilterPropTypes = Pick<PropTypes, 'filterParameters'>;

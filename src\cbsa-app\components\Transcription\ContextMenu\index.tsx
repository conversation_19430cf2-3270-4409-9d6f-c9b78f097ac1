import { flatten, values } from 'lodash';
import { memo } from 'react';

import { TranscriptionViewableWords } from '@common-modules/mediaDetails/models';

import { TranscriptionPropTypes } from '../TranscriptionPropTypes';
import { findContiguousWords, search } from '../utils';

import ContextMenuView from './ContextMenuView';

const ContextMenu = ({
  transcription,
  selectedWords,
  onDeselectAll,
  redactedWords,
  onRedactWords,
  onUnredactWords,
  searchResults,
  word,
  onViewNotes,
  onDeleteNotes,
  onClose,
}: ContextMenuPropTypes) => {
  if (!word) {
    return null;
  }
  const anchorEl = document.getElementById(word.id) || undefined;
  const redactedWord = redactedWords[word.id];
  const isRedacted = !!redactedWord;
  const hasNotes = ((rw) => !!rw && !!rw.redactionNotes)(redactedWord);

  type Is = (w: TranscriptionViewableWords | undefined) => boolean;
  const isSel: Is = (w) => !!w && !!selectedWords[w.id];
  const isRed: Is = (w) => !!w && !!redactedWords[w.id];
  const isSearch: Is = (w) => !!w && searchResults.some((r) => !!r[w.id]);

  const allWords = isSearch(word)
    ? searchResults
    : search(
        findContiguousWords(transcription, word, isSel(word) ? isSel : isRed)
          .map((w) => w.words)
          .join(' '),
        transcription,
        false
      );
  const numAll = allWords.length;

  const onRedact = () => {
    if (selectedWords[word.id]) {
      onRedactWords(values(selectedWords));
      onDeselectAll();
    } else {
      onRedactWords([word]);
    }
  };

  const onUnredact = () => {
    onUnredactWords(
      findContiguousWords(
        transcription,
        word,
        isSearch(word) ? isSearch : isSel(word) ? isSel : isRed
      )
    );
    onDeselectAll();
  };

  const onRedactAll = () => {
    onRedactWords(flatten(allWords.map(values)));
    onDeselectAll();
  };

  const onUnredactAll = () => {
    allWords.map(values).forEach(onUnredactWords);
    onDeselectAll();
  };

  const onEditNote = () => {
    if (redactedWord) {
      onViewNotes([
        redactedWord.startRedactionMs,
        redactedWord.stopRedactionMs,
        redactedWord.redactionNotes,
      ]);
    }
  };

  const onDeleteNote = () => {
    if (redactedWord) {
      onDeleteNotes([
        redactedWord.startRedactionMs,
        redactedWord.stopRedactionMs,
        redactedWord.redactionNotes,
      ]);
    }
  };

  return (
    <ContextMenuView
      {...{
        anchorEl,
        isRedacted,
        hasNotes,
        numAll,
        onRedact,
        onRedactAll,
        onUnredact,
        onUnredactAll,
        onEditNote,
        onDeleteNote,
        onClose,
      }}
    />
  );
};

export default memo(ContextMenu);

export interface ContextMenuPropTypes
  extends Pick<
    TranscriptionPropTypes,
    | 'transcription'
    | 'selectedWords'
    | 'onDeselectAll'
    | 'redactedWords'
    | 'onRedactWords'
    | 'onUnredactWords'
    | 'onViewNotes'
    | 'onDeleteNotes'
  > {
  readonly searchResults: ReadonlyArray<
    Record<string, TranscriptionViewableWords>
  >;
  readonly word?: TranscriptionViewableWords;
  readonly onClose: () => void;
}

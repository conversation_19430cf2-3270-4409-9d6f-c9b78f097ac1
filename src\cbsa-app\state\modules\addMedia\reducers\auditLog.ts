import { TDOId } from '@cbsa-modules/universal';
import { Re } from '../reducers';
import { GetAuditLogsResponse } from '../services/queries/auditLog';

// export const getTdoAuditLogSuccess: Re<{
//   readonly auditEvents: any;
// }> = (state, { payload, meta }) => {
export const getTdoAuditLogSuccess: Re<GetAuditLogsResponse> = (
  state,
  { payload, meta }
) => {
  interface AuditEvent {
    auditLogId: string;
    tdoId?: TDOId;
    action?: string;
    timestamp?: string;
    userName?: string;
  }
  return {
    ...state,
    auditEvents: {
      ...state.auditEvents,
      [meta.variables.terms[0].tdoId]: (
        payload.auditEvents?.records || []
      ).reduce<AuditEvent[]>((acc, auditEvent) => {
        acc.push({
          auditLogId: auditEvent.id,
          tdoId: auditEvent.payload.tdoId,
          action: auditEvent.payload.action,
          timestamp: auditEvent.payload.timestamp,
          userName: auditEvent.payload.userName,
        });
        return acc;
      }, []),
    },
  };
};

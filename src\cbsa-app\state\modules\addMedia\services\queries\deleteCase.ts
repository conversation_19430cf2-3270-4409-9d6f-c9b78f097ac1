export const DELETE_CASE_FOLDER_QUERY = `
  mutation deleteFolder($id: ID!) {
    deleteFolder(input: {
      id: $id,
      orderIndex: 0,
    }) {
      id
      message
    }
  }`;
export interface DeleteCaseFolderQueryResponse {
  deleteFolder: {
    id: string;
    message: string;
  };
}

export const UPDATE_STATUS_OF_CASE_SDO_TO_DELETED_QUERY = `
  mutation updateRedactCase($id: ID!, $schemaId: ID!, $data: JSONData!) {
    createStructuredData(input: {
      id: $id,
      schemaId: $schemaId,
      data: $data,
    }) {
      id
      data
    }
  }`;

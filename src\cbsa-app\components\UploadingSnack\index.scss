.uploadingSnack {
  position: absolute;
  background: white;
  border-radius: 3px;
  box-shadow: 0 3px 10px #24242459;
  display: flex;
  flex-direction: column;
  padding: 20px;
  bottom: 14px;
  left: 20px;
  min-width: 200px;
  max-width: 300px;
  max-height: calc(100vh - 28px);
  z-index: 999;

  &Title {
    color: #005c7e;
    font-size: 14px;
    font-weight: bold;
    padding-bottom: 10px;
  }

  &Items {
    overflow: scroll;
  }

  &Item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    height: 36px;

    &Text {
      color: black;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &Progress {
      padding-left: 20px;
    }

    &:last-of-type {
      padding: 10px 0 0 0;
    }
  }

  hr {
    border-top: 0.5px solid #70767b;
    margin: 5px 0;
  }
}

.enter {
  transform: translateX(-300px);
  transition: transform 300ms ease;
}

.enterActive {
  transform: translateX(0);
}

.exit {
  transform: translateX(0);
}

.exitActive {
  transform: translateX(-300px);
  transition: transform 300ms ease;
}

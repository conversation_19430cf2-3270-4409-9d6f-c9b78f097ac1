import makeStyles from '@mui/styles/makeStyles';

const background = '#182025';
const color = '#CFD8DC';

export const useStyles = makeStyles(() => ({
  container: {
    boxShadow: 'none',
    width: '100%',
    height: '100%',
    background,
  },

  tabNameWrapper: {
    background,
    color,
    width: '100%',
  },

  tabName: {
    paddingLeft: '20px',
    paddingTop: '15px',
    color,
    fontFamily: 'Roboto, Arial, Helvetica, sans-serif',
    fontSize: '16px',
    fontWeight: 500,
    lineHeight: '19px',
  },

  button: {
    height: '36px',
    width: '173px',
    color: '#fff',
    fontFamily: 'Roboto, Arial, Helvetica, sans-serif',
    fontSize: '14px',
    fontWeight: 500,
    lineHeight: '16px',
    marginTop: '35px !important',
  },

  errorStateIcon: {
    width: '90px',
    height: '87px',
    marginTop: '60px',
  },

  message: {
    color: '#CFD8DC',
    fontFamily: 'Roboto',
    fontSize: 16,
    fontWeight: 'normal',
    marginTop: 40,
    lineHeight: '19px',
  },
  buttonDerp: {
    marginTop: '40px !important',
    color: 'rgb(255, 255, 255) !important',
    backgroundColor: 'rgb(25, 118, 210) !important',
    padding: '6px 16px !important',
    borderRadius: '4px !important',
  },
  content: {},
}));

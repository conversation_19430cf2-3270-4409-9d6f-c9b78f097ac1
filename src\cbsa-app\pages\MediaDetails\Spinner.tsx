import Modal from '@mui/material/Modal';
import CircularProgress from '@mui/material/CircularProgress';
import { useStyles } from './styles';
import { ThemeProvider } from '@mui/material';
import { defaultTheme } from '@cbsa/styles/materialThemes';

const SpinnerContent = (props: { message: string }) => {
  const classes = useStyles();
  return (
    <Modal open>
      <div className={classes.spinnerContainer}>
        <div style={{ textAlign: 'center', marginBottom: '10px' }}>
          <CircularProgress color="secondary" />
        </div>
        <div style={{ textAlign: 'center' }}>{props.message}</div>
      </div>
    </Modal>
  );
};

const Spinner = (props: { message: string }) => (
  <ThemeProvider theme={defaultTheme}>
    <SpinnerContent {...props} />
  </ThemeProvider>
);

export default Spinner;

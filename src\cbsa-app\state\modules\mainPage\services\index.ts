import { modules } from '@veritone/glc-redux';
import callGraphQLApi from '@helpers/callGraphQLApi';
import { TreeObjectId } from '@common-modules/universal/models/Brands';
import {
  Case,
  FolderId,
  lookupLatestCaseSchemaId,
  Thunk,
} from '@cbsa-modules/universal';
import {
  FETCH_CASES_SUCCESS,
  FETCH_CASES_FAILURE,
  FETCH_SUMMARY_SUCCESS,
  FETCH_SUMMARY_FAILURE,
  CBSA_MAINPAGE_NOOP,
  PUT_CASE_SUCCESS,
  PUT_CASE_FAILURE,
  DELETE_CASE_SUCCESS,
  DELETE_CASE_FAILURE,
  ARCHIVE_CASE_SUCCESS,
  ARCHIVE_CASE_FAILURE,
  PUT_CASE,
} from '../actions';

import { FolderSummary, MainPageStore } from '../MainPageStore';

import {
  newFolder_parentIdQuery,
  newFolder_folderQuery,
  newFolder_sdoQuery,
  newFolder_linkQuery,
  deleteFolder_folderSummaryQuery,
  deleteFolder_deleteFolder,
  deleteFolder_updateSdo,
  archiveCase_caseInfo,
  NewFolderParentIdQueryResponse,
  NewFolderFolderQueryResponse,
  NewFolderSdoQueryResponse,
  ArchiveCaseCaseInfo,
} from './queries';

import { ARCHIVE_CASE_QUERY } from '@cbsa-modules/addMedia/services/queries/archiveCase';
import {
  createRootFoldersQuery,
  CreateRootFoldersResponse,
} from '@redact/state/modules/casemedia/services/queries';
import {
  arrayHasContents,
  isObjectWithProperty,
  isObjectWithStringProperty,
} from '@common/shared/util';
export const DATABASE_UPDATE_DELAY = 1_500;

export const fetchCasesSearch: Thunk<{
  searchText: MainPageStore['searchText'];
  statusFilter: MainPageStore['statusFilter'];
  showArchived: MainPageStore['showArchived'];
  casesSortColumn: MainPageStore['casesSortColumn'];
  casesSortOrder: MainPageStore['casesSortOrder'];
  paginationAmount: MainPageStore['paginationAmount'];
  paginationStart: MainPageStore['paginationStart'];
}> =
  ({
    searchText,
    statusFilter,
    showArchived,
    casesSortColumn,
    casesSortOrder,
    paginationAmount,
    paginationStart,
  }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const state = getState();
    const token =
      modules.auth.selectOAuthToken(state) ||
      modules.auth.selectSessionToken(state);
    const { apiRoot } = modules.config.getConfig<Window['config']>(state);
    const queryJson = {
      index: ['mine'],
      limit: paginationAmount,
      offset: paginationStart - 1,
      type: schemaId,
      query: {
        operator: 'and',
        conditions: [
          {
            field: 'status',
            operator: 'query_string',
            value: `!deleted`,
          },
        ],
      },
      sort: [
        {
          field: casesSortColumn,
          order: casesSortOrder,
        },
      ],
    };

    if (statusFilter !== 'all') {
      queryJson.query.conditions.push({
        field: 'status',
        operator: 'query_string',
        value: statusFilter,
      });
    }

    if (showArchived) {
      queryJson.query.conditions.push({
        field: 'archive',
        operator: 'term',
        value: 'archived',
      });
    } else {
      queryJson.query.conditions.push({
        field: 'archive',
        operator: 'query_string',
        value: '!archived',
      });
    }

    if (searchText) {
      searchText.split(' ').forEach((currentWord) => {
        if (currentWord) {
          queryJson.query.conditions.push({
            field: 'caseName.fulltext',
            operator: 'query_string',
            value: `*${currentWord.toLowerCase()}*`,
          });
        }
      });
    }

    try {
      const results = await fetch(`${apiRoot}/api/search`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(queryJson),
      }).then((a) => a.json());

      dispatch(FETCH_CASES_SUCCESS(results));
    } catch (err) {
      dispatch(
        FETCH_CASES_FAILURE(
          isObjectWithProperty(err, 'message') ? err.message : 'Unknown Error'
        )
      );
    }
  };

export const fetchCasesSummary: Thunk<object> =
  (_a) => async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const state = getState();
    const token =
      modules.auth.selectOAuthToken(state) ||
      modules.auth.selectSessionToken(state);
    const { apiRoot } = modules.config.getConfig(state);
    const fetchUrl = `${apiRoot}/api/search`;

    const casesCreatedQuery = {
      index: ['mine'],
      limit: 1,
      offset: 0,
      type: schemaId,
      query: {
        operator: 'and',
        conditions: [
          {
            field: 'archive',
            operator: 'query_string',
            value: '!archived',
          },
          {
            field: 'status',
            operator: 'query_string',
            value: '!deleted',
          },
        ],
      },
    };

    const casesApprovedQuery = {
      index: ['mine'],
      limit: 1,
      offset: 0,
      type: schemaId,
      query: {
        operator: 'and',
        conditions: [
          {
            field: 'status',
            operator: 'term',
            value: 'approved',
          },
          {
            field: 'archive',
            operator: 'query_string',
            value: '!archived',
          },
        ],
      },
    };

    const casesReopenedQuery = {
      index: ['mine'],
      limit: 1,
      offset: 0,
      type: schemaId,
      query: {
        operator: 'and',
        conditions: [
          {
            field: 'archive',
            operator: 'term',
            value: 'reopened',
          },
        ],
      },
    };

    const casesReadyForExportQuery = {
      index: ['mine'],
      limit: 1,
      offset: 0,
      type: schemaId,
      query: {
        operator: 'and',
        conditions: [
          {
            field: 'status',
            operator: 'term',
            value: 'readyForExport',
          },
          {
            field: 'archive',
            operator: 'query_string',
            value: '!archived',
          },
        ],
      },
    };

    try {
      const casesCreatedPromise = fetch(fetchUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(casesCreatedQuery),
      })
        .then((a) => a.json())
        .then((a) => a.totalResults);

      const casesApprovedPromise = fetch(fetchUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(casesApprovedQuery),
      })
        .then((a) => a.json())
        .then((a) => a.totalResults);

      const casesReopenedPromise = fetch(fetchUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(casesReopenedQuery),
      })
        .then((a) => a.json())
        .then((a) => a.totalResults);

      const casesReadyForExportPromise = fetch(fetchUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(casesReadyForExportQuery),
      })
        .then((a) => a.json())
        .then((a) => a.totalResults);

      const [casesCreated, casesApproved, casesReopened, casesReadyForExport] =
        await Promise.all([
          casesCreatedPromise,
          casesApprovedPromise,
          casesReopenedPromise,
          casesReadyForExportPromise,
        ]);

      dispatch(
        FETCH_SUMMARY_SUCCESS({
          casesCreated,
          casesApproved,
          casesReopened,
          casesReadyForExport,
        })
      );
    } catch (err) {
      dispatch(
        FETCH_SUMMARY_FAILURE(
          isObjectWithProperty(err, 'message') ? err.message : 'Unknown Error'
        )
      );
    }
  };

export const putNewCase: Thunk<{ newCaseName: Case['name'] }> =
  ({ newCaseName }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    const currentTime = new Date().toISOString();

    try {
      // Fetch parentId
      const { rootFolders } =
        await callGraphQLApi<NewFolderParentIdQueryResponse>({
          actionTypes: [
            CBSA_MAINPAGE_NOOP,
            CBSA_MAINPAGE_NOOP,
            PUT_CASE_FAILURE,
          ],
          query: newFolder_parentIdQuery,
          variables: {},
          dispatch,
          getState,
          throwOnError: true,
        });

      if (rootFolders?.length > 0) {
        const parentId = rootFolders[0]?.id;
        // Create the folder object
        const { createFolder: newFolder } =
          await callGraphQLApi<NewFolderFolderQueryResponse>({
            actionTypes: [
              CBSA_MAINPAGE_NOOP,
              CBSA_MAINPAGE_NOOP,
              PUT_CASE_FAILURE,
            ],
            query: newFolder_folderQuery,
            variables: {
              parentId,
              newCaseName,
            },
            dispatch,
            getState,
            throwOnError: true,
          });

        // Create the case sdo
        const {
          createStructuredData: { id: newSdoId, data: newSdo },
        } = await callGraphQLApi<NewFolderSdoQueryResponse>({
          actionTypes: [
            CBSA_MAINPAGE_NOOP,
            CBSA_MAINPAGE_NOOP,
            PUT_CASE_FAILURE,
          ],
          query: newFolder_sdoQuery,
          variables: {
            newCaseName,
            folderTreeObjectId: newFolder.treeObjectId,
            currentTime,
            schemaId: schemaId,
          },
          dispatch,
          getState,
          throwOnError: true,
        });

        await new Promise<void>((resolve) => {
          setTimeout(() => {
            resolve();
          }, DATABASE_UPDATE_DELAY);
        });

        // Link the folder and the case sdo together
        await callGraphQLApi({
          actionTypes: [
            CBSA_MAINPAGE_NOOP,
            CBSA_MAINPAGE_NOOP,
            PUT_CASE_FAILURE,
          ],
          query: newFolder_linkQuery,
          variables: {
            schemaId: schemaId,
            folderId: newFolder.treeObjectId,
            sdoId: newSdoId,
          },
          dispatch,
          getState,
          throwOnError: true,
        });

        dispatch(
          PUT_CASE_SUCCESS({
            caseId: newFolder.treeObjectId as unknown as FolderId,
            caseData: newSdo,
          })
        );
      } else {
        const { createRootFolders } =
          await callGraphQLApi<CreateRootFoldersResponse>({
            actionTypes: [
              CBSA_MAINPAGE_NOOP,
              CBSA_MAINPAGE_NOOP,
              PUT_CASE_FAILURE,
            ],
            query: createRootFoldersQuery,
            variables: {},
            dispatch,
            getState,
            throwOnError: true,
          });
        if (createRootFolders?.length > 0) {
          dispatch(PUT_CASE(newCaseName));
        }
      }
    } catch (err) {
      dispatch(
        PUT_CASE_FAILURE(
          isObjectWithStringProperty(err, 'message')
            ? err.message
            : 'Unknown Error',
          undefined
        )
      );
    }
  };

export const deleteCase: Thunk<{ deleteId: TreeObjectId }> =
  ({ deleteId }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    try {
      /* Fetches folder summary. To delete a case, we
       ** must delete the folder and update the status
       ** of the associated case to 'deleted'.
       */

      // Fetch the folder summary
      const { folder: folderSummary } = await callGraphQLApi<{
        folder: FolderSummary;
      }>({
        actionTypes: [
          CBSA_MAINPAGE_NOOP,
          CBSA_MAINPAGE_NOOP,
          CBSA_MAINPAGE_NOOP,
        ],
        query: deleteFolder_folderSummaryQuery,
        variables: {
          folderTreeObjectId: deleteId,
        },
        throwOnError: true,
        dispatch,
        getState,
      });

      // Do not delete empty folders
      if (folderSummary.childTDOs.count > 0) {
        dispatch(DELETE_CASE_FAILURE('Folder Not Empty'));
        return;
      }
      // if the folder is not linked,
      // you will not be able to update sdo to deleted
      if (!arrayHasContents(folderSummary.contentTemplates)) {
        dispatch(
          DELETE_CASE_FAILURE(
            'contentTemplate That Links Folder to SDO Does Not Exist'
          )
        );
        return;
      }
      const contentTemplate = folderSummary.contentTemplates[0];
      const updateDateTime = new Date().toISOString();

      // deleting folder and updating sdo can happen at same time.
      await Promise.all([
        // delete the folder object
        callGraphQLApi({
          actionTypes: [
            CBSA_MAINPAGE_NOOP,
            CBSA_MAINPAGE_NOOP,
            CBSA_MAINPAGE_NOOP,
          ],
          query: deleteFolder_deleteFolder,
          variables: {
            folderTreeObjectId: deleteId,
          },
          getState,
          dispatch,
          throwOnError: true,
        }),
        // update the case sdo
        callGraphQLApi({
          actionTypes: [
            CBSA_MAINPAGE_NOOP,
            CBSA_MAINPAGE_NOOP,
            CBSA_MAINPAGE_NOOP,
          ],
          query: deleteFolder_updateSdo,
          variables: {
            schemaId: schemaId,
            sdoId: contentTemplate.sdo.id,
            updatedCaseData: {
              ...contentTemplate.sdo.data,
              modifiedDateTime: updateDateTime,
              status: 'deleted',
            },
          },
          getState,
          dispatch,
          throwOnError: true,
        }),
      ]);

      // TODO: Validate these types
      dispatch(DELETE_CASE_SUCCESS(deleteId as unknown as FolderId));
    } catch (err) {
      console.error(err);
      dispatch(
        DELETE_CASE_FAILURE(
          isObjectWithStringProperty(err, 'message')
            ? err.message
            : 'Unknown Error'
        )
      );
    }
  };

export const archiveCase: Thunk<{ archiveId: TreeObjectId }> =
  ({ archiveId }) =>
  async (dispatch, getState) => {
    const schemaId = await lookupLatestCaseSchemaId(dispatch, getState);
    if (!schemaId) {
      return;
    }
    try {
      const {
        structuredDataObjects: { records: caseSdoResults },
      } = await callGraphQLApi<ArchiveCaseCaseInfo>({
        actionTypes: [
          CBSA_MAINPAGE_NOOP,
          CBSA_MAINPAGE_NOOP,
          CBSA_MAINPAGE_NOOP,
        ],
        query: archiveCase_caseInfo,
        variables: {
          folderTreeObjectId: archiveId,
          schemaId,
        },
        dispatch,
        getState,
        throwOnError: true,
      });

      if (!arrayHasContents(caseSdoResults)) {
        throw new Error('SDO Not Found');
      }

      const caseInfo = caseSdoResults[0];
      const modifiedDateTime = new Date().toISOString();

      let archive: Case['archive'] = 'archived';
      if (caseInfo.data.archive === 'archived') {
        archive = 'reopened';
      }

      const data: Case = {
        ...caseInfo.data,
        modifiedDateTime,
        archive,
      };

      await callGraphQLApi({
        actionTypes: [
          CBSA_MAINPAGE_NOOP,
          CBSA_MAINPAGE_NOOP,
          CBSA_MAINPAGE_NOOP,
        ],
        query: ARCHIVE_CASE_QUERY,
        variables: {
          schemaId,
          id: caseInfo.id,
          data,
        },
        getState,
        dispatch,
        throwOnError: true,
      });

      dispatch(ARCHIVE_CASE_SUCCESS(archiveId as unknown as FolderId));
    } catch (err) {
      console.error(err);
      dispatch(
        ARCHIVE_CASE_FAILURE(
          isObjectWithStringProperty(err, 'message')
            ? err.message
            : 'Unknown Error'
        )
      );
    }
  };

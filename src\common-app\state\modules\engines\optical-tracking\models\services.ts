import { TDOId } from '@cbsa/state/modules/universal';
import { BoundingPolyRect, Point } from '@common-modules/mediaDetails/models';

export interface ProcessEngineRequestWithOverlay {
  readonly id: string;
  readonly overlay: string;
  readonly timeMs: number;
  readonly trackBack: boolean;
  readonly trackForward: boolean;
  readonly isUndo?: boolean;
}

export interface ProcessEngineRequest {
  readonly id: string;
  readonly overlay: string;
  readonly boundingPoly: BoundingPolyRect;
  readonly timeMs: number;
  readonly parentUdrId: string;
  readonly trackBack: boolean;
  readonly trackForward: boolean;
}

export interface CreateEngineJobRequest extends ProcessEngineRequest {
  readonly id: string;
  readonly tdoId: TDOId;
  readonly tdoName: string;
  readonly clusterId?: string;
  readonly opticalTrackingEngineId: string;
  // readonly webstreamAdapterEngineId: string;
  // readonly webstreamTargetUrl: string;
}

export interface CreateEngineJobResponse {
  readonly createJob: {
    readonly id: string;
  };
}

export interface CreateEngineJobMetaResponse {
  readonly variables: {
    readonly input: {
      readonly targetId: string;
      readonly isReprocessJob: boolean;
      readonly tasks: ReadonlyArray<{
        readonly engineId: string;
        readonly payload: {
          readonly backTrackFrameLimit: number;
          readonly boundingPolys: ReadonlyArray<{
            readonly boundingPoly: ReadonlyArray<Point>;
            readonly timeMs: number;
            readonly parentUdrId: string;
          }>;
        };
      }>;
    };
  };
}

export interface LoadMediaDataSuccess {
  readonly temporalDataObject: {
    readonly assets: {
      readonly records: ReadonlyArray<{
        readonly createdDateTime: string;
        readonly signedUri: string;
        readonly type: string;
      }>;
    };
  };
}

export interface LoadMediaDataMetaSuccess {
  readonly operationName?: string;
  readonly query?: string;
  readonly response: {
    readonly data: LoadMediaDataSuccess;
    readonly status: number;
  };
  readonly variables: {
    readonly id: string;
  };
  readonly _internalRequestId: string;
  readonly _shouldTrackRequestsIndividually: boolean;
}

export interface SetDownloadingJobs {
  readonly createJob: {
    readonly id: string;
    readonly status: string;
    readonly target: {
      readonly name: string;
    };
    readonly targetId: string;
  };
}

export interface SetDownloadingMetaJobs {
  readonly operationName?: string;
  readonly query: string;
  readonly response?: {
    readonly data?: SetDownloadingJobs;
    readonly status: number;
  };
  readonly variables: {
    readonly engineId: string;
    readonly id: string;
    readonly payload: {
      readonly exportAssetType: string;
      readonly mode: string;
      readonly user: string;
    };
    readonly tdoName: string;
    readonly _internalRequestId: string;
    readonly _shouldTrackRequestsIndividually: boolean;
  };
}

export interface SetDownloadingJobStatus {
  readonly job: {
    readonly createdDateTime: string;
    readonly id: string;
    readonly status: string;
  };
}

export interface SetDownloadingJobMetaStatus {
  readonly operationName?: string;
  readonly query: string;
  readonly response: {
    readonly data: SetDownloadingJobStatus;
    readonly status: number;
  };
  readonly variables: {
    readonly jobId: string;
  };
  readonly _internalRequestId: string;
  readonly _shouldTrackRequestsIndividually: boolean;
}

import { CaseId, FolderId, TDOId } from "../brands";

export type RequestHeader = Record<string,string>;
export interface CreateFolderRequest {
  name: string;
  description: string;
  parentFolderId?: FolderId;
  userId?: string;
}
export interface ExternalIds {
  govQARequestId?: string;
  foiaXpressRequestId?: string;
  casepointRequestId?: string;
  nuixRequestId?: string;
  exterroRequestId?: string;
}
interface Detections {
  runDetection?: boolean;
  runHeadDetection?: boolean;
  runPersonDetection?: boolean;
}
export interface IngestMediaRequest extends Detections{
  existingCaseId?: string;
  createNewCase?: CreateFolderRequest;
  externalIds: ExternalIds;
  urls: Array<string>
  runTranscription: boolean;
}
export interface IngestMediaFileRequest extends Detections {
  caseId: CaseId;
  externalIds: ExternalIds;
  url: string;
  fileType?: string;
  runTranscription: boolean;
  email: string;
}
export interface CreateTdoRequest {
  url: string;
  caseId?: CaseId;
  externalIds: ExternalIds;
}
export interface IngestJobRequest extends Detections {
  tdoId: TDOId,
  url: string;
  fileType?: string;
  runTranscription: boolean;
  email: string;
}

export interface CheckFolderExistsRequest {
  folderId: FolderId;
}

export interface CheckNameExistsRequest  {
  name: string;
  folderId: FolderId;
}

export interface getFilesByCaseIdRequest {
  caseId: CaseId;
  limit: number;
  offset: number;
}

export interface getChildFoldersRequest {
  folderId: FolderId;
  limit: number;
  offset: number;
}


import os
import requests
import datetime
import time
import traceback
import json
import subprocess
import logging

class TestSuite:
    '''Encapsulate data and methods for a test suite'''

    def __init__(self, test_suite_name, debug=False):
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
        self.debug = debug
        self.get_env_vars()
        self.create_log_files(test_suite_name)
        self.token = None

    def create_log_files(self, test_suite_name):
        self.out_log = open(os.path.join(self.logs_dir, test_suite_name + '_output.out'), "w+")
        self.err_log = open(os.path.join(self.logs_dir, test_suite_name + '_failure.out'), "w+")

    def get_env_vars(self):
        '''Check and capture required environment vars'''
        self.aiwaretests_root = os.environ['AIWARETESTS_ROOT'].strip()
        if self.aiwaretests_root == '':
            raise Exception("Environment variable AIWARETESTS_ROOT not defined")

        self.logs_dir = os.environ['LOGS_DIR'].strip()
        if self.logs_dir == '':
            raise Exception("Environment variable LOGS_DIR not defined")

        self.aiware_username = os.environ['AIWARE_USERNAME'].strip()
        if self.aiware_username == '':
            raise Exception("Environment variable AIWARE_USERNAME not defined")

        self.aiware_password = os.environ['AIWARE_PASSWORD'].strip()
        if self.aiware_password == '':
            raise Exception("Environment variable AIWARE_PASSWORD not defined")

        self.gql_uri = os.environ['GRAPHQL_URI'].strip()
        if self.gql_uri == '':
            raise Exception("Environment variable GRAPHQL_URI not defined")

        self.test_file_uri = os.environ['TEST_FILE_URI'].strip()
        if self.test_file_uri == '':
            raise Exception("Environment variable TEST_FILE_URI not defined")

        self.test_file_name = os.environ['TEST_FILE_NAME'].strip()
        if self.test_file_name == '':
            raise Exception("Environment variable TEST_FILE_NAME not defined")

        self.max_gql_ms = os.environ['MAX_GRAPHQL_MS'].strip()
        if self.max_gql_ms == '':
            raise Exception("Environment variable MAX_GRAPHQL_MS not defined")
        self.max_gql_ms = int(self.max_gql_ms)

        self.max_job_secs = os.environ['MAX_JOB_SECS'].strip()
        if self.max_job_secs == '':
            raise Exception("Environment variable MAX_JOB_SECS not defined")
        self.max_job_secs = int(self.max_job_secs)

        self.job_status_check_interval_secs = os.environ['JOB_STATUS_CHECK_INTERVAL_SECS'].strip()
        if self.job_status_check_interval_secs == '':
            raise Exception("Environment variable JOB_STATUS_CHECK_INTERVAL_SECS not defined")
        self.job_status_check_interval_secs = int(self.job_status_check_interval_secs)

        self.audit_events_check_interval_secs = os.environ['AUDIT_EVENTS_CHECK_INTERVAL_SECS'].strip()
        if self.audit_events_check_interval_secs == '':
            raise Exception("Environment variable AUDIT_EVENTS_CHECK_INTERVAL_SECS not defined")
        self.audit_events_check_interval_secs = int(self.audit_events_check_interval_secs)

        self.max_audit_events_check_secs = os.environ['MAX_AUDIT_EVENTS_CHECK_SECS'].strip()
        if self.max_audit_events_check_secs == '':
            raise Exception("Environment variable MAX_AUDIT_EVENTS_CHECK_SECS not defined")
        self.max_audit_events_check_secs = int(self.max_audit_events_check_secs)

    def print_env_vars(self):
        logging.info(f"gql_uri: {self.gql_uri}")
        logging.info(f"aiware_username: {self.aiware_username}")
        logging.info(f"aiware_password: {self.aiware_password}")
        logging.info(f"test_file_uri: {self.test_file_uri}")
        logging.info(f"test_file_name: {self.test_file_name}")
        logging.info(f"max_gql_ms: {self.max_gql_ms}")
        logging.info(f"max_job_secs: {self.max_job_secs}")
        logging.info(f"job_status_check_interval_secs: {self.job_status_check_interval_secs}")
        logging.info(f"audit_events_check_interval_secs: {self.audit_events_check_interval_secs}")
        logging.info(f"max_audit_events_check_secs: {self.max_audit_events_check_secs}")

    def print_msg(self, msg, is_error=False):
        logging.info(msg)
        self.out_log.write(msg + '\n')
        if is_error:
            self.err_log.write(msg + '\n')

    def make_request(self, query, query_name, variables=None):
        headers = {
            'content-type': 'application/json'
        }

        if self.token != None:
            headers['Authorization'] = 'Bearer ' + self.token

        body = {
            'query': query
        }

        if variables != None:
            body['variables'] = variables

        start = time.time()
        r = requests.post(self.gql_uri, json=body, headers=headers)
        end = time.time()
        elapsed_ms = int((end - start) * 1000)

        responseBody = json.dumps(r.json())

        if self.debug:
            self.print_msg("Request")
            self.print_msg("  query: " + query)
            self.print_msg("  variables: " + json.dumps(variables))
            self.print_msg("  headers: " + json.dumps(headers))
            self.print_msg("Response")
            self.print_msg("  statusCode: " + str(r.status_code))
            self.print_msg("  body: " + responseBody)
            self.print_msg("ElapsedMs: " + str(elapsed_ms))

        if r.status_code != 200 or 'errors' in r.json() != None:
            self.print_msg("Error calling " + query_name, True)
            self.print_msg("status code: " + str(r.status_code), True)
            self.print_msg("response body: " + responseBody, True)
            exit(1)

        if elapsed_ms > self.max_gql_ms:
            self.print_msg(f"{query_name} took {elapsed_ms} ms and exceeded max limit {self.max_gql_ms}", True)
            exit(1)

        return r

    def get_token(self):
        query = """mutation {
             userLogin(input: {
                userName: "%s"
                password: "%s"
             }) {
               token
             }
           }""" % (self.aiware_username, self.aiware_password)

        r = self.make_request(query, "userLogin")

        self.token = r.json()['data']['userLogin']['token']

    def create_tdo(self):
        current_utc_time = get_current_utc_time()
        query = """mutation {
            createTDO(input: {
                name: "%s"
                startDateTime: "%s"
                stopDateTime: "%s"
                addToIndex: true
                details: {
                    tags: [
                        {
                            value: "in redaction",
                            redactionStatus: "Draft",
                        },
                    ],
                    veritoneFile: {
                        fileName: "%s",
                    },
                    veritoneProgram: {
                        programLiveImage: "",
                    },
                },
            }) {
                id
            }
        }""" % (self.test_file_name, current_utc_time, current_utc_time, self.test_file_name)

        r = self.make_request(query, "createTDO")

        return r.json()['data']['createTDO']['id']

    def create_tdo_with_asset(self):
        query = """mutation {
            createTDOWithAsset(input: {
                name: "%s"
                startDateTime: "%s"
                contentType: "video/mp4"
                uri: "%s"
            }) {
                id
            }
        }""" % (self.test_file_name, get_current_utc_time(), self.test_file_uri)

        r = self.make_request(query, "createTDOWithAsset")

        return r.json()['data']['createTDOWithAsset']['id']

    def create_job_v2f(self, tdo_id, tasks_array):
        variables = {
            "tdo": tdo_id,
            "tasks": tasks_array
        }
        query = """mutation($tdo: ID, $tasks: [CreateTask!]) {
            createJob(input: {
                targetId: $tdo
                tasks: $tasks
            }) {
                id
            }
        }"""

        r = self.make_request(query, "createJob_v2f", variables)

        return r.json()['data']['createJob']['id']

    def create_job_v3f(self, tdo_id):
        query = """mutation {
            createJob(input: {
                targetId: "%s"
                tasks: [
                    {
                        # webstream adapter
                        engineId: "9e611ad7-2d3b-48f6-a51b-0a1ba40fe255"
                        payload: {
                            url: "%s"
                        }
                    },
                    {
                        # SI2 playback segment creator
                        engineId: "352556c7-de07-4d55-b33f-74b1cf237f25"
                    },
                    {
                        # SI2 chunk audio creator
                        engineId: "8bdb0e3b-ff28-4f6e-a3ba-887bd06e6440"
                        payload:{
                            ffmpegTemplate: "audio"
                            customFFMPEGProperties:{
                                chunkSizeInSeconds: "60"
                            }
                        }
                    },
                    {
                        # Speechmatics V3F
                        engineId: "e97d1564-39ff-4016-a034-e1f32aa9eb7d"
                    }
                ]
            }) {
                id
            }
        }""" % (tdo_id, self.file_uri)

        r = self.make_request(query, "createJob_v3f")

        return r.json()['data']['createJob']['id']

    def job_status(self, job_id):
        query = """query {
            job(id: "%s") {
                status
            }
        }""" % (job_id)

        r = self.make_request(query, "job status")

        return r.json()['data']['job']['status']

    def emit_audit_event(self, tdo_id, audit_event_payload):
        variables = {
            "application": "redact",
            "payload" : audit_event_payload
        }
        query = """mutation ($application: String, $payload: JSONData!) {
            emitAuditEvent(input: {
                application: $application
                payload: $payload
            }) {
                id
            }
        }"""

        r = self.make_request(query, "emitAuditEvent", variables)

        return r.json()['data']['emitAuditEvent']['id']

    def audit_events(self, tdo_id):
        variables = {
            "application": "redact",
            "terms": [
                {
                    "tdoId" : tdo_id
                }
            ]
        }
        query = """query ($application: String, $terms: [JSONData!]){
            auditEvents(application: $application, terms: $terms) {
                records {
                    id
                    payload
                    application
                }
            }
        }"""

        r = self.make_request(query, "auditEvents", variables)

        return r.json()['data']['auditEvents']['records']

    def delete_tdo(self, tdo_id):
        query = """mutation {
            deleteTDO(id: "%s") {
                id
                message
            }
        }""" % (tdo_id)

        r = self.make_request(query, "deleteTDO")

        return r.json()['data']['deleteTDO']['id']

    def get_signed_writable_url(self, key):
        '''Returns 2 different URLs, one for use with PUT, and another for use with GET'''

        query = """query {
            getSignedWritableUrl(key: "%s") {
                url
                key
                bucket
                expiresInSeconds
                getUrl
                unsignedUrl
            }
        }""" % (key)

        r = self.make_request(query, "getSignedWritableUrl")

        data = r.json()['data']['getSignedWritableUrl']

        return data['url'], data['getUrl']

### Utility functions ###

def get_current_utc_time():
        d = datetime.datetime.utcnow()
        return d.isoformat("T") + "Z"

def get_audit_event(audit_event_id, all_audit_events):
    if len(all_audit_events) == 0:
        return None
    for e in all_audit_events:
        if e['id'] == audit_event_id:
            return e
    return None

def upload_file(source_uri, dest_uri):
    if source_uri == "":
        raise Exception("upload_file: source_uri is empty")
    if dest_uri == "":
        raise Exception("upload_file: dest_uri is empty")
    cmd = f'curl -X PUT -H "Content-Type: video/mp4" -T {source_uri} {dest_uri}'
    logging.info(cmd)
    cp = subprocess.run(["curl", "-X", "PUT", "-H", "Content-Type: video/mp4", "-T", source_uri, dest_uri],
                        universal_newlines=True, stderr=subprocess.PIPE)
    if cp.returncode != 0:
        raise Exception(cp.stderr)
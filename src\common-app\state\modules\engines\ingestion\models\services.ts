import { TDOId } from '@common-modules/universal/models/Brands';
import { ClusterSimThresholdType } from '@common/state/modules/mediaDetails/models';
export interface ProcessEngineRequest {
  readonly tdoId: TDOId;
  readonly tdoName: string;
  readonly payload: {
    readonly tdoId: TDOId;
    readonly url: string;
    readonly urlHeaders?: string;
    readonly fileType?: string;
    readonly faceDetectionThreshold?: number;
    readonly legacyClustering?: boolean;
    readonly clusterSimThreshold?: ClusterSimThresholdType;
  };
}

export interface ProcessEngineSuccessRequest {
  readonly tdoId: TDOId;
  readonly jobId: string;
}

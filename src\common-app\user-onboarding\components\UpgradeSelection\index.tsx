import { connect } from 'react-redux';
import { modules } from '@veritone/glc-redux';

import {
  actionOpenFastSpring,
  actionShowRequestQuoteForm,
} from '../../actions';
import UpgradeSelectionController from './UpgradeSelectionController';

const {
  config: { getConfig },
} = modules;

export default connect(
  (s: any) => {
    const config = getConfig<Window['config']>(s);
    return {
      requestQuoteUrl: config.requestQuoteUrl,
    };
  },
  {
    onRequestQuote: actionShowRequestQuoteForm,
    onUpgradeAccount: actionOpenFastSpring,
  }
)(UpgradeSelectionController);

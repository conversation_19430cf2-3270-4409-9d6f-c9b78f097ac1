import {
  Button,
  Grid2 as Grid,
  ThemeProvider,
  // Theme,
  StyledEngineProvider,
  Paper,
  Typography,
} from '@mui/material';

import { buttonTheme } from '@cbsa/styles/materialThemes';

import * as styles from './styles.scss';

import { I18nTranslate } from '@common/i18n';

const TranscriptNullStateTabView = ({
  onStartJob,
  isDisable,
  videoLoaded,
}: {
  onStartJob: () => void;
  isDisable: boolean;
  videoLoaded: boolean;
}) => (
  <Paper
    className={styles.container}
    data-veritone-component="transcription-null-state-tab"
  >
    <Grid container className={styles.tabNameWrapper}>
      <Grid size={{ xs: 12 }} className={styles.tabName}>
        {I18nTranslate.TranslateMessage('transcription')}
      </Grid>
    </Grid>
    <Grid
      container
      alignItems="center"
      direction="column"
      className={styles.content}
    >
      <Grid>
        <i className={'icon-transcription ' + styles.nullStateIcon} />
      </Grid>
      <Grid size={{ xs: 6 }}>
        <Typography
          align="center"
          variant="body2"
          color={'secondary'}
          className={styles.message}
        >
          {I18nTranslate.TranslateMessage('transcriptionMessage')}
        </Typography>
      </Grid>
      <Grid>
        <StyledEngineProvider injectFirst>
          <ThemeProvider theme={buttonTheme}>
            <Button
              variant="contained"
              color={'primary'}
              className={styles.button}
              onClick={onStartJob}
              disabled={isDisable || !videoLoaded}
              data-veritone-element="faces-null-state-transcription-button"
            >
              {I18nTranslate.TranslateMessage('transcribeAudio')}
            </Button>
          </ThemeProvider>
        </StyledEngineProvider>
      </Grid>
    </Grid>
  </Paper>
);

export default TranscriptNullStateTabView;

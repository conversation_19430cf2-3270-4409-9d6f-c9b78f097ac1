import {
  memo,
  useMemo,
  useState,
  useEffect,
  useRef,
  ReactNode,
  Ref,
} from 'react';
import classnames from 'classnames';
import Box from '@mui/material/Box';
import * as styles from '../styles.scss';
import { IntlShape, useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { getActiveState } from '../utils';
import Qimg from '@redact-components/Qimg';
import ClusterGroup from '../ClusterGroup';
import IconButton from '@mui/material/IconButton';
import PushPinOutlined from '@mui/icons-material/PushPinOutlined';
import Checkbox from '@redact-components/CheckBox';
import MoreHoriz from '@mui/icons-material/MoreHoriz';
import ClickToEdit from '@redact-components/ClickToEdit';
import { selectFeatureFlags } from '@common/user-permissions';
import PropTypes, { SeekMediaTimePayload } from '../PropTypes';
import { ClusterItem } from '@common-modules/mediaDetails/models';
import { ReactComponent as IconUDR } from '@resources/images/object_type/UDR_24x24px.svg';
import { ReactComponent as IconHead } from '@resources/images/object_type/Head_24x24px.svg';
import { ReactComponent as IconPlate } from '@resources/images/object_type/Plate_24x24px.svg';
// import { ReactComponent as IconVehicle } from '@resources/images/object_type/Vehicle_24x24px.svg';
import { ReactComponent as IconVehicle } from '@resources/images/object_type/Vehicle_24x24px_PlateColor.svg';
import { ReactComponent as IconLaptop } from '@resources/images/object_type/Laptop_24x24px.svg';
import { ReactComponent as IconNotepad } from '@resources/images/object_type/Notepad_24x24px.svg';
import { ReactComponent as IconCard } from '@resources/images/object_type/Card_24x24px.svg';
import { ReactComponent as IconPerson } from '@resources/images/object_type/Person_24x24px.svg';
import { ReactComponent as IconMerged } from '@resources/images/object_type/Merged_24x24px.svg';
import { Virtuoso, VirtuosoHandle } from 'react-virtuoso';
import {
  UDR_COLOR,
  HEAD_COLOR,
  PLATE_COLOR,
  VEHICLE_COLOR,
  LAPTOP_COLOR,
  NOTEPAD_COLOR,
  CARD_COLOR,
  PERSON_COLOR,
  MIXED_COLOR,
  MERGED_COLOR,
  CLUSTER_TYPE,
  CLUSTER_SEGMENT_SORT_TYPE,
  GROUP_TYPE,
} from '@helpers/constants';
import { actionLogAuditEvent } from '@common/state/modules/mediaDetails';
import { Menu, MenuItem, SvgIcon } from '@mui/material';
import { ArrowLeft } from '@mui/icons-material';
import { componentActions } from '@redact/components/MediaDetailsPage/Editor/MediaPlayer/reduxHelpers';
import { enqueueSnackbar } from '@common-modules/snackbar';
import MergeGroupPopup from './MergedGroupPopup';
import ClusterNameDialog, {
  CustomLabelInfo,
} from '../Dialogs/MergeClusterName';

const COLLAPSED_ROW_HEIGHT = 84;
const MERGED_GROUP_INDICATOR_HEIGHT = 20;

const SELECTED_OPTION_BG_COLOR = 'rgba(0, 0, 0, 0.08)';

export const objectTypeIconsMap: {
  [key in GROUP_TYPE]?: {
    IconComponent: React.FC<React.SVGProps<SVGSVGElement>>;
    objectProps: {
      stroke?: string;
      fill?: string;
    };
  };
} = {
  udr: {
    IconComponent: IconUDR,
    objectProps: { stroke: UDR_COLOR },
  },
  head: {
    IconComponent: IconHead,
    objectProps: { fill: HEAD_COLOR },
  },
  licensePlate: {
    IconComponent: IconPlate,
    objectProps: { fill: PLATE_COLOR },
  },
  vehicle: {
    IconComponent: IconVehicle,
    objectProps: { fill: VEHICLE_COLOR },
  },
  laptop: {
    IconComponent: IconLaptop,
    objectProps: { fill: LAPTOP_COLOR },
  },
  notepad: {
    IconComponent: IconNotepad,
    objectProps: { fill: NOTEPAD_COLOR },
  },
  card: {
    IconComponent: IconCard,
    objectProps: { fill: CARD_COLOR },
  },
  person: {
    IconComponent: IconPerson,
    objectProps: { fill: PERSON_COLOR },
  },
};

export const objectTypeColorMap: { [key in GROUP_TYPE]?: string } = {
  udr: UDR_COLOR,
  head: HEAD_COLOR,
  licensePlate: PLATE_COLOR,
  vehicle: VEHICLE_COLOR,
  laptop: LAPTOP_COLOR,
  notepad: NOTEPAD_COLOR,
  card: CARD_COLOR,
  person: PERSON_COLOR,
};

export const getObjectTypeIcons = (types: GROUP_TYPE[]) => {
  const displays = [] as {
    type: GROUP_TYPE;
    IconComponent: React.FC<React.SVGProps<SVGSVGElement>>;
    objectProps: {
      stroke?: string;
      fill?: string;
    };
  }[];
  for (const type of types) {
    const icon = objectTypeIconsMap[type];
    if (icon) {
      displays.push({ ...icon, type });
    }
  }
  return displays;
};

// instead of function just make a map/dictionary? and use above two maps?
export const getObjectTypeDisplay = (type: CLUSTER_TYPE, intl: IntlShape) => {
  switch (type) {
    case 'udr':
      return {
        IconComponent: IconUDR,
        color: UDR_COLOR,
        text: intl
          .formatMessage({ id: 'udr', defaultMessage: 'UDR' })
          .toUpperCase(),
        objectProps: { stroke: UDR_COLOR },
      };
    case 'head':
      return {
        IconComponent: IconHead,
        color: HEAD_COLOR,
        text: intl
          .formatMessage({ id: 'head', defaultMessage: 'Head' })
          .toUpperCase(),
        objectProps: { fill: HEAD_COLOR },
      };
    case 'licensePlate':
      return {
        IconComponent: IconPlate,
        color: PLATE_COLOR,
        text: intl
          .formatMessage({ id: 'plate', defaultMessage: 'Plate' })
          .toUpperCase(),
        objectProps: { fill: PLATE_COLOR },
      };
    case 'vehicle':
      return {
        IconComponent: IconVehicle,
        color: VEHICLE_COLOR,
        text: intl
          .formatMessage({ id: 'vehicle', defaultMessage: 'Vehicle' })
          .toUpperCase(),
        objectProps: { fill: VEHICLE_COLOR },
      };
    case 'laptop':
      return {
        IconComponent: IconLaptop,
        color: LAPTOP_COLOR,
        text: intl
          .formatMessage({ id: 'laptop', defaultMessage: 'Laptop' })
          .toUpperCase(),
        objectProps: { fill: LAPTOP_COLOR },
      };
    case 'notepad':
      return {
        IconComponent: IconNotepad,
        color: NOTEPAD_COLOR,
        text: intl
          .formatMessage({ id: 'notepad', defaultMessage: 'Notepad (PII)' })
          .toUpperCase(),
        objectProps: { fill: NOTEPAD_COLOR },
      };
    case 'card':
      return {
        IconComponent: IconCard,
        color: CARD_COLOR,
        text: intl
          .formatMessage({ id: 'card', defaultMessage: 'ID Card (PII)' })
          .toUpperCase(),
        objectProps: { fill: CARD_COLOR },
      };
    case 'person':
      return {
        IconComponent: IconPerson,
        color: PERSON_COLOR,
        text: intl
          .formatMessage({ id: 'person', defaultMessage: 'Person' })
          .toUpperCase(),
        objectProps: { fill: PERSON_COLOR },
      };
    case 'mixed':
      return {
        IconComponent: IconMerged, // this never gets used
        color: MIXED_COLOR,
        text: '', // icons only //intl.formatMessage({ id: 'mixed' }).toUpperCase(),
        objectProps: { fill: MIXED_COLOR },
      };
    default:
      console.warn('Unknown type', type);
      return null;
  }
};

const ClusterListView = ({
  virtuosoRef,
  clusterList,
  selected,
  clusterMergeGroupIds,
  clusterMergeSegments,
  setSelectedGroups,
  setClusterMergeGroupIds,
  setClusterMergeSegments,
  changeClusterLabel,
  changeClusterPinned,
  changeSegmentSortType,
  highlightedOverlay,
  onHighlightPoly,
  children: [ClusterListFilter, ClusterListSort],
  updateClusterAssignment,
  handleScrollToIndex,
}: ClusterListViewPropTypes) => {
  const intl = useIntl();
  const listContainerRef = useRef<HTMLDivElement>(null);
  const [listContainerWidth, setListContainerWidth] = useState<number>(0);
  const [listContainerHeight, setListContainerHeight] = useState(0);
  const [groupMenuAnchorPosition, setGroupMenuAnchorPosition] = useState<{
    mouseX: number;
    mouseY: number;
  } | null>(null);
  const [anchorMergeNamedGroupPopup, setAnchorMergeNamedGroupPopup] =
    useState<HTMLElement | null>(null);
  const [anchorMergePinnedGroupPopup, setAnchorMergePinnedGroupPopup] =
    useState<HTMLElement | null>(null);
  const [menuCl, setMenuCl] = useState<ClusterItem | null>(null);
  const [pinnedClusters, setPinnedClusters] = useState<ClusterItem[]>([]);
  const [isOpenClusterNameDialog, setIsOpenClusterNameDialog] =
    useState<boolean>(false);
  const [clusterLabel, setClusterLabel] = useState<CustomLabelInfo>({
    label: '',
  });
  const [customClusterLabels, setCustomClusterLabels] = useState<
    CustomLabelInfo[] | undefined
  >(undefined);

  const { analyzeInterpolation } = useSelector(selectFeatureFlags);
  const dispatch = useDispatch();

  const sortedClusterList = useMemo(
    () =>
      [...clusterList].sort((a, b) =>
        !a.pinnedDate && !b.pinnedDate
          ? 0
          : (a?.pinnedDate || 0) > (b?.pinnedDate || 0)
            ? -1
            : 1
      ),
    [clusterList]
  );

  const [lastInteractedClusterId, setLastInteractedClusterId] = useState<
    string | null
  >(null);

  const [isExpanded, setExpanded] = useState<
    Record<string, boolean | undefined>
  >({});

  useEffect(() => {
    const filterPinnedCls = clusterList.filter(
      (cl) => !!cl.pinnedDate && cl.id !== menuCl?.id
    );
    setPinnedClusters(filterPinnedCls);

    if (analyzeInterpolation) {
      console.log(
        `Cumulative Interpolation Time: ${clusterList.reduce(
          (total, clusterItem) =>
            total +
            clusterItem.segments
              .filter(({ isManualInterpolation }) => isManualInterpolation)
              .reduce(
                (groupTotal, { startTimeMs, stopTimeMs }) =>
                  groupTotal + (stopTimeMs - startTimeMs),
                0
              ),
          0
        )} ms`
      );
    }
  }, [analyzeInterpolation, clusterList, menuCl]);

  useEffect(() => {
    if (highlightedOverlay) {
      const isExpandedNew: { [key: string]: boolean } = {};
      for (const cl of clusterList) {
        let flag = false;
        for (const group of cl.groups) {
          if (group.id === highlightedOverlay.groupId) {
            isExpandedNew[cl.id] = true;
            flag = true;
            break;
          }
        }
        if (!flag) {
          if (isExpanded[cl.id]) {
            isExpandedNew[cl.id] = false;
          }
        }
      }
      setExpanded({ ...isExpanded, ...isExpandedNew });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [highlightedOverlay]);

  const expanded = useMemo(
    () => (ci: ClusterItem) => !!isExpanded[ci.id],
    [isExpanded]
  );

  const active = (cl: ClusterItem) => getActiveState(cl.segments, selected);

  const handleChecked = (cl: ClusterItem) => () => {
    const isActive = active(cl) !== 1;
    const objToLog = {
      clusterId: cl.id,
      userLabel: cl.userLabel ?? '',
      startTimeMs: cl.startTimeMs,
      stopTimeMs: cl.stopTimeMs,
      type: cl.type,
    };
    dispatch(
      actionLogAuditEvent(
        `Cluster Toggled: {inRedaction: ${isActive}, clusterItem: ${JSON.stringify(
          objToLog
        )} }`
      )
    );
    return setSelectedGroups({
      selected: {
        // [cl.id]: isActive, // we don't need to include cluster ids
        ...cl.segments
          .map((s) =>
            s.subsegmentIds.reduce<{ [key: string]: boolean }>((ids, id) => {
              ids[id] = isActive;
              return ids;
            }, {})
          )
          .reduce((ss, s) => Object.assign(ss, s), {}),
      },
    });
  };

  const onHighlightPolyHandle =
    (cl: ClusterItem) =>
    (payload: SeekMediaTimePayload, clickOnCollapsed?: boolean) => {
      if (clickOnCollapsed) {
        setExpanded({ ...isExpanded, [cl.id]: !isExpanded[cl.id] });
      }
      onHighlightPoly(payload);
    };

  const handleLabelEdited = (cl: ClusterItem) => (value: string) => {
    changeClusterLabel({
      clusterId: cl.id,
      type: cl.type,
      userLabel: value.trim(),
    });
  };

  const onChangeSegmentSortType =
    (cl: ClusterItem) => (type: CLUSTER_SEGMENT_SORT_TYPE) => {
      changeSegmentSortType({
        clusterId: cl.id,
        segmentSortType: type,
      });
    };

  const onExpandMore = (cl: ClusterItem) => () => {
    const isExpandedLocal = { ...isExpanded, [cl.id]: !isExpanded[cl.id] };

    if (isExpandedLocal[cl.id]) {
      const scrollToIndex = clusterList.findIndex(({ id }) => cl.id === id);
      handleScrollToIndex(scrollToIndex);
    }

    setExpanded(isExpandedLocal);
  };

  useEffect(() => {
    function resizeHandler() {
      const container = listContainerRef.current;

      if (!container) {
        return;
      }

      if (container.clientHeight > 0) {
        setListContainerHeight(container.clientHeight);
      }

      if (container.clientWidth > 0) {
        setListContainerWidth(container.clientWidth);
      }
    }

    const container = listContainerRef.current;

    if (!container) {
      return;
    }

    setListContainerHeight(
      container.clientHeight > 0 ? container.clientHeight : window.innerHeight
    );
    setListContainerWidth(
      container.clientWidth > 0 ? container.clientWidth : window.innerWidth
    );

    window.addEventListener('resize', resizeHandler);

    return () => {
      window.removeEventListener('resize', resizeHandler);
    };
  }, []);

  useEffect(() => {
    if (!highlightedOverlay) {
      return;
    }

    const scrollToIndex = clusterList.findIndex(({ groups }) =>
      groups.some((group) => highlightedOverlay?.groupId === group.id)
    );

    handleScrollToIndex(scrollToIndex);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [highlightedOverlay]);

  const onPin = (cl: ClusterItem, pinned: boolean) => {
    changeClusterPinned({
      clusterId: cl.id,
      type: cl.type,
      pinnedDate: pinned ? Date.now() : undefined,
    });
    setGroupMenuAnchorPosition(null);
  };

  const renderClusterRow = (index: number) => {
    const cl = clusterList[index];
    if (!cl) {
      return;
    }

    const objectTypeDisplay = getObjectTypeDisplay(cl.type, intl);
    if (!objectTypeDisplay) {
      return;
    }

    let { color } = objectTypeDisplay;
    if (cl.dominantType) {
      color = objectTypeColorMap[cl.dominantType] || MERGED_COLOR;
    }
    const isPinned = !!cl.pinnedDate;
    const isSelected = clusterMergeGroupIds[cl.id];
    const isShowMenuIcon = menuCl?.id === cl.id;

    const onSelectClusters: React.MouseEventHandler<HTMLDivElement> = (e) => {
      e.preventDefault();

      if (e.shiftKey && lastInteractedClusterId) {
        const lastSelectedIndex = sortedClusterList.findIndex(
          ({ id }) => id === lastInteractedClusterId
        );

        const currentSelectedIndex = sortedClusterList.findIndex(
          ({ id }) => id === cl.id
        );

        if (lastSelectedIndex === -1 || currentSelectedIndex === -1) {
          return;
        }

        const selectedClusterIds = sortedClusterList
          .slice(
            Math.min(lastSelectedIndex, currentSelectedIndex),
            Math.max(lastSelectedIndex, currentSelectedIndex) + 1
          )
          .map(({ id }) => id);
        setClusterMergeGroupIds({
          clusterIds: selectedClusterIds,
          selected: !isSelected,
        });

        setLastInteractedClusterId(cl.id);
      } else if (e.ctrlKey || e.metaKey) {
        setClusterMergeGroupIds({
          clusterIds: [cl.id],
          selected: !isSelected,
        });

        setLastInteractedClusterId(cl.id);
      }
    };

    const SEGMENT_HEIGHT = 39;
    const NO_SEGMENTS_DISPLAYED = 13;
    const clusterSegmentHeight =
      cl.segments.length * SEGMENT_HEIGHT <
      SEGMENT_HEIGHT * NO_SEGMENTS_DISPLAYED
        ? cl.segments.length * SEGMENT_HEIGHT
        : SEGMENT_HEIGHT * NO_SEGMENTS_DISPLAYED;

    return (
      <Box
        data-testid="clusterList-row"
        key={index}
        id={`cluster-${cl.id}`}
        className={classnames(
          styles.listRow,
          isSelected ? styles.selected : isPinned && styles.pinned
        )}
        display="flex"
        flexDirection="row"
        style={{
          position: 'relative',
          height:
            (cl.isMergedGroup || cl.groups.length > 1
              ? MERGED_GROUP_INDICATOR_HEIGHT
              : 0) +
            (expanded(cl)
              ? COLLAPSED_ROW_HEIGHT +
                clusterSegmentHeight +
                (cl.type === 'mixed' ? 48 : 26)
              : COLLAPSED_ROW_HEIGHT),
        }}
        onClick={onSelectClusters}
        onContextMenu={(e) => {
          if (e.ctrlKey) {
            // Allow the default browser context menu when Ctrl + Right click
            return;
          }

          e.preventDefault();
          e.stopPropagation();
          setMenuCl(cl);
          setGroupMenuAnchorPosition(
            !groupMenuAnchorPosition
              ? {
                  mouseX: e.clientX,
                  mouseY: e.clientY,
                }
              : null // Prevent contextmenu to re-locale existing one
          );
        }}
        onMouseDown={(e) => {
          // Prevents the default browser behavior triggered by Shift + Click.
          // This is required because preventDefault() in onClick() is not enough to prevent text selection in children components.
          if (e.shiftKey) {
            e.preventDefault();
          }
        }}
      >
        <Box display="flex" className={styles.col1}>
          <Checkbox
            onChange={handleChecked(cl)}
            checked={active(cl) !== -1}
            indeterminate={active(cl) === 0}
            canChange={(e) => !e.ctrlKey && !e.metaKey && !e.shiftKey}
            data-veritone-element="face-item-check-box"
          />
        </Box>
        <Box
          display="flex"
          flexDirection="column"
          className={classnames(styles.col2, styles.polyPreview)}
        >
          <Qimg
            src={cl.picUri.trim()}
            loading="lazy"
            style={{ border: `1px solid ${color}` }}
          />
        </Box>
        <Box
          display="flex"
          flexDirection="column"
          className={classnames(styles.fg10auto, styles.col3)}
        >
          <Box
            display="flex"
            className={styles.label}
            title={cl.userLabel ? cl.userLabel : ''}
          >
            <ClickToEdit
              wrapperClass="wrapperClass"
              inputClass="inputClass"
              textClass="textClass"
              value={cl.userLabel ? cl.userLabel : ''}
              endEditing={handleLabelEdited(cl)}
            />
          </Box>
          <Box display="flex" flexDirection="column">
            <ClusterGroup
              segments={cl.segments}
              clusterType={cl.type}
              highlightedOverlay={highlightedOverlay}
              setSelectedGroups={setSelectedGroups}
              setClusterMergeSegments={setClusterMergeSegments}
              selected={selected}
              clusterMergeGroupIds={clusterMergeGroupIds}
              clusterMergeSegments={clusterMergeSegments}
              onHighlightPoly={onHighlightPolyHandle(cl)}
              onExpandMore={onExpandMore(cl)}
              isExpanded={expanded(cl)}
              currentCluster={cl}
              clusterList={clusterList}
              updateClusterAssignment={updateClusterAssignment}
              isGroupSelected={isSelected}
              segmentSortType={cl.segmentSortType}
              onChangeSegmentSortType={onChangeSegmentSortType(cl)}
              clusterSegmentHeight={clusterSegmentHeight}
            />
          </Box>
          {(cl.isMergedGroup || cl.groups.length > 1) && (
            <Box
              display="flex"
              flexDirection="column"
              className={styles.objectType}
            >
              <div
                data-test="detection-name"
                className={styles.clusterFooterObjectType}
              >
                <SvgIcon style={{ width: 18, height: 18, marginRight: 5 }}>
                  {IconMerged && <IconMerged {...{ fill: MERGED_COLOR }} />}
                </SvgIcon>
                <span style={{ color: MERGED_COLOR }}>
                  {intl
                    .formatMessage({
                      id: 'mergedGroup',
                      defaultMessage: 'Merged Group',
                    })
                    .toUpperCase()}
                </span>
              </div>
            </Box>
          )}
        </Box>
        <Box className={styles.col4}>
          <IconButton
            style={{
              width: '40px',
              height: '40px',
              color: 'white',
              background: 'transparent',
            }}
            data-testid="cluster-card-menu"
            className={classnames(!isShowMenuIcon && styles.moreButton)}
            onClick={(e) => {
              e.stopPropagation();
              setMenuCl(cl);
              setGroupMenuAnchorPosition(
                !groupMenuAnchorPosition
                  ? {
                      mouseX: e.clientX,
                      mouseY: e.clientY,
                    }
                  : null // Prevent contextmenu to re-locale existing one
              );
            }}
          >
            <MoreHoriz />
          </IconButton>
          <IconButton
            style={{
              width: '40px',
              height: '40px',
              color: 'white',
              background: 'transparent',
              display: isPinned ? 'block' : 'none',
            }}
            onClick={() => onPin(cl, false)}
            data-testid="cluster-card-pin"
          >
            <PushPinOutlined />
          </IconButton>
        </Box>
      </Box>
    );
  };

  const showDeleteFailureSnackbar = () => {
    const message = intl.formatMessage({
      id: 'deleteGroupFailure',
      defaultMessage: 'Failed to delete group.',
    });
    dispatch(enqueueSnackbar({ message, variant: 'error' }));
  };

  const clickDeleteGroup = () => {
    setGroupMenuAnchorPosition(null);
    if (!menuCl) {
      showDeleteFailureSnackbar();
      return;
    }

    const udrGroupIds = menuCl.groups
      .filter((group) => group.type === 'udr')
      .map((group) => group.id);
    const detectionGroups = menuCl.groups
      .filter((group) => group.type !== 'udr')
      .map((group) => ({ id: group.id, type: group.type }));

    dispatch(
      componentActions.onDeleteCluster(menuCl.id, udrGroupIds, detectionGroups)
    );
  };

  const handleMergeWithPinnedGroup = (
    event: React.MouseEvent<HTMLLIElement, MouseEvent>
  ) => {
    const pinnedGroup = pinnedClusters[0];
    if (pinnedClusters.length === 1 && pinnedGroup) {
      handleSelectCluster(pinnedGroup);
    } else if (pinnedClusters.length > 1) {
      setAnchorMergePinnedGroupPopup(event.currentTarget);
    }
  };

  const handleMergeGroup = (
    clusterLabel: string,
    assignedClusterId?: string,
    cluster?: ClusterItem | null
  ) => {
    const groups = [...(cluster?.groups || []), ...(menuCl?.groups || [])];
    const groupIds: string[] = [];
    for (const group of groups) {
      groupIds.push(group.id);
    }
    updateClusterAssignment({
      assignedClusterId,
      clusterUserLabel: clusterLabel,
      segments: [],
      groupIds,
    });
    setAnchorMergeNamedGroupPopup(null);
    setAnchorMergePinnedGroupPopup(null);
    setGroupMenuAnchorPosition(null);
    setCustomClusterLabels(undefined);
  };

  const handleCloseMergeGroupNameDialog = () => {
    setIsOpenClusterNameDialog(false);
    setClusterLabel({ label: '' });
  };

  const handleSelectCluster = (cluster: ClusterItem) => {
    cluster && handleMergeGroup(cluster.userLabel!, cluster.id, cluster);
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      className={styles.clusterListViewContainer}
    >
      {ClusterListFilter}
      {ClusterListSort}
      <div
        data-testid="results-tab-object"
        id="cluster-list-container"
        className={styles.listContainer}
        ref={listContainerRef}
      >
        {clusterList.length > 0 && (
          <Virtuoso
            ref={virtuosoRef}
            data={sortedClusterList}
            totalCount={clusterList.length}
            itemContent={renderClusterRow}
            style={{ height: listContainerHeight, width: listContainerWidth }}
          />
        )}
      </div>
      <Menu
        id="cluster-card-menu"
        open={Boolean(groupMenuAnchorPosition)}
        onClose={() => {
          setGroupMenuAnchorPosition(null);
        }}
        anchorReference="anchorPosition"
        anchorPosition={
          groupMenuAnchorPosition
            ? {
                top: groupMenuAnchorPosition.mouseY,
                left: groupMenuAnchorPosition.mouseX,
              }
            : undefined
        }
        MenuListProps={{
          'aria-labelledby': 'cluster-card-button',
        }}
        TransitionProps={{
          onExited: () => setMenuCl(null),
        }}
        onContextMenu={(e) => {
          if (e.ctrlKey) {
            // Allow the default browser context menu when Ctrl + Right click
            return;
          }

          e.preventDefault();
          e.stopPropagation();
          // Close the menu if it is already open
          setGroupMenuAnchorPosition(null);
        }}
      >
        <MenuItem onClick={() => menuCl && onPin(menuCl, !menuCl.pinnedDate)}>
          {intl.formatMessage({
            id: menuCl?.pinnedDate ? 'unpin' : 'pinToTop',
            defaultMessage: 'Unpin',
          })}
        </MenuItem>
        <MenuItem
          onClick={() => {
            if (menuCl) {
              setClusterMergeGroupIds({
                clusterIds: [menuCl.id],
                selected: !clusterMergeGroupIds[menuCl.id],
              });

              setLastInteractedClusterId(menuCl.id);
            }
            setGroupMenuAnchorPosition(null);
          }}
        >
          {intl.formatMessage({
            id:
              menuCl?.id && clusterMergeGroupIds[menuCl.id]
                ? 'deselect'
                : 'select',
            defaultMessage: 'Deselect',
          })}
        </MenuItem>
        <MenuItem
          onClick={handleMergeWithPinnedGroup}
          disabled={pinnedClusters.length < 1}
          sx={{
            backgroundColor: anchorMergePinnedGroupPopup
              ? SELECTED_OPTION_BG_COLOR
              : 'transparent',
          }}
          data-testid="merge-with-pinned-group-option"
        >
          <ArrowLeft
            sx={{
              position: 'absolute',
              left: -3,
              display: pinnedClusters.length > 1 ? 'block' : 'none',
            }}
            data-testid="merge-with-pinned-group-expand-icon"
          />
          {intl.formatMessage({
            id: 'mergeWithPinnedGroup',
            defaultMessage: 'Merge with Pinned Group',
          })}
        </MenuItem>
        <MenuItem
          onClick={(e) => setAnchorMergeNamedGroupPopup(e.currentTarget)}
          sx={{
            backgroundColor: anchorMergeNamedGroupPopup
              ? SELECTED_OPTION_BG_COLOR
              : 'transparent',
          }}
          data-testid="merge-with-named-group-option"
        >
          <ArrowLeft sx={{ position: 'absolute', left: -3 }} />
          {intl.formatMessage({
            id: 'mergeWithNamedGroup',
            defaultMessage: 'Merge with Named Group',
          })}
        </MenuItem>
        <MenuItem onClick={clickDeleteGroup}>
          {intl.formatMessage({
            id: 'deleteGroup',
            defaultMessage: 'Delete Group',
          })}
        </MenuItem>
      </Menu>
      <MergeGroupPopup
        anchorEl={anchorMergeNamedGroupPopup}
        open={Boolean(anchorMergeNamedGroupPopup)}
        onClose={() => setAnchorMergeNamedGroupPopup(null)}
        currentCluster={menuCl}
        clusterList={clusterList}
        onSelectCluster={handleSelectCluster}
      />
      <MergeGroupPopup
        anchorEl={anchorMergePinnedGroupPopup}
        open={Boolean(anchorMergePinnedGroupPopup)}
        onClose={() => setAnchorMergePinnedGroupPopup(null)}
        currentCluster={menuCl}
        clusterList={pinnedClusters}
        onSelectCluster={handleSelectCluster}
        isPin
      />
      <ClusterNameDialog
        isOpen={isOpenClusterNameDialog}
        onClose={handleCloseMergeGroupNameDialog}
        handleConfirm={handleMergeGroup}
        value={clusterLabel}
        customLabels={customClusterLabels}
        title={intl.formatMessage({
          id: 'mergeConfirm',
          defaultMessage: 'Merge confirmation',
        })}
        content={
          customClusterLabels && customClusterLabels?.length > 1
            ? intl.formatMessage({
                id: 'mergeMultipleText',
                defaultMessage:
                  'Selection includes multiple named groups! Please select name for the merged group:',
              })
            : intl.formatMessage({
                id: 'mergeConfirmText',
                defaultMessage:
                  'Creating new merged group. Please confirm group name:',
              })
        }
      />
    </Box>
  );
};

export type ClusterListViewPropTypes = Pick<
  PropTypes,
  | 'clusterList'
  | 'highlightedOverlay'
  | 'setSelectedGroups'
  | 'setClusterMergeGroupIds'
  | 'setClusterMergeSegments'
  | 'changeClusterLabel'
  | 'changeClusterPinned'
  | 'changeSegmentSortType'
  | 'selected'
  | 'clusterMergeGroupIds'
  | 'clusterMergeSegments'
  | 'updateClusterAssignment'
> & {
  readonly virtuosoRef: Ref<VirtuosoHandle>;
  readonly onHighlightPoly: (
    payload: SeekMediaTimePayload,
    clickOnCollapsed?: boolean
  ) => void;
  readonly children: Array<ReactNode>;
  readonly handleScrollToIndex: (index: number) => void;
};

export default memo(ClusterListView);

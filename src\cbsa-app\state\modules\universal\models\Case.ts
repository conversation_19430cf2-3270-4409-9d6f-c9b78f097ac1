import { CaseId, TreeObjectId } from '@common-modules/universal/models/Brands';

export type CaseStatus =
  | 'new'
  | 'processing'
  | 'error'
  | 'readyForReview'
  | 'approved'
  | 'readyForExport'
  | 'deleted';

export interface Case {
  archive: 'archived' | 'reopened' | '';
  createdDateTime: string;
  exportTdoId?: string;
  id: CaseId;
  modifiedDateTime: string;
  name: string;
  priority?: number;
  processingTime?: number;
  status: CaseStatus | '';
  treeObjectId?: TreeObjectId;
  readonly folderTreeObjectId?: TreeObjectId; // Strictly read from BE
  readonly caseName?: string; // Strictly read from BE
}

import classnames from 'classnames';
import { memo } from 'react';
import Box from '@mui/material/Box';

import Checkbox from '@cbsa-components/CheckBox';
import PropTypes from '../PropTypes';
import SortIcon from '../SortIcon';
import { useStyles } from '../styles';
import { getActiveState } from '../utils';
import { I18nTranslate } from '@common/i18n';

const ClusterListSort = ({
  clusterList,
  selected,
  setSelectedGroups,
  setSortBy,
  sortBy,
}: ClusterListSortPropTypes) => {
  const classes = useStyles();
  const { column, direction } = sortBy;

  const handleSort = (type: typeof column) => () => {
    if (column === type) {
      setSortBy({
        column: type,
        direction: direction === 'asc' ? 'desc' : 'asc',
      });
    } else {
      setSortBy({
        column: type,
        direction: 'asc',
      });
    }
  };

  const handleSelectedAllGroup = () => {
    const isSelectAll = clusterList.every(
      (cs) => getActiveState(cs.segments, selected) !== 1
    );
    setSelectedGroups({
      selected: clusterList
        .map((cluster) =>
          cluster.segments
            .map((segment) =>
              segment.subsegmentIds.reduce<{ [key: string]: boolean }>(
                (ids, id) => {
                  ids[id] = isSelectAll;
                  return ids;
                },
                {}
              )
            )
            .reduce((ss, s) => Object.assign(ss, s), {
              // [cluster.id]: isSelectAll, // we don't need to include cluster ids
            })
        )
        .reduce((s, g) => Object.assign(s, g), {}),
    });
  };

  let checkboxState: number | undefined = undefined;
  for (const cluster of clusterList) {
    const active = getActiveState(cluster.segments, selected);
    checkboxState ??= active;

    if (checkboxState === active && active) {
      continue;
    }

    checkboxState = 0;
    break;
  }

  return (
    <Box
      className={classnames(classes.fg10auto, classes.sort)}
      display="flex"
      flexDirection="row"
      alignItems="baseline"
      style={{ paddingBottom: '0.5rem' }}
    >
      <Box display="flex" alignItems="center" className={classes.col1}>
        <Checkbox
          onChange={handleSelectedAllGroup}
          checked={checkboxState !== -1 ? true : false}
          indeterminate={![-1, undefined, 1].includes(checkboxState)}
          data-veritone-element="face-item-check-box-all"
        />
      </Box>
      <Box
        className={classnames(classes.fg10auto)}
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
      >
        <Box
          className={classnames(
            classes.colTime,
            sortBy.column === 'startTimeMs' && classes.activeSort
          )}
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="center"
          onClick={handleSort('startTimeMs')}
        >
          <span className={classes.clickable}>
            {I18nTranslate.TranslateMessage('mostRecent')}{' '}
            <SortIcon type="startTimeMs" sortBy={sortBy} />
          </span>
        </Box>
      </Box>
    </Box>
  );
};

export default memo(ClusterListSort);

export type ClusterListSortPropTypes = Pick<
  PropTypes,
  'clusterList' | 'selected' | 'setSelectedGroups' | 'sortBy' | 'setSortBy'
>;

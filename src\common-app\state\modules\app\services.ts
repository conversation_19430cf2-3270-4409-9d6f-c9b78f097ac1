import callGraph<PERSON><PERSON>pi from '@helpers/callGraphQLApi';
import { FETCH_APPLICATION_CONFIGS_SUCCESS } from './actions';
import { UnknownAction } from '@reduxjs/toolkit';

export interface ApplicationConfigsResponse {
  applicationConfig: {
    records: {
      configType: string;
      configKey: string;
      value: string | null;
      valueJSON: Record<string, unknown> | null;
    }[];
  };
}

export const queryApplicationConfigsService =
  (applicationId: string) =>
  async (dispatch: (a: UnknownAction) => void, getState: () => any) => {
    const query = `
      query GetRedactConfig($applicationId: ID!) {
        applicationConfig(appId: $applicationId){
          records {
            configType
            configKey
            value
            valueJSON
          }
        }
      }`;
    return await callGraphQLApi<ApplicationConfigsResponse>({
      actionTypes: ['noop', FETCH_APPLICATION_CONFIGS_SUCCESS, 'noop'],
      query,
      variables: { applicationId },
      dispatch,
      getState,
      bailout: undefined,
    });
  };

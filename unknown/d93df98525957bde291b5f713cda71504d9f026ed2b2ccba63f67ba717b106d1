import {
  REMOVE_COMMENT,
  UPDATE_COMMENT,
  UPDATE_COMMENT_SUCCESS,
} from '../actions';
import { produce, WritableDraft } from 'immer';
import { omit, uniq } from 'lodash';
import { MainStore } from '../store';
import { current } from '@reduxjs/toolkit';
import {
  FetchMediaCommentsResponse,
  FetchMediaUserResponse,
} from '@common-modules/mediaDetails/services';
import { GetActionCreatorPayloadT } from '@utils';
import { TDOId } from '../../universal/models/Brands';

export const fetchMediaComments = (state: WritableDraft<MainStore>) => ({
  ...state,
  isFetchingComments: true,
});

export const fetchMediaCommentsSuccess = (
  state: WritableDraft<MainStore>,
  {
    payload: {
      structuredDataObjects: { offset, records },
    },
  }: {
    payload: FetchMediaCommentsResponse;
  }
) => ({
  ...state,
  mediaComments: Object.values(records).reduce(
    (prev, { data }) => ({ ...prev, [data.commentId]: data }),
    offset ? { ...state.mediaComments } : {}
  ),
  isFetchingComments: false,
});

export const addNewComment = (
  state: WritableDraft<MainStore>,
  {
    payload,
  }: {
    payload: {
      commentId: string;
      tdoId: TDOId;
      userId: string;
      searchName: string;
      currentTime: number;
    };
  }
) => {
  state.mediaComments[payload.commentId] = {
    id: payload.commentId,
    comment: '',
    done: false,
    read: false,
    deleted: false,
    archived: false,
    createdDateTime: '',
    modifiedDateTime: '',
    tdoId: payload.tdoId,
    createdBy: payload.userId,
    commentId: payload.commentId,
    searchName: payload.searchName,
    mediaTimestamp: payload.currentTime,
  };
};
// ({
//   ...state,
//   mediaComments: {
//     [payload.commentId]: {
//       comment: '',
//       done: false,
//       read: false,
//       deleted: false,
//       archived: false,
//       createdDateTime: '',
//       modifiedDateTime: '',
//       tdoId: payload.tdoId,
//       createdBy: payload.userId,
//       commentId: payload.commentId,
//       searchName: payload.searchName,
//       mediaTimestamp: payload.currentTime,
//     },
//     ...state.mediaComments,
//   },
// });

export const updateCommentSuccess = (
  state: WritableDraft<MainStore>,
  {
    payload,
  }: { payload: GetActionCreatorPayloadT<typeof UPDATE_COMMENT_SUCCESS> }
) => {
  const {
    createStructuredData: { data },
    isUndo,
  } = payload;
  const currentState = current(state);
  const previousComment = currentState.mediaComments[data.commentId];
  const modify = previousComment && previousComment.createdDateTime !== '';

  return produce(state, (draft) => {
    draft.mediaComments[data.commentId] = data;

    if (!isUndo) {
      draft.history.past.push({
        action: {
          creator: UPDATE_COMMENT,
          payload: { comment: data, modify: true, isUndo: true },
        },
        inverseAction: modify
          ? {
              creator: UPDATE_COMMENT,
              payload: { comment: previousComment, modify, isUndo: true },
            }
          : { creator: REMOVE_COMMENT, payload: { commentId: data.commentId } },
      });
      draft.history.future = [];
    }
  });
};

export const removeComment = (
  state: WritableDraft<MainStore>,
  { payload }: { payload: { commentId: string } }
) => ({
  ...state,
  mediaComments: omit(state.mediaComments, payload.commentId),
});

// export const fetchMediaCommentsUserSuccess = (
//   state: MainStore,
//   {
//     payload,
//   }: {
//     payload: {
//       basicUserInfo: {
//         id: string;
//         firstName: string;
//         lastName: string;
//         imageUrl: string;
//       };
//     };
//   }
// ) => ({
//   ...state,
//   mediaCommentsUsers: {
//     ...state.mediaCommentsUsers,
//     [payload.basicUserInfo.id]: {
//       firstName: payload.basicUserInfo.firstName,
//       lastName: payload.basicUserInfo.lastName,
//       imageUrl: payload.basicUserInfo.imageUrl,
//     },
//   },
// });

export const fetchMediaCommentsUserSuccess = (
  state: WritableDraft<MainStore>,
  {
    payload,
  }: {
    payload: {
      basicUserInfo: {
        id: string;
        firstName: string;
        lastName: string;
        imageUrl: string | null;
      };
    };
  }
) => {
  state.mediaCommentsUsers[payload.basicUserInfo.id] = payload.basicUserInfo;
};

export const selectComment = (
  state: WritableDraft<MainStore>,
  { payload }: { payload: { commentId: string } }
) => {
  const { commentId } = payload;
  const { selectedMediaComments: selected } = state;
  const index = selected.indexOf(commentId);
  return {
    ...state,
    selectedMediaComments:
      index > -1
        ? [...selected.slice(0, index), ...selected.slice(index + 1)]
        : [...selected, commentId],
  };
};

export const selectCommentsOnTimeline = (
  state: WritableDraft<MainStore>,
  { payload }: { payload: { commentId: string } }
) => {
  const { commentId } = payload;
  const { selectedMediaComments, mediaComments } = state;

  const mediaTimeStamp = mediaComments[commentId]?.mediaTimestamp;
  const commentsAtTimestamp = Object.values(mediaComments)
    .filter((comment) => comment.mediaTimestamp === mediaTimeStamp)
    .map((comment) => comment.commentId);

  const isSelected =
    selectedMediaComments.filter((comment) =>
      commentsAtTimestamp.includes(comment)
    ).length === commentsAtTimestamp.length;

  return {
    ...state,
    selectedMediaComments: isSelected
      ? [
          ...selectedMediaComments.filter(
            (comment) => !commentsAtTimestamp.includes(comment)
          ),
        ]
      : uniq([...commentsAtTimestamp]),
  };
};

export const fetchMediaUserSuccess = (
  state: WritableDraft<MainStore>,
  { payload }: { payload: FetchMediaUserResponse }
) => ({
  ...state,
  user: payload.me,
});
// })
//   state.user = payload.me;
//   return;
// }

export const basicUserQueryIsSupported = (state: WritableDraft<MainStore>) => ({
  ...state,
  isBasicUserQuerySupported: false,
});

export const updateStatusHideComment = (
  state: WritableDraft<MainStore>,
  { payload }: { payload: { isHideComment: boolean } }
) => {
  const { isHideComment } = payload;

  return {
    ...state,
    isHideComment,
  };
};

import { connect } from 'react-redux';
import { createSelector } from 'reselect';

import { actionOpenSendEmailInvite } from '../../actions';
// @ts-expect-error Types are invalid - needs fixing
import { selectUserIsNotEnterprise } from '../../selectors';
import InviteAppButtonView from './InviteAppButtonView';

const InviteAppButton = ({ isUpgradable, onOpen }: InviteAppButtonPropTypes) =>
  isUpgradable ? <InviteAppButtonView onOpen={onOpen} /> : null;

export default connect(
  createSelector(selectUserIsNotEnterprise, (isUpgradable: boolean) => ({
    isUpgradable,
  })),
  {
    onOpen: actionOpenSendEmailInvite,
  }
)(InviteAppButton);

export interface InviteAppButtonPropTypes {
  readonly isUpgradable: boolean;
  readonly onOpen: typeof actionOpenSendEmailInvite;
}

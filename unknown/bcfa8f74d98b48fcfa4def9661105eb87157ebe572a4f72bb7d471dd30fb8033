import { isNil, get, flatten, values, mapValues } from 'lodash';

import {
  GroupedBoundingPoly,
  buildTimelineCollection,
  CollectionWrapper,
  updateTimelineCollection,
  ClusterGroupsResponse,
  ClusterGroupsUpdateResponse,
  DetectionCollectionUpdateR<PERSON>ponse,
  DetectionSeriesLengthResponse,
  LOAD_MEDIA_DATA_ASSET_SUCCESS,
  OUT_BOUNDING_POLY_COLLECTION,
  OUT_DETECTION_COLLECTION_UPDATE,
  OUT_CLUSTER_MAP,
  OUT_DETECTION_CLUSTER_GROUPS,
  OUT_UDR_CLUSTER_GROUPS,
  OUT_DETECTION_CLUSTER_GROUPS_UPDATE,
  OUT_UDR_CLUSTER_GROUPS_UPDATE,
  OUT_DETECTION_SERIES_LENGTH,
  OUT_PROGRESS,
  OUT_TRANSCRIPTION_COLLECTION,
  OUT_SELECTED_BOUNDINGPOLY_GROUP,
  OUT_UDR_ASSET,
  OUT_UDR_ASSET_UPDATE,
  ProgressResponse,
  SAVE_MEDIA_DATA_SUCCESS,
  SaveMediaDataRequest,
  TranscriptionResponse,
  OUT_FETCH_FACE_DETECTION_START,
  OUT_FETCH_FACE_DETECTION_SUCCESS,
  OUT_FETCH_FACE_DETECTION_FAILURE,
  OUT_FACE_DETECTION_PROCESSING_START,
  OUT_FACE_DETECTION_PROCESSING_STOP,
  OUT_FACE_DETECTION_READY,
  OUT_UDR_COLLECTION,
  OUT_DATA_FETCHED_FOR_DETECTION_TYPE,
  DataFetchedForDetectionType,
  OUT_SET_HIGHLIGHTED_OVERLAY,
  SET_LAST_SAVE_MEDIA_DATA_DISPLAY,
  SET_SAVE_MAX_ATTEMPT_FAILURE,
  CHANGE_SHAPE,
  SET_DETECTION_TYPE_REDACTION,
  SET_INDIVIDUAL_REDACTION,
  SET_SAVE_IS_RUNNING,
  CHANGE_CODE,
  LastSaveMediaDataDisplayPayload,
  BoundingPolyTree,
  OUT_UPDATED_CLUSTER_ASSIGNMENT,
  ClusterMapResponse,
} from '@worker';

import {
  CHANGE_OVERLAY,
  CHANGE_CLUSTER_LABEL,
  CHANGE_CLUSTER_PINNED,
  NEW_OVERLAY,
  RESIZE_OVERLAY_SEGMENT,
  CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM,
  CHANGE_UDR_GROUP_LABEL,
  ON_SPRAY_PAINT_SAVE,
  DELETE_DETECTION_OVERLAY,
  DELETE_DETECTION_IN_FRAME,
  DELETE_DETECTION_SEGMENT,
  DELETE_DETECTION_GROUP,
  DELETE_UDR_OVERLAY,
  DELETE_UDR_IN_FRAME,
  DELETE_UDR_SEGMENT,
  DELETE_UDR_GROUP,
  SetSelectedGroupsRequest,
  CHANGE_CLUSTER_SEGMENT_SORT_TYPE,
} from '../actions';
import { defaultState, MainStore } from '../store';
import { FaceDetectionInfo } from '../store.models';
import {
  UDRsPolyAssetGroupSeriesItem,
  UDRsPolyAssetStatus,
} from '../models/UDRsPolyAsset';
import { HighlightedOverlay, ClusterItemGroup } from '../models';
import { CaseReducer, createReducer } from '@reduxjs/toolkit';
import { produce, produceWithPatches, WritableDraft } from 'immer';

import { CLUSTER_SEGMENT_SORT_TYPE } from '@helpers/constants';
import { onSetMergeSelectClusterIdsHelper } from '@common-modules/mediaDetails/reducers/view';
import {
  convertToFilterParameterType,
  DeepWritable,
  GetActionCreatorPayloadT,
} from '@utils';

type Re<P = unknown> = CaseReducer<MainStore, { payload: P; type: string }>;

const onOutDetectionSeriesLength: Re<DetectionSeriesLengthResponse> = (
  state,
  action
) => ({
  ...state,
  detectionSeriesLengthWithDeleted:
    action.payload.detectionSeriesLengthWithDeleted,
});

const onOutClusterMap: Re<ClusterMapResponse> = (state, action) => ({
  ...state,
  clusterMap: action.payload,
});

const onOutDetectionClusterGroups: Re<ClusterGroupsResponse> = (
  state,
  action
) => ({
  ...state,
  // TODO: Why is the payload type considered readonly? Fix if possible
  detectionClusterGroups: action.payload as DeepWritable<typeof action.payload>,
});

const onOutUdrClusterGroups: Re<ClusterGroupsResponse> = (state, action) => ({
  ...state,
  // TODO: Why is the payload type considered readonly? Fix if possible
  udrClusterGroups: action.payload as DeepWritable<typeof action.payload>,
});

const onOutDetectionClusterGroupsUpdate: Re<ClusterGroupsUpdateResponse> = (
  state,
  action
) => {
  // there might be a better way to do this
  const detectionClusterGroups: { [key: string]: ClusterItemGroup } = {};
  Object.assign(detectionClusterGroups, state.detectionClusterGroups);
  for (const [groupId, groupUpdate] of Object.entries(action.payload)) {
    if (!groupUpdate) {
      delete detectionClusterGroups[groupId];
    } else {
      detectionClusterGroups[groupId] = groupUpdate;
    }
  }

  return {
    ...state,
    // TODO: Is this safe to do? Why is part of the group considered readonly?
    detectionClusterGroups: detectionClusterGroups as DeepWritable<
      typeof detectionClusterGroups
    >,
  };
};

const onOutUdrClusterGroupsUpdate: Re<ClusterGroupsUpdateResponse> = (
  state,
  action
) => {
  // there might be a better way to do this
  const udrClusterGroups: { [key: string]: ClusterItemGroup } = {};
  Object.assign(udrClusterGroups, state.udrClusterGroups);
  for (const [groupId, groupUpdate] of Object.entries(action.payload)) {
    if (!groupUpdate) {
      delete udrClusterGroups[groupId];
    } else {
      udrClusterGroups[groupId] = groupUpdate;
    }
  }

  return {
    ...state,
    // TODO: Is this safe to do? Why is part of the group considered readonly?
    udrClusterGroups: udrClusterGroups as DeepWritable<typeof udrClusterGroups>,
  };
};

const onOutProgress: Re<ProgressResponse> = (state, action) => {
  const newState = {
    ...state,
    progress: {
      ...state.progress,
      ...action.payload,
    },
  };

  return {
    ...newState,
    faceDetection: {
      ...newState.faceDetection,
      statusMessage: getFaceDetectionStatusMessage(
        newState.faceDetection,
        newState.progress
      ),
    },
  };
};

const onOutCollection: Re<{
  [objectType: string]: BoundingPolyTree | undefined;
}> = (state, action) =>
  // important! wrap collection on main thread not worker to avoid proxy errors
  ({
    ...state,
    detectionCollections: mapValues(action.payload, (collection) =>
      collection ? new CollectionWrapper(collection) : undefined
    ),
  });

const onOutDetectionCollectionUpdate: Re<DetectionCollectionUpdateResponse> = (
  state,
  action
) => {
  // group updated clusters by object type
  const updatedClustersByObjectType: {
    [objectType: string]: {
      [clusterLabel: string]: Array<GroupedBoundingPoly>;
    };
  } = {};

  if (action.payload) {
    for (const [clusterLabel, cluster] of Object.entries(action.payload)) {
      const clusterArr: Array<GroupedBoundingPoly> = [];

      let clusterObjectType;
      const series = cluster.series;
      if (!isNil(series) && series?.length > 0) {
        series.forEach((f) => {
          clusterArr.push({
            id: f.id,
            subsegmentId: f.subsegmentId,
            groupId: f.groupId,
            clusterId: f.clusterId,
            type: f.object.type,
            startTimeMs: f.startTimeMs,
            stopTimeMs: f.stopTimeMs,
            boundingPoly: f.object.boundingPoly,
            isDeleted: f.object.isDeleted,
            isEditable: true,
            shapeType: f.shapeType,
            redactionCode: f.redactionCode,
            redactionConfig: f.redactionConfig,
            version: f.version,
          });
        });
        clusterObjectType = clusterArr[0]?.type || 'head';
      } else {
        clusterObjectType = cluster.type;
      }

      // vehicle is grouped into the licensePlate collection
      if (clusterObjectType === 'vehicle') {
        clusterObjectType = 'licensePlate';
      }

      if (!updatedClustersByObjectType[clusterObjectType]) {
        updatedClustersByObjectType[clusterObjectType] = {};
      }

      const updateClusters = updatedClustersByObjectType[clusterObjectType];
      if (updateClusters !== undefined) {
        updateClusters[clusterLabel] = clusterArr;
      }
    }
  }

  if (state.detectionCollections) {
    const updatedCollections: { [key: string]: CollectionWrapper | undefined } =
      {};

    for (const [objectType, updateClusters] of Object.entries(
      updatedClustersByObjectType
    )) {
      const collectionKey = convertToFilterParameterType(objectType); // collection keys are same keys as filterParameters
      if (!collectionKey) {
        return state; // something wrong
      }
      const currentCollection =
        state.detectionCollections[collectionKey]?.collection;

      //  update the collection
      let updatedCollection = undefined;
      if (currentCollection) {
        updatedCollection = updateTimelineCollection(
          currentCollection,
          updateClusters
        );
      }
      updatedCollections[collectionKey] = updatedCollection
        ? new CollectionWrapper(updatedCollection)
        : undefined;
    }

    const newState = {
      ...state,
      detectionCollections: {
        ...state.detectionCollections,
        ...updatedCollections,
      },
    };

    return newState;
  } else {
    return state;
  }
};

const onOutTranscription: Re<TranscriptionResponse> = (state, action) => ({
  ...state,
  // TODO: Why is the payload type considered readonly? Fix if possible
  ...(action.payload as DeepWritable<typeof action.payload>),
});

const onChangeOverlay: Re<unknown> = (state) => ({
  ...state,
  tdoIsChanged: true,
});

export const onChangeUdrsPolyAssetGroupSeriesItem: Re<{
  id: string;
  seriesItem: UDRsPolyAssetGroupSeriesItem;
  changeType: 'start' | 'end' | 'stick';
}> = (state, { payload }) => {
  const { seriesItem, changeType } = payload;

  let currentPosition = state.currentPosition;

  if (changeType === 'start' || changeType === 'end') {
    currentPosition =
      changeType === 'start' ? seriesItem.startTimeMs : seriesItem.stopTimeMs;
  }

  return {
    ...state,
    currentPosition,
    tdoIsChanged: true,
  };
};

const onChangeUdrGroupLabel: Re<{
  groupId: string;
  label: string;
}> = (state, action) => {
  const { groupId, label } = action.payload;

  const newState = state;
  newState.tdoIsChanged = true;

  const clusterId = state.udrClusterGroups[groupId]?.clusterId;

  // if group is the only group assigned to a cluster update the cluster label as well so behavior is same a normal udr
  // in the future we may want to revisit how udr rows are displayed when part of cluster
  if (clusterId) {
    const clusterMapItem = newState.clusterMap[clusterId];
    if (clusterMapItem) {
      if (
        clusterMapItem.detectionGroupIds.length === 0 &&
        clusterMapItem.udrGroupIds.length === 1
      ) {
        clusterMapItem.userLabel = label;
      }
    }
  }

  return newState;
};

const onChangeClusterUserLabel: Re<{
  clusterId: string;
  userLabel: string;
}> = (state, action) => {
  const newState = state;
  newState.tdoIsChanged = true;

  // consider just sending over update from web-worker instead of doing updates below

  const { userLabel, clusterId } = action.payload;

  const clusterMapItem = newState.clusterMap[clusterId];

  let groupId: string | undefined = undefined;

  // check if cluster exists
  if (clusterMapItem && clusterId) {
    clusterMapItem.userLabel = userLabel;

    // if cluster is only a single group then update the group label as well
    if (
      clusterMapItem.detectionGroupIds.length === 1 &&
      clusterMapItem.udrGroupIds.length === 0
    ) {
      groupId = clusterMapItem.detectionGroupIds[0]!; // check is above
    } else if (
      clusterMapItem.detectionGroupIds.length === 0 &&
      clusterMapItem.udrGroupIds.length === 1
    ) {
      groupId = clusterMapItem.udrGroupIds[0]!; // check is above
    }
  } else {
    // else must be a groupId
    groupId = clusterId;
  }

  if (groupId) {
    // udr groups need to update here to propagate name change to udr timeline row
    const udrGroup = state.udrsState.udrAsset.boundingPolys[groupId];
    if (udrGroup) {
      udrGroup.userLabel = userLabel;
    }
  }

  return newState;
};

const onChangeClusterPinned: Re<{
  clusterId: string;
  type: string;
  pinnedDate: number | undefined;
  generatedId: string;
}> = (state, action) => {
  // check if cluster exists
  const clusterMapItem = state.clusterMap[action.payload.clusterId];
  if (clusterMapItem) {
    return {
      ...state,
      tdoIsChanged: true,
      clusterMap: {
        ...state.clusterMap,
        [action.payload.clusterId]: {
          ...clusterMapItem,
          pinnedDate: action.payload.pinnedDate,
        },
      },
    };
  }

  // must be a groupId - need to create cluster
  const groupId = action.payload.clusterId;
  const newClusterId = action.payload.generatedId;

  const udrGroupIds: string[] = [];
  const detectionGroupIds: string[] = [];

  let userLabel;
  if (action.payload.type === 'udr') {
    const item = state.udrClusterGroups[groupId];
    if (item) {
      udrGroupIds.push(groupId);
      userLabel = state.udrClusterGroups[groupId]?.userLabel;
      state.udrClusterGroups = {
        ...state.udrClusterGroups,
        [groupId]: {
          ...item,
          clusterId: newClusterId,
        },
      };
    }
  } else {
    const item = state.detectionClusterGroups[groupId];
    if (item) {
      detectionGroupIds.push(groupId);
      userLabel = state.detectionClusterGroups[groupId]?.userLabel;
      state.detectionClusterGroups = {
        ...state.detectionClusterGroups,
        [groupId]: {
          ...item,
          clusterId: newClusterId,
        },
      };
    }
  }

  state.tdoIsChanged = true;
  state.clusterMap = {
    ...state.clusterMap,
    [newClusterId]: {
      id: newClusterId,
      pinnedDate: action.payload.pinnedDate,
      userLabel, // copy over group userLabel
      detectionGroupIds,
      udrGroupIds,
    },
  };
};

const onChangeSegmentSortType: Re<{
  clusterId: string;
  segmentSortType: CLUSTER_SEGMENT_SORT_TYPE;
}> = (state, action) => {
  // This function is only for multi-group clusters, with type === 'mixed',
  // so clusterMapItem should be exists
  const clusterMapItem = state.clusterMap[action.payload.clusterId];
  if (clusterMapItem) {
    return {
      ...state,
      tdoIsChanged: true,
      clusterMap: {
        ...state.clusterMap,
        [action.payload.clusterId]: {
          ...clusterMapItem,
          sortBy: action.payload.segmentSortType,
        },
      },
    };
  }
};

const onSaveTdoAsset: Re<{
  createAsset: {
    id: string;
    createdDateTime: string;
  };
}> = (state, action) => ({
  ...state,
  tdoIsChanged: false,
  lastSaveMediaDataDisplayTime: action?.payload?.createAsset?.createdDateTime,
  isSaveRunning: false,
  isShowConfirmLeave: false,
});

const setSaveMaxAttemptFailure: Re<void> = (state) =>
  produce(state, (draft) => {
    draft.tdoIsChanged = true;
    draft.isSaveRunning = false;
    draft.isSaveFailed = true;
  });

const setSaveIsRunning = (
  state: WritableDraft<MainStore>,
  action: { payload: { isSaveRunning: boolean } }
) => ({
  ...state,
  isSaveRunning: action?.payload?.isSaveRunning,
});

const setLastSaveMediaDataDisplay: Re<LastSaveMediaDataDisplayPayload> = (
  state,
  action
) => ({
  ...state,
  tdoIsChanged: false,
  lastSaveMediaDataDisplayTime: action?.payload?.lastSaveMediaDataDisplayTime,
});

const onOutSelectedBoundingPoly = (
  state: WritableDraft<MainStore>,
  action: { payload: SetSelectedGroupsRequest }
) => ({
  ...state,
  selectedPolyGroups: action.payload.selected,
});

const onOutUdrAsset = (
  state: WritableDraft<MainStore>,
  // action: { payload: UDRsPolyAsset }
  action: ReturnType<typeof OUT_UDR_ASSET>
) => ({
  ...state,
  isFetchingClusterListData: true,
  udrsState: {
    ...state.udrsState,
    // TODO: Is this safe? Why is the payload type considered partly readonly?
    udrAsset: action.payload as DeepWritable<typeof action.payload>,
    localUDRAsset: action.payload as DeepWritable<typeof action.payload>,
  },
});

const onOutUdrAssetUpdate = (
  state: WritableDraft<MainStore>,
  // action: { payload: UDRsPolyAsset }
  action: ReturnType<typeof OUT_UDR_ASSET_UPDATE>
) => {
  // update udrAsset and udrCollection
  const udrGroupUpdate: { [groupId: string]: GroupedBoundingPoly[] } = {};
  for (const [groupId, value] of Object.entries(action.payload.boundingPolys)) {
    const udrList: GroupedBoundingPoly[] = [];

    if (!isNil(value)) {
      value?.series.forEach((item) =>
        udrList.push({
          id: item.id,
          subsegmentId: item.id,
          groupId,
          clusterId: value.clusterId,
          type: 'udr' as const,
          startTimeMs: item.startTimeMs,
          stopTimeMs: item.stopTimeMs,
          boundingPoly: item.object.boundingPoly,
          isDeleted: false,
          isEditable: UDRsPolyAssetStatus.PENDING !== value.status,
          shapeType: value.shapeType,
          redactionCode: value.redactionCode,
          redactionConfig: value.redactionConfig,
          version: value.version,
        })
      );
    }
    udrGroupUpdate[groupId] = udrList;
  }

  let udrCollection = state.udrCollection?.collection; // extract actual collection from wrapper

  //  update the udrCollection
  if (state.udrCollection) {
    udrCollection = updateTimelineCollection(
      state.udrCollection.collection,
      udrGroupUpdate
    );
  } else {
    // must create collection if undefined - consider moving this elsewhere

    // copied from selectMediaDuration since cannot invoke the selector inside the reducer
    const mediaDuration = state.tdo
      ? get(state.tdo, 'primaryAsset.jsondata.mediaDuration') ||
        (new Date(state.tdo.stopDateTime).getTime() -
          new Date(state.tdo.startDateTime).getTime()) /
          1000
      : 0;

    const fps = state.tdo?.details?.veritoneFile?.videoFrameRate;

    // build the collection
    udrCollection = buildTimelineCollection(
      flatten(values(udrGroupUpdate)),
      0,
      mediaDuration,
      1, // 1 second leaf span produces a much smaller tree - faster to pass from worker to main and update
      Date.now(),
      fps
    );
  }

  const newState = {
    ...state,
    udrCollection: udrCollection
      ? new CollectionWrapper(udrCollection)
      : undefined,
    udrsState: {
      ...state.udrsState,
      udrAsset: {
        ...state.udrsState.udrAsset,
        boundingPolys: {
          ...state.udrsState.udrAsset.boundingPolys,
          ...action.payload.boundingPolys,
        },
      },
      localUDRAsset: {
        ...state.udrsState.udrAsset,
        boundingPolys: {
          // TODO: Is this safe? Why is the payload type considered partly readonly?
          ...state.udrsState.udrAsset.boundingPolys,
          ...action.payload.boundingPolys,
        },
      },
    },
  };

  // TODO: Is this safe? Why are some parts readonly?
  return newState as DeepWritable<typeof newState>;
};

const onLoadMediaDataAsset: Re<SaveMediaDataRequest> = (
  state,
  {
    payload: { selectedPolyGroups = {}, audioRedactions = [] }, // , clusterMap = {} },
  }
) => ({
  ...state,
  selectedPolyGroups,
  // clusterMap, // web-worker will send a processed version over for now
  transcriptionView: {
    ...state.transcriptionView,
    redactions: audioRedactions,
  },
});

function getFaceDetectionStatusMessage(
  faceDetectionInfo: FaceDetectionInfo,
  _progress: { faceDetection?: number }
): string {
  const { isFetching, isProcessing } = faceDetectionInfo;
  let statusMessage = '';

  if (isFetching) {
    statusMessage = 'fetchDetAssets';
  } else if (isProcessing) {
    statusMessage = 'procDetAssets';
  }

  return statusMessage;
}

const onFetchFaceDetectionStart: Re<void> = (state) => {
  const newState = {
    ...state,
    faceDetection: {
      ...state.faceDetection,
      isFetching: true,
    },
  };

  return {
    ...newState,
    faceDetection: {
      ...newState.faceDetection,
      statusMessage: getFaceDetectionStatusMessage(
        newState.faceDetection,
        newState.progress
      ),
    },
  };
};

const onFetchFaceDetectionSuccess: Re<void> = (state) => ({
  ...state,
  faceDetection: {
    ...state.faceDetection,
    isFetching: false,
    statusMessage: '',
  },
});

const onFetchFaceDetectionFailure: Re<void> = (state) => ({
  ...state,
  faceDetection: {
    ...state.faceDetection,
    isFetching: false,
    statusMessage: '',
  },
});

const onFaceDetectionProcessingStart: Re<void> = (state) => {
  const newState = {
    ...state,
    faceDetection: {
      ...state.faceDetection,
      isProcessing: true,
    },
  };

  return {
    ...newState,
    faceDetection: {
      ...newState.faceDetection,
      statusMessage: getFaceDetectionStatusMessage(
        newState.faceDetection,
        newState.progress
      ),
    },
  };
};

const onFaceDetectionProcessingStop: Re<void> = (state) => ({
  ...state,
  faceDetection: {
    ...state.faceDetection,
    isProcessing: false,
    statusMessage: '',
  },
});

const onFaceDetectionReady: Re<void> = (state) => ({
  ...state,
  faceDetection: {
    ...state.faceDetection,
    isReady: true,
  },
});

const onSprayPaintSave: Re = (state) => ({
  ...state,
  tdoIsChanged: true,
});

const onDataFetchedForDetectionTypeUpdate: Re<DataFetchedForDetectionType> = (
  state,
  action
) => ({
  ...state,
  dataFetchedForDetectionType: action.payload,
});

const onOutUDRCollection: Re<BoundingPolyTree | undefined> = (
  state,
  action
) => ({
  ...state,
  udrCollection: action.payload
    ? new CollectionWrapper(action.payload)
    : undefined,
});

const onOutSetHighlightedOverlay: Re<HighlightedOverlay> = (state, action) => ({
  ...state,
  highlightedOverlay: action.payload,
});

const onOutChangeShape: Re = (state) => ({
  ...state,
  tdoIsChanged: true,
});

const onOutSetIndividualRedaction: Re = (state) => ({
  ...state,
  tdoIsChanged: true,
});

const onOutChangeCode: Re = (state) => ({
  ...state,
  tdoIsChanged: true,
});

const onSetDetectionTypeRedaction: Re = (state) => ({
  ...state,
  tdoIsChanged: true,
});

const onOutUpdatedClusterAssignment: Re<
  GetActionCreatorPayloadT<typeof OUT_UPDATED_CLUSTER_ASSIGNMENT>
> = (state, action) => {
  const { clusterId, clusterMap } = action.payload;
  const [nextState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.tdoIsChanged = true;

      draft.clusterMap = clusterMap; // later just update modified clusters

      // update cluster/group/segment selections to just the newly merged/unmerged cluster
      draft.clusterMergeSegments = {};

      // highlight/select the merged/unmerged cluster
      const clusterMapItem = draft.clusterMap[clusterId];
      if (clusterMapItem) {
        onSetMergeSelectClusterIdsHelper(
          draft,
          [clusterId],
          true,
          clusterMapItem.detectionGroupIds,
          clusterMapItem.udrGroupIds
        );
      }
    }
  );

  return produce(nextState, (draft) => {
    draft.history.past.push({ patches, inversePatches });
    draft.history.future = [];
  });
};

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(OUT_DETECTION_SERIES_LENGTH, onOutDetectionSeriesLength)
    .addCase(OUT_CLUSTER_MAP, onOutClusterMap)
    .addCase(OUT_DETECTION_CLUSTER_GROUPS, onOutDetectionClusterGroups)
    .addCase(OUT_UDR_CLUSTER_GROUPS, onOutUdrClusterGroups)
    .addCase(
      OUT_DETECTION_CLUSTER_GROUPS_UPDATE,
      onOutDetectionClusterGroupsUpdate
    )

    .addCase(OUT_UDR_CLUSTER_GROUPS_UPDATE, onOutUdrClusterGroupsUpdate)
    .addCase(OUT_PROGRESS, onOutProgress)
    .addCase(OUT_BOUNDING_POLY_COLLECTION, onOutCollection)
    .addCase(OUT_DETECTION_COLLECTION_UPDATE, onOutDetectionCollectionUpdate)
    .addCase(OUT_UDR_COLLECTION, onOutUDRCollection)
    .addCase(OUT_TRANSCRIPTION_COLLECTION, onOutTranscription)
    .addCase(OUT_SELECTED_BOUNDINGPOLY_GROUP, onOutSelectedBoundingPoly)
    .addCase(OUT_UDR_ASSET, onOutUdrAsset)
    .addCase(OUT_UDR_ASSET_UPDATE, onOutUdrAssetUpdate)
    .addCase(NEW_OVERLAY, onChangeOverlay)
    .addCase(DELETE_DETECTION_OVERLAY, onChangeOverlay)
    .addCase(DELETE_DETECTION_IN_FRAME, onChangeOverlay)
    .addCase(DELETE_DETECTION_SEGMENT, onChangeOverlay)
    .addCase(DELETE_DETECTION_GROUP, onChangeOverlay)
    .addCase(DELETE_UDR_OVERLAY, onChangeOverlay)
    .addCase(DELETE_UDR_IN_FRAME, onChangeOverlay)
    .addCase(DELETE_UDR_SEGMENT, onChangeOverlay)
    .addCase(DELETE_UDR_GROUP, onChangeOverlay)
    .addCase(CHANGE_OVERLAY, onChangeOverlay)
    .addCase(
      CHANGE_UDRS_POLY_ASSET_GROUP_SERIES_ITEM,
      onChangeUdrsPolyAssetGroupSeriesItem
    )
    .addCase(CHANGE_UDR_GROUP_LABEL, onChangeUdrGroupLabel)
    .addCase(CHANGE_CLUSTER_LABEL, onChangeClusterUserLabel)
    .addCase(CHANGE_CLUSTER_PINNED, onChangeClusterPinned)
    .addCase(CHANGE_CLUSTER_SEGMENT_SORT_TYPE, onChangeSegmentSortType)
    .addCase(SAVE_MEDIA_DATA_SUCCESS, onSaveTdoAsset)
    .addCase(SET_SAVE_MAX_ATTEMPT_FAILURE, setSaveMaxAttemptFailure)
    .addCase(SET_SAVE_IS_RUNNING, setSaveIsRunning)
    .addCase(LOAD_MEDIA_DATA_ASSET_SUCCESS, onLoadMediaDataAsset)
    .addCase(RESIZE_OVERLAY_SEGMENT, onChangeOverlay)
    .addCase(OUT_FETCH_FACE_DETECTION_START, onFetchFaceDetectionStart)
    .addCase(OUT_FETCH_FACE_DETECTION_SUCCESS, onFetchFaceDetectionSuccess)
    .addCase(OUT_FETCH_FACE_DETECTION_FAILURE, onFetchFaceDetectionFailure)
    .addCase(
      OUT_FACE_DETECTION_PROCESSING_START,
      onFaceDetectionProcessingStart
    )
    .addCase(OUT_FACE_DETECTION_PROCESSING_STOP, onFaceDetectionProcessingStop)
    .addCase(OUT_FACE_DETECTION_READY, onFaceDetectionReady)
    .addCase(ON_SPRAY_PAINT_SAVE, onSprayPaintSave)
    .addCase(
      OUT_DATA_FETCHED_FOR_DETECTION_TYPE,
      onDataFetchedForDetectionTypeUpdate
    )
    .addCase(OUT_SET_HIGHLIGHTED_OVERLAY, onOutSetHighlightedOverlay)
    .addCase(SET_LAST_SAVE_MEDIA_DATA_DISPLAY, setLastSaveMediaDataDisplay)
    .addCase(CHANGE_SHAPE, onOutChangeShape)
    .addCase(SET_DETECTION_TYPE_REDACTION, onSetDetectionTypeRedaction)
    .addCase(SET_INDIVIDUAL_REDACTION, onOutSetIndividualRedaction)
    .addCase(CHANGE_CODE, onOutChangeCode)
    .addCase(OUT_UPDATED_CLUSTER_ASSIGNMENT, onOutUpdatedClusterAssignment);
});

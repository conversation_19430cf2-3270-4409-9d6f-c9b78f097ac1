import { Re } from '../reducers';
import { TDOId } from '@cbsa-modules/universal';
import { FETCH_MEDIA_RESPONSE } from '../services/queries/fetchMedia';

export const fetchMedia: Re = (state) => ({
  ...state,
  media: {},
  loaders: {
    ...state.loaders,
    isFetchingMedia: true,
  },
});

export const fetchMediaSuccess: Re<{
  readonly folder: FETCH_MEDIA_RESPONSE['folder'];
}> = (state, { payload }) => {
  const media: {
    [id: TDOId]: FETCH_MEDIA_RESPONSE['folder']['childTDOs']['records']['0'];
  } = {};
  (payload.folder?.childTDOs?.records || []).forEach((mediaData) => {
    media[mediaData.id] = mediaData;
  });
  return {
    ...state,
    media,
    loaders: {
      ...state.loaders,
      isFetchingMedia: false,
    },
  };
};

export const fetchMediaFailure: Re = (state) => ({
  ...state,
  loaders: {
    ...state.loaders,
    isFetchingMedia: false,
  },
});

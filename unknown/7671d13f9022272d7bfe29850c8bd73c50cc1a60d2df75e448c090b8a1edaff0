import express from 'express';
import { isEmpty, pick } from 'lodash';
import { IngestMediaRequest, RequestHeader } from '../model/requests';
import { createCaseAdapter } from '../adapters/createCase';
import { Messages } from '../errors/messages';
import { ingestMediaAdapter } from '../adapters/ingestMedia';
import { StatusCodes } from '../errors/statusCodes';
import { getRootFolderAdapter } from '../adapters/getRootFolder';
import { checkNameExistsAdapter } from '../adapters/checkNameExists';
import { checkFolderExistsAdapter } from '../adapters/checkFolderExists';
import { getUserInfoByTokenAdapter } from '../adapters/getUserInfoByToken';
import { isAPITokenKey, isCaseId, isFolderId, isSessionToken, isValidString } from '../validations/helpers';
import { getUserInfoAdapter } from '../adapters/getUserInfo';
import { CaseId, FolderId } from '../model/brands';

const ingestMedias = async (headers: RequestHeader, caseId: CaseId, email: string, req: IngestMediaRequest) => {
  const ingestedJobs: NonNullable<Awaited<ReturnType<typeof ingestMediaAdapter>>>[] = [];
  for (const url of req.urls) {
    const jobResponse = await ingestMediaAdapter(headers, {
      url: url,
      caseId: caseId,
      runDetection: req.runDetection,
      runHeadDetection: req.runHeadDetection,
      runPersonDetection: req.runPersonDetection,
      runTranscription: req.runTranscription,
      email: email,
      externalIds: req.externalIds
    });

    if (jobResponse) {
      ingestedJobs.push(jobResponse);
    }
  }
  return ingestedJobs;
};

const isValidUrls = (urls: string[]): boolean => {
  for (const url of urls) {
    try {
      new URL(url);
    } catch (_error) {
      return false;
    }
  }
  return true;
}

export const ingestMedia = async (
  req: express.Request,
  res: express.Response
) => {
  const { existingCaseId, createNewCase, urls } = req.body as IngestMediaRequest;
  let caseId: CaseId | undefined = undefined;
  let rootFolderId: FolderId | undefined = undefined;
  let isNameExist = false;
  const headers = pick(req.headers, ['authorization']);

  if (!(urls?.length > 0)) {
    return res.status(StatusCodes.BadRequest).json({
      error: Messages.urlRequired,
    });
  }
  if (!isValidUrls(urls)) {
    return res.status(StatusCodes.BadRequest).json({
      error: Messages.inValidUrls,
    });
  }


  let userInfo = undefined;
  if (isAPITokenKey(headers)) {
    userInfo = await getUserInfoByTokenAdapter(headers);
    if (userInfo?.error) {
      return res.status(StatusCodes.BadRequest).json({
        error: userInfo.error,
      });
    }
    if (isEmpty(userInfo?.userId)) {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.FetchUserInfoFail,
      });
    }
  }

  if (isSessionToken(headers)) {
    userInfo = await getUserInfoAdapter(headers);
    if (isEmpty(userInfo?.email)) {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.getUserInfoFail,
      });
    }
  }

  if(isCaseId(existingCaseId)) {
    const folder = await checkFolderExistsAdapter( headers, { folderId: existingCaseId} );
    const isCase = (folder?.contentTemplates?.length || 0) > 0;
    if (!isCase) {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.InvalidExistingCaseId,
      });
    }
    caseId = existingCaseId;
  } else {
    if (!createNewCase || !isValidString(createNewCase.name)) {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.requestIdOrRequestDetailRequired,
      });
    }
    const { name, description, parentFolderId } = createNewCase;
    if (!isFolderId(parentFolderId)) {
      rootFolderId = (await getRootFolderAdapter(headers)) as FolderId | undefined;
      if (!isFolderId(rootFolderId)) {
        return res.status(StatusCodes.BadRequest).json({
          error: Messages.fetchRootFolderError,
        });
      }
      isNameExist = await checkNameExistsAdapter(headers, { folderId: rootFolderId, name: name});
    }
    else {
      const folder = await checkFolderExistsAdapter( headers, { folderId: parentFolderId} );
      if (folder?.id !== parentFolderId) {
        return res.status(StatusCodes.BadRequest).json({
          error: Messages.ParentFolderNotExists
        });
      }

      const isCase = (folder.contentTemplates?.length || 0) > 0;
      if (isCase) {
        return res.status(StatusCodes.BadRequest).json({
            error: Messages.InvalidParentFolderId,
          });
      }

      const isFolderDepthInLimit = (folder.folderPath?.length || 0) < 4;
        if (!isFolderDepthInLimit ) {
          return res.status(StatusCodes.BadRequest).json({
            error: Messages.CaseDepthLimitReached,
          });
      }

      isNameExist = await checkNameExistsAdapter(headers, { folderId: parentFolderId, name: name});
    }
    if (isNameExist) {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.NameExistsAlready,
      });
    }

    caseId = await createCaseAdapter(headers, {
      userId: userInfo?.userId,
      name: name,
      description: description || name,
      parentFolderId: parentFolderId || rootFolderId
    }) as CaseId;

    if (!isCaseId(caseId)) {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.createCaseFail,
      });
    }
  }

  const ingestedJobs = await ingestMedias(headers, caseId, userInfo?.email ,req.body);

  return ingestedJobs?.length > 0 ?
    res.status(StatusCodes.Success).json({
      caseId: caseId,
      ingestedJobs: ingestedJobs
    }):
    res.status(StatusCodes.BadRequest).json({
      error: Messages.ingestMediaFail
    });
  }
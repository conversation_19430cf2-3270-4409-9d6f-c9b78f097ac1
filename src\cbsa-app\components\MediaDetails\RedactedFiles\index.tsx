import {
  <PERSON><PERSON>,
  <PERSON>ir<PERSON>Progress,
  Grid2 as <PERSON>rid,
  Theme<PERSON><PERSON><PERSON>,
  StyledEngineProvider,
  <PERSON>lt<PERSON>,
  DialogTitle,
  Dialog,
  DialogActions,
} from '@mui/material';
import * as React from 'react';
import Alert from '@mui/material/Alert';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Player } from 'video-react';
import { connect, ConnectedProps } from 'react-redux';

import {
  DELETE_LAST_ASSET,
  DOWNLOAD_ASSET,
  goToResultsTab,
  local,
  PLAY_REDACTED_ASSET,
  selectRedactedAssetStarted,
  selectTdo,
  selectContentTypeHasVideo,
  UPDATE_REDACTED_FILE_METRICS,
  SEND_REDACTED_FILE_TO_GOVQA,
  SEND_REDACTED_FILE_TO_FOIAXPRESS,
  SEND_REDACTED_FILE_TO_CASEPOINT,
  selectGovQAState,
  patchGovQAState,
  selectFOIAXpressState,
  patchFOIAXpressState,
  selectCasepointState,
  patchCasepointState,
} from '@common-modules/mediaDetails';

import { buttonTheme } from '@cbsa/styles/materialThemes';
import ControlBar from '@common-components/ControlBar';
import DeleteRedactionModal from './DeleteRedactionModal';

import { I18nTranslate } from '@common/i18n';
import * as styles from './styles.scss';
import getLastRedactedFile from '@helpers/getLastRedactedFile';

const maxFilenameLength = 40;

interface GovQAState {
  isFormShown: boolean;
  sessionId: string;
  errorMessage?: string;
  isSendingFile: boolean;
  isStatusDialogShown: boolean;
  authErrorMessage?: string;
  successMessage?: string;
}

interface FOIAXpressState {
  isFormShown: boolean;
  sessionId: string;
  errorMessage?: string;
  isSendingFile: boolean;
  isStatusDialogShown: boolean;
  authErrorMessage?: string;
  successMessage?: string;
}

interface CasepointState {
  isFormShown: boolean;
  sessionId: string;
  errorMessage?: string;
  isSendingFile: boolean;
  isStatusDialogShown: boolean;
  authErrorMessage?: string;
  successMessage?: string;
}

interface Props extends PropsFromRedux {
  govQA: GovQAState;
  foiaXpress: FOIAXpressState;
  casepoint: CasepointState;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
class RedactedFiles extends React.Component<Props> {
  state = {
    deleteRedactionModalOpen: false,
    govQAForm: {},
    foiaXpressForm: {},
    casepointForm: {},
  };

  componentDidMount() {
    // subscribe state change
    if (this.videoPlayerRef.current && !this.subscribeToStateChange) {
      this.subscribeToStateChange = true;
      this.videoPlayerRef.current.subscribeToStateChange(
        this.handleStateChange.bind(this)
      );
    }
  }

  componentDidUpdate() {
    const { asset, onGoToResultsTab } = this.props;
    if (!asset?.details) {
      onGoToResultsTab?.();
    }
    if (this.videoPlayerRef.current && !this.subscribeToStateChange) {
      this.subscribeToStateChange = true;
      this.videoPlayerRef.current.subscribeToStateChange(
        this.handleStateChange.bind(this)
      );
    }
  }

  onFormCancel = () => {
    this.setState({
      govQAForm: {},
      foiaXpressForm: {},
      casepointForm: {},
    });

    this.props.patchGovQAState?.({ isFormShown: false, authErrorMessage: '' });
    this.props.patchFOIAXpressState?.({
      isFormShown: false,
      authErrorMessage: '',
    });
    this.props.patchCasepointState?.({
      isFormShown: false,
      authErrorMessage: '',
    });
  };

  onStatusDialogConfirm = () => {
    this.props.patchGovQAState({ isStatusDialogShown: false });
    this.props.patchFOIAXpressState({ isStatusDialogShown: false });
    this.props.patchCasepointState({ isStatusDialogShown: false });
  };

  subscribeToStateChange = false;
  videoPlayerRef = React.createRef<Player>();

  handleStateChange<S extends Record<string, any>>(state: S, prevState: S) {
    // copy player state to this component's state
    if (
      !this.props.redactedAssetStarted &&
      state.hasStarted &&
      !prevState.hasStarted
    ) {
      this.props.actionPlayRedactedAsset();
      this.props.actionUpdateFileRedactedMetrics();
    }
  }

  showDeleteRedactionModal = () => {
    this.setState({ deleteRedactionModalOpen: true });
  };

  closeDeleteRedactionModal = () => {
    this.setState({ deleteRedactionModalOpen: false });
  };

  deleteLastAsset = () => {
    if (this.props.asset?.id) {
      this.props.actionDeleteLastAsset(this.props.asset.id);
    }

    this.setState({ deleteRedactionModalOpen: false });
  };

  downloadLastAsset = () => {
    this.props.actionDownloadAsset();
  };

  sendRedactedFileToGovQaWithAuth = () => {
    this.props.actionSendRedactedFileToGovQa(this.state.govQAForm);

    this.props.patchGovQAState({ isFormShown: false, errorMessage: '' });

    this.setState({
      govQAForm: {},
    });
  };

  onSendToGovQaClick = () => {
    // if (this.props.govQA.sessionId) {
    // if (1 === 1) {
    this.props.actionSendRedactedFileToGovQa();
    // } else {
    //   this.props.patchGovQAState({ isFormShown: true });
    // }
  };

  sendRedactedFileToFOIAXpressWithAuth = () => {
    this.props.actionSendRedactedFileToFOIAXpress(this.state.foiaXpressForm);

    this.props.patchFOIAXpressState({ isFormShown: false, errorMessage: '' });

    this.setState({
      foiaXpressForm: {},
    });
  };

  onSendToFOIAXpressClick = () => {
    this.props.actionSendRedactedFileToFOIAXpress();
  };

  sendRedactedFileToCasepointWithAuth = () => {
    this.props.actionSendRedactedFileToCasepoint(this.state.casepointForm);

    this.props.patchCasepointState({ isFormShown: false, errorMessage: '' });

    this.setState({
      casepointForm: {},
    });
  };

  onSendToCasepointClick = () => {
    this.props.actionSendRedactedFileToCasepoint();
  };

  getFilenameElement(originFilename: string) {
    const isNameTruncated = originFilename.length > maxFilenameLength;

    if (isNameTruncated) {
      return (
        <Tooltip title={originFilename} classes={{ tooltip: styles.tooltip }}>
          <div className={styles.filename} data-veritone-element={'filename'}>
            {originFilename.substring(0, maxFilenameLength) + '...'}
          </div>
        </Tooltip>
      );
    } else {
      return (
        <div className={styles.filename} data-veritone-element={'filename'}>
          {originFilename}
        </div>
      );
    }
  }

  render() {
    const { asset, isAudioData, downloadStarted, tdo, thumbnailUrl } =
      this.props;

    if (!tdo || !asset?.details) {
      return <div />;
    }

    const filenameElement = this.getFilenameElement(tdo.name);

    const { metrics } = asset.details;
    const modifiedDateTime = new Date(asset.modifiedDateTime);

    const deleteButtonStyle: React.CSSProperties = {
      border: '1px solid #6C7C84',
      color: '#cfd8dc',
      textTransform: 'uppercase',
    };
    if (downloadStarted) {
      deleteButtonStyle.border = '1px solid rgb(91, 111, 126)';
      deleteButtonStyle.color = 'rgb(91, 111, 126)';
      deleteButtonStyle.textTransform = 'uppercase';
    }

    const downloadButtonStyle: React.CSSProperties = {
      marginLeft: 11.5,
      textTransform: 'uppercase',
    };
    if (downloadStarted) {
      downloadButtonStyle.background = '#23557c';
      downloadButtonStyle.color = '#5b6f7e';
    }

    const hasGovQaRequestId = !!tdo.details.govQARequestId;
    const hasFOIAXpressRequestId = !!tdo.details.foiaXpressRequestId;
    const hasCasepointRequestId = !!tdo.details.casepointRequestId;

    return (
      <Grid
        container
        direction={'row'}
        spacing={2}
        style={{ paddingTop: 12 }}
        data-veritone-component="redacted-file-tab"
      >
        <Grid size={{ xs: 4 }}>
          <Player
            ref={this.videoPlayerRef}
            src={asset.signedUri}
            poster={thumbnailUrl}
            aspectRatio={'16:9'}
            style={{ 'video-react-video': { position: 'absolute !important' } }}
          >
            <BigPlayButton
              position="center"
              className={styles.mediaPlayButton}
            />
            <ControlBar autoHide className={styles.controlBar} />
          </Player>
        </Grid>
        <Grid size={{ xs: 8 }}>
          <Grid container direction={'row'} alignItems={'center'} spacing={2}>
            <Grid size={{ xs: 6 }}>
              <div>
                <div className={styles.lastRedactedFiles}>
                  {I18nTranslate.TranslateMessage('latestRedactedFile')}
                </div>
                {filenameElement}
                <div
                  className={styles.modifiedDateTime}
                  data-veritone-element={'modifiedDateTime'}
                >
                  {I18nTranslate.TranslateDate(modifiedDateTime)}
                </div>
              </div>
            </Grid>
            <div className={styles.wrapper}>
              {!isAudioData && (
                <div>
                  <div className={styles.metricWrapper}>
                    <span
                      className={'icon-face'}
                      style={{ color: '#B0BEC5', fontSize: '20px' }}
                    />
                  </div>
                  <div
                    className={styles.metricValueLabel}
                    data-veritone-element={'metrics-faces'}
                  >
                    {metrics.faces}
                  </div>
                  <span className={styles.separator} />
                  <div className={styles.metricWrapper}>
                    <span
                      className={'icon-user-defined-edit'}
                      style={{ color: '#B0BEC5', fontSize: '20px' }}
                    />
                  </div>
                  <div
                    className={styles.metricValueLabel}
                    data-veritone-element={'metrics-udrs'}
                  >
                    {metrics.udrs}
                  </div>
                  <span className={styles.separator} />
                </div>
              )}

              <div className={styles.metricWrapper}>
                <span
                  className={'icon-visibility'}
                  style={{ color: '#B0BEC5', fontSize: '20px' }}
                />
              </div>
              <div
                className={styles.metricValueLabel}
                data-veritone-element={'metrics-views'}
              >
                {metrics.views}
              </div>
              <span className={styles.separator} />
              <div className={styles.metricWrapper}>
                <span
                  className={'icon-file_download'}
                  style={{ color: '#B0BEC5', fontSize: '20px' }}
                />
              </div>
              <div
                className={styles.metricValueLabel}
                data-veritone-element={'metrics-downloads'}
              >
                {metrics.downloads}
              </div>
            </div>
          </Grid>
          <Grid size={{ xs: 12 }}>
            <StyledEngineProvider injectFirst>
              <ThemeProvider theme={buttonTheme}>
                <div className={styles.buttonWrapper}>
                  <Button
                    className={styles.deleteButton}
                    variant="outlined"
                    color="primary"
                    disabled={downloadStarted}
                    onClick={this.showDeleteRedactionModal}
                    style={deleteButtonStyle}
                    data-veritone-element="redacted-files-delete-button"
                  >
                    {I18nTranslate.TranslateMessage('delete')}
                  </Button>
                  {this.state.deleteRedactionModalOpen && (
                    <DeleteRedactionModal
                      open
                      onClose={this.closeDeleteRedactionModal}
                      onSubmit={this.deleteLastAsset}
                    />
                  )}
                  <Button
                    className={styles.downloadButton}
                    variant="contained"
                    color="primary"
                    style={downloadButtonStyle}
                    disabled={downloadStarted}
                    onClick={this.downloadLastAsset}
                    data-veritone-element="redacted-files-download-button"
                  >
                    {I18nTranslate.TranslateMessage('download')}
                    {downloadStarted && (
                      <CircularProgress
                        size={23.85}
                        color={'secondary'}
                        className={styles.downloadSpinner}
                      />
                    )}
                  </Button>
                  {hasGovQaRequestId ? (
                    <Button
                      className={styles.downloadButton}
                      variant="contained"
                      color="primary"
                      style={{ ...downloadButtonStyle, width: 150 }}
                      onClick={this.onSendToGovQaClick}
                      data-veritone-element="redacted-files-govqa-button"
                      disabled={this.props.govQA.isSendingFile}
                    >
                      {I18nTranslate.TranslateMessage('sendToGovQa')}
                      {this.props.govQA.isSendingFile && (
                        <CircularProgress
                          size={23.85}
                          color={'secondary'}
                          className={styles.downloadSpinner}
                        />
                      )}
                    </Button>
                  ) : null}
                  {hasFOIAXpressRequestId ? (
                    <Button
                      className={styles.downloadButton}
                      variant="contained"
                      color="primary"
                      style={{
                        ...downloadButtonStyle,
                        width: 150,
                        lineHeight: '15px',
                      }}
                      onClick={this.onSendToFOIAXpressClick}
                      data-veritone-element="redacted-files-foiaxpress-button"
                      disabled={this.props.foiaXpress.isSendingFile}
                    >
                      {I18nTranslate.TranslateMessage('sendToFOIAXpress')}
                      {this.props.foiaXpress.isSendingFile && (
                        <CircularProgress
                          size={23.85}
                          color={'secondary'}
                          className={styles.downloadSpinner}
                        />
                      )}
                    </Button>
                  ) : null}
                  {hasCasepointRequestId ? (
                    <Button
                      className={styles.downloadButton}
                      variant="contained"
                      color="primary"
                      style={{
                        ...downloadButtonStyle,
                        width: 150,
                        lineHeight: '15px',
                      }}
                      onClick={this.onSendToCasepointClick}
                      data-veritone-element="redacted-files-casepoint-button"
                      disabled={this.props.casepoint.isSendingFile}
                    >
                      {I18nTranslate.TranslateMessage('sendToCasepoint')}
                      {this.props.casepoint.isSendingFile && (
                        <CircularProgress
                          size={23.85}
                          color={'secondary'}
                          className={styles.downloadSpinner}
                        />
                      )}
                    </Button>
                  ) : null}
                </div>
              </ThemeProvider>
            </StyledEngineProvider>
          </Grid>
        </Grid>

        <Dialog
          aria-labelledby="simple-dialog-title"
          open={this.props.govQA.isFormShown}
        >
          <DialogTitle>
            {I18nTranslate.TranslateMessage('GovQAAuthentication')}
          </DialogTitle>
          {this.props.govQA.authErrorMessage ? (
            <Alert style={{ width: 384 }} severity="error">
              {this.props.govQA.authErrorMessage}
            </Alert>
          ) : null}
          <DialogActions>
            <Button onClick={this.onFormCancel} color="primary">
              {I18nTranslate.TranslateMessage('cancel')}
            </Button>
            <Button
              onClick={this.sendRedactedFileToGovQaWithAuth}
              color="primary"
              autoFocus
            >
              {I18nTranslate.TranslateMessage('ok')}
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          aria-labelledby="simple-dialog-title"
          open={this.props.govQA.isStatusDialogShown}
        >
          {this.props.govQA.errorMessage && (
            <Alert style={{ width: 384 }} severity="error">
              {this.props.govQA.errorMessage}
            </Alert>
          )}
          {this.props.govQA.successMessage && (
            <Alert style={{ width: 384 }} severity="success">
              {this.props.govQA.successMessage}
            </Alert>
          )}

          <DialogActions>
            <Button
              onClick={this.onStatusDialogConfirm}
              color="primary"
              autoFocus
            >
              {I18nTranslate.TranslateMessage('ok')}
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          aria-labelledby="simple-dialog-title"
          open={this.props.foiaXpress.isFormShown}
        >
          <DialogTitle>FOIAXpress Authentication</DialogTitle>
          {this.props.foiaXpress.authErrorMessage ? (
            <Alert style={{ width: 384 }} severity="error">
              {this.props.foiaXpress.authErrorMessage}
            </Alert>
          ) : null}
          <DialogActions>
            <Button onClick={this.onFormCancel} color="primary">
              {I18nTranslate.TranslateMessage('cancel')}
            </Button>
            <Button
              onClick={this.sendRedactedFileToFOIAXpressWithAuth}
              color="primary"
              autoFocus
            >
              {I18nTranslate.TranslateMessage('ok')}
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          aria-labelledby="simple-dialog-title"
          open={this.props.foiaXpress.isStatusDialogShown}
        >
          {this.props.foiaXpress.errorMessage && (
            <Alert style={{ width: 384 }} severity="error">
              {this.props.foiaXpress.errorMessage}
            </Alert>
          )}
          {this.props.foiaXpress.successMessage && (
            <Alert style={{ width: 384 }} severity="success">
              {this.props.foiaXpress.successMessage}
            </Alert>
          )}

          <DialogActions>
            <Button
              onClick={this.onStatusDialogConfirm}
              color="primary"
              autoFocus
            >
              {I18nTranslate.TranslateMessage('ok')}
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          aria-labelledby="simple-dialog-title"
          open={this.props.casepoint.isFormShown}
        >
          <DialogTitle>Casepoint Authentication</DialogTitle>
          {this.props.casepoint.authErrorMessage ? (
            <Alert style={{ width: 384 }} severity="error">
              {this.props.casepoint.authErrorMessage}
            </Alert>
          ) : null}
          <DialogActions>
            <Button onClick={this.onFormCancel} color="primary">
              {I18nTranslate.TranslateMessage('cancel')}
            </Button>
            <Button
              onClick={this.sendRedactedFileToCasepointWithAuth}
              color="primary"
              autoFocus
            >
              {I18nTranslate.TranslateMessage('ok')}
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          aria-labelledby="simple-dialog-title"
          open={this.props.casepoint.isStatusDialogShown}
        >
          {this.props.casepoint.errorMessage && (
            <Alert style={{ width: 384 }} severity="error">
              {this.props.casepoint.errorMessage}
            </Alert>
          )}
          {this.props.casepoint.successMessage && (
            <Alert style={{ width: 384 }} severity="success">
              {this.props.casepoint.successMessage}
            </Alert>
          )}

          <DialogActions>
            <Button
              onClick={this.onStatusDialogConfirm}
              color="primary"
              autoFocus
            >
              {I18nTranslate.TranslateMessage('ok')}
            </Button>
          </DialogActions>
        </Dialog>
      </Grid>
    );
  }
}
const mapState = (state: any) => ({
  asset: getLastRedactedFile(selectTdo(state)),
  auditLogs: local(state).auditLogs,
  downloadStarted: local(state).downloadStarted,
  isAudioData: !selectContentTypeHasVideo(state),
  pollingDownloadDone: local(state).pollingDownloadDone,
  redactedAssetStarted: selectRedactedAssetStarted(state),
  tdo: selectTdo(state),
  thumbnailUrl: local(state).tdo?.thumbnailUrl,
  govQA: selectGovQAState(state),
  foiaXpress: selectFOIAXpressState(state),
  casepoint: selectCasepointState(state),
});
const mapDispatch = (dispatch: (to_dispatch: any) => unknown) => ({
  onGoToResultsTab: () => goToResultsTab(),
  actionPlayRedactedAsset: () => dispatch(PLAY_REDACTED_ASSET()),
  actionUpdateFileRedactedMetrics: () =>
    dispatch(UPDATE_REDACTED_FILE_METRICS()),
  actionDownloadAsset: () => dispatch(DOWNLOAD_ASSET()),
  actionSendRedactedFileToGovQa: (auth?: Record<string, any>) =>
    dispatch(SEND_REDACTED_FILE_TO_GOVQA(auth)),
  actionSendRedactedFileToFOIAXpress: (auth?: Record<string, any>) =>
    dispatch(SEND_REDACTED_FILE_TO_FOIAXPRESS(auth)),
  actionSendRedactedFileToCasepoint: (auth?: Record<string, any>) =>
    dispatch(SEND_REDACTED_FILE_TO_CASEPOINT(auth)),
  actionDeleteLastAsset: (id: string) => dispatch(DELETE_LAST_ASSET({ id })),
  patchGovQAState: (govQAPartialState: Partial<GovQAState>) =>
    dispatch(patchGovQAState(govQAPartialState)),
  patchFOIAXpressState: (foiaXpressPartialState: Partial<FOIAXpressState>) =>
    dispatch(patchFOIAXpressState(foiaXpressPartialState)),
  patchCasepointState: (casepointPartialState: Partial<CasepointState>) =>
    dispatch(patchCasepointState(casepointPartialState)),
});

// TODO: Fix
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const connector = connect(mapState, mapDispatch);
type PropsFromRedux = ConnectedProps<typeof connector>;

// export default connector(RedactedFiles);

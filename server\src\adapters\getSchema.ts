import { LastestSchemaResponse } from '../model/responses';
import { callGQL } from '../api/callGraphql';
import { fetchLatestSchemaQuery } from '../api/queries';
import { RequestHeader } from '../model/requests';
import { Logger } from '../logger';
import { Messages } from '../errors/messages';

export const getSchemaIdAdapter = async (headers: RequestHeader, dataRegistryId: string) => {
  const query = fetchLatestSchemaQuery(dataRegistryId);
  const schemaId = await callGQL<LastestSchemaResponse>(headers, query)
    .then((res) => res.dataRegistry?.publishedSchema?.id)
    .catch((err) => {
      Logger.error(Messages.fetchSchemaError + JSON.stringify(err));
    });
  return schemaId;
};

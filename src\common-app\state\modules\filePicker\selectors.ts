import { isNaN, mean } from 'lodash';
import { createSelector } from 'reselect';
import { DefaultStateTypes, namespace } from './index';

const selectStore = (s: { [namespace]: DefaultStateTypes }) => s[namespace];

export const selectPickerState = (id: string) =>
  createSelector(selectStore, (s) => s[id]?.state || 'selecting');

export const selectPickerProgressPercent = (id: string) =>
  createSelector(selectStore, (s) => {
    const currentProgress = s[id]?.progressPercentByFileKey;
    if (!currentProgress) {
      return 0;
    }

    const meanProgress = mean(Object.values(currentProgress));
    const rounded = Math.round(meanProgress);
    return isNaN(rounded) ? 0 : rounded;
  });

export const selectPickerSuccess = (id: string) =>
  createSelector(selectStore, (s) => s[id]?.success);

export const selectPickerError = (id: string) =>
  createSelector(selectStore, (s) => s[id]?.error);

export const selectPickerWarning = (id: string) =>
  createSelector(selectStore, (s) => s[id]?.warning);

export const selectPickerStatusMessage = (id: string) =>
  createSelector(selectStore, (s) => s[id]?.warning ?? s[id]?.error ?? '');

import { callGQL } from '../api/callGraphql';
import { Messages } from '../errors/messages';
import { IngestJobRequest, RequestHeader } from '../model/requests';
import { createIngestJobQuery } from '../api/queries';
import {
  CreateJobFail,
  CreateJobSuccess,
  IngestJobResponse,
} from '../model/responses';
import { Logger } from '../logger';
import { getAppConfig } from './getAppConfig';

export const createIngestJob = async (
  headers: RequestHeader,
  request: IngestJobRequest,
  retrycount = 0
): Promise<CreateJobSuccess | CreateJobFail> => {
  const maxRetryCount = 3;
  try {
    const detections = {
      head: request.runHeadDetection ?? request.runDetection,
      person: request.runPersonDetection,
    };
    const { transcriptionEngineId, transcriptionEngineOptions, featureFlags } = await getAppConfig(headers);
    const query = createIngestJobQuery(
      detections,
      request.runTranscription,
      transcriptionEngineId,
      transcriptionEngineOptions,
      featureFlags
    );
    const variables = {
      id: request.tdoId,
      tdoName: request.tdoId,
      payload: {
        targetId: request.tdoId,
        url: request.url,
        user: request.email,
        chunkSize: 10000000,
        zeropadOffsetAudio: true,
      },
    };
    const response = await callGQL<IngestJobResponse>(
      headers,
      query,
      variables
    );
    return response?.createJob;
  } catch (_err) {
    if (retrycount <= maxRetryCount) {
      return await createIngestJob(headers, request, retrycount + 1);
    } else {
      Logger.error(Messages.createJobFail);
      return {
        targetId: request.tdoId,
        status: Messages.createJobFailForTdo,
        // status: _err instanceof Error ? _err.message : Messages.createJobFail,
      };
    }
  }
};

import {
  take,
  takeEvery,
  all,
  fork,
  put,
  select,
  takeLatest,
} from 'typed-redux-saga/macro';
import { videoActions } from 'video-react';
import {
  actionFullLoadPlayer,
  actionUpdateSessionCurrentTime,
  selectPlayer,
  selectSessionCurrentTime,
} from './index';

export function* videoReactSaga() {
  yield* all([fork(watchProgressChange), fork(watchResetPlayerStore)]);
}

function* watchProgressChange() {
  yield* takeLatest('video-react/PROGRESS_CHANGE', reSeek);
}
function* reSeek() {
  const sessionCurrentTime = yield* select(selectSessionCurrentTime);
  const state = yield* select(selectPlayer);

  if (sessionCurrentTime) {
    yield* put(
      videoActions.handleTimeUpdate({
        ...state,
        currentTime: sessionCurrentTime / 1000,
      })
    );
    yield* put(actionFullLoadPlayer(true));
  }
}

function* watchResetPlayerStore() {
  yield* takeEvery('player/RESET_PLAYER_STORE', onResetPlayerStore);
}
function* onResetPlayerStore() {
  yield* take(actionUpdateSessionCurrentTime);
  yield* put(actionUpdateSessionCurrentTime(0));
}

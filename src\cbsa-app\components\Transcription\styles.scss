@use 'sass:color';

.transcription {
  padding: 0 15px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-basis: auto;
  height: 100%;

  > div {
    > span {
      user-select: none;
      display: flex;
      flex-shrink: 0;
      color: #ccc;
      font-size: 14px;
      justify-content: center;
      align-items: center;
      height: 26px;
      margin: 2px 0;
      padding: 0 4px;
      cursor: pointer;
      //      &:hover {
      //        color: color.mix(#4a90e2, #fff, 40%);
      //        background: color.mix(#4a90e2, #182025, 40%);
      //      }
    }
  }
}

.vList {
  div {
    padding: 0 15px;
    > span {
      user-select: none;
      display: inline-block;
      color: #ccc;
      font-size: 14px;
      height: 26px;
      margin: 2px 0;
      padding: 0 4px;
      cursor: pointer;
      &:hover {
        color: color.mix(#4a90e2, #fff, 40%);
        background: color.mix(#4a90e2, #182025, 40%);
      }
    }
  }
}

.transcriptionSearch {
  padding: 0 16px;
}

.transcriptionDisplay {
  padding: 0 16px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  overflow: auto;
  height: 100%;
  align-content: flex-start;
}

.redacted {
  color: #444 !important;
  background: #000 !important;
  &:hover {
    color: color.mix(#4a90e2, #444, 40%) !important;
    background: color.mix(#4a90e2, #000, 40%) !important;
  }
}

.focused {
  color: color.mix(#4a90e2, #fff, 70%) !important;
  background: color.mix(#4a90e2, #182025, 70%) !important;
  &:hover {
    color: color.mix(#4a90e2, #fff, 60%) !important;
    background: color.mix(#4a90e2, #182025, 80%) !important;
  }
}

.searchHighlight {
  color: black !important;
  background: #fff9c4bb !important;
  &.searchFocus {
    background: #fff9c4 !important;
  }
  &:hover {
    background: color.mix(#4a90e2, #fff9c4, 50%) !important;
  }
}

.tracking {
  color: #f9a02c !important;
  background: #f9a02c22 !important;
  text-shadow: 0 0 0 #f9a02c !important;
  &.focused {
    background: color.mix(#4a90e2, #f9a02c22, 40%) !important;
  }
  &:hover {
    color: color.mix(#4a90e2, #f9a02c, 40%) !important;
    text-shadow: 0 0 0 color.mix(#4a90e2, #f9a02c, 40%) !important;
    background: color.mix(#4a90e2, #f9a02c22, 40%) !important;
  }
}

.searchUnderline::before {
  border-bottom-color: rgb(255, 255, 255, 0.15) !important;
}

.progress {
  color: #2196f3;
}

### Steps to create comments schema using core-graphql queries

1. Create new data registry 
```
mutation createDataRegistry {
  createDataRegistry(input: {
    id: "88c0b643-5504-452e-9f31-0f13344892ad"
    name: "TDO Lock"
    description: "Locks a TDO for user by one user at a time."
    source: "field deprecated"
  }) {
    id
  }
}
```

2. Create Schema Draft
```
# Note: Use the `dataRegistryId` value from the `createDataRegistry` mutation.
mutation createSchemaDraft {
  upsertSchemaDraft(input: {
    dataRegistryId: "88c0b643-5504-452e-9f31-0f13344892ad"
    majorVersion: 1,
    schema: {
      type: "object",
      title: "tdo-lock",
      required: [
        "name",
        "tdoId",
        "userId",
        "lastAccessed"
      ],
      properties: {
        name: {
          type: "string"
        },
        tdoId: {
          type: "string"
        },
        userId: {
          type: "string"
        },
        lastAccessed: {
          type: "dateTime"
        }        
      },
      description: "TDO Lock"
    }
  }) {
    id
    majorVersion
    minorVersion
    status
    definition
  }
}
```

3. Publish Schema Draft

```
# Note: Pass in the Schema ID for the `id` value
mutation publishSchemaDraft($id: ID!) {
  updateSchemaState(input: {
    id: $id
    status: published
  }) {
    id
    majorVersion
    minorVersion
    status
  }
}
```


import { useEffect, useState } from 'react';
import {
  AccordionSummary as MuiAccordionSummary,
  AccordionDetails as MuiAccordionDetails,
  AccordionProps,
  AccordionSummaryProps,
  Skeleton,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import MuiAccordion from '@mui/material/Accordion';
import { ArrowForwardIosSharp } from '@mui/icons-material';
import { ArrayOrSingle } from 'ts-essentials';

const AccordionContainer = styled((props: AccordionProps) => (
  <MuiAccordion elevation={0} square {...props}>
    {props.children}
  </MuiAccordion>
))(() => ({
  display: 'flex',
  maxHeight: '25%',
  overflow: 'hidden',
  flexDirection: 'column',
  borderBottom: '.5px solid #003D7E4D',
  '&.Mui-expanded': {
    margin: '0',
  },
  '&:last-of-type': {
    borderBottom: 0,
  },
  '&.MuiAccordion-root:before': {
    height: 0,
  },
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={
      <ArrowForwardIosSharp fontSize="small" style={{ fill: '#4E4E4E' }} />
    }
    {...props}
  />
))(() => ({
  gap: '10px',
  height: '60px',
  color: '#4E4E4E',
  fontSize: '14px',
  padding: '0 20px',
  fontWeight: 'bold',
  flexDirection: 'row-reverse',
  '& .MuiAccordionSummary-expandIcon': {
    padding: 0,
  },
  '& .MuiAccordionSummary-expandIcon.Mui-expanded': {
    transform: 'rotate(90deg)',
  },
  '& .MuiSvgIcon-root': {
    fill: '#808080',
  },
}));

const AccordionDetails = styled(MuiAccordionDetails)(() => ({
  color: '#4E4E4E',
  fontSize: '14px',
  overflow: 'scroll',
  padding: '0 20px 20px',
  flexDirection: 'column',
  maxHeight: 'calc(((100vh - 200px) / 4) - 64px)',
}));

const Accordion = ({ title, isLoading, children }: Props) => {
  const [isOpen, setOpen] = useState(false);

  useEffect(() => {
    setOpen(Array.isArray(children) || isLoading);
  }, [children, isLoading]);

  return (
    <AccordionContainer
      data-testid="accordion"
      expanded={isOpen}
      onChange={() => setOpen(!isOpen)}
    >
      <AccordionSummary data-testid="accordion-summary">
        {title}
      </AccordionSummary>
      <AccordionDetails>
        {((isLoading && Array.isArray(children)) || !isLoading) && children}
        {isLoading && <Skeleton data-testid="skeleton" />}
      </AccordionDetails>
    </AccordionContainer>
  );
};

interface Props {
  readonly title: string;
  readonly isLoading: boolean;
  readonly children: ArrayOrSingle<
    string | React.ReactElement<any, string | React.JSXElementConstructor<any>>
  >;
}

export default Accordion;

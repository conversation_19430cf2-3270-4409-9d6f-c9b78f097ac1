import { DAG_REFERENCE_IDS } from '@helpers/constants';

export const GET_SIGNED_WRITABLE_URL_QUERY = `
  query getSignedWritableUrl($key: String, $expiresInSeconds: Int) {
    getSignedWritableUrl(key: $key, expiresInSeconds: $expiresInSeconds) {
      url
      getUrl
      unsignedUrl
      key
      bucket
      expiresInSeconds
    }
  }`;
export interface GetSignedWritableUrlResponse {
  getSignedWritableUrl: {
    url: string;
    getUrl: string;
    unsignedUrl: string;
    key: string;
    bucket: string;
    expiresInSeconds: number;
  };
}

export const CREATE_TDO_QUERY = `
  mutation createTDO($input: CreateTDO) {
    createTDO(input: $input) {
      id
      name
      status
      details
    }
  }`;
export interface CreateTDOResponse {
  createTDO: {
    id: string;
    name: string;
    status: string;
    details: JSON;
  };
}

export const CREATE_ASSET_FOR_IMAGE_QUERY = `
  mutation createAssetForImage($id: ID!, $unsignedUrl: String, $assetType: String, $contentType: String) {
    createAsset(input: {
      containerId: $id
      uri: $unsignedUrl
      assetType: $assetType
      contentType: $contentType
    }) {
      id
    }
  }`;
export interface CreateAssetForImageResponse {
  createAsset: {
    id: string;
  };
}

export const SET_PRIMARY_ASSET_FOR_IMAGE_QUERY = `
  mutation setPrimaryAsset($input: UpdateTDO) {
    updateTDO(input: $input) {
      id
    }
  }`;

export const CREATE_ENGINE_JOB_QUERY = `
    mutation createJobRedactObj(
      $id: ID!,
      $clusterId: ID!,
      $batchId: ID!,
      $glcIngestionEngineId: ID!,
      $fastChunkerEngineId: ID!,
      $detectionEngineId: ID!,
      $payload: JSONData!
      ) {
      createJob(input: {
          targetId: $id,
          clusterId: $clusterId,
          jobConfig: {
            batchId: $batchId,
          },
        tasks: [
          { # GLC Ingestor
            engineId: $glcIngestionEngineId
            payload: $payload
            executionPreferences: {
              priority: -20
            }
            ioFolders: [
              {
                referenceId: "ingestOutputFolder"
                mode: chunk
                type: output
              }
            ]
          },
          { # Chunk Video
            engineId: $fastChunkerEngineId
            payload: {
              comment: "FastVideoChunk"
              app: "redact"
              chunkDurationSecs: 60
              chunkOverlapSecs: 3
            }
            executionPreferences: {
              parentCompleteBeforeStarting: true
              priority: -5
            }
            ioFolders: [
              {
                referenceId: "chunkVideoInputFolder"
                mode: chunk
                type: input
              },
              {
                referenceId: "chunkVideoOutputFolder"
                mode: chunk
                type: output
              },
            ],
          },
          { # Head Detection
            engineId: $detectionEngineId
            payload: {
              confidenceThreshold: 0.40,
              videoType: "Bodycam",
              stepSizeDetection: 3,
              app: "redact",
              detectHead: true,
              detectLaptop: true,
              detectLicense: true,
              detectVehicle: true,
              mergeVehiclePlate: "plate",
              detectorType: "full"
            }
            executionPreferences: {
              maxEngines: 20
              parentCompleteBeforeStarting: true
              priority: -5
            }
            ioFolders: [
              {
                referenceId: "hdInputFolder"
                mode: chunk
                type: input
              },
              {
                referenceId: "hdOutputFolder"
                mode: chunk
                type: output
              }
            ]
          },
          { # Output Writer for HD
            engineId: "8eccf9cc-6b6d-4d7d-8cb3-7ebf4950c5f3"
              executionPreferences: {
                parentCompleteBeforeStarting: true
                priority: -15
              }
              ioFolders: [
                {
                  referenceId: "${DAG_REFERENCE_IDS['owFromHead']}"
                  mode: chunk
                  type: input
                }
              ]
          },
        ],
        routes: [
              # {  # Ingestor --> Chunk Audio
              #   parentIoFolderReferenceId: "ingestOutputFolder"
              #   childIoFolderReferenceId: "chunkAudioInputFolder"
              #   options: {}
              # },
              # {  # ChunkAudio --> Transcription
              #   parentIoFolderReferenceId: "chunkAudioOutputFolder"
              #   childIoFolderReferenceId: "transcriptionInputFolder"
              #   options: {}
              # },
              # {  # Transcription --> Output Writer
              #   parentIoFolderReferenceId: "transcriptionOutputFolder"
              #   childIoFolderReferenceId: "${DAG_REFERENCE_IDS['owFromTranscription']}"
              #   options: {}
              # },
              { # Ingestor to Chunk Video
                parentIoFolderReferenceId: "ingestOutputFolder"
                childIoFolderReferenceId: "chunkVideoInputFolder"
                options: {}
              },
              {  # Chunk Video --> Head Detection
                parentIoFolderReferenceId: "chunkVideoOutputFolder"
                childIoFolderReferenceId: "hdInputFolder"
                options: {}
              },
              {  # Head Detection --> Output Writer
                parentIoFolderReferenceId: "hdOutputFolder"
                childIoFolderReferenceId: "${DAG_REFERENCE_IDS['owFromHead']}"
                options: {}
              },
        ]
    }) {
        id
        targetId
        status
      }
    }`;

import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import Dialog from '@common-components/ConfirmDialog';

const ApproveCaseDialog = ({ isOpen, onConfirm, onClose }: Props) => {
  const intl = useIntl();

  return (
    <Dialog
      isOpen={isOpen}
      title={I18nTranslate.TranslateMessage('approveCaseTitle')}
      content={I18nTranslate.TranslateMessage('approveCaseContent')}
      confirmText={intl.formatMessage({ id: 'approve' })}
      onConfirm={onConfirm}
      cancelText={intl.formatMessage({ id: 'cancel' })}
      onCancel={onClose}
    />
  );
};

interface Props {
  readonly isOpen: boolean;
  readonly onConfirm: () => void;
  readonly onClose: () => void;
}

export default ApproveCaseDialog;

import { Router } from 'express';
import { validateToken } from './validations/validator';
import { healthcheck } from './controllers/healthcheck';
import { fileStatus } from './controllers/fileStatus';
import { createCase } from './controllers/createCase';
import { isAuthorized } from './controllers/authorization';
import { ingestMedia } from './controllers/ingestMedia';
import { createFolder } from './controllers/createFolder';
import { deleteFolder } from './controllers/deleteFolder';
import { deleteCase } from './controllers/deleteCase';
import { caseFileList} from './controllers/caseFileList';
import { redactedMedia } from './controllers/redactedMedia';
import { deleteFile } from './controllers/deleteFile';
import { mediaFileDetails } from './controllers/mediaFileDetails';

const routes = Router();

routes.get('/', (_req, res) => res.json({ message: 'Redact API!' }));
routes.get('/ping', healthcheck);
routes.get('/isAuthorized', validateToken, isAuthorized);
routes.get('/fileStatus/:tdoId', validateToken, fileStatus);
routes.get('/redactedMedia/:tdoId', validateToken, redactedMedia);
routes.get('/mediaFileDetails/:tdoId', validateToken, mediaFileDetails);
routes.get('/caseFileList/:caseId', validateToken, caseFileList);

routes.post('/createFolder', validateToken, createFolder);
routes.post('/createCase', validateToken, createCase);
routes.post('/ingestMedia', validateToken, ingestMedia);

routes.delete('/deleteFolder/:folderId', validateToken, deleteFolder);
routes.delete('/deleteCase/:caseId', validateToken, deleteCase);
routes.delete('/deleteFile/:tdoId', validateToken, deleteFile);
export default routes;

import { getLastAuditLog, getLastRedactExport } from '../selectors';
import * as actions from '../actions';
import { HighlightedOverlay, Tag } from '../models';
import { fetchCaseMediaSuccess } from './fetchCaseMedia';
import { findLast, findLastIndex, get, size, uniqBy } from 'lodash';
import { SummaryTDO } from '@redact-modules/mainPage/models';
import { cleanDeleteOverlayCache } from '../helpers/cache-helper';
import { defaultState, MainStore } from '../store';
import { getUDRGroupIntervalBasedSegments } from '@common/web-worker/selectors/clusters';
import { ROUTE_HOME, ROUTE_MEDIA_DETAILS } from '@common-modules/routing';
import {
  IN_SAVE_CHANGES,
  OUT_FETCH_REDACT_TDO_SUCCESS,
  // OUT_FETCH_DOWNLOAD_ASSET_SUCCESS,
  OUT_POLL_DOWNLOAD_ASSET_SUCCESS,
  OUT_POLL_DOWNLOAD_ASSET_FAILED,
  OUT_CREATE_JOB_FAIL,
  FETCH_DOWNLOAD_ASSET_FAILURE,
  FETCH_REDACT_TDO_FAILURE,
} from '@worker';
import {
  CREATE_JOB_FACES_ACTION,
  CREATE_JOB_FACES_FAILURE,
  ON_CHANGE_DETECT_OBJECTS,
  SHOW_DETECT_OPTIONS_DIALOG,
  CREATE_JOB_FACES,
  DETECT_OPTIONS,
} from '@common-modules/facesTabModule';
import {
  PROCESS_ENGINE_REQUEST as PROCESS_ENGINE_TRANSCRIPTION_REQUEST,
  CREATE_ENGINE_JOB_FAILURE as CREATE_ENGINE_JOB_TRANSCRIPTION_FAILURE,
} from '@common-modules/engines/transcription/actions';
import {
  selectComment,
  removeComment,
  addNewComment,
  fetchMediaCommentsUserSuccess,
  fetchMediaComments,
  updateCommentSuccess,
  fetchMediaCommentsSuccess,
  fetchMediaUserSuccess,
  basicUserQueryIsSupported,
  selectCommentsOnTimeline,
  updateStatusHideComment,
} from './mediaComments';
import { DEFAULT_GLOBAL_SETTINGS } from '@helpers/constants';

import { Action, createReducer } from '@reduxjs/toolkit';
import { produce, produceWithPatches, WritableDraft } from 'immer';
import getLastRedactedFile from '@helpers/getLastRedactedFile';
import { FETCH_INTEGRATION_INFO_SUCCESS } from '@helpers/externalIntegrationApi';

const getOriginalFileStatus = (details?: SummaryTDO['details']) => {
  const tag = details?.tags?.find(
    (tag) => tag?.value === actions.IN_REDACTION_TAG_KEY
  );
  return tag?.redactionStatus || 'Draft';
};

const onDeleteUDRSegment = (
  state: WritableDraft<MainStore>,
  action: {
    type: string;
    payload: actions.DeleteSegmentPayload;
  }
) => {
  const { groupId } = action.payload;
  const newState = onUpdateDeletedOverlaysCache(state, action);

  const { selectedUDRGroupId } = newState.udrsState;

  if (groupId === selectedUDRGroupId) {
    return produce(newState, (draft) => {
      draft.udrsState.selectedUDRGroupId = undefined;
    });
  }

  return newState;
};

const onDeleteUDRGroup = (
  state: WritableDraft<MainStore>,
  action: {
    type: string;
    payload: { boundingBox: { groupId: string } };
  }
) => {
  const { boundingBox } = action.payload;
  const groupId = boundingBox.groupId;
  const newState = onUpdateDeletedOverlaysCache(state, action);

  const { selectedUDRGroupId } = newState.udrsState;

  if (groupId === selectedUDRGroupId) {
    return produce(newState, (draft) => {
      draft.udrsState.selectedUDRGroupId = undefined;
    });
  }

  return newState;
};

const onUpdateDeletedOverlaysCache = (
  state: WritableDraft<MainStore>,
  action: Action<any>
) => {
  const cleanedDeletedOverlaysCache = cleanDeleteOverlayCache(
    state.deletedOverlaysCache
  );

  const [newState, patches, inversePatches] = produceWithPatches(
    state,
    (draft) => {
      draft.deletedOverlaysCache = cleanedDeletedOverlaysCache;
    }
  );

  const updateTimestamp = new Date().getTime();

  switch (true) {
    case actions.DELETE_CLUSTER.match(action):
      // CTODO there might be more we need to do here
      return produce(newState, (draft) => {
        draft.history.past.push({ patches, inversePatches });
        draft.history.future = [];
        draft.tdoIsChanged = true;
      });

    case actions.DELETE_UDR_OVERLAY.match(action):
    case actions.DELETE_UDR_IN_FRAME.match(action):
    case actions.DELETE_DETECTION_OVERLAY.match(action):
    case actions.DELETE_DETECTION_IN_FRAME.match(action):
      return produce(newState, (draft) => {
        draft.deletedOverlaysCache.overlayIdsMap[
          action.payload.boundingBox.id
        ] = updateTimestamp;
        draft.history.past.push({ patches, inversePatches });
        draft.history.future = [];
      });

    case actions.DELETE_UDR_SEGMENT.match(action): {
      const { groupId, segmentId, timeMs } = action.payload;

      const boundingPolysGroup = groupId
        ? newState.udrsState.udrAsset.boundingPolys[groupId]
        : null;

      if (groupId && boundingPolysGroup?.series) {
        const segments = getUDRGroupIntervalBasedSegments(
          boundingPolysGroup.series,
          groupId
        );

        // find segment
        let deleteSegment;
        if (segmentId) {
          deleteSegment = segments.find((seg) => seg.id === segmentId);
        } else {
          deleteSegment = segments.find(
            (seg) => seg.startTimeMs <= timeMs && seg.stopTimeMs > timeMs
          );
        }

        const objectIds = deleteSegment?.objectIds || [];

        return produce(newState, (draft) => {
          for (const objectId of objectIds) {
            draft.deletedOverlaysCache.overlayIdsMap[objectId] =
              updateTimestamp;
          }
          draft.history.past.push({ patches, inversePatches });
          draft.history.future = [];
        });
      }
      break;
    }
    case actions.DELETE_UDR_GROUP.match(action):
      return produce(newState, (draft) => {
        draft.deletedOverlaysCache.overlaySubsegmentIdsMap[
          action.payload.boundingBox.subsegmentId
        ] = updateTimestamp;
        draft.history.past.push({ patches, inversePatches });
        draft.history.future = [];
      });
    case actions.DELETE_DETECTION_SEGMENT.match(action): {
      const { timeMs, segmentId, groupId } = action.payload;
      if (!groupId) {
        return newState;
      }

      const group = state.detectionClusterGroups[groupId];
      if (!group) {
        return newState;
      }

      // search for segment
      let segment;
      if (segmentId) {
        // faster when segmentId is available
        segment = group.segments.find((segment) => segment.id === segmentId);
      } else {
        segment = group.segments.find(
          (segment) =>
            segment.startTimeMs <= timeMs && segment.stopTimeMs > timeMs
        );
      }

      const overlayIdsMapPartialUpdate = segment?.objectIds.reduce<
        Record<string, number>
      >((map, id) => {
        map[id] = updateTimestamp;
        return map;
      }, {});

      return produce(newState, (draft) => {
        draft.deletedOverlaysCache.overlayIdsMap = {
          ...draft.deletedOverlaysCache.overlayIdsMap,
          ...overlayIdsMapPartialUpdate,
        };
        draft.history.past.push({ patches, inversePatches });
        draft.history.future = [];
      });
    }
    case actions.DELETE_DETECTION_GROUP.match(action): {
      const { groupId } = action.payload.boundingBox;
      if (!groupId) {
        return newState;
      }

      const group = state.detectionClusterGroups[groupId];
      if (!group) {
        return newState;
      }

      return produce(newState, (draft) => {
        group.segments.forEach(
          (segment) =>
            // GTODO should this be looping over subsegments?
            (draft.deletedOverlaysCache.overlaySubsegmentIdsMap[segment.id] =
              updateTimestamp)
        );
        draft.history.past.push({ patches, inversePatches });
        draft.history.future = [];
      });
    }
  }
  return newState;
};

const onSetDeletedOverlaysCache = (
  state: WritableDraft<MainStore>,
  action: { payload: typeof state.deletedOverlaysCache }
) => ({
  ...state,
  deletedOverlaysCache: action.payload,
});

export const onFetchTDOSuccess = (
  state: WritableDraft<MainStore>,
  action: { payload: { temporalDataObject: NonNullable<typeof state.tdo> } }
) => ({
  ...state,
  fetchingTdo: false,
  tdo: {
    ...action.payload?.temporalDataObject,
    details: {
      ...action.payload?.temporalDataObject?.details,
      settings: {
        ...DEFAULT_GLOBAL_SETTINGS,
        interpolationMaxStretch: undefined, // preserve as undefined so we don't modify legacy TDOs
        ...action.payload?.temporalDataObject?.details?.settings,
      },
    },
  },
  failedFetchingTdo: false,
  originalFileStatus: getOriginalFileStatus(
    action.payload.temporalDataObject.details || {}
    // get(action, 'payload.temporalDataObject.details', {})
  ),
  trimInterval: get(
    action,
    'payload.temporalDataObject.details.redact.trimInterval',
    undefined
  ),
});

export default createReducer(defaultState, (builder) => {
  builder
    .addCase(ROUTE_HOME, () => defaultState)
    .addCase(ROUTE_MEDIA_DETAILS, () => defaultState)

    .addCase(actions.PAGE_LOADED, (state) => ({
      ...state,
      loaded: true,
    }))

    .addCase(actions.FETCH_TDO, (state) => ({
      ...state,
      tdo: null,
      fetchingTdo: true,
      failedFetchingTdo: false,
      facesSelectedGroup: null,
    }))
    .addCase(actions.FETCH_TDO_WITH_FAILURE, (state, action) => ({
      ...state,
      fetchingTdo: false,
      tdo: get(action, 'payload.temporalDataObject', null),
      failedFetchingTdo: false,
    }))
    .addCase(actions.SET_AUDIOWAVES, (state, action) => ({
      ...state,
      audiowaves: action.payload,
    }))
    .addCase(actions.REFRESH_TDO_PRIMARY_ASSET_ID_SUCCESS, (state, action) => {
      if (!state.tdo) {
        return;
      }

      if (state.tdo.primaryAsset) {
        state.tdo.primaryAsset.id =
          action.payload.temporalDataObject.primaryAsset.id;
      } // else {
      // TODO: The original code did this, but it doesn't make any sense to me to refresh the id on
      // a non-existent primaryAsset. Should we really be doing this?
      // state.tdo.primaryAsset = {
      //   id: action.payload.temporalDataObject.primaryAsset.id,
      // };
      // }
    })
    .addCase(actions.REFRESH_TDO_TASKS_SUCCESS, (state, action) => {
      const mergeRecords = get(
        action,
        'payload.temporalDataObject.tasks.records',
        []
      ).concat(get(state, 'tdo.tasks.records', []));
      if (state.tdo) {
        state.tdo.tasks = { records: uniqBy(mergeRecords, 'id') }; // take first occurrence of task id
      }
    })
    .addCase(actions.REDACTED_FILE_COMPLETED, (state, action) => ({
      ...state,
      numberOfRedactedFile:
        action.payload.tdoId === get(state, 'tdo.id')
          ? state.numberOfRedactedFile + 1
          : state.numberOfRedactedFile,
    }))
    .addCase(actions.SELECT_REDACTED_FILES_TAB, (state) => ({
      ...state,
      mediaDetailsPageSelectedTab: actions.SELECT_REDACTED_FILES_TAB.type,
      numberOfRedactedFile: 0,
    }))
    .addCase(actions.SELECT_RESULTS_TAB, (state) => ({
      ...state,
      mediaDetailsPageSelectedTab: actions.SELECT_RESULTS_TAB.type,
    }))
    .addCase(actions.FACES_SELECT_GROUP_FACES, (state, action) => ({
      ...state,
      facesSelectedGroup: action.payload.groupId,
    }))
    .addCase(actions.FACE_HIGHLIGHT, (state, action) => {
      const { payload } = action;
      let newHighlightedOverlay: HighlightedOverlay | undefined = undefined;

      if (payload !== null && payload.groupId) {
        const { id, timeMs, groupId, type } = payload;
        newHighlightedOverlay = {
          id,
          timeMs,
          groupId,
          type,
        };
      }

      const prevHighlightedOverlay = state.highlightedOverlay;
      const selectedUDRGroupId = state.udrsState.selectedUDRGroupId;

      const shouldResetSelectedUDRGroupId =
        selectedUDRGroupId &&
        selectedUDRGroupId !== newHighlightedOverlay?.groupId &&
        prevHighlightedOverlay?.groupId !== selectedUDRGroupId;

      state.highlightedOverlay = newHighlightedOverlay;

      if (shouldResetSelectedUDRGroupId) {
        state.udrsState.selectedUDRGroupId = undefined;
      }
    })
    .addCase(actions.FACE_HIGHLIGHT_UNSELECT, (state) => {
      const isSomethingSelected = size(state.highlightedOverlay) > 0;

      state.highlightedOverlay = isSomethingSelected
        ? undefined // All logic related to highlightedOverlay is checked to ensure the highlightedOverlay condition exists, so this is safe
        : state.highlightedOverlay;
    })
    .addCase(actions.FACES_SELECT_ENGINE, (state, action) => ({
      ...state,
      facesSelectedEngineId: action.payload.facesSelectedEngineId,
    }))
    // SAVE button
    .addCase(IN_SAVE_CHANGES, (state) => ({
      ...state,
      tdoIsChanged: false,
    }))
    .addCase(actions.UPDATE_CURRENT_TIME_MEDIA_PLAYER, (state, action) => ({
      ...state,
      currentPosition: action.payload.currentPosition,
    }))
    .addCase(actions.START_DRAG_AND_DROP_POSITION_BAR, (state) => ({
      ...state,
      dragAndDropPositionBar: true,
    }))
    .addCase(actions.DROP_POSITION_BAR, (state) => ({
      ...state,
      dragAndDropPositionBar: false,
    }))
    .addCase(actions.FETCH_DELETE_LAST_ASSET_SUCCESS, (state) => {
      if (!state.tdo) {
        return;
      }
      const lastRedactedMediaAsset = getLastRedactedFile(state.tdo);
      let redactedMediaRecords = state.tdo?.redactedMediaAssets?.records || [];
      if (lastRedactedMediaAsset) {
        redactedMediaRecords = redactedMediaRecords.filter(
          (record) => record.id !== lastRedactedMediaAsset.id
        );
      }

      const lastAuditLogAsset = getLastAuditLog(state.tdo);
      let auditLogRecords = state.tdo?.auditLogAssets?.records || [];
      if (lastAuditLogAsset) {
        auditLogRecords = auditLogRecords.filter(
          (record) => record.id !== lastAuditLogAsset.id
        );
      }

      const lastExportAsset = getLastRedactExport(state.tdo);
      let redactExportRecords = state.tdo?.redactExportAssets?.records || [];
      if (lastExportAsset) {
        redactExportRecords = redactExportRecords.filter(
          (record) => record.id !== lastExportAsset.id
        );
      }

      state.tdo.redactedMediaAssets = Object.assign(
        state.tdo.redactedMediaAssets || {},
        {
          records: redactedMediaRecords,
        }
      );
      state.tdo.auditLogAssets = Object.assign(state.tdo.auditLogAssets || {}, {
        records: auditLogRecords,
      });
      state.tdo.redactExportAssets = Object.assign(
        state.tdo.redactExportAssets || {},
        {
          records: redactExportRecords,
        }
      );
    })
    .addCase(actions.STATUS_CHANGE, (state, action) => {
      let stateCopy = { ...state };
      const status = action.payload.status ? action.payload.status : 'Draft';

      // if no tdo exit
      if (!state.tdo || !stateCopy.tdo) {
        return state;
      }

      if (state.tdo.details?.tags) {
        let tag: Tag | null = null;
        const tagIndex = findLastIndex(
          state.tdo.details.tags,
          (item) => item?.value === actions.IN_REDACTION_TAG_KEY
        );

        if (tagIndex > -1) {
          tag =
            findLast(
              state.tdo.details.tags,
              (item) => item?.value === actions.IN_REDACTION_TAG_KEY
            ) ?? null;
        }

        const newTags = state.tdo.details.tags.slice();
        if (tag) {
          newTags[tagIndex] = { ...tag, redactionStatus: status };
        } else {
          newTags.push({
            value: actions.IN_REDACTION_TAG_KEY,
            redactionStatus: status,
          });
        }

        stateCopy = {
          ...stateCopy,
          tdo: {
            ...state.tdo,
            details: {
              ...state.tdo.details,
              tags: newTags,
            },
          },
        };
      } else {
        // TypeScript cannot ensure that every attribute of stateCopy copied from state is not null.
        if (stateCopy.tdo.details) {
          stateCopy.tdo.details.tags = [
            { value: actions.IN_REDACTION_TAG_KEY, redactionStatus: status },
          ];
        } else {
          stateCopy.tdo.details = {
            tags: [
              { value: actions.IN_REDACTION_TAG_KEY, redactionStatus: status },
            ],
            settings: DEFAULT_GLOBAL_SETTINGS,
          };
        }
      }

      if (
        status === state.originalFileStatus ||
        (status === 'Draft' && !state.originalFileStatus)
      ) {
        stateCopy.isFileStatusChanged = false;
      } else {
        stateCopy.isFileStatusChanged = true;
      }

      return stateCopy;
    })
    .addCase(actions.FETCH_STATUS_CHANGE, (state) => ({
      ...state,
      fetchingUpdateStatus: true,
      failedUpdateStatus: false,
    }))
    .addCase(actions.FETCH_STATUS_CHANGE_SUCCESS, (state, action) => {
      const { updateTDO } = action.payload;
      return {
        ...state,
        originalFileStatus: getOriginalFileStatus(updateTDO?.details),
        isFileStatusChanged: false,
        fetchingUpdateStatus: false,
        failedUpdateStatus: false,
      };
    })
    .addCase(actions.FETCH_STATUS_CHANGE_FAILURE, (state) => ({
      ...state,
      fetchingUpdateStatus: false,
      failedUpdateStatus: true,
    }))
    .addCase(actions.PLAY_REDACTED_ASSET, (state) => {
      const { metrics } = getLastRedactedFile(state.tdo)?.details || {};
      if (metrics) {
        metrics.views += 1;
      }
      state.redactedAssetStarted = true;
    })
    // download
    .addCase(actions.DOWNLOAD_ASSET, (state) => {
      const { metrics } = getLastRedactedFile(state.tdo)?.details || {};
      if (metrics) {
        metrics.downloads += 1;
      }
      // start spinner when click download
      state.downloadStarted = true;
    })
    .addCase(actions.RETRY_DOWNLOAD, (state) => ({
      ...state,
      downloadStarted: true,
    }))
    .addCase(actions.UPDATE_DOWNLOAD_URL, (state, action) => ({
      ...state,
      downloadUrl: action.payload.downloadUrl,
    }))
    // 3 action to check status download in Media Detail
    .addCase(actions.DOWNLOAD_ASSET_JOB_STATUS, (state) => ({
      ...state,
      failedGettingDownloadJobStatus: false,
    }))
    .addCase(actions.DOWNLOAD_ASSET_JOB_STATUS_SUCCESS, (state, action) => ({
      ...state,
      downloadJobStatus: action.payload.job.status,
      failedGettingDownloadJobStatus: false,
    }))
    .addCase(actions.DOWNLOAD_ASSET_JOB_STATUS_FAILURE, (state) => ({
      ...state,
      failedGettingDownloadJobStatus: true,
    }))
    .addCase(actions.FETCH_DOWNLOAD_ASSET, (state) => ({
      ...state,
      fetchingCreateDownloadTask: true,
      pollingDownloadDone: false,
    }))
    .addCase(actions.FETCH_DOWNLOAD_ASSET_SUCCESS, (state) => ({
      ...state,
      fetchingCreateDownloadTask: false,
    }))
    .addCase(actions.FETCH_DOWNLOAD_ASSET_FAILURE, (state) => ({
      ...state,
      fetchingCreateDownloadTask: false,
    }))
    .addCase(actions.POLL_DOWNLOAD_ASSET, (state) => ({
      ...state,
      pollingDownloadDone: false,
    }))
    .addCase(actions.POLL_DOWNLOAD_ASSET_SUCCESS, (state, action) => ({
      ...state,
      auditLogs: get(action, 'payload.temporalDataObject.assets.records', []),
      failedGettingDownloadJobStatus: false,
      pollingDownloadDone: true,
      downloadJobStatus: false,
      downloadJob: null,
    }))
    .addCase(actions.POLL_DOWNLOAD_ASSET_FAILURE, (state) => ({
      ...state,
      pollingDownloadDone: true,
    }))
    .addCase(OUT_POLL_DOWNLOAD_ASSET_SUCCESS, (state, action) => {
      state.downloadStarted = false;
      if (!state.tdo) {
        return;
      }
      state.tdo.redactExportAssets = Object.assign(
        state.tdo.redactExportAssets || {},
        {
          records:
            action?.payload?.payload?.temporalDataObject?.assets.records || [],
        }
      );
      // pollingDownloadDone: true,
    })
    .addCase(OUT_POLL_DOWNLOAD_ASSET_FAILED, (state) => ({
      ...state,
      downloadStarted: false,
    }))
    .addCase(CREATE_JOB_FACES_ACTION, (state) => ({
      ...state,
      buttonDetectFaceDisable: true,
    }))
    .addCase(actions.UN_DISABLE_FACE_BUTTON, (state) => ({
      ...state,
      buttonDetectFaceDisable: false,
    }))
    .addCase(actions.DISABLE_FACE_BUTTON, (state) => ({
      ...state,
      buttonDetectFaceDisable: true,
    }))
    .addCase(PROCESS_ENGINE_TRANSCRIPTION_REQUEST, (state) => ({
      ...state,
      buttonTranscriptionDisable: true,
    }))
    .addCase(OUT_CREATE_JOB_FAIL, (state, action) => {
      const { type } = action.payload;

      switch (type) {
        case FETCH_DOWNLOAD_ASSET_FAILURE.type:
          return {
            ...state,
            downloadStarted: false,
          };
        case FETCH_REDACT_TDO_FAILURE.type:
          return {
            ...state,
            gettingRedactionJobStatus: false,
          };
        case CREATE_JOB_FACES_FAILURE.type:
          return {
            ...state,
            buttonDetectFaceDisable: false,
          };
        case CREATE_ENGINE_JOB_TRANSCRIPTION_FAILURE.type:
          return {
            ...state,
            buttonTranscriptionDisable: false,
          };
        default:
          return { ...state };
      }
    })
    .addCase(actions.DISCARD_CHANGES, (state) => ({
      ...state,
      tdoIsChanged: false,
    }))
    //    VTN-10079 MDP Page not updating when Redact Engine Process Completes
    .addCase(actions.REDACTION_JOB_STATUS, (state) => ({
      ...state,
      gettingRedactionJobStatus: true,
      failedGettingRedactionJobStatus: false,
    }))
    .addCase(actions.REDACTION_JOB_STATUS_SUCCESS, (state, action) => ({
      ...state,
      redactionJobStatus: action.payload.job.status,
      redactionJobCreatedDateTime: action.payload.job.createdDateTime,
      failedGettingRedactionJobStatus: false,
    }))
    .addCase(actions.REDACTION_JOB_STATUS_FAILURE, (state) => ({
      ...state,
      gettingRedactionJobStatus: false,
      failedGettingRedactionJobStatus: true,
    }))
    .addCase(actions.REDACTION_JOB_STATUS_ACTION_STOP_POLLING, (state) => ({
      ...state,
      redactionJob: null,
      creatingRedactionJob: false,
      failedCreatingRedactionJob: false,
      failedGettingJobStatus: false,
    }))
    .addCase(OUT_FETCH_REDACT_TDO_SUCCESS, (state, action) => ({
      ...state,
      redactionJob: action.payload.createJob,
      gettingRedactionJobStatus: true,
      redactedAssetStarted: false,
    }))
    .addCase(actions.FETCH_REDACTED_MEDIA_SUCCESS, (state, action) => {
      if (state.tdo && action.payload) {
        const records =
          [] as typeof action.payload.temporalDataObject.redactedMediaAssets.records;
        const auditRecords =
          [] as typeof action.payload.temporalDataObject.auditLogAssets.records;
        const exportRecords =
          [] as typeof action.payload.temporalDataObject.redactExportAssets.records;

        Array.prototype.push.apply(
          records,
          get(
            action,
            'payload.temporalDataObject.redactedMediaAssets.records',
            []
          )
        );
        Array.prototype.push.apply(
          auditRecords,
          get(action, 'payload.temporalDataObject.auditLogAssets.records', [])
        );
        Array.prototype.push.apply(
          exportRecords,
          get(
            action,
            'payload.temporalDataObject.redactExportAssets.records',
            []
          )
        );
        if (state.tdo.redactedMediaAssets) {
          state.tdo.redactedMediaAssets.records = records;
        } else {
          state.tdo.redactedMediaAssets = { records };
        }
        if (state.tdo.auditLogAssets) {
          state.tdo.auditLogAssets.records = auditRecords;
        } else {
          state.tdo.auditLogAssets = { records: auditRecords };
        }
        if (state.tdo.redactExportAssets) {
          state.tdo.redactExportAssets.records = exportRecords;
        } else {
          state.tdo.redactExportAssets = { records: exportRecords };
        }
      }
      state.gettingRedactionJobStatus = false;
      state.redactionJobStatus = false;
      state.redactionJobCreatedDateTime = null;
      state.redactionJob = null;
    })
    .addCase(actions.SHOW_BLOCK_NAVIGATION_MODAL, (state, action) => ({
      ...state,
      isShowConfirmLeave: true,
      message: action.payload.message,
      canLeave: action.payload.canLeave,
    }))
    .addCase(actions.HIDE_BLOCK_NAVIGATION_MODAL, (state) => ({
      ...state,
      isShowConfirmLeave: false,
      message: '',
      canLeave: null,
    }))
    .addCase(actions.ACTION_KEY_SHIFT, (state, action) => ({
      ...state,
      isHoldingShift: action.payload.isKeyDown,
    }))
    .addCase(actions.MEDIA_HAS_AUDIO, (state, action) => ({
      ...state,
      hasAudio: action.payload.hasAudio,
    }))
    .addCase(actions.VIDEO_HAS_LOADED, (state, action) => ({
      ...state,
      videoLoaded: action.payload.videoLoaded,
    }))
    .addCase(actions.SET_THUMBNAIL_TRACK, (state, action) => ({
      ...state,
      thumbnailTracks: action.payload.thumbnailTracks,
    }))
    .addCase(actions.SET_SHAKA_PLAYER, (state, action) => ({
      ...state,
      shakaPlayer: action.payload.shakaPlayer || undefined, // TODO: Should this be null or undefined?
    }))
    .addCase(
      actions.SET_GOVQA_SESSIONID,
      (state, { payload: { sessionId } }) => ({
        ...state,
        integrations: {
          ...state.integrations,
          govQa: {
            ...state.integrations.govQa,
            sessionId: sessionId || undefined,
          },
        },
      })
    )
    .addCase(
      actions.PATCH_EXTERNAL_INTEGRATION_STATE,
      (state, { payload }) => ({
        ...state,
        integrations: {
          ...state.integrations,
          externalIntegration: {
            ...state.integrations.externalIntegration,
            ...payload,
          },
        },
      })
    )
    .addCase(actions.PATCH_GOVQA_STATE, (state, { payload }) => ({
      ...state,
      integrations: {
        ...state.integrations,
        govQa: {
          ...state.integrations.govQa,
          ...payload,
        },
      },
    }))
    .addCase(actions.PATCH_FOIAXPRESS_STATE, (state, { payload }) => ({
      ...state,
      integrations: {
        ...state.integrations,
        foiaXpress: {
          ...state.integrations.foiaXpress,
          ...payload,
        },
      },
    }))
    .addCase(actions.PATCH_CASEPOINT_STATE, (state, { payload }) => ({
      ...state,
      integrations: {
        ...state.integrations,
        casepoint: {
          ...state.integrations.casepoint,
          ...payload,
        },
      },
    }))
    .addCase(actions.PATCH_EXTERRO_STATE, (state, { payload }) => ({
      ...state,
      integrations: {
        ...state.integrations,
        exterro: {
          ...state.integrations.exterro,
          ...payload,
        },
      },
    }))
    .addCase(actions.PATCH_NUIX_STATE, (state, { payload }) => ({
      ...state,
      integrations: {
        ...state.integrations,
        nuix: {
          ...state.integrations.nuix,
          ...payload,
        },
      },
    }))
    .addCase(
      actions.SET_SELECTED_UDR_GROUP,
      (state, { payload: { groupId } }) => {
        let newState = {
          ...state,
          udrsState: {
            ...state.udrsState,
            selectedUDRGroupId: groupId,
          },
        };

        const highlightedOverlay = state.highlightedOverlay;

        if (
          groupId &&
          highlightedOverlay?.id &&
          highlightedOverlay.groupId !== groupId
        ) {
          newState = {
            ...newState,
            highlightedOverlay: undefined,
          };
        }

        return newState;
      }
    )
    .addCase(actions.SET_IS_SAVE_RUNNING, (state, action) => ({
      ...state,
      isSaveRunning: action?.payload?.isSaveRunning,
    }))
    .addCase(actions.TOGGLE_CASE_MEDIA_DRAWER, (state) => ({
      ...state,
      isCaseMediaDrawerOpen: !state.isCaseMediaDrawerOpen,
    }))
    .addCase(actions.DELETE_CASE_DRAWER_TDO_SUCCESS, (state, action) => ({
      ...state,
      caseMedia: {
        ...state.caseMedia,
        media: [
          ...((state.caseMedia?.media || []).filter(
            (file) => file.id !== action?.payload?.tdoId
          ) || []),
        ],
      },
    }))
    .addCase(actions.DELETE_DETECTION_OVERLAY, onUpdateDeletedOverlaysCache)
    .addCase(actions.DELETE_DETECTION_IN_FRAME, onUpdateDeletedOverlaysCache)
    .addCase(actions.DELETE_DETECTION_SEGMENT, onUpdateDeletedOverlaysCache)
    .addCase(actions.DELETE_DETECTION_GROUP, onUpdateDeletedOverlaysCache)
    .addCase(actions.DELETE_UDR_OVERLAY, onUpdateDeletedOverlaysCache)
    .addCase(actions.DELETE_UDR_IN_FRAME, onUpdateDeletedOverlaysCache)
    .addCase(actions.DELETE_UDR_SEGMENT, onDeleteUDRSegment)
    .addCase(actions.DELETE_UDR_GROUP, onDeleteUDRGroup)
    .addCase(actions.DELETE_CLUSTER, onUpdateDeletedOverlaysCache)
    .addCase(actions.SET_DELETED_OVERLAYS_CACHE, onSetDeletedOverlaysCache)
    // @ts-expect-error TODO: Fix types
    .addCase(actions.FETCH_CASE_MEDIA_SUCCESS, fetchCaseMediaSuccess)
    // @ts-expect-error TODO: Fix types
    .addCase(actions.FETCH_TDO_SUCCESS, onFetchTDOSuccess)
    .addCase(actions.SELECT_COMMENT, selectComment)
    .addCase(actions.SELECT_COMMENT_ON_TIMELINE, selectCommentsOnTimeline)
    .addCase(actions.REMOVE_COMMENT, removeComment)
    .addCase(actions.ADD_NEW_COMMENT, addNewComment)
    .addCase(actions.UPDATE_STATUS_HIDE_COMMENT, updateStatusHideComment)
    .addCase(
      actions.FETCH_MEDIA_COMMENTS_USER_SUCCESS,
      fetchMediaCommentsUserSuccess
    )
    .addCase(actions.FETCH_MEDIA_COMMENTS, fetchMediaComments)
    .addCase(actions.UPDATE_COMMENT_SUCCESS, updateCommentSuccess)
    .addCase(actions.FETCH_MEDIA_COMMENTS_SUCCESS, fetchMediaCommentsSuccess)
    .addCase(actions.FETCH_MEDIA_USER_SUCCESS, fetchMediaUserSuccess)
    .addCase(actions.BASIC_USER_QUERY_IS_SUPPORTED, basicUserQueryIsSupported)
    .addCase(actions.TDO_NAME_CHANGE_SUCCESS, (state, action) => {
      const { updateTDO } = action.payload;
      if (!state.tdo || state.tdo.id !== updateTDO.id) {
        console.error('TDO not found while updating name');
        return state;
      }

      return {
        ...state,
        tdo: {
          ...state.tdo,
          ...updateTDO,
        },
      };
    })
    .addCase(ON_CHANGE_DETECT_OBJECTS, (state, action) => {
      state.detectObjects[action.payload.name] = action.payload.value;
    })
    .addCase(SHOW_DETECT_OPTIONS_DIALOG, (state, action) => ({
      ...state,
      showDetectOptionsDialog: action.payload.value,
    }))
    .addCase(CREATE_JOB_FACES, (state) => ({
      ...state,
      detectObjects: {
        [DETECT_OPTIONS.HEAD_OBJECTS]: false,
        [DETECT_OPTIONS.PERSON_OBJECTS]: false,
      },
    }))
    .addCase(
      actions.SET_IS_PLAYER_SEEKED,
      (state, { payload: isPlayerSeeked }) => ({
        ...state,
        isPlayerSeeked,
      })
    )
    .addCase(FETCH_INTEGRATION_INFO_SUCCESS, (state, { payload }) => ({
      ...state,
      integrations: {
        ...state.integrations,
        externalIntegration: {
          ...state.integrations.externalIntegration,
          config: payload.me.organization.integrationConfig?.config || {},
        },
      },
    }));
});

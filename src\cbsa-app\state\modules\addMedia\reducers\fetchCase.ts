import { Re } from '../reducers';
import { AddMediaStore } from '../AddMediaStore';
import { Case } from '@cbsa-modules/universal';
import {
  FETCH_CASE_NOTIFICATIONS_QUERY_RESPONSE,
  FETCH_TDOS_QUERY_RESPONSE,
} from '../services/queries/fetchCase';
import { ElementOf } from 'ts-essentials';

export const fetchTdosSuccess: Re<FETCH_TDOS_QUERY_RESPONSE> = (
  state,
  { payload }
) => {
  const tdos: Record<
    string,
    ElementOf<FETCH_TDOS_QUERY_RESPONSE['folder']['childTDOs']['records']>
  > = {};
  (payload.folder?.childTDOs?.records || []).forEach(
    (tdo) => (tdos[tdo?.id] = tdo)
  );

  return {
    ...state,
    tdos,
    loaders: {
      ...state.loaders,
      isLoadingCaseTdosInitial: false,
    },
  };
};

export const onSetLoaders: Re<Partial<AddMediaStore['loaders']>> = (
  state,
  { payload }
) => ({
  ...state,
  loaders: {
    ...state.loaders,
    ...payload,
  },
});

export const onSetCaseDetails: Re<Case> = (state, { payload }) => ({
  ...state,
  caseDetails: {
    ...payload,
  },
});

export const fetchCaseNotificationsSuccess: Re<
  FETCH_CASE_NOTIFICATIONS_QUERY_RESPONSE
> = (state, { payload }) => ({
  ...state,
  notifications: payload.structuredDataObjects?.records,
});

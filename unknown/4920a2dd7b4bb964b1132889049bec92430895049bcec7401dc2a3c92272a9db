import express from 'express';
import { isEmpty, pick } from 'lodash';
import { checkNameExistsAdapter } from '../adapters/checkNameExists';
import { getRootFolderAdapter } from '../adapters/getRootFolder';
import { checkFolderExistsAdapter } from '../adapters/checkFolderExists';
import { createFolderAdapter } from '../adapters/createFolder';
import { Messages } from '../errors/messages';
import { StatusCodes } from '../errors/statusCodes';
import { getUserInfoByTokenAdapter } from '../adapters/getUserInfoByToken';
import { isAPITokenKey, isFolderId } from '../validations/helpers';
import { FolderId } from '../model/brands';

export const createFolder = async (
  req: express.Request,
  res: express.Response
) => {
  const headers = pick(req.headers, ['authorization']);
  let userInfo = undefined;
  if(isAPITokenKey(headers)) {
    userInfo  = await getUserInfoByTokenAdapter(headers);
    if (userInfo?.error) {
      return res.status(StatusCodes.BadRequest).json({
        error: userInfo.error,
      });
    }
    if (isEmpty(userInfo?.userId)) {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.FetchUserInfoFail
      });
    }
  }

  const { parentFolderId, name, description } = req.body;
  let isNameExist = false;
  let rootFolderId = undefined;

  if (isEmpty(name?.trim())) {
    return res.status(StatusCodes.BadRequest).json({
      error: Messages.NameIsRequired
    });
  }

  if (!isFolderId(parentFolderId)) {
    rootFolderId = (await getRootFolderAdapter(headers) || '') as FolderId; // Safe to set '' as FolderId?
    if (!isFolderId(rootFolderId)) {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.fetchRootFolderError,
      });
    }
    isNameExist = await checkNameExistsAdapter(headers, { folderId: rootFolderId, name: name});
  } else {
    rootFolderId = parentFolderId
    const folder = await checkFolderExistsAdapter( headers, { folderId: parentFolderId} );
    if (folder) {
      const isCase = (folder.contentTemplates?.length || 0) > 0;
      const isFolderDepthInLimit = (folder?.folderPath?.length || 0) < 3;
      if (isCase) {
        return res.status(StatusCodes.BadRequest).json({
          error: Messages.InvalidParentFolderId,
        });
      }
      if (!isFolderDepthInLimit ) {
        return res.status(StatusCodes.BadRequest).json({
          error: Messages.FolderDepthLimitReached,
        });
      }
      isNameExist = await checkNameExistsAdapter(headers, { folderId: parentFolderId, name: name});
    }
    else {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.ParentFolderNotExists
      });
    }
  }

  if (isNameExist) {
    return res.status(StatusCodes.BadRequest).json({
      error: Messages.NameExistsAlready,
    });
  } else {
    const createFolder = await createFolderAdapter(headers,  {
        parentFolderId: rootFolderId,
        name: name,
        description: description || name,
        userId: userInfo?.userId
    });
    if (createFolder) {
      return res.status(StatusCodes.InsertedSuccess).json({
        newFolderId: createFolder.id,
        name: createFolder.name
      });
    } else {
      return res.status(StatusCodes.BadRequest).json({
        error: Messages.createFolderFail,
      });
    }
  }
};


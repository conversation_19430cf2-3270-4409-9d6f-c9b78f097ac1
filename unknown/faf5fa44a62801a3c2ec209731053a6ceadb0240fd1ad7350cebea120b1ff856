import { useIntl } from 'react-intl';
import { I18nTranslate } from '@i18n';
import Dialog from '@common-components/ConfirmDialog';

const ReadyForReviewCaseDialog = ({ isOpen, onConfirm, onClose }: Props) => {
  const intl = useIntl();
  return (
    <Dialog
      isOpen={isOpen}
      title={I18nTranslate.TranslateMessage('readyForReviewTitle')}
      content={I18nTranslate.TranslateMessage('readyForReviewTitleContent')}
      confirmText={intl.formatMessage({ id: 'readyForReview' })}
      cancelText={intl.formatMessage({ id: 'cancel' })}
      onConfirm={onConfirm}
      onCancel={onClose}
    />
  );
};

interface Props {
  readonly isOpen: boolean;
  readonly onConfirm: () => void;
  readonly onClose: () => void;
}

export default ReadyForReviewCaseDialog;
